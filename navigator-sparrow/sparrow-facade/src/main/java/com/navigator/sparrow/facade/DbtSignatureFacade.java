package com.navigator.sparrow.facade;


import cn.signit.sdk.pojo.ErrorResp;
import cn.signit.sdk.pojo.webhook.response.WebhookResponse;
import com.navigator.sparrow.pojo.dto.CheckRequestDTO;
import com.navigator.sparrow.pojo.dto.StartEnvelopeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 15:35
 */
@FeignClient(name = "navigator-sparrow-service")
@Component
public interface DbtSignatureFacade {
    /**
     * 回调接口
     *
     * @param webhookResponse
     */
    @PostMapping("/callbackFunction")
    void callbackFunction(@RequestBody WebhookResponse webhookResponse);

    /**
     * 发起签署接口
     */
    @PostMapping("/envelopesStart")
    ErrorResp envelopesStart(@RequestBody StartEnvelopeDTO startEnvelopeDTO);


    /**
     * 易企签撤销签署流程
     *
     * @param startEnvelopeDTO
     */
    @PostMapping("/revocation")
    boolean revocation(@RequestBody StartEnvelopeDTO startEnvelopeDTO);


    /**
     * 查询用户是否实名
     *
     * @param checkRequestDTO
     * @return
     */
    @PostMapping("/hasAuthentication")
    boolean hasAuthentication(@RequestBody CheckRequestDTO checkRequestDTO) throws Exception;
}
