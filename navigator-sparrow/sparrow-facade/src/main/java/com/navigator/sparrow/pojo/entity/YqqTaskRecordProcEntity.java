package com.navigator.sparrow.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/21 10:36
 */

@Data
@Accessors(chain = true)
@ApiModel(value="yqqTaskRecordProcEntity对象", description="")
@TableName("dbz_yqq_task_record_proc")
public class YqqTaskRecordProcEntity implements Serializable {

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "yqq_task_record表Id")
    private Integer taskRecordId;

    @ApiModelProperty(value = "处理节点")
    private Integer disposeNode;

    @ApiModelProperty(value = "接口参数json参数")
    private String data;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
}
