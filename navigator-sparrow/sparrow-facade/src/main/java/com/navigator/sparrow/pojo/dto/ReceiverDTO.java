package com.navigator.sparrow.pojo.dto;

import com.navigator.trade.pojo.enums.SignatureTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/25 10:19
 */
@Data
@Accessors(chain = true)
public class ReceiverDTO {
    //接收方所在企业名称
    private String enterpriseName;
    //签署人姓名
    private String receiverName;
    //签署人号码
    private String receiverContact;
    //签章位置(关键字)
    private String keyword;
    //签章顺序
    private Integer assignedSequence;
    //是否是non-frame
    private Integer ldcFrame;
    //客户是否已经签章
    private Integer isCustomerSignature;
    //客户是否已经签章
    private Integer signatureType = SignatureTypeEnum.LDC_ONE_SIGNATURE.getValue();

}
