package com.navigator.sparrow.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/26 14:19
 */
@Data
@Accessors(chain = true)
public class CheckRequestDTO {
    //需要查询实名认证的手机号
    private String phone;
    //唯一编码
    private String customTag;
    //客户id
    private Integer customerId;

    /*
    extends AbstractSignitRequest<CheckResponseDTO>
    @Override
    public Class<CheckResponseDTO> getResponseClass() {
        return CheckResponseDTO.class;
    }*/
}
