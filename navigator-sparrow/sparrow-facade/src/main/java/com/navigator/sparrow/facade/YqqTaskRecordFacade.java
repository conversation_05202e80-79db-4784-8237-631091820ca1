package com.navigator.sparrow.facade;

import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/22 9:48
 */

@Component
@FeignClient(name = "navigator-sparrow-service")
public interface YqqTaskRecordFacade {


    /**
     * 将文件保存进入blob中
     *
     * @param bizId
     * @return
     */
    @GetMapping("/yqqFileSaveBlob")
    boolean yqqFileSaveBlob(@RequestParam(value = "bizId") Integer bizId);

    /**
     * 绑定协议和文件关系
     *
     * @param bizId
     * @return
     */
    @GetMapping("/bindingSignFileRelation")
    boolean bindingSignFileRelation(@RequestParam(value = "bizId") Integer bizId);


    /**
     * 事件完成(发送邮件)修改协议状态
     *
     * @param bizId
     * @return
     */
    @GetMapping("/callbackAfterProcess")
    boolean callbackAfterProcess(@RequestParam(value = "bizId") Integer bizId);


    /**
     * 根据bizId发起签章
     *
     * @param bizId
     * @return
     */
    @GetMapping("/sendYQQRequest")
    boolean sendYQQRequest(@RequestParam(value = "bizId") Integer bizId);

    /**
     * 根据bizId发起签章
     *
     * @return
     */
    @GetMapping("/unfinishedYqqTaskRecord")
    List<YqqTaskRecordEntity> unfinishedYqqTaskRecord();
}
