package com.navigator.sparrow.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/7 17:15
 */

@Getter
@AllArgsConstructor
public enum SelectedAuthTypesEnum {

    SIGN_PIN(1, "SIGN_PIN","密码"),
    SMS_CODE(2, "SMS_CODE","短信"),
    EMAIL_CODE(3, "EMAIL_CODE","邮件"),
    ;

    private Integer value;
    private String desc;
    String description;
}
