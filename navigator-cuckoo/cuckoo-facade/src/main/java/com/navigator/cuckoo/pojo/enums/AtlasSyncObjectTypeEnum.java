package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * 同步对象类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Getter
public enum AtlasSyncObjectTypeEnum {
    /**
     * 同步对象状态
     */
    CONTRACT("Contract", "合同"),
    PRICE("Price", "定价单"),
    DELIVERY("Delivery Request", "提货"),
    ;
    final String value;
    final String desc;

    AtlasSyncObjectTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasSyncObjectTypeEnum getByValue(String value) {
        for (AtlasSyncObjectTypeEnum typeEnum : AtlasSyncObjectTypeEnum.values()) {
            if (Objects.equals(value, typeEnum.getValue())) {
                return typeEnum;
            }
        }
        return AtlasSyncObjectTypeEnum.CONTRACT;
    }

}
