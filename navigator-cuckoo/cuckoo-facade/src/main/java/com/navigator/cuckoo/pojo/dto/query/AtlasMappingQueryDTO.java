package com.navigator.cuckoo.pojo.dto.query;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * atlas 查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
public class AtlasMappingQueryDTO {
    /**
     * 公司主体
     */
    private String companyBusinessEntity;

    /**
     * 合同采销类型（1.采购 2.销售）
     */
    private Integer salesType;

    /**
     * 合同主体
     */
    private String contractBusinessEntity;

    /**
     * 合同编号
     */
    private String navContractCode;

    /**
     * ATLAS主体
     */
    private String atlasBusinessEntity;

    /**
     * ATLAS合同编号
     */
    private String atlasContractCode;


    /**
     * Split from ATLAS(当子合同为在ATLAS拆分建立的，前台显示 标记)
     */
    private Integer splitFlag;

    /**
     * 创建开始时间
     */
    private String createStartTime;

    /**
     * 创建结束时间
     */
    private String createEndTime;
}
