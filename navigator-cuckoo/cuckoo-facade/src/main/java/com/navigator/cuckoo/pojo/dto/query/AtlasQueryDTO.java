package com.navigator.cuckoo.pojo.dto.query;

import com.navigator.cuckoo.pojo.enums.AtlasSyncRecordListTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * atlas 查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
public class AtlasQueryDTO {

    /**
     * 传输对象
     */
    private String objectType;

    /**
     * 传输时间
     */
    private String syncStartTime;

    /**
     * 传输时间
     */
    private String syncEndTime;

    /**
     * 所属公司/实体
     */
    private String businessEntity;

    /**
     * 业务单编号
     */
    private String businessDocId;

    /**
     * 传输状态
     */
    private String syncStatus;

    /**
     * 列表类型 1：全部 2：待处理
     * <p>
     * {@link AtlasSyncRecordListTypeEnum}
     */
    private Integer recordListType;

    /**
     * 操作类型
     */
    private String operationType;
}
