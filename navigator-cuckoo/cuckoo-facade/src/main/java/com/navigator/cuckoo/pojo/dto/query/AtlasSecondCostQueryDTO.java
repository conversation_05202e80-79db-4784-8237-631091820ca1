package com.navigator.cuckoo.pojo.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * atlas 2nd cost 查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
public class AtlasSecondCostQueryDTO {

    @ApiModelProperty(value = "价格字段")
    private String priceDetailField;

    @ApiModelProperty(value = "同步场景：新增-CREATE，更新-Amendment，拆分-Splitting")
    private String syncAction;

    @ApiModelProperty(value = "交货方式")
    private Integer deliveryType;

    @ApiModelProperty(value = "交货方式名称")
    private String deliveryTypeName;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;


}
