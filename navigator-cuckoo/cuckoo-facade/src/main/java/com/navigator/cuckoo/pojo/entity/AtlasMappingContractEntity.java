package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * ATLAS合同关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_contract_execute")
@ApiModel(value = "AtlasMappingContractEntity对象", description = "ATLAS合同关系表")
public class AtlasMappingContractEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "回执消息里面的origMessageId")
    private String uuid;

    @ApiModelProperty(value = "公司主体")
    private String companyBusinessEntity;

    @ApiModelProperty(value = "ATLAS主体")
    private String atlasBusinessEntity;

    @ApiModelProperty(value = "合同主体")
    private String contractBusinessEntity;

    @ApiModelProperty(value = "合同采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "航海家合同编号")
    private String navContractCode;

    @ApiModelProperty(value = "ATLAS合同编号")
    private String atlasContractCode;

    @ApiModelProperty(value = "Split from ATLAS(当子合同为在ATLAS拆分建立的) ")
    private Integer splitFlag;

    @ApiModelProperty(value = "合同数量")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "回执接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date callBackTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "已执行数量")
    private BigDecimal executedNum;

    @ApiModelProperty(value = "已分配数量")
    private BigDecimal allocatedNum;

    // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07
    @ApiModelProperty(value = "版本号")
    private Integer version;

}
