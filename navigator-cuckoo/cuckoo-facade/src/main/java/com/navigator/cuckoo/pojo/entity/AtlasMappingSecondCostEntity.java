package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 次级费用映射表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_mapping_second_cost")
@ApiModel(value = "AtlasMappingSecondCostEntity对象", description = "")
public class AtlasMappingSecondCostEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增Id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "次级费用code")
    private String code;

    @ApiModelProperty(value = "次级费用名称")
    private String name;

    @ApiModelProperty(value = "交货方式")
    private String deliveryType;

    @ApiModelProperty(value = "交货方式名称")
    private String deliveryTypeName;

    @ApiModelProperty(value = "单价明细字段")
    private String priceDetailField;

    @ApiModelProperty(value = "同步场景：新增-CREATE，更新-Amendment，拆分-Splitting")
    private String syncAction;

    @ApiModelProperty(value = "业务线,默认是现货合同")
    private String buCode;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "状态（0.禁用 1.启用）")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
