package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * contractData
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractDataDTO {
    /**
     * 部门 默认值：S389501
     */
    private String department;

    /**
     * 所属商务 (全名)
     */
    private String trader;

    /**
     * 签订日期 格式：”YYYY-MM-DD”
     */
    private String contractDate;

    /**
     * 是否采购合同
     * a) 本合同是采购合同: Y
     * b) 本合同是销售合同: N
     */
    private String isPurchase;

    /**
     * 买/卖方主体编码
     */
    private String counterpartyID;

    /**
     * 货品代码
     */
    private String commodityID;

    /**
     * 总数量
     */
    private String contractQuantity;

    /**
     * 数量单位，默认值：MT
     */
    private String quantityUnit;

    /**
     * 包装代码
     */
    private String packingCode;

    /**
     * 袋皮扣重(kg)
     * 如果不扣重：0
     * 其他情况：取数值
     * 空值：保留空值
     */
    private String packingWeight;

    /**
     * 交提货方式 :“自提”，值为：EXW，“配送”，值为：CNF
     */
    private String contractTerms;

    /**
     * 目的港
     */
    private String destination;

    /**
     * 发货库点
     */
    private String warehouse;

    /**
     *
     */
    private String periodType;

    /**
     * 开始交货日期
     */
    private String periodStart;

    /**
     * 截止交货日期
     */
    private String periodEnd;

    /**
     * 交货工厂
     */
    private String terminal;

    /**
     * 备注
     */
    private String comment;

    /**
     * 溢短装
     */
    private String tolerance;

    /**
     * 发票类型代码
     */
    private String taxCode;

    /**
     * 履约保证金释放方式
     * a) 随车按比例释放:  0
     * b) 抵扣最后一笔: 1
     */
    private String cashReleaseMethod;

    /**
     * 代加工 Y/N
     */
    private String isOEM;

    /**
     * Mapped form 区域
     */
    private String marketZone;

    /**
     * 是否是第三方交割库-非LDC库（不定库中的）
     */
    private String thirdpartyTerminalOnly;

    /**
     * 是否双签
     */
    private String isApproved;

    /**
     * 是否已关闭合同
     */
    private String isClosed;

    /**
     * 是否是DCE
     */
    private String isExchangeContract;

    /**
     * 仓单号码
     */
    private String warrantNumber;

    /**
     * 解约定赔标识 默认值：WS
     */
    private String shippingStatus;

}
