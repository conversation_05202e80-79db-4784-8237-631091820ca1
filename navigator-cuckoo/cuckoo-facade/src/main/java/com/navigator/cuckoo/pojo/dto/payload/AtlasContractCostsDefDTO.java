package com.navigator.cuckoo.pojo.dto.payload;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * contractCosts
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
public class AtlasContractCostsDefDTO {
    /**
     * 传输ATLAS次级费用
     */
    private AtlasContractCostsDTO contractCosts;

    /**
     * 次级费用排除字段
     */
    private List<String> excludeFields;
}
