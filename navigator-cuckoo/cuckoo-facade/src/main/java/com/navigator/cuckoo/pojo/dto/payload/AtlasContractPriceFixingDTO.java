package com.navigator.cuckoo.pojo.dto.payload;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * AtlasContractPriceFixingDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
public class AtlasContractPriceFixingDTO {

    /**
     * 定价单id
     */
    private String fixingID;

    /**
     * 审核时间
     */
    private String fixingDate;

    /**
     * 期货合约
     */
    private String fixingPrompt;

    /**
     * 盘面成交价
     */
    private String fixingPrice;

    /**
     * 已定价量
     */
    private String fixingQuantity;

    /**
     * Send "BEO" for sales contracts,
     * "SEO" for purchase contracts
     */
    private String fixingCode;


}
