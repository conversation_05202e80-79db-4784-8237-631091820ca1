package com.navigator.cuckoo.pojo.dto.ack;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * items
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/17
 */
@Data
public class AtlasAckItemDTO {
    private String ackBusinessEntity;
    private String ackBusinessDocID;
    private LineItemAckDTO lineItemAckList;

    @Data
    public static class LineItemAckDTO {
        private List<LineItemAck> lineItemAck;
    }

    @Data
    public static class LineItemAck {
        private String origItemNumber;
        private ParameterList parameterList;
        private ReturnMessageList returnMessageList;
    }

    @Data
    public static class ReturnMessageList {
        private List<AtlasReturnMessageDTO> returnMessage;
    }

    @Data
    public static class ParameterList {
        private List<AtlasParameterDTO> parameter;
    }
}
