package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 同步状态 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-17
 */
@Getter
public enum AtlasSyncStatusEnum {
    /**
     * 同步状态
     */
    NOT_CALL_ATLAS("NOT-CALL-ATLAS", "未调用ATLAS API"),
    SENT_SUCCESS("SENT-SUCCESS", "调用ATLAS API成功"),
    SENT_ERROR("SENT-ERROR", "调用ATLAS API失败"),
    ACK_SUCCESS("ACK-SUCCESS", "ATLAS处理成功"),
    ACK_ERROR("ACK-ERROR", "ATLAS处理失败"),
    ;
    String value;
    String desc;

    AtlasSyncStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasSyncStatusEnum getByValue(String value) {
        for (AtlasSyncStatusEnum statusEnum : AtlasSyncStatusEnum.values()) {
            if (value.equals(statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return AtlasSyncStatusEnum.SENT_SUCCESS;
    }

}
