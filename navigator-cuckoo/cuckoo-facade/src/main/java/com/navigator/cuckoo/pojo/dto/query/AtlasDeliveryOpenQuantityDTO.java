package com.navigator.cuckoo.pojo.dto.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasDeliveryOpenQuantityDTO {

    private OpenQuantityDetailDTO openQuantityDetails;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OpenQuantityDetailDTO {

        private String shareContractList;

        private List<OpenQuantityDetailItemDTO> parameterList;

        private String contractList;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class OpenQuantityDetailItemDTO {

        private String businessEntity;

        private String contractType;

        private String currency;

        private String commodityID;

        private String contractTerms;

        private BigDecimal openQuantity;

        private String terminal;

        private String warrantNumber = "";
    }
}
