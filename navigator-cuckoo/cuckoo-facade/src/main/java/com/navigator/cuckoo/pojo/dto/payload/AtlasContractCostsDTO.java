package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * contractCosts
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractCostsDTO {
    private List<ContractCostDTO> costList;

    @Data
    @Accessors(chain = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ContractCostDTO {
        /**
         * 费用代码
         */
        private String code;

        /**
         * 费用类型 默认：R
         */
        private String type;

        /**
         * 费用金额
         */
        private String rateInclVAT;

        /**
         * 货币类型 默认: CNY
         */
        private String currency;

        /**
         * priceCode 默认: PMT
         */
        private String priceCode;

        /**
         * 是否是采销 S:Y P:N
         */
        private String isPayable;

        /**
         * 客户MdmID
         */
        private String counterpartyID;
    }
}
