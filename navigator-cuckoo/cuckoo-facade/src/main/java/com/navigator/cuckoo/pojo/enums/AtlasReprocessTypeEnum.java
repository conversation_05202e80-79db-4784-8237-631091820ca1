package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 重新推送 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Getter
public enum AtlasReprocessTypeEnum {
    /**
     * 重新推送状态
     */
    INIT(0, "", "初始值"),
    REPROCESSED(1, "reprocessed", "重新传输"),
    CANCELLED(2, "cancelled", "放弃"),
    ;
    int value;
    String name;
    String desc;

    AtlasReprocessTypeEnum(int value, String name, String desc) {
        this.value = value;
        this.name = name;
        this.desc = desc;
    }

    public static AtlasReprocessTypeEnum getByValue(int value) {
        for (AtlasReprocessTypeEnum typeEnum : AtlasReprocessTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return AtlasReprocessTypeEnum.INIT;
    }

}
