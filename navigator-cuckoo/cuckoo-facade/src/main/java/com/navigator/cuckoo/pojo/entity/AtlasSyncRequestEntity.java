package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ATLAS 请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_sync_request")
@ApiModel(value = "AtlasSyncRequestEntity对象", description = "ATLAS 请求记录表")
public class AtlasSyncRequestEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "TTid")
    private Integer ttId;

    @ApiModelProperty(value = "业务单据id")
    private Integer bizId;

    @ApiModelProperty(value = "业务单据号，本次为合同编号")
    private String bizCode;

    @ApiModelProperty(value = "传输对象，区分本次接口传输的是合同、定价单")
    private String objectType;

    @ApiModelProperty(value = "处理类型，区分本次接口传输的是新增、修改")
    private String operationType;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "请求场景")
    private Integer tradeType;

    @ApiModelProperty(value = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date syncTime;

    @ApiModelProperty(value = "同步状态")
    private String syncStatus;

    @ApiModelProperty(value = "传输原始信息")
    private String requestInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "接口信息传输用户")
    private String createdBy;

    @ApiModelProperty(value = "接口信息最后修改用户")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "操作来源")
    @TableField(exist = false)
    private String optionSource;

}
