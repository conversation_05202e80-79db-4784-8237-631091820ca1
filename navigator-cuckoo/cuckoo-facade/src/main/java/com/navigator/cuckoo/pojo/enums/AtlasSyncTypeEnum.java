package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 同步类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Getter
public enum AtlasSyncTypeEnum {
    /**
     * 同步状态
     */
    SYNC(1, "同步请求"),
    ASYNC(2, "异步请求"),
    ;
    int value;
    String desc;

    AtlasSyncTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasSyncTypeEnum getByValue(int value) {
        for (AtlasSyncTypeEnum typeEnum : AtlasSyncTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return AtlasSyncTypeEnum.ASYNC;
    }

}
