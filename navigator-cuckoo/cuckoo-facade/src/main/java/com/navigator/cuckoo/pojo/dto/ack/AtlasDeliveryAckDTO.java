package com.navigator.cuckoo.pojo.dto.ack;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * DR execution回调json的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/18
 */
@Data
public class AtlasDeliveryAckDTO {

    private DeliveryAckDTO delivery;

    @Data
    public static class DeliveryAckDTO {
        private FunctionalDocID functionalDocID;
        private String functionalAckIsRequired;
        private DeliveryHeaderDTO deliveryHeader;
        private DeliveryDataDTO deliveryData;
    }

    @Data
    public static class FunctionalDocID {
        private String businessAppID;
        private String businessEntity;
        private String businessDocID;
    }

    @Data
    public static class DeliveryHeaderDTO {
        private List<TargetApplicationDTO> targetApplication;
    }

    @Data
    public static class TargetApplicationDTO {
        private String applicationCode;
    }

    @Data
    public static class DeliveryDataDTO {
        private String registrationType;
        private String creationStatus;
        private List<DeliveryNoteListDTO> deliveryNoteList;
    }

    @Data
    public static class DeliveryNoteListDTO {
        private String deliveryNo;
        private String contractNo;
        private List<DeliveryNoteItemListDTO> deliveryNoteItemList;
    }

    @Data
    public static class DeliveryNoteItemListDTO {
        private String plannedQuantity;
        private String weightUnit;
    }
}
