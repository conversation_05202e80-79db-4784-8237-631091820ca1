package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * split
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/20
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractSplitDTO {

    /**
     * 同步的拆分类型
     */
    private String splitType;

    /**
     * 原定价单的集合
     */
    private List<OriginalPriceFixingDTO> originalPriceFixingList;

    @Data
    public static class OriginalPriceFixingDTO {
        /**
         * 原定价单id
         */
        private String originalFixingID;
    }
}
