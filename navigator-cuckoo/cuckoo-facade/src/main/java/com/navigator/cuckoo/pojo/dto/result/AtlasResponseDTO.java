package com.navigator.cuckoo.pojo.dto.result;

import com.navigator.common.enums.ResultCodeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.poi.ss.formula.functions.T;

/**
 * <p>
 * Atlas ResponseDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Builder
@Accessors(chain = true)
public class AtlasResponseDTO {
    private Integer code;
    private String message;
    private Object data;


    public static AtlasResponseDTO success(T data) {
        return AtlasResponseDTO.builder()
                .code(ResultCodeEnum.OK.getCode())
                .message(ResultCodeEnum.OK.getMsg())
                .data(data)
                .build();
    }

    public static AtlasResponseDTO success() {
        return success(null);
    }
}
