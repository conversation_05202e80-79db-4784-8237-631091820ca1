package com.navigator.cuckoo.pojo.dto.payload;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * AtlasContractCreatePriceDetailDTO
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
public class AtlasContractPriceDetailDTO {


    /**
     * 价格名称
     */
    private String name;


    /**
     * 价格中文名称
     */
    private String nameCN;


    /**
     * 价格
     */
    private String value;


    /**
     * isFOB
     */
    private String isFOB;


}
