package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * contractPricing
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractPricingDTO {
    /**
     * 根据“合同类型”赋值：
     * If (一口价 or 暂定价) then N
     * If(基差 or 基差暂定价 or Basis turn to Flat) then F
     */
    private String pricingMethod;

    /**
     * 根据“合同类型”赋值：
     * If (一口价 or 基差  ) then N
     * If(暂定价 or 基差暂定价) then Y
     */
    private String isDeferred;

    /**
     * 默认值：CNY
     */
    private String currency;

    /**
     * 默认值：PMT
     */
    private String priceCode;

    /**
     * 付款方式代码
     */
    private String paymentTerm;

    /**
     * 含税单价
     */
    private String priceInclVAT;

    /**
     * 根据dbt_contract的合同采销类型判断：销售：BEO 采购：SEO
     */
    private String fixingType;

    /**
     * 交易所名称
     */
    private String fnoMarket;

    /**
     * 转月前的期货合约 "YYYY-MM-32"
     */
    private String originalPrompt;

    /**
     * 期货合约 "YYYY-MM-32"
     */
    private String prompt;

    /**
     * 点价截止日期
     */
    private String fixationDueDate;

    /**
     * 合同价格明细累加
     */
    private String premiumDiscount;

    /**
     * 是否全部定价
     */
    private String fullyFixed;

    /**
     * 转月申请的盘面成交价
     */
    private String monthRollSpread;

    /**
     * 定价单
     */
    private List<AtlasContractPriceFixingDTO> priceFixingList;

    /**
     * 价格明细
     */
    private List<AtlasContractPriceDetailDTO> priceDetailList;


}
