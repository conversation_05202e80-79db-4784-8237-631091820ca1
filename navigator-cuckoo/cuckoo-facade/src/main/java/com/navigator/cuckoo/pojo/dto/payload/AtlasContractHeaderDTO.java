package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * header
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractHeaderDTO {
    /**
     * 系统ID 默认值：NVG
     */
    private String businessAppID;

    /**
     * 所属公司/实体 默认值：x9
     */
    private String businessEntity;

    /**
     * 业务单据号
     */
    private String businessDocID;

    /**
     * 拆分：业务单据号
     */
    private String originalBusinessDocID;

    /**
     * 全局唯一变量UUID
     */
    private String uuid;
}
