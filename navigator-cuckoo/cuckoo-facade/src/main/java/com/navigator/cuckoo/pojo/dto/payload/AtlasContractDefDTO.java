package com.navigator.cuckoo.pojo.dto.payload;


import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * atlas 合同DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasContractDefDTO {
    private ContractInfo contract;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class ContractInfo {
        private AtlasContractHeaderDTO header;

        private AtlasContractDataDTO contractData;

        private AtlasContractPricingDTO contractPricing;

        private AtlasContractSplitDTO contractSplit;

        private AtlasContractCostsDTO contractCosts;

        public ContractInfo(AtlasContractHeaderDTO header, AtlasContractDataDTO contractData, AtlasContractPricingDTO contractPricing, AtlasContractCostsDTO costsDTO) {
            this.header = header;
            this.contractData = contractData;
            this.contractPricing = contractPricing;

            // 次级费用可选
            if (CollectionUtil.isNotEmpty(costsDTO.getCostList())) {
                this.contractCosts = costsDTO;
            }
        }

        public ContractInfo(AtlasContractHeaderDTO header, AtlasContractPricingDTO contractPricing) {
            this.header = header;
            this.contractPricing = contractPricing;
        }

        public ContractInfo(AtlasContractHeaderDTO header, AtlasContractDataDTO contractData, AtlasContractPricingDTO contractPricing, AtlasContractSplitDTO contractSplit, AtlasContractCostsDTO costsDTO) {
            this.header = header;
            this.contractData = contractData;
            this.contractPricing = contractPricing;
            this.contractSplit = contractSplit;

            // 次级费用可选
            if (CollectionUtil.isNotEmpty(costsDTO.getCostList())) {
                this.contractCosts = costsDTO;
            }
        }
    }

    /**
     * Create
     */
    public AtlasContractDefDTO createDefDTO(AtlasContractHeaderDTO headerDTO, AtlasContractDataDTO createDataDTO, AtlasContractPricingDTO pricingDTO, AtlasContractCostsDTO costsDTO) {
        this.contract = new ContractInfo(headerDTO, createDataDTO, pricingDTO, costsDTO);
        return this;
    }

    /**
     * Modify
     */
    public AtlasContractDefDTO modifyDefDTO(AtlasContractHeaderDTO headerDTO, AtlasContractDataDTO createDataDTO, AtlasContractPricingDTO pricingDTO, AtlasContractCostsDTO costsDTO) {
        this.contract = new ContractInfo(headerDTO, createDataDTO, pricingDTO, costsDTO);
        return this;
    }

    /**
     * Split
     */
    public AtlasContractDefDTO splitDefDTO(AtlasContractHeaderDTO headerDTO, AtlasContractDataDTO createDataDTO, AtlasContractPricingDTO pricingDTO, AtlasContractSplitDTO contractSplit, AtlasContractCostsDTO costsDTO) {
        this.contract = new ContractInfo(headerDTO, createDataDTO, pricingDTO, contractSplit, costsDTO);
        return this;
    }

    /**
     * Price
     */
    public AtlasContractDefDTO priceDefDTO(AtlasContractHeaderDTO headerDTO, AtlasContractPricingDTO pricingDTO) {
        this.contract = new ContractInfo(headerDTO, pricingDTO);
        return this;
    }


}


