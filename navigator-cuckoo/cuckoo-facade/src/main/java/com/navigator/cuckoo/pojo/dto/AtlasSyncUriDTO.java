package com.navigator.cuckoo.pojo.dto;

import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AtlasSyncUriDTO {

    @ApiModelProperty(value = "同步合同的DTO")
    private AtlasContractDefDTO contractDefDTO;

    @ApiModelProperty(value = "同步提货的DTO")
    private AtlasDeliveryDefDTO deliveryDefDTO;

    @ApiModelProperty(value = "操作类型")
    private String operationType;

    @ApiModelProperty(value = "请求场景")
    private Integer tradeType;

    @ApiModelProperty(value = "请求地址")
    private String requestUri;

}
