package com.navigator.cuckoo.pojo.vo;

import com.navigator.cuckoo.pojo.entity.AtlasSyncCallbackEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * record 前端列表
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/23
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtlasSyncRecordVO extends AtlasSyncRecordEntity {

    /**
     * 当前是这个uuid第几次接收到回执
     */
    private Integer ackSequence = 1;

    /**
     * 回执记录
     */
    private AtlasSyncCallbackEntity callbackEntity;

}
