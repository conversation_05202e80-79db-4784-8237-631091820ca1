package com.navigator.cuckoo.pojo.dto.importer;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class DepartmentMappingImportDTO {

    @Excel(name = "航海家货品名称")
    private String goodsName;

    @Excel(name = "Department for H1")
    private String departmentForH1;

    @Excel(name = "Department for HA")
    private String departmentForHA;

    @Excel(name = "Department for HE")
    private String departmentForHE;

    @Excel(name = "MDM ID(现货)")
    private String spotMdmId;

    @Excel(name = "MDM ID(仓单)")
    private String warrantMdmId;

    @Excel(name = "MDM ID(豆二仓单)")
    private String soybean2MdmId;

    @Excel(name = "备注")
    private String remark;

}
