package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * <p>
 * ATLAS提货状态
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-06
 */
@Getter
public enum AtlasSyncDeliveryStatusEnum {
    /**
     * 提货单状态
     */
    REJECTED("Rejected", "拒绝"),
    BLOCKED("Blocked", "阻塞"),
    CONFIRMATION("Awaiting confirmation", "待确认"),
    CONFIRMED("Confirmed", "审核通过"),
    CANCELLATION("Cancellation Requested", "作废待审核"),
    CANCELED("Cancelled", "已作废"),
    ;
    final String value;
    final String desc;

    AtlasSyncDeliveryStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasSyncDeliveryStatusEnum getByValue(String value) {
        for (AtlasSyncDeliveryStatusEnum statusEnum : AtlasSyncDeliveryStatusEnum.values()) {
            if (Objects.equals(value, statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return AtlasSyncDeliveryStatusEnum.CONFIRMATION;
    }

}
