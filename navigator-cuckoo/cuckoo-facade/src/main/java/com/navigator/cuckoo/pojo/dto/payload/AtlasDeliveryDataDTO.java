package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * contractData
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasDeliveryDataDTO {
    /**
     * 买/卖方主体编码
     */
    private String counterpartyID;

    /**
     * 货品代码
     */
    private String commodityID;

    /**
     * 提货申请数量
     */
    private String deliveryQuantity;

    /**
     * 提货工厂
     */
    private String deliveryFactory;

    /**
     * TBD
     */
    private String terminal;

    /**
     * 计划提货日期
     */
    private String planDeliverydate;

    /**
     * LDC库/外库 1.LDC库 2.外库
     */
    private String warehouseType;

    /**
     * 提货库点
     */
    private String deliveryWarehouse;

    /**
     * 交提货方式
     */
    private String contractTerms;

    /**
     * 车/船号
     */
    private String truckNumber;

    /**
     * 挂车号
     */
    private String trailerNumber;

    /**
     * 司机姓名
     */
    private String driver;

    /**
     * 司机身份证号
     */
    private String driverID;

    /**
     * 司机电话
     */
    private String driverTel;

    /**
     * 随车电话
     */
    private String truckTel;

    /**
     * 是否拼车/船
     */
    private String isCarpool;

    /**
     * 拼车/船信息
     */
    private String carpoolInfo;

    /**
     * 拼车/船优先执行
     */
    private String carpoolPriority;

    /**
     * 申请人
     */
    private String createdBy;

    /**
     * 申请时间
     */
    private String createdAt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 运输方式
     */
    private String transportationMode;

    /**
     * 是否是DCE
     */

    private String isExchangeDelivery;

    /**
     * 是否是豆二
     */
    private String isSoybean2;

    /**
     * 仓单号码
     */
    private String warrantNumber;

    /**
     * 提货方
     */

    private String picker;


    /**
     * 有几家拼车数量
     */
    private String isFinal;

}
