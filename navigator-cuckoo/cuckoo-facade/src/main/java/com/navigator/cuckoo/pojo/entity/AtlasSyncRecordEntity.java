package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ATLAS 传输记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_sync_record")
@ApiModel(value = "AtlasSyncRecordEntity对象", description = "ATLAS 传输记录表")
public class AtlasSyncRecordEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联的requestId")
    private Integer requestId;

    @ApiModelProperty(value = "唯一编码")
    private String uuid;

    @ApiModelProperty(value = "业务单据id")
    private Integer bizId;

    @ApiModelProperty(value = "业务单据号，本次为合同编号")
    private String bizCode;

    @ApiModelProperty(value = "传输对象，区分本次接口传输的是合同、定价单")
    private String objectType;

    @ApiModelProperty(value = "处理类型，区分本次接口传输的是新增、修改")
    private String operationType;

    @ApiModelProperty(value = "请求场景")
    private Integer tradeType;

    @ApiModelProperty(value = "同步次数")
    private Integer tryTimes;

    @ApiModelProperty(value = "同步类型：1.同步 2.异步")
    private Integer syncType = 2;

    @ApiModelProperty(value = "业务单据号，本次为合同编号")
    private String businessDocId;

    @ApiModelProperty(value = "所属公司/实体")
    private String businessEntity;

    @ApiModelProperty(value = "请求atlas传输的信息")
    private String atlasRequestInfo;

    @ApiModelProperty(value = "atlas返回的信息")
    private String atlasResultsInfo;

    @ApiModelProperty(value = "接口状态")
    private String syncStatus;

    @ApiModelProperty(value = "提货委托在ATLAS CN的初步存在状态")
    private String status;

    @ApiModelProperty(value = "提货委托在ATLAS CN的处理状态")
    private String subStatus;

    @ApiModelProperty(value = "是否已经被重新传输或放弃")
    private String reprocessed;

    @ApiModelProperty(value = "目标系统")
    private String targetSystem;

    @ApiModelProperty(value = "接收到回执的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ackTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "是否删除（0:未删除 1:已删除）")
    private Integer isDeleted;

    @ApiModelProperty(value = "接口信息传输用户")
    private String createdBy;

    @ApiModelProperty(value = "接口信息最后修改用户")
    private String updatedBy;

    @ApiModelProperty(value = "接口信息传输时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "接口信息最后修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "接口请求的uri")
    @TableField(exist = false)
    private String requestUri;

}
