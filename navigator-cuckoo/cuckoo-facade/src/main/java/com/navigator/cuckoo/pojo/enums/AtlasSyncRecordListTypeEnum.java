package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 同步列表 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-18
 */
@Getter
public enum AtlasSyncRecordListTypeEnum {
    /**
     * 同步状态
     */
    ALL(1, "全部列表"),
    PENDING(2, "待处理列表"),
    ;
    int value;
    String desc;

    AtlasSyncRecordListTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasSyncRecordListTypeEnum getByValue(int value) {
        for (AtlasSyncRecordListTypeEnum typeEnum : AtlasSyncRecordListTypeEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return AtlasSyncRecordListTypeEnum.ALL;
    }

}
