package com.navigator.cuckoo.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 合同对外暴露的接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@FeignClient(name = "navigator-cuckoo-service")
public interface AtlasContractFacade {

    // ============================= 服务调用  =================================
    @PostMapping("/syncContractRequest")
    Result<Void> syncContractRequest(@RequestBody AtlasSyncRequestDTO syncRequestDTO);

    @PostMapping("/syncDeliveryRequest")
    Result<Void> syncDeliveryRequest(@RequestBody AtlasSyncRequestDTO syncRequestDTO);

    @GetMapping("/getContractOpenQuantity")
    Result<BigDecimal> getContractOpenQuantity(@RequestParam("businessEntity") String businessEntity, @RequestParam("contractCode") String contractCode);

    @PostMapping("/getDeliveryOpenQuantity")
    Result<AtlasDeliveryOpenQuantityDTO> getDeliveryOpenQuantity(@RequestParam("counterpartyId") String counterpartyId, @RequestBody AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO);

    @GetMapping("getByNavContractCode")
    Result<AtlasMappingContractEntity> getByNavContractCode(@RequestParam("contractCode") String contractCode);

    @PostMapping("/updateAtlasMappingContract")
    Result updateAtlasMappingContract(@RequestBody AtlasMappingContractEntity atlasMappingContractEntity);

    @GetMapping("getContractBlockedQuantity")
    Result<BigDecimal> getContractBlockedQuantity(@RequestParam("contractCode") String contractCode);

    // ============================= 前端调用  =================================
    @PostMapping("/getSyncRecordList")
    Result getSyncRecordList(@RequestBody QueryDTO<AtlasQueryDTO> queryDTO);

    @PostMapping("/reSyncContractRequest")
    Result<Void> reSyncContractRequest(@RequestBody AtlasRetryDTO atlasRetryDTO);

    @GetMapping("/getBusinessEntityNameList")
    Result<List<String>> getBusinessEntityNameList();

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    @GetMapping("/getOperationTypeList")
    Result<List<String>> getOperationTypeList();
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End

    @PostMapping("/getMappingContractList")
    Result getMappingContractList(@RequestBody QueryDTO<AtlasMappingQueryDTO> queryDTO);

    // ============================= 接口重试 =================================
    @PostMapping("/rebuildRequestEntity")
    Result<Void> rebuildRequestEntity(@RequestBody AtlasSyncRequestDTO syncRequestDTO);

    @PostMapping("/reSyncByRequestId")
    Result<Void> reSyncByRequestId(@RequestParam Integer requestId);

    @PostMapping("/reSyncByRecordId")
    Result<Void> reSyncByRecordId(@RequestParam Integer recordId);

    // ============================= 导入相关  =================================
    @PostMapping("/importAtlasDepartmentMapping")
    Result<String> importAtlasDepartmentMapping(@RequestParam(value = "file") MultipartFile file);

}
