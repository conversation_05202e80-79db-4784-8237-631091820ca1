package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ATLAS 同步记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_operation_log")
@ApiModel(value = "AtlasOperationLogEntity对象", description = "ATLAS 同步记录表")
public class AtlasOperationLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "关联记录Id")
    private String referId;

    @ApiModelProperty(value = "业务编码")
    private String bizCode;

    @ApiModelProperty(value = "调用者系统")
    private String requestSystem;

    @ApiModelProperty(value = "操怍类型")
    private String operationType;

    @ApiModelProperty(value = "操怍类型")
    private String operationSource;

    @ApiModelProperty(value = "被调用者信息")
    private String targetSystem;

    @ApiModelProperty(value = "被调用者接口")
    private String requestUrl;

    @ApiModelProperty(value = "参数信息")
    private String requestInfo;

    @ApiModelProperty(value = "调用结果反馈信息")
    private String responseInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "接口操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "接口操作人")
    private String createdBy;


}
