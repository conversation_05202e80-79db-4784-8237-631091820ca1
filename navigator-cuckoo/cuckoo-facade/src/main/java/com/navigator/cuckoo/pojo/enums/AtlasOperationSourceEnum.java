package com.navigator.cuckoo.pojo.enums;

import lombok.Getter;

/**
 * <p>
 * 操作来源类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-26
 */
@Getter
public enum AtlasOperationSourceEnum {
    /**
     * 操作来源类型
     */
    TRADE_SERVICE_DIRECT_CALL(1, "Trade服务直接调用"),
    TRADE_SERVICE_MESSAGE_CALL(2, "Trade服务消息服务调用"),
    ATLAS_SERVICE_CALL(3, "ATLAS服务调用"),
    MANUAL_RETRY(4, "手动重试"),
    MANUAL_CANCELLED(5, "手动放弃"),
    INTERFACE_RETRY(6, "接口重试"),
    INTERFACE_AUTO_RETRY(7, "接口自动重试"),
    NEVER(8, "接口不同步"),
    ;
    int value;
    String desc;

    AtlasOperationSourceEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AtlasOperationSourceEnum getByValue(int value) {
        for (AtlasOperationSourceEnum typeEnum : AtlasOperationSourceEnum.values()) {
            if (value == typeEnum.getValue()) {
                return typeEnum;
            }
        }
        return AtlasOperationSourceEnum.TRADE_SERVICE_DIRECT_CALL;
    }

}
