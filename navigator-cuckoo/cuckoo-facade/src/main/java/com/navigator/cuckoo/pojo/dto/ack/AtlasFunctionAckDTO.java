package com.navigator.cuckoo.pojo.dto.ack;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * 回调json的DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/17
 */
@Data
public class AtlasFunctionAckDTO {

    private FunctionalAckDTO functionalAck;

    @Data
    public static class FunctionalAckDTO {
        public AtlasAckDocDTO functionalDocID;
        public List<AtlasAckHeaderDTO> header;
        public List<AtlasAckItemDTO> items;
    }
}
