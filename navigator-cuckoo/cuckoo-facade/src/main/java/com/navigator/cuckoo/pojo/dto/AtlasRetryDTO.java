package com.navigator.cuckoo.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * atlas 回调DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
public class AtlasRetryDTO {

    /**
     * Record id 集合
     */
    private List<Integer> recordIds;

    /**
     * 1.重新传输 2.放弃重试
     * {@link com.navigator.cuckoo.pojo.enums.AtlasReprocessTypeEnum}
     */
    private int reprocessType;

}
