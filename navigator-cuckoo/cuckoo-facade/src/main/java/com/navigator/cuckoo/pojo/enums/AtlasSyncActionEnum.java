package com.navigator.cuckoo.pojo.enums;

import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 同步动作 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-29
 */
@Getter
public enum AtlasSyncActionEnum {
    /**
     * 同步动作
     */
    CREATE("CREATE", "新增",
            Arrays.asList(ContractTradeTypeEnum.NEW.getValue(),
                    ContractTradeTypeEnum.ALLOCATE.getValue(),
                    ContractTradeTypeEnum.ASSIGN.getValue()),
            Arrays.asList(ContractTradeTypeEnum.NEW.getValue(),
                    ContractTradeTypeEnum.ALLOCATE.getValue(),
                    ContractTradeTypeEnum.ASSIGN.getValue())),

    MODIFY("Amendment", "修改",
            Arrays.asList(
                    ContractTradeTypeEnum.REVISE_NORMAL.getValue(),
                    ContractTradeTypeEnum.TRANSFER_ALL.getValue(),
                    ContractTradeTypeEnum.FIXED.getValue()),
            Arrays.asList(
                    ContractTradeTypeEnum.REVISE_NORMAL.getValue(),
                    ContractTradeTypeEnum.FIXED.getValue())),

    PRICE("Pricing", "定价",
            Arrays.asList(ContractTradeTypeEnum.PRICE.getValue()),
            Arrays.asList(ContractTradeTypeEnum.PRICE.getValue())),

    PRICE_UPDATE("priceUpdate", "定价完成",
            Arrays.asList(ContractTradeTypeEnum.PRICE_RESULT.getValue()),
            Arrays.asList(ContractTradeTypeEnum.PRICE_RESULT.getValue())),

    SPLIT("Splitting", "拆分",
            Arrays.asList(
                    ContractTradeTypeEnum.SPLIT_NORMAL.getValue(),
                    ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(),
                    ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue(),
                    ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                    ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(),
                    ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue()),
            Arrays.asList(
                    ContractTradeTypeEnum.SPLIT_NORMAL.getValue(),
                    ContractTradeTypeEnum.SPLIT_CHANGE_CUSTOMER.getValue(),
                    ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue())),

    TRANSFER_MONTH("Month Rolling", "转月", new ArrayList<>(),
            Arrays.asList(
                    ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                    ContractTradeTypeEnum.TRANSFER_ALL.getValue())),

    REVERSE_PRICE("Depricing", "反点价", new ArrayList<>(),
            Arrays.asList(
                    ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(),
                    ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue())),

    WASHOUT("Washout", "解约定赔",
            Arrays.asList(ContractTradeTypeEnum.WASHOUT.getValue()),
            Arrays.asList(ContractTradeTypeEnum.WASHOUT.getValue())),

    BUY_BACK("Buyback", "回购",
            Arrays.asList(ContractTradeTypeEnum.BUYBACK.getValue()),
            Arrays.asList(ContractTradeTypeEnum.BUYBACK.getValue())),

    CLOSED("Closed", "关闭",
            Arrays.asList(ContractTradeTypeEnum.CLOSED.getValue()),
            Arrays.asList(ContractTradeTypeEnum.CLOSED.getValue())),

    WARRANT_WITHDRAW("WarrantyWithdraw", "注销撤回",
            Arrays.asList(ContractTradeTypeEnum.WARRANT_WITHDRAW.getValue()),
            Arrays.asList(ContractTradeTypeEnum.WARRANT_WITHDRAW.getValue())),

    DELIVERY_REQUEST("Delivery", "提货申请", null, null),

    DELIVERY_REQUEST_CANCEL("DeliveryCancel", "提货作废", null, null),

    DR_CONTRACT_EXE_UPDATE("DR_CONTRACT_EXE_UPDATE", "DR合同执行更新", null, null),

    // 查询接口
    QUERY_CONTRACT_OPEN_QUANTITY("QUERY_CONTRACT_OPEN_QUANTITY", "查询合同openQuantity", null, null),
    QUERY_DELIVERY_OPEN_QUANTITY("QUERY_DELIVERY_OPEN_QUANTITY", "查询DR openQuantity", null, null),
    QUERY_BLOCKED_QUANTITY("QUERY_BLOCKED_QUANTITY", "查询blocked数量", null, null),
    ;

    final String value;
    final String desc;
    final List<Integer> tradeTypeList;
    final List<Integer> operationTypeList;

    AtlasSyncActionEnum(String value, String desc, List<Integer> tradeTypeList, List<Integer> operationTypeList) {
        this.value = value;
        this.desc = desc;
        this.tradeTypeList = tradeTypeList;
        this.operationTypeList = operationTypeList;
    }

    public static AtlasSyncActionEnum getByValue(String value) {
        for (AtlasSyncActionEnum statusEnum : AtlasSyncActionEnum.values()) {
            if (value.equals(statusEnum.getValue())) {
                return statusEnum;
            }
        }
        return AtlasSyncActionEnum.CREATE;
    }

    /**
     * 根据交易类型获取同步动作（同步动作）
     * 转月、反点价 → 修改、拆分
     *
     * @param tradeType 交易类型
     * @return
     */
    public static AtlasSyncActionEnum getActionByTradeType(Integer tradeType) {
        for (AtlasSyncActionEnum statusEnum : AtlasSyncActionEnum.values()) {
            if (statusEnum.getTradeTypeList().contains(tradeType)) {
                return statusEnum;
            }
        }
        return AtlasSyncActionEnum.CREATE;
    }

    /**
     * 根据交易类型获取操作类型（页面展示）
     *
     * @param tradeType 交易类型
     * @return
     */
    public static AtlasSyncActionEnum getOperationByTradeType(Integer tradeType) {
        for (AtlasSyncActionEnum statusEnum : AtlasSyncActionEnum.values()) {
            if (statusEnum.getOperationTypeList().contains(tradeType)) {
                return statusEnum;
            }
        }
        return AtlasSyncActionEnum.CREATE;
    }

}
