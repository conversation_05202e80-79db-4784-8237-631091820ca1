package com.navigator.cuckoo.pojo.dto.payload;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AtlasDeliveryDefDTO {
    private DeliveryRequest deliveryRequest;

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class DeliveryRequest {
        private AtlasContractHeaderDTO header;
        private AtlasDeliveryDataDTO data;

        public DeliveryRequest(AtlasContractHeaderDTO header, AtlasDeliveryDataDTO data) {
            this.header = header;
            this.data = data;
        }
    }

    public AtlasDeliveryDefDTO deliveryDTO(AtlasContractHeaderDTO headerDTO, AtlasDeliveryDataDTO createDataDTO) {
        this.deliveryRequest = new AtlasDeliveryDefDTO.DeliveryRequest(headerDTO, createDataDTO);
        return this;
    }
}
