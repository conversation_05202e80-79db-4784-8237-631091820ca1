package com.navigator.cuckoo.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * ATLAS 接口回调表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_atlas_sync_callback")
@ApiModel(value = "AtlasSyncCallbackEntity对象", description = "ATLAS 接口回调表")
public class AtlasSyncCallbackEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "回执消息里面的origMessageId")
    private String uuid;

    @ApiModelProperty(value = "业务单据id")
    private Integer bizId;

    @ApiModelProperty(value = "业务单据号，本次为合同编号")
    private String bizCode;

    @ApiModelProperty(value = "同步次数")
    private Integer tryTimes;

    @ApiModelProperty(value = "同步类型：1.同步 2.异步")
    private Integer syncType;

    @ApiModelProperty(value = "回执消息里面的businessDocID")
    private String businessDocId;

    @ApiModelProperty(value = "回执消息里面的ackBusinessDocId")
    private String ackBusinessDocId;

    @ApiModelProperty(value = "ATLAS系统回传信息")
    private String ackData;

    @ApiModelProperty(value = "回执信息（成功/失败+描述）")
    private String returnMsg;

    @ApiModelProperty(value = "回执信息code")
    @TableField(exist = false)
    private String returnMessageCode;

    @ApiModelProperty(value = "接收到回执的时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date ackTime;

    @ApiModelProperty(value = "操作类型")
    @TableField(exist = false)
    private String operationType;

    @ApiModelProperty(value = "Atlas接口返回的sub_status")
    @TableField(exist = false)
    private String subStatus;

    @ApiModelProperty(value = "ATLAS接口返回的审批备注")
    @TableField(exist = false)
    private String approvalComments;

    @ApiModelProperty(value = "ATLAS接口返回的审批时间")
    @TableField(exist = false)
    private String approvalTime;

    @ApiModelProperty(value = "ATLAS接口返回的车辆状态")
    @TableField(exist = false)
    private String queueStatus;
}
