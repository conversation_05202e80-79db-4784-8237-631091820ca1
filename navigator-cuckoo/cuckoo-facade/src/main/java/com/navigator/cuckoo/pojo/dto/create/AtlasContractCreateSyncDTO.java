package com.navigator.cuckoo.pojo.dto.create;


import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * atlas 同步DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Accessors(chain = true)
public class AtlasContractCreateSyncDTO {
    /**
     * sync dto
     */
    private AtlasContractDefDTO atlasContractDefDTO;

    private AtlasDeliveryDefDTO atlasDeliveryDefDTO;

    /**
     * endpoint url
     */
    private String endPointUrl;

    /**
     * 操作来源：手动/自动
     */
    private String optionSource;

    /**
     * 操作类型：新增/修改/定价/拆分
     */
    private String optionType;

    /**
     * 请求场景
     */
    private Integer tradeType;
}


