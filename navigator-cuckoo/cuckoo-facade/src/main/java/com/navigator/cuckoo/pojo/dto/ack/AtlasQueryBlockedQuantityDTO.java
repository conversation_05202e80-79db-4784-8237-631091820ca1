package com.navigator.cuckoo.pojo.dto.ack;

import lombok.Data;

import java.util.List;

/**
 * <p>
 * blocked quantity dto
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-12
 */
@Data
public class AtlasQueryBlockedQuantityDTO implements java.io.Serializable {

    private BlockedQuantityDetails blockedQuantityDetails;

    @Data
    public static class BlockedQuantityDetails {
        private List<ContractList> contractList;
    }

    @Data
    public static class ContractList {
        private String contractNo;
        private String blockedQuantity;
    }

    public AtlasQueryBlockedQuantityDTO() {
        this.blockedQuantityDetails = new BlockedQuantityDetails();
    }
}
