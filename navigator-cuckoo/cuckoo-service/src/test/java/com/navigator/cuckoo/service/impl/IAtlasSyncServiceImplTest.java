package com.navigator.cuckoo.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.google.gson.Gson;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.SystemRuleItemEntity;
import com.navigator.admin.pojo.enums.systemrule.DepositUseRuleEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.cuckoo.pojo.dto.ack.AtlasFunctionAckDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDataDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractHeaderDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractPricingDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasSecondCostQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingSecondCostEntity;
import com.navigator.cuckoo.service.IAtlasMappingService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractPriceBaseEntity;
import com.navigator.trade.pojo.entity.ContractPriceEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.DeliveryModeEnum;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 同步测试
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/17
 */
@SpringBootTest
@RunWith(SpringRunner.class)
class IAtlasSyncServiceImplTest {
    @Resource
    private ContractFacade contractFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private SystemRuleFacade systemRuleFacade;
    @Resource
    private DeliveryTypeFacade deliveryTypeFacade;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private IAtlasMappingService mappingService;
    @Resource
    private AtlasTradeRemoteService tradeRemoteService;

    @Test
    void syncContractRequest() {
        ContractEntity contractEntity = contractFacade.getBasicContractById(1000);

        // contract
        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }

        // trader
        EmployEntity traderEntity = employFacade.getEmployById(contractEntity.getOwnerId());

        // customer

        // packingWeight
        String packingWeight = "";
        if (contractEntity.getNeedPackageWeight() == 0) {
            packingWeight = "0";
        } else {
            SystemRuleItemEntity itemEntity = systemRuleFacade.getRuleItemById(Integer.parseInt(contractEntity.getPackageWeight()));
            if (null != itemEntity) {
                packingWeight = itemEntity.getRuleValue().toLowerCase().split("kg")[0];
            }
        }

        // deliveryType
        String contractTerms = "";
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        if (null != deliveryTypeEntity && deliveryTypeEntity.getType().equals(DeliveryModeEnum.TAKE.getValue())) {
            contractTerms = "EXW";
        }
        if (null != deliveryTypeEntity && deliveryTypeEntity.getType().equals(DeliveryModeEnum.SEND.getValue())) {
            contractTerms = "CNF";
        }

        // header Info
        AtlasContractHeaderDTO headerDTO = new AtlasContractHeaderDTO();
        headerDTO.setBusinessAppID("NVG")
                .setBusinessEntity("x9")
                .setBusinessDocID(contractEntity.getContractCode())
                .setUuid(IdUtil.simpleUUID());

        // contract data
        AtlasContractDataDTO createDataDTO = new AtlasContractDataDTO();
        createDataDTO.setDepartment("S389501")
                .setTrader(null == traderEntity ? "" : traderEntity.getNickName())
                .setContractDate(DateUtil.formatDate(contractEntity.getSignDate()))
                .setIsPurchase(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? "Y" : "N")
                .setCounterpartyID("")
                .setCommodityID("")
                .setContractQuantity(String.valueOf(contractEntity.getContractNum()))
                .setQuantityUnit("MT")
                .setPackingCode("")
                .setPackingWeight(packingWeight)
                .setContractTerms(contractTerms)
                .setDestination("")
                .setWarehouse("")
                .setPeriodType("D")
                .setPeriodStart(DateUtil.formatDate(contractEntity.getDeliveryStartTime()))
                .setPeriodEnd(DateUtil.formatDate(contractEntity.getDeliveryEndTime()))
                .setTerminal("")
                .setComment("")
                .setTolerance(String.valueOf(contractEntity.getWeightTolerance()))
                .setTaxCode("")
                .setCashReleaseMethod(contractEntity.getDepositReleaseType() == DepositUseRuleEnum.RATIO.getValue() ? "0" : "1")
                .setIsOEM(contractEntity.getOem() == 1 ? "Y" : "N");

        // contract pricing
        AtlasContractPricingDTO pricingDTO = new AtlasContractPricingDTO();
        pricingDTO.setPricingMethod(contractEntity.getContractType() == ContractTypeEnum.YI_KOU_JIA.getValue() ? "N" : "F")
                .setCurrency("CNY")
                .setPriceCode("PMT")
                .setPaymentTerm("")
                .setPriceInclVAT(String.valueOf(contractEntity.getUnitPrice()));

//        AtlasContractDefDTO atlasContractDefDTO = new AtlasContractDefDTO().createDefDTO(headerDTO, createDataDTO, pricingDTO);
//
//        System.out.println(atlasContractDefDTO);
//
//        System.out.println(new Gson().toJson(atlasContractDefDTO));

//        atlasRemoteFacade.syncAtlasCreateInfo(new AtlasContractCreateDefDTO(headerDTO, createDataDTO, pricingDTO));

    }

    @Test
    void testCallBackJson() {
        String data = "{\n" +
                "\t\"functionalAck\": {\n" +
                "\t  \"functionalDocID\" : {\n" +
                "\t\t\"businessAppID\" : \"NVG\",\n" +
                "\t\t\"businessEntity\" : \"x9\",\n" +
                "\t\t\"businessDocID\" : \"TJIBSBMS2350072-003\",\n" +
                "\t\t\"originalBusinessDocID\" : \"\",\n" +
                "\t\t\"userID\" : \"ITAS_SVC_PRD\"\n" +
                "\t  },\n" +
                "\t  \"header\" : [ {\n" +
                "\t\t\"ackBusinessApplication\" : \"ATLAS_CN\",\n" +
                "\t\t\"origMessageID\" : \"4b3133d-cd5d-4b33-b348-f69a0135ad52\",\n" +
                "\t\t\"origMessageTimeStamp\" : \"22-03-2023 09:33:03\",\n" +
                "\t\t\"originalBusinessOperation\" : \"CREATE\",\n" +
                "\t\t\"originalBusinessObject\" : \"Contract\",\n" +
                "\t\t\"status\" : \"SUCCESS\"\n" +
                "\t  } ],\n" +
                "\t  \"items\" : [ {\n" +
                "\t\t\"ackBusinessEntity\" : \"X9\",\n" +
                "\t\t\"ackBusinessDocID\": \"S17170.000\",\n" +
                "\t\t\"lineItemAckList\" : {\n" +
                "\t\t  \"lineItemAck\" : [ {\n" +
                "\t\t\t\"origItemNumber\" : \"*\",\n" +
                "\t\t\t\"returnMessageList\" : {\n" +
                "\t\t\t\t\"returnMessage\" : [ {\n" +
                "\t\t\t\t\t\"returnMessageType\" : \"SUCCESS\",\n" +
                "\t\t\t\t\t\"returnMessageCode\" : \"200\",\n" +
                "\t\t\t\t\t\"returnMessageText\" : \"Contrat creation successful\" \n" +
                "\t\t\t\t}\n" +
                "\t\t\t\t]\n" +
                "\t\t\t}\n" +
                "\t\t  } ]\n" +
                "\t\t}\n" +
                "\t  } ]\n" +
                "\t}\n" +
                "}";

        AtlasFunctionAckDTO atlasFunctionAckDTO = new Gson().fromJson(data, AtlasFunctionAckDTO.class);

        System.out.println(atlasFunctionAckDTO);
    }

    @Test
    void testGetIsFinal() {
        DeliveryApplyEntity deliveryApply = new DeliveryApplyEntity();
        deliveryApply.setIsCarpool(1);
        deliveryApply.setCarpoolCount(3);
        deliveryApply.setCarpoolPriority(3);
        deliveryApply.setPlateNumber("京A12345");
        deliveryApply.setPlanDeliveryTime(DateUtil.parseDate("2024-09-19"));

        List<DeliveryApplyAllocateEntity> allocateEntityList = new ArrayList<>();
        //10	Regular
        DeliveryApplyAllocateEntity entity1 = new DeliveryApplyAllocateEntity();
        entity1.setWarrantNumber("");
        entity1.setAllocationQty(BigDecimal.valueOf(10));
        entity1.setIsExchangeDelivery(0);
        entity1.setIsSoybean2(0);
        allocateEntityList.add(entity1);

        //5	DCE-WR001
        DeliveryApplyAllocateEntity entity2 = new DeliveryApplyAllocateEntity();
        entity2.setWarrantNumber("DCE-WR001");
        entity2.setAllocationQty(BigDecimal.valueOf(5));
        entity2.setIsExchangeDelivery(1);
        entity2.setIsSoybean2(0);
        allocateEntityList.add(entity2);

        //5	DCE-WR002
        DeliveryApplyAllocateEntity entity3 = new DeliveryApplyAllocateEntity();
        entity3.setWarrantNumber("DCE-WR002");
        entity3.setAllocationQty(BigDecimal.valueOf(5));
        entity3.setIsExchangeDelivery(1);
        entity3.setIsSoybean2(0);
        allocateEntityList.add(entity3);

        //5	Soybean-DCE-WR001
        DeliveryApplyAllocateEntity entity4 = new DeliveryApplyAllocateEntity();
        entity4.setWarrantNumber("Soybean-DCE-WR001");
        entity4.setAllocationQty(BigDecimal.valueOf(5));
        entity4.setIsExchangeDelivery(1);
        entity4.setIsSoybean2(1);
        allocateEntityList.add(entity4);

        //5	Soybean-DCE-WR002
        DeliveryApplyAllocateEntity entity5 = new DeliveryApplyAllocateEntity();
        entity5.setWarrantNumber("Soybean-DCE-WR002");
        entity5.setAllocationQty(BigDecimal.valueOf(5));
        entity5.setIsExchangeDelivery(1);
        entity5.setIsSoybean2(1);
        allocateEntityList.add(entity5);

        Integer isFinal = getIsFinal(deliveryApply, allocateEntityList);

        System.out.println("allocateEntityList:" + allocateEntityList);

        System.out.println("isFinal:" + isFinal);
    }


    /**
     * 计算最终拼车人数
     *
     * @param deliveryApply      提货申请
     * @param allocateEntityList 预分配信息
     * @return 拼车isFinal
     */
    private Integer getIsFinal(DeliveryApplyEntity deliveryApply, List<DeliveryApplyAllocateEntity> allocateEntityList) {

        int isFinal = 1;

        // 先判断是否是拼车
        if (deliveryApply.getIsCarpool() != null && deliveryApply.getIsCarpool() == 0) {
            return isFinal;
        }

        // 按照条件查询redis是否存在拼车 1.是拼车 2.车牌号 3.提货日期
        String key = "deliveryApply:isFinal:" + deliveryApply.getIsCarpool() + "_" + deliveryApply.getPlateNumber() + "_"
                + DateUtil.format(deliveryApply.getPlanDeliveryTime(), "yyyy-MM-dd");

        if (Objects.isNull(redisUtil.get(key))) {
            // 第一个DR:is final = share number + split number(DR拆分的预分配个数) – 1
            isFinal = deliveryApply.getCarpoolCount() + allocateEntityList.size() - 1;
        } else {
            // 上一个is final + split number(DR拆分的预分配个数) – 1
            isFinal = (Integer) redisUtil.get(key) + allocateEntityList.size() - 1;
        }

        // 放入redis
        redisUtil.set(key, isFinal);

        // 先排序 1.是否是豆二  2.是否是DCE 3.现货 4.仓单号从小到大  然后设置排序字段
        allocateEntityList.sort(Comparator.comparingInt((DeliveryApplyAllocateEntity o) -> {
            Integer soy2 = o.getIsSoybean2();
            Integer isDce = o.getIsExchangeDelivery();

            // 排序依据优先级
            if (soy2 != null && soy2.equals(1)) {
                return -1000;
            } else if (isDce != null && isDce.equals(1)) {
                return -500;
            } else {
                return 0;
            }
        }).thenComparing((o1, o2) -> {
            String warrant1 = o1.getWarrantNumber();
            String warrant2 = o2.getWarrantNumber();

            return Comparator.nullsLast(String::compareTo)
                    .compare(warrant1, warrant2);
        }));

        // 按照排序字段设置prioritySort
        int priorityBase = deliveryApply.getCarpoolPriority() * 100;
        for (int i = 0; i < allocateEntityList.size(); i++) {
            DeliveryApplyAllocateEntity allocateEntity = allocateEntityList.get(i);
            allocateEntity.setPrioritySort(priorityBase + i);
        }
        return isFinal;
    }

    @Test
    void test2ndCost() {
        List<String> excludeFields = new ArrayList<>();

        // 直接获取次级费用信息
        List<String> priceDetailFieldList = mappingService.getSecondCostPriceFields();

        ContractPriceEntity contractPriceEntity = tradeRemoteService.getContractPriceDetail(3881);

        for (String priceDetailField : priceDetailFieldList) {
            try {
                Field field = ContractPriceBaseEntity.class.getDeclaredField(priceDetailField);
                field.setAccessible(true);
                BigDecimal priceDetailValue = (BigDecimal) field.get(contractPriceEntity);

                // 只处理大于0的字段
                if (priceDetailValue != null && priceDetailValue.signum() > 0) {
                    excludeFields.add(priceDetailField);
                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Test
    void test2ndCostQuery() {
        AtlasMappingSecondCostEntity secondCostEntity = mappingService.getSecondCostByCondition(new AtlasSecondCostQueryDTO()
                .setPriceDetailField("delayPrice")
                .setSyncAction("Amendment")
                .setBuCode("ST")
                .setDeliveryType(70)
                .setDeliveryTypeName("送到买方工厂仓库 卖方车板交货 "));

        System.out.println(secondCostEntity);
    }
}
