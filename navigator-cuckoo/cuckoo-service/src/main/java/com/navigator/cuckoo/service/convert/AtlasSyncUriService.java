package com.navigator.cuckoo.service.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.cuckoo.dao.AtlasOperationLogDao;
import com.navigator.cuckoo.dao.AtlasSyncCallbackDao;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncUriDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import com.navigator.cuckoo.pojo.entity.AtlasOperationLogEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncCallbackEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.enums.AtlasOperationSourceEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncStatusEnum;
import com.navigator.cuckoo.remote.AtlasRemoteFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractStatusEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * URI 转换的实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncUriService {
    @Value("${atlas.endpoint.url}")
    private String endpointUrl;
    @Value("${atlas.endpoint.sub-contract-path}")
    private String subContractPath;
    @Value("${atlas.endpoint.contract-open-quantity-path}")
    private String contractOpenQuantityPath;
    @Value("${atlas.endpoint.dr-open-quantity-path}")
    private String drOpenQuantityPath;
    @Value("${atlas.endpoint.dr-create-path}")
    private String drCreatePath;
    @Value("${atlas.endpoint.dr-cancel-path}")
    private String drCancelPath;

    // dao
    private final AtlasSyncCallbackDao syncCallbackDao;
    private final AtlasSyncRecordDao syncRecordDao;
    private final AtlasOperationLogDao operationLogDao;
    // service
    private final AtlasTradeRemoteService tradeRemoteService;
    // facade
    private final AtlasRemoteFacade atlasRemoteFacade;

    /**
     * 同步接口
     *
     * @param syncUriDTO 同步的DTO
     */
    public ResponseEntity<String> syncAtlasInfo(AtlasSyncUriDTO syncUriDTO) throws Exception {

        log.info("syncAtlasInfo syncUriDTO:{}", syncUriDTO);

        AtlasSyncActionEnum syncActionEnum;
        // 根据交易类型或操作类型获取同步动作（DR没有tradeType，需要根据operationType获取）
        if (syncUriDTO.getTradeType() != null) {
            syncActionEnum = AtlasSyncActionEnum.getActionByTradeType(syncUriDTO.getTradeType());
        } else {
            syncActionEnum = AtlasSyncActionEnum.getByValue(syncUriDTO.getOperationType());
        }

        // 合同信息
        AtlasContractDefDTO contractDefDTO = syncUriDTO.getContractDefDTO();

        switch (syncActionEnum) {
            case CREATE:
            case BUY_BACK:
                String createEndPointUrl = endpointUrl + subContractPath;
                syncUriDTO.setRequestUri(createEndPointUrl);
                return atlasRemoteFacade.syncAtlasCreateInfo(new URI(createEndPointUrl), contractDefDTO);
            case MODIFY:
            case CLOSED:
            case WARRANT_WITHDRAW:
                String modifyEndPointUrl = endpointUrl + subContractPath +
                        contractDefDTO.getContract().getHeader().getBusinessEntity() + "_" + getAckAtlasContractCode(contractDefDTO.getContract().getHeader().getBusinessDocID());
                syncUriDTO.setRequestUri(modifyEndPointUrl);
                return atlasRemoteFacade.syncAtlasModifyInfo(new URI(modifyEndPointUrl), contractDefDTO);
            case SPLIT:
            case WASHOUT:
                String splitEndPointUrl = endpointUrl + subContractPath +
                        contractDefDTO.getContract().getHeader().getBusinessEntity() + "_" + getAckAtlasContractCode(contractDefDTO.getContract().getHeader().getOriginalBusinessDocID());
                syncUriDTO.setRequestUri(splitEndPointUrl + "/split");
                return atlasRemoteFacade.syncAtlasSplitInfo(new URI(splitEndPointUrl), contractDefDTO);
            case PRICE:
            case PRICE_UPDATE:
                String priceEndPointUrl = endpointUrl + subContractPath +
                        contractDefDTO.getContract().getHeader().getBusinessEntity() + "_" + getAckAtlasContractCode(contractDefDTO.getContract().getHeader().getBusinessDocID());
                syncUriDTO.setRequestUri(priceEndPointUrl + "/pricing");
                return atlasRemoteFacade.syncAtlasPricingInfo(new URI(priceEndPointUrl), contractDefDTO);
            case DELIVERY_REQUEST:
                String drEndPointUrl = endpointUrl + drCreatePath;
                syncUriDTO.setRequestUri(drEndPointUrl);
                return atlasRemoteFacade.syncAtlasDeliveryRequest(new URI(drEndPointUrl), syncUriDTO.getDeliveryDefDTO());
            case DELIVERY_REQUEST_CANCEL:
                String deliveryId = syncUriDTO.getDeliveryDefDTO().getDeliveryRequest().getHeader().getBusinessEntity() + "_" + syncUriDTO.getDeliveryDefDTO().getDeliveryRequest().getHeader().getBusinessDocID();

                String cancelEndPointUrl = endpointUrl + drCancelPath.replace("{deliveryId}", deliveryId);
                syncUriDTO.setRequestUri(cancelEndPointUrl);

                return atlasRemoteFacade.syncAtlasDeliveryRequestCancel(new URI(cancelEndPointUrl), new AtlasDeliveryDefDTO());
            default:
                return null;
        }
    }

    public BigDecimal getContractOpenQuantity(String businessEntity, String contractCode) {

        String url = endpointUrl + contractOpenQuantityPath.replace("{contractCode}", businessEntity + "_" + contractCode);
        // 记录日志
        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();

        try {
            ResponseEntity<String> responseEntity = atlasRemoteFacade.getContractOpenQuantity(new URI(url));

            // 记录返回结果
            operationLogEntity.setResponseInfo(new Gson().toJson(responseEntity));

            log.info("getContractOpenQuantity responseEntity:{}", responseEntity);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                if (responseEntity.getBody() == null || responseEntity.getBody().isEmpty()) {
                    return BigDecimal.ZERO;
                }
                return new BigDecimal(responseEntity.getBody());
            }
        } catch (URISyntaxException e) {
            log.error("Error parsing URI: " + url, e);
            operationLogEntity.setResponseInfo(e.getMessage());
        } finally {
            // 记录日志
            operationLogEntity
                    .setBizCode(contractCode)
                    .setRequestSystem(SystemEnum.CUCKOO.getName())
                    .setOperationType(AtlasSyncActionEnum.QUERY_CONTRACT_OPEN_QUANTITY.getValue())
                    .setTargetSystem(SystemEnum.ATLAS.getName())
                    .setOperationSource(AtlasOperationSourceEnum.TRADE_SERVICE_DIRECT_CALL.getDesc())
                    .setRequestUrl(url)
                    .setRequestInfo("")
                    .setCreatedAt(new Date());
            operationLogDao.save(operationLogEntity);
        }
        return BigDecimal.ZERO;
    }

    public AtlasDeliveryOpenQuantityDTO getDeliveryOpenQuantity(String counterpartyId, AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO) {
        String url = endpointUrl + drOpenQuantityPath.replace("{counterpartyId}", counterpartyId);

        // 记录日志
        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();

        try {
            ResponseEntity<String> responseEntity = atlasRemoteFacade.getDeliveryOpenQuantity(new URI(url), deliveryOpenQuantityDTO);

            // 记录返回结果
            operationLogEntity.setResponseInfo(new Gson().toJson(responseEntity));

            log.info("getDeliveryOpenQuantity responseEntity:{}", responseEntity);
            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                if (responseEntity.getBody() != null && !responseEntity.getBody().isEmpty()) {
                    String jsonBody = responseEntity.getBody();

                    log.info("Raw response body: {}", jsonBody);

                    // 去掉最外层的引号（如果存在）
                    if (jsonBody.startsWith("\"") && jsonBody.endsWith("\"")) {
                        jsonBody = jsonBody.substring(1, jsonBody.length() - 1);
                    }
                    // 将双重反斜杠替换为单个反斜杠
                    jsonBody = jsonBody.replace("\\\"", "\"");

                    log.info("Processed JSON body: {}", jsonBody);

                    try {
                        return new Gson().fromJson(jsonBody, AtlasDeliveryOpenQuantityDTO.class);
                    } catch (JsonSyntaxException e) {
                        log.error("Error parsing JSON to AtlasDeliveryOpenQuantityDTO", e);
                    }
                }
            }
        } catch (URISyntaxException e) {
            log.error("Error parsing URI: " + url, e);
            operationLogEntity.setResponseInfo(e.getMessage());
        } finally {
            // 记录日志
            operationLogEntity
                    .setBizCode(counterpartyId)
                    .setRequestSystem(SystemEnum.CUCKOO.getName())
                    .setOperationType(AtlasSyncActionEnum.QUERY_DELIVERY_OPEN_QUANTITY.getValue())
                    .setTargetSystem(SystemEnum.ATLAS.getName())
                    .setOperationSource(AtlasOperationSourceEnum.TRADE_SERVICE_DIRECT_CALL.getDesc())
                    .setRequestUrl(url)
                    .setRequestInfo(new Gson().toJson(deliveryOpenQuantityDTO))
                    .setCreatedAt(new Date());
            operationLogDao.save(operationLogEntity);
        }

        return null;
    }

    /**
     * 根据航海家合同查询ATLAS合同号
     *
     * @param contractCode nav合同编号
     * @return
     */
   public String getAckAtlasContractCode(String contractCode) {
    if (contractCode == null || contractCode.isEmpty()) {
        return null;
    }

    List<AtlasSyncCallbackEntity> syncCallbackEntityList = syncCallbackDao.getListByBusinessDocId(contractCode);
    if (CollectionUtil.isEmpty(syncCallbackEntityList)) {
        return null;
    }

    try {
        return syncCallbackEntityList.stream()
                .map(atlasSyncCallbackEntity -> {
                    String uuid = atlasSyncCallbackEntity.getUuid();
                    if (uuid == null) {
                        return null;
                    }

                    AtlasSyncRecordEntity recordEntity = syncRecordDao.getByUuid(uuid);
                    return (recordEntity != null && AtlasSyncStatusEnum.ACK_SUCCESS.getValue().equals(recordEntity.getSyncStatus())) ?
                            atlasSyncCallbackEntity.getAckBusinessDocId() : null;
                })
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(null);
    } catch (Exception e) {
        log.error("Error fetching ACK Atlas contract code for contract: " + contractCode, e);
        return null;
    }
}

    /**
     * 查询合同的BLOCKED_QUANTITY
     *
     * @param contractCode 合同编号
     * @return BLOCKED_QUANTITY
     */
    public BigDecimal getContractBlockedQuantity(String contractCode) {
        ContractEntity contractEntity = tradeRemoteService.getBasicContractByCode(contractCode);

        if (null == contractEntity) {
            return BigDecimal.ZERO;
        }

        // 判断尾量是否大于0
        BigDecimal closeTailNum = contractEntity.getCloseTailNum();
        if (BigDecimalUtil.isGreaterThanZero(closeTailNum)) {
            return closeTailNum;
        }

        // 修改中、拆分中、关闭中状态下，查询OPEN_QUANTITY
        if (Arrays.asList(ContractStatusEnum.MODIFYING.getValue(),
                ContractStatusEnum.SPLITTING.getValue(),
                ContractStatusEnum.CLOSING.getValue()).contains(contractEntity.getStatus())) {

            SiteEntity siteEntity = tradeRemoteService.getBusinessEntityBySiteCode(contractEntity.getSiteCode());

            BigDecimal openQuantity = getContractOpenQuantity(siteEntity.getAtlasCode(), contractEntity.getContractCode());
            if (BigDecimalUtil.isGreaterThanZero(openQuantity)) {
                return openQuantity;
            }
        }

        return BigDecimal.ZERO;
    }
}
