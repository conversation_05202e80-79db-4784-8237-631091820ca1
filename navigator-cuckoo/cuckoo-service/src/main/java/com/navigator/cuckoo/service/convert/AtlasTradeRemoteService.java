package com.navigator.cuckoo.service.convert;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.*;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.delivery.facade.DeliveryApplyAllocateFacade;
import com.navigator.delivery.facade.DeliveryApplyAtlasFacade;
import com.navigator.delivery.facade.DeliveryWarehouseFacade;
import com.navigator.delivery.pojo.dto.DeliveryApplyCarpoolInfoDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.trade.facade.*;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasTradeRemoteService {
    private final ContractFacade contractFacade;
    private final TtTranferFacade ttTranferFacade;
    private final TradeTicketFacade tradeTicketFacade;
    private final EmployFacade employFacade;
    private final SystemRuleFacade systemRuleFacade;
    private final DeliveryTypeFacade deliveryTypeFacade;
    private final CustomerFacade customerFacade;
    private final SkuFacade skuFacade;
    private final AttributeFacade attributeFacade;
    private final WarehouseFacade warehouseFacade;
    private final PayConditionFacade payConditionFacade;
    private final ContractPriceFacade contractPriceFacade;

    private final DeliveryApplyAtlasFacade deliveryApplyAtlasFacade;
    private final DeliveryApplyAllocateFacade applyAllocateFacade;
    private final DeliveryWarehouseFacade deliveryWarehouseFacade;
    private final SiteFacade siteFacade;
    private final FactoryFacade factoryFacade;

    /**
     * 根据id查询合同基础信息
     *
     * @param contractId 合同id
     * @return ContractEntity
     */
    public ContractEntity getBasicContractById(Integer contractId) {
        ContractEntity contractEntity = contractFacade.getBasicContractById(contractId);

        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }
        return contractEntity;
    }

    /**
     * 根据id查询合同基础信息
     *
     * @param contractCode 合同code
     * @return ContractEntity
     */
    public ContractEntity getBasicContractByCode(String contractCode) {
        ContractEntity contractEntity = null;
        try {
            contractEntity = contractFacade.getBasicContractByCode(contractCode);
        } catch (Exception e) {
            log.error("getBasicContractByCode error :{}", e.getMessage());
        }
        return contractEntity;
    }

    /**
     * 根据id查询employ信息
     *
     * @param employId employId
     * @return EmployEntity
     */
    public EmployEntity getEmployById(Integer employId) {
        EmployEntity employEntity = null;
        try {
            employEntity = employFacade.getEmployById(employId);
        } catch (Exception e) {
            log.error("getEmployById error :{}", e.getMessage());
        }
        return employEntity;
    }

    /**
     * 根据id查询交提货方式信息
     *
     * @param DeliveryTypeId 交提货方式id
     * @return DeliveryTypeEntity
     */
    public DeliveryTypeEntity getDeliveryTypeById(Integer DeliveryTypeId) {
        DeliveryTypeEntity deliveryTypeEntity = null;
        try {
            deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(DeliveryTypeId);
        } catch (Exception e) {
            log.error("getDeliveryTypeById error :{}", e.getMessage());
        }
        return deliveryTypeEntity;
    }

    /**
     * 根据id查询客户基础信息
     *
     * @param customerId 客户id
     * @return CustomerEntity
     */
    public CustomerEntity getBasicCustomerById(Integer customerId) {
        CustomerEntity customerEntity = null;

        try {
            customerEntity = customerFacade.queryCustomerById(customerId);

        } catch (Exception e) {
            log.error("getBasicCustomerById error :{}", e.getMessage());
        }
        return customerEntity;
    }

    /**
     * 根据id查询商品詳情信息
     *
     * @param skuId 货品id
     * @return GoodsDTO
     */
    public SkuEntity getSkuBySkuId(Integer skuId) {
        SkuEntity skuEntity = null;

        try {
            skuEntity = skuFacade.getSkuById(skuId);
        } catch (Exception e) {
            log.error("getSkuBySkuId error :{}", e.getMessage());
        }
        return skuEntity;
    }

    /**
     * 根据id查询商品Mdm信息
     *
     * @param skuId                货品id
     * @param contractCategoryType 合同类别
     * @return GoodsDTO
     */
    public String getSkuMdmId(Integer skuId, Integer contractCategoryType) {
        String skuMdmId = "";
        try {
            Result result = skuFacade.querySkuMdmList(new SkuMdmQO().setSkuId(skuId).setType(contractCategoryType));
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                List<SkuMdmEntity> mdmEntityList = gson.fromJson(gson.toJson(result.getData()), new TypeToken<List<SkuMdmEntity>>() {
                }.getType());

                if (!mdmEntityList.isEmpty()) {
                    skuMdmId = mdmEntityList.get(0).getMdmId();
                }
            }
        } catch (Exception e) {
            log.error("getSkuMdmId error :{}", e.getMessage());
        }
        return skuMdmId;
    }


    /**
     * 根据id查询包装信息
     *
     * @param packageId 包装id
     * @return AttributeValueEntity
     */
    public AttributeValueEntity getPackageById(Integer packageId) {
        AttributeValueEntity attributeValueEntity = null;

        try {
            attributeValueEntity = attributeFacade.getAttributeValueById(packageId);
        } catch (Exception e) {
            log.error("getPackageById error :{}", e.getMessage());
        }
        return attributeValueEntity;
    }

    /**
     * 根据id查询发货库点信息
     *
     * @param warehouseId 库点Id
     * @return FactoryWarehouseEntity
     */
    public WarehouseEntity getWarehouseById(Integer warehouseId) {
        WarehouseEntity warehouseEntity = null;
        try {
            Result result = warehouseFacade.getWarehouseById(warehouseId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                warehouseEntity = gson.fromJson(gson.toJson(result.getData()), WarehouseEntity.class);
            }
        } catch (Exception e) {
            log.error("getShipWarehouseById error :{}", e.getMessage());
        }
        return warehouseEntity;
    }

    /**
     * 根据库点id查询MarketZone
     *
     * @param warehouseId 库点Id
     * @return MarketZone
     */
    public String getWarehouseMarketZone(Integer warehouseId) {
        String marketZone = null;
        try {
            Result result = warehouseFacade.getWarehouseMarketZone(warehouseId);
            if (result.isSuccess()) {
                marketZone = (String) result.getData();
            }
        } catch (Exception e) {
            log.error("getWarehouseMarketZone error :{}", e.getMessage());
        }
        return marketZone;
    }

    /**
     * 根据code查询交货工厂信息
     *
     * @param factoryCode 工厂code
     * @return FactoryEntity
     */
    public FactoryEntity getFactoryByCode(String factoryCode) {
        FactoryEntity factoryEntity = null;

        try {
            factoryEntity = factoryFacade.getFactoryByCode(factoryCode);
        } catch (Exception e) {
            log.error("getFactoryByCode error :{}", e.getMessage());
        }
        return factoryEntity;
    }

    /**
     * 根据配置id查询详细配置信息
     *
     * @param systemRuleId 配置id
     * @return String
     */
    public SystemRuleItemEntity getSystemRuleById(Integer systemRuleId) {
        SystemRuleItemEntity itemEntity = null;
        try {
            itemEntity = systemRuleFacade.getRuleItemById(systemRuleId);
        } catch (Exception e) {
            log.error("getSystemRuleById error :{}", e.getMessage());
        }
        return itemEntity;
    }

    /**
     * 根据发票类型查询发票信息
     *
     * @param invoiceType 发票类型
     * @return String
     */
    public SystemRuleItemEntity getInvoiceType(Integer invoiceType) {
        SystemRuleItemEntity itemEntity = null;
        try {
            itemEntity = systemRuleFacade.getInvoiceType(null, invoiceType, "");
        } catch (Exception e) {
            log.error("getInvoiceType error :{}", e.getMessage());
        }
        return itemEntity;
    }

    /**
     * 根据id查询付款条件信息
     *
     * @param payConditionId 付款条件id
     * @return PayConditionEntity
     */
    public PayConditionEntity getPayConditionById(Integer payConditionId) {
        PayConditionEntity payConditionEntity = null;
        try {
            Result result = payConditionFacade.getPayConditionById(payConditionId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                payConditionEntity = gson.fromJson(gson.toJson(result.getData()), PayConditionEntity.class);
            }
        } catch (Exception e) {
            log.error("getPayConditionById error :{}", e.getMessage());
        }
        return payConditionEntity;
    }

    /**
     * 根据合同id查询合同价格信息
     *
     * @param contractId 合同id
     * @return ContractPriceEntity
     */
    public ContractPriceEntity getContractPriceDetail(Integer contractId) {
        ContractPriceEntity contractPriceEntity = null;
        try {
            contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractId);
        } catch (Exception e) {
            log.error("getContractPriceDetail error :{}", e.getMessage());
        }
        return contractPriceEntity;
    }

    /**
     * 计算价格
     *
     * @param priceDetailBO 价格计算参数
     * @return BigDecimal
     */
    public BigDecimal calculatePriceBo(PriceDetailBO priceDetailBO) {
        BigDecimal price = BigDecimal.ZERO;
        try {
            price = contractPriceFacade.calculatePriceBo(priceDetailBO);
        } catch (Exception e) {
            log.error("calculatePriceBo error :{}", e.getMessage());
        }
        return price;
    }

    /**
     * 根据合同id查询定价单信息
     *
     * @param contractId 合同id
     * @return ContractEntity
     */
    public List<ConfirmPriceDTO> getPriceFixingList(Integer contractId) {
        List<ConfirmPriceDTO> confirmPriceDTOList = new ArrayList<>();

        try {
            Result result = contractFacade.getTTPriceByContractId(contractId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                confirmPriceDTOList = gson.fromJson(gson.toJson(result.getData()), new TypeToken<List<ConfirmPriceDTO>>() {
                }.getType());
                /*// 筛选num>0的定价单
                if (!confirmPriceDTOList.isEmpty()) {
                    confirmPriceDTOList.removeIf(confirmPriceDTO -> confirmPriceDTO.getNum().compareTo(BigDecimal.ZERO) == 0);
                }*/
            }
        } catch (Exception e) {
            log.error("getPriceFixingList error :{}", e.getMessage());
        }
        return confirmPriceDTOList;
    }

    /**
     * 根据ttid查询转月信息
     *
     * @param ttId ttid
     * @return
     */
    public TTTranferEntity getTransferContractByTtId(Integer ttId) {
        try {
            return ttTranferFacade.getTransferByTtId(ttId);
        } catch (Exception e) {
            log.error("getTransferContractByTtId error :{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据id查询交货申请信息
     *
     * @param applyId 交货申请id
     * @return DeliveryApplyEntity
     */
    public DeliveryApplyEntity getDeliveryApplyById(Integer applyId) {
        try {
            Result result = deliveryApplyAtlasFacade.getDeliveryApplyById(applyId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                return gson.fromJson(gson.toJson(result.getData()), DeliveryApplyEntity.class);
            }
        } catch (Exception e) {
            log.error("getDeliveryApplyById error :{}", e.getMessage());
        }
        return null;
    }

    public DeliveryApplyCarpoolInfoDTO getCarpoolDeliveryRequestInfo(String plateNumber, String planDeliveryTime) {
        try {
            Result<DeliveryApplyCarpoolInfoDTO> result = deliveryApplyAtlasFacade.getCarpoolDeliveryRequestInfo(plateNumber, planDeliveryTime);
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("getCarpoolDeliveryRequestInfo error :{}", e.getMessage());
        }
        return null;
    }

    public DeliveryWarehouseEntity getDeliveryWarehouseById(Integer deliveryWarehouseId) {
        try {
            Result<DeliveryWarehouseEntity> result = deliveryWarehouseFacade.getDeliveryWarehouseById(deliveryWarehouseId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                return gson.fromJson(gson.toJson(result.getData()), DeliveryWarehouseEntity.class);
            }
        } catch (Exception e) {
            log.error("getDeliveryWarehouseById error :{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据siteCode查询主体信息
     *
     * @param siteCode 账套code
     * @return
     */
    public SiteEntity getBusinessEntityBySiteCode(String siteCode) {
        try {
            return siteFacade.getSiteByCode(siteCode);
        } catch (Exception e) {
            log.error("getTransferContractByTtId error :{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据条件查询主体信息
     *
     * @param companyId           主体ID
     * @param deliveryFactoryCode 工厂code
     * @return
     */
    public SiteEntity getBusinessEntityByCondition(Integer companyId, String deliveryFactoryCode) {
        try {
            return siteFacade.getSiteByCompanyIdAndFactoryCode(companyId, deliveryFactoryCode);
        } catch (Exception e) {
            log.error("getTransferContractByTtId error :{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据applyId查询分配信息
     *
     * @param applyId 申请id
     * @return
     */
    public List<DeliveryApplyAllocateEntity> getDeliveryRequestAllocateInfo(Integer applyId) {
        List<DeliveryApplyAllocateEntity> applyAllocateEntityList = new ArrayList<>();

        try {
            Result<List<DeliveryApplyAllocateEntity>> result = applyAllocateFacade.getDeliveryApplyAllocateByApplyId(applyId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                applyAllocateEntityList = gson.fromJson(gson.toJson(result.getData()), new TypeToken<List<DeliveryApplyAllocateEntity>>() {
                }.getType());
            }
        } catch (Exception e) {
            log.error("getDeliveryApplyAllocateByApplyId error :{}", e.getMessage());
        }
        return applyAllocateEntityList;
    }

    public void updateDeliveryApply(DeliveryApplyEntity deliveryApply) {
        deliveryApplyAtlasFacade.updateDeliveryApply(deliveryApply);
    }

    public List<String> getBusinessEntityNameList() {
        Result<List<String>> result = siteFacade.getAtlasSiteCodeList();
        if (result.isSuccess()) {
            return result.getData();
        }
        return new ArrayList<>();
    }

    public BigDecimal getContractBlockedNum(String contractCode) {
        Result<BigDecimal> blockedNum = contractFacade.getContractBlockedNum(contractCode);
        if (blockedNum.isSuccess()) {
            return blockedNum.getData();
        }
        return BigDecimal.ZERO;
    }

    public ContractHistoryEntity getContractHistoryEntity(Integer contractId, Integer mainVersion) {
        try {
            Result<ContractHistoryEntity> contractHistoryEntity = contractFacade.getContractHistoryEntity(contractId, mainVersion);
            if (contractHistoryEntity.isSuccess()) {
                return contractHistoryEntity.getData();
            }
        } catch (Exception e) {
            log.error("getContractHistoryEntity error :{}", e.getMessage());
        }
        return null;
    }

    public TTAddEntity getWashoutDetailByTTId(Integer ttId) {
        try {
            Result result = tradeTicketFacade.getTtAddByTtId(ttId);
            if (result.isSuccess()) {
                Gson gson = new GsonBuilder().setDateFormat("yyyy-MM-dd HH:mm:ss").create();
                return gson.fromJson(gson.toJson(result.getData()), TTAddEntity.class);
            }
        } catch (Exception e) {
            log.error("getWashoutDetailByTTId error :{}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据合同id获取源合同的合同类型
     *
     * @param contractId 合同id
     * @return 合同类型
     */
    public Integer getOriginalContractType(Integer contractId) {
        try {
            Result<Integer> result = contractFacade.getOriginalContractType(contractId);
            if (result.isSuccess()) {
                return result.getData();
            }
        } catch (Exception e) {
            log.error("getOriginalContractType error :{}", e.getMessage());
        }
        return ContractTypeEnum.YI_KOU_JIA.getValue();
    }

}

