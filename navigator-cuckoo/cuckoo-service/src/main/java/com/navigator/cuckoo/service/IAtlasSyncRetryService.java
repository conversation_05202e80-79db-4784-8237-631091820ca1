package com.navigator.cuckoo.service;

import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;

/**
 * <p>
 * ATLAS请求记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IAtlasSyncRetryService {

    /**
     * 重新生成request记录，不触发同步接口
     */
    void rebuildRequestEntity(AtlasSyncRequestDTO atlasSyncRequestDTO);

    /**
     * 前端选择重试，会触发同步接口
     */
    void reSyncContractRequest(AtlasRetryDTO atlasRetryDTO);

    /**
     * 根据record ID重试
     */
    void reSyncByRecordId(Integer recordId);

    /**
     * 根据request ID重试
     */
    void reSyncByRequestId(Integer requestId);

}
