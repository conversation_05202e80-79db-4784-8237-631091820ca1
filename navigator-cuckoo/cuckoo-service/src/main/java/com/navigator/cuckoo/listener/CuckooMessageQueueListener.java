package com.navigator.cuckoo.listener;

import com.google.gson.Gson;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.service.IAtlasSyncService;
import feign.RetryableException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

/**
 * <p>
 * Cuckoo 消息队列监听
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/25
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class CuckooMessageQueueListener {

    @Value("${messageQueue.cuckoo.syncQueueName}")
    private String syncQueueName;
    @Value("${messageQueue.cuckoo.syncDeadLetterName}")
    private String syncDeadLetterName;

    // service
    private final IAtlasSyncService syncService;

    /**
     * Trade → Cuckoo 消息监听
     */
    @JmsListener(destination = "${messageQueue.cuckoo.syncQueueName}", containerFactory = "cuckooJmsListenerContainerFactory")
    public void receiveQueueMessage(String message) {
        log.info("[{}]Queue Message received: {}", syncQueueName, message);

        try {
            AtlasSyncRequestDTO syncRequestDTO = new Gson().fromJson(message, AtlasSyncRequestDTO.class);

            syncService.syncContractRequest(syncRequestDTO);
        } catch (RetryableException e) {
            log.error("method: receiveQueueMessage, send Cuckoo requestData error,retry......: {}", e.getMessage());

            // 显式抛出异常：需要进行消息重试
            throw new BusinessException(ResultCodeEnum.SYNC_CUCKOO_CONTRACT_EXCEPTION, e.getMessage());
        } catch (Exception e) {
            log.error("method: receiveQueueMessage, send Cuckoo requestData error: {}", e.getMessage());
        }
    }

    /**
     * 同步Cuckoo死信队列监听
     *
     * @param message
     */
    @JmsListener(destination = "${messageQueue.cuckoo.syncDeadLetterName}", containerFactory = "cuckooJmsListenerContainerFactory")
    public void receiveDLQueueMessage(String message) {
        log.info("[{}]DLQ Message received: {}", syncDeadLetterName, message);
    }
}
