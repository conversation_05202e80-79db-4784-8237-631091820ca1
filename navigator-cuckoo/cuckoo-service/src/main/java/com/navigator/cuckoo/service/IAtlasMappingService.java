package com.navigator.cuckoo.service;

import com.navigator.cuckoo.pojo.dto.query.AtlasSecondCostQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.entity.AtlasMappingSecondCostEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
public interface IAtlasMappingService {

    String getMappingMarketByFutureLetter(String futureLetter);

    String getDepartmentCode(ContractEntity contractEntity, String syncAction);

    String getTaxCodeMdmCode(String invoiceTypeMdmCode, BigDecimal taxRate, Integer contractCategoryType);

    AtlasMappingContractEntity getByNavContractCode(String contractCode);

    boolean updateAtlasMappingContract(AtlasMappingContractEntity atlasMappingContractEntity);

    List<String> getSecondCostPriceFields();

    AtlasMappingSecondCostEntity getSecondCostByCondition(AtlasSecondCostQueryDTO atlasSecondCostQueryDTO);

    String importAtlasDepartmentMapping(MultipartFile file);
}
