package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasMappingSecondCostMapper;
import com.navigator.cuckoo.pojo.dto.query.AtlasSecondCostQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingSecondCostEntity;
import jodd.util.StringUtil;

import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

@Dao
public class AtlasMappingSecondCostDao extends BaseDaoImpl<AtlasMappingSecondCostMapper, AtlasMappingSecondCostEntity> {

    public AtlasMappingSecondCostEntity getByCondition(AtlasSecondCostQueryDTO queryDTO) {
        // 创建查询构造器
        LambdaQueryWrapper<AtlasMappingSecondCostEntity> queryWrapper = Wrappers.<AtlasMappingSecondCostEntity>lambdaQuery()
                .eq(AtlasMappingSecondCostEntity::getPriceDetailField, queryDTO.getPriceDetailField())
                .eq(AtlasMappingSecondCostEntity::getStatus, DisableStatusEnum.ENABLE.getValue());

        // 根据syncAction处理
        String syncAction = queryDTO.getSyncAction();
        if (StringUtil.isNotBlank(syncAction)) {
            // 查询条件
            queryWrapper.and(wrapper -> wrapper
                    .eq(AtlasMappingSecondCostEntity::getSyncAction, "ALL")
                    .or()
                    .like(AtlasMappingSecondCostEntity::getSyncAction, syncAction));
        }

        // 根据BuCode处理
        String buCode = queryDTO.getBuCode();
        if (StringUtil.isNotBlank(buCode)) {
            queryWrapper.and(wrapper -> wrapper
                    .eq(AtlasMappingSecondCostEntity::getBuCode, "ALL")
                    .or()
                    .like(AtlasMappingSecondCostEntity::getBuCode, buCode));
        }

        // 执行查询
        List<AtlasMappingSecondCostEntity> costEntityList = list(queryWrapper);

        // 使用 Iterator 遍历并过滤掉不符合条件的元素
        Iterator<AtlasMappingSecondCostEntity> iterator = costEntityList.iterator();
        String deliveryTypeQuery = queryDTO.getDeliveryType() != null ? queryDTO.getDeliveryType().toString() : null;
        String deliveryTypeNameQuery = queryDTO.getDeliveryTypeName();

        while (iterator.hasNext()) {
            AtlasMappingSecondCostEntity secondCostEntity = iterator.next();
            String deliveryType = secondCostEntity.getDeliveryType();

            // 过滤条件 deliveryType不为空时，按照deliveryType进行过滤
            if (StringUtil.isNotBlank(deliveryType)) {
                if (Arrays.stream(deliveryType.split(",")).noneMatch(s -> s.equals(deliveryTypeQuery))) {
                    iterator.remove();
                }
            } else if (StringUtil.isNotBlank(deliveryTypeNameQuery)) {
                // 过滤条件 deliveryType为空时，按照deliveryTypeName进行过滤
                String deliveryTypeName = secondCostEntity.getDeliveryTypeName();
                if (!deliveryTypeName.equals("ALL") && !deliveryTypeNameQuery.contains(deliveryTypeName)) {
                    iterator.remove();
                }
            }
        }

        // 返回结果
        return costEntityList.stream().findFirst().orElse(null);
    }

    public List<String> getSecondCostPriceFields() {
        // 筛选所有启用状态的 priceDetailField并转换为List并去重
        return list(Wrappers.<AtlasMappingSecondCostEntity>lambdaQuery()
                .eq(AtlasMappingSecondCostEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .select(AtlasMappingSecondCostEntity::getPriceDetailField))
                .stream()
                .map(AtlasMappingSecondCostEntity::getPriceDetailField)
                .distinct()
                .collect(Collectors.toList());
    }
}
