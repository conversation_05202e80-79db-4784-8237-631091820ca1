package com.navigator.cuckoo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.gson.Gson;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.dao.AtlasSyncRequestDao;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncUriDTO;
import com.navigator.cuckoo.pojo.dto.create.AtlasContractCreateSyncDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRequestEntity;
import com.navigator.cuckoo.pojo.enums.AtlasOperationSourceEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncObjectTypeEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncStatusEnum;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import com.navigator.cuckoo.service.IAtlasSyncCallbackService;
import com.navigator.cuckoo.service.IAtlasSyncService;
import com.navigator.cuckoo.service.convert.AtlasSyncConvertService;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 同步接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/15
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncServiceImpl implements IAtlasSyncService {
    @Value("${atlas.syncSwitch.open:0}")
    private Integer syncSwitch;

    @Value("${atlas.messageSync.open:0}")
    private Integer messageSync;

    @Value("${messageQueue.atlas.syncQueueName}")
    private String messageQueueName;

    // dao
    private final AtlasSyncRequestDao syncRequestDao;
    private final AtlasSyncRecordDao syncRecordDao;

    // service
    private final JmsTemplate jmsTemplate;
    private final AtlasSyncConvertService convertService;
    private final AtlasSyncUriService uriService;
    private final IAtlasSyncCallbackService syncCallbackService;
    private final IAtlasOperationLogService operationLogService;


    @Override
    public void syncContractRequest(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("=====[ATLAS#IAtlasSyncServiceImpl#syncContractRequest:{}] 结束请求方法=====", syncRequestDTO);

        // save trade init data
        AtlasSyncRequestEntity requestEntity = saveRequestInfo(syncRequestDTO);

        // sync contract by requestEntity
        syncByRequestEntity(requestEntity);

        log.info("=====[ATLAS#IAtlasSyncServiceImpl#syncContractRequest:{}] 结束请求方法=====", syncRequestDTO);
    }

    @Override
    public void syncByRequestEntity(AtlasSyncRequestEntity requestEntity) {
        // process request data
        AtlasSyncActionEnum syncActionEnum = AtlasSyncActionEnum.getByValue(requestEntity.getOperationType());

        switch (syncActionEnum) {
            case DELIVERY_REQUEST:
                List<AtlasDeliveryDefDTO> atlasDeliveryDefDTOS = convertService.processDeliveryRequestData(requestEntity);

                // 根据提货申请 → 分配的DR信息 → 同步Atlas
                atlasDeliveryDefDTOS.forEach(atlasDeliveryDefDTO -> {
                    String uuid = atlasDeliveryDefDTO.getDeliveryRequest().getHeader().getUuid();
                    String businessEntity = atlasDeliveryDefDTO.getDeliveryRequest().getHeader().getBusinessEntity();

                    // save processed record data
                    AtlasSyncRecordEntity recordEntity = saveRecordInfo(requestEntity, uuid, businessEntity, new Gson().toJson(atlasDeliveryDefDTO));

                    // send Atlas data
                    syncAtlasInfo(recordEntity, null, atlasDeliveryDefDTO, requestEntity.getOptionSource());
                });
                break;
            case DELIVERY_REQUEST_CANCEL:
                List<AtlasDeliveryDefDTO> cancelDeliveryDefDTOS = convertService.processDeliveryRequestCancelData(requestEntity);

                // 根据提货申请取消 → 分配的DR信息 → 同步Atlas
                cancelDeliveryDefDTOS.forEach(cancelDeliveryDefDTO -> {
                    String deliveryUuid = cancelDeliveryDefDTO.getDeliveryRequest().getHeader().getUuid();
                    String deliveryBusinessEntity = cancelDeliveryDefDTO.getDeliveryRequest().getHeader().getBusinessEntity();

                    // save processed record data
                    AtlasSyncRecordEntity cancelRecordEntity = saveRecordInfo(requestEntity, deliveryUuid, deliveryBusinessEntity, "");

                    // send Atlas data
                    syncAtlasInfo(cancelRecordEntity, null, cancelDeliveryDefDTO, requestEntity.getOptionSource());
                });
                break;
            default:
                // 合同接口的同步
                AtlasContractDefDTO atlasContractDefDTO = convertService.processContractRequestData(requestEntity);
                String uuid = atlasContractDefDTO.getContract().getHeader().getUuid();
                String businessEntity = atlasContractDefDTO.getContract().getHeader().getBusinessEntity();

                // save processed record data
                AtlasSyncRecordEntity recordEntity = saveRecordInfo(requestEntity, uuid, businessEntity, new Gson().toJson(atlasContractDefDTO));

                // send Atlas data
                syncAtlasInfo(recordEntity, atlasContractDefDTO, null, requestEntity.getOptionSource());
                break;
        }
    }

    @Override
    public AtlasSyncRequestEntity saveRequestInfo(AtlasSyncRequestDTO syncRequestDTO) {

        AtlasSyncRequestEntity requestEntity = BeanUtil.toBean(syncRequestDTO, AtlasSyncRequestEntity.class);

        if (null != requestEntity) {
            requestEntity.setSyncTime(new Date())
                    .setRequestInfo(new Gson().toJson(syncRequestDTO))
                    .setSyncStatus(AtlasSyncStatusEnum.NOT_CALL_ATLAS.getValue());

            // 定价完成的特殊处理
            if (requestEntity.getOperationType().equals(AtlasSyncActionEnum.PRICE_UPDATE.getValue())) {
                requestEntity.setObjectType(AtlasSyncObjectTypeEnum.PRICE.getValue());
            }

            boolean save = syncRequestDao.save(requestEntity);
            if (save) {
                syncRequestDTO.setRequestId(requestEntity.getId());
            }
        }
        return requestEntity;
    }

    private AtlasSyncRecordEntity saveRecordInfo(AtlasSyncRequestEntity requestEntity, String uuid, String businessEntity, String requestInfo) {

        AtlasSyncRecordEntity recordEntity = new AtlasSyncRecordEntity();
        recordEntity.setRequestId(requestEntity.getId())
                .setUuid(uuid)
                .setBizId(requestEntity.getBizId())
                .setBizCode(requestEntity.getBizCode())
                .setObjectType(requestEntity.getObjectType())
                .setOperationType(requestEntity.getOperationType())
                .setTradeType(requestEntity.getTradeType())
                .setTryTimes(0)
                .setBusinessDocId(requestEntity.getBizCode())
                .setBusinessEntity(businessEntity)
                .setAtlasRequestInfo(requestInfo)
                .setSyncStatus(AtlasSyncStatusEnum.NOT_CALL_ATLAS.getValue())
                .setTargetSystem(SystemEnum.ATLAS.getName())
                .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                .setCreatedBy(requestEntity.getCreatedBy())
                .setUpdatedBy(requestEntity.getUpdatedBy());

        syncRecordDao.save(recordEntity);

        return recordEntity;
    }

    /**
     * 接口同步 - 细化operation_log的记录
     *
     * @param recordEntity   用于更新回调
     * @param contractDefDTO 同步合同的内容
     * @param deliveryDefDTO 同步提货的内容
     * @param optionSource   操作来源：手动/自动
     */
    private void syncAtlasInfo(AtlasSyncRecordEntity recordEntity,
                               AtlasContractDefDTO contractDefDTO,
                               AtlasDeliveryDefDTO deliveryDefDTO,
                               String optionSource) {
        // 同步前的校验
        try {
            if (contractDefDTO != null) {
                String taxCode = contractDefDTO.getContract().getContractData().getTaxCode();
                if (StringUtils.isBlank(taxCode)) {
                    recordEntity.setAtlasResultsInfo("taxCode is empty");
                    syncRecordDao.updateById(recordEntity);
                    operationLogService.saveOperationLogByRecord(recordEntity, AtlasOperationSourceEnum.NEVER.getDesc());
                    return;
                }

                String paymentTerm = contractDefDTO.getContract().getContractPricing().getPaymentTerm();
                if (StringUtils.isBlank(paymentTerm)) {
                    recordEntity.setAtlasResultsInfo("paymentTerm is empty");
                    syncRecordDao.updateById(recordEntity);
                    operationLogService.saveOperationLogByRecord(recordEntity, AtlasOperationSourceEnum.NEVER.getDesc());
                    return;
                }
            }
        } catch (Exception e) {
            log.error("sync before check exception:{}", e.getMessage());
        }

        // 是否开启ATLAS接口同步
        if (syncSwitch == 1) {
            // 是否启用消息队列
            if (messageSync == 1) {
                // assemble sync dto
                AtlasContractCreateSyncDTO createSyncDTO = new AtlasContractCreateSyncDTO()
                        .setAtlasContractDefDTO(contractDefDTO)
                        .setAtlasDeliveryDefDTO(deliveryDefDTO)
                        .setOptionSource(optionSource)
                        .setTradeType(recordEntity.getTradeType())
                        .setOptionType(recordEntity.getOperationType());
                // send to service bus
                jmsTemplate.convertAndSend(messageQueueName, new Gson().toJson(createSyncDTO));

                log.info("===============[{}] Queue Message Send Success : {}===============", messageQueueName, createSyncDTO);
            } else {
                String requestUri = "";

                try {
                    AtlasSyncUriDTO syncUriDTO = new AtlasSyncUriDTO()
                            .setContractDefDTO(contractDefDTO)
                            .setDeliveryDefDTO(deliveryDefDTO)
                            .setTradeType(recordEntity.getTradeType())
                            .setOperationType(recordEntity.getOperationType());

                    ResponseEntity<String> responseEntity = uriService.syncAtlasInfo(syncUriDTO);
                    requestUri = syncUriDTO.getRequestUri();

                    // 处理回调
                    syncCallbackService.processSyncCallBack(recordEntity, responseEntity);

                } catch (Exception exception) {
                    exception.printStackTrace();
                    log.error("direct send sync exception:{}", exception.getMessage());

                    // 异常回调
                    syncCallbackService.processSyncCallBackError(recordEntity);

                    recordEntity.setAtlasResultsInfo(exception.getMessage());
                }

                // 记录日志
                String newOperationSource = AtlasOperationSourceEnum.TRADE_SERVICE_DIRECT_CALL.getDesc();
                if (StringUtils.isNotBlank(optionSource)) {
                    newOperationSource = newOperationSource + "|" + optionSource;
                }

                operationLogService.saveOperationLogByRecord(recordEntity.setRequestUri(requestUri), newOperationSource);
            }
        } else {
            operationLogService.saveOperationLogByRecord(recordEntity, AtlasOperationSourceEnum.NEVER.getDesc());
        }
    }
}
