package com.navigator.cuckoo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.google.gson.Gson;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.common.util.JwtUtils;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.dao.AtlasSyncRequestDao;
import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncUriDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRequestEntity;
import com.navigator.cuckoo.pojo.enums.AtlasOperationSourceEnum;
import com.navigator.cuckoo.pojo.enums.AtlasReprocessTypeEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncObjectTypeEnum;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import com.navigator.cuckoo.service.IAtlasSyncCallbackService;
import com.navigator.cuckoo.service.IAtlasSyncRetryService;
import com.navigator.cuckoo.service.IAtlasSyncService;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * ATLAS重试 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncRetryServiceImpl implements IAtlasSyncRetryService {
    // dao
    private final AtlasSyncRequestDao syncRequestDao;
    private final AtlasSyncRecordDao syncRecordDao;
    // service
    private final IAtlasSyncService syncService;
    private final IAtlasOperationLogService operationLogService;
    private final IAtlasSyncCallbackService syncCallbackService;
    private final AtlasTradeRemoteService tradeRemoteService;
    private final AtlasSyncUriService uriService;

    @Override
    public void reSyncContractRequest(AtlasRetryDTO atlasRetryDTO) {
        log.info("=====[ATLAS#AtlasSyncRetryServiceImpl#reSyncContractRequest:{}] 开始请求方法=====", atlasRetryDTO);

        for (Integer recordId : atlasRetryDTO.getRecordIds()) {
            AtlasSyncRecordEntity recordEntity = syncRecordDao.getById(recordId);
            if (null != recordEntity) {
                // acquire request info
                AtlasSyncRequestEntity requestEntity = syncRequestDao.getById(recordEntity.getRequestId());

                if (null != requestEntity) {
                    // 更新人
                    String updateName = "";
                    EmployEntity employ = tradeRemoteService.getEmployById(Integer.parseInt(JwtUtils.getCurrentUserId()));
                    if (null != employ) {
                        updateName = employ.getName();
                    }

                    // 更新操作类型
                    recordEntity.setReprocessed(AtlasReprocessTypeEnum.getByValue(atlasRetryDTO.getReprocessType()).getName())
                            .setUpdatedBy(updateName);
                    syncRecordDao.updateById(recordEntity);

                    // 重新传输
                    if (atlasRetryDTO.getReprocessType() == AtlasReprocessTypeEnum.REPROCESSED.getValue()) {
                        // rebuild request
                        AtlasSyncRequestEntity newRequest = BeanUtil.toBean(requestEntity, AtlasSyncRequestEntity.class);
                        newRequest.setId(null)
                                .setCreatedAt(new Date())
                                .setUpdatedAt(new Date())
                                .setOptionSource(AtlasOperationSourceEnum.MANUAL_RETRY.getDesc());

                        syncRequestDao.save(newRequest);

                        // rebuild record info
                        syncService.syncByRequestEntity(newRequest);

                    } else if (atlasRetryDTO.getReprocessType() == AtlasReprocessTypeEnum.CANCELLED.getValue()) {
                        // 记录日志
                        operationLogService.saveOperationLogByRecord(recordEntity.setUpdatedBy(updateName)
                                , AtlasOperationSourceEnum.MANUAL_CANCELLED.getDesc());
                    }
                }
            }
        }

        log.info("=====[ATLAS#AtlasSyncRetryServiceImpl#reSyncContractRequest:{}] 结束请求方法=====", atlasRetryDTO);
    }

    @Override
    public void reSyncByRecordId(Integer recordId) {
        AtlasSyncRecordEntity recordEntity = syncRecordDao.getById(recordId);
        if (null != recordEntity) {

            AtlasContractDefDTO contractDefDTO = null;
            AtlasDeliveryDefDTO deliveryDefDTO = null;

            if (AtlasSyncObjectTypeEnum.DELIVERY.getValue().equals(recordEntity.getObjectType())) {
                deliveryDefDTO = new Gson().fromJson(recordEntity.getAtlasRequestInfo(), AtlasDeliveryDefDTO.class);
            } else {
                contractDefDTO = new Gson().fromJson(recordEntity.getAtlasRequestInfo(), AtlasContractDefDTO.class);
            }

            String requestUri = "";

            try {
                // 手动增加重试次数
                syncRecordDao.updateById(recordEntity
                        .setTryTimes(recordEntity.getTryTimes() + 1)
                        .setUpdatedAt(new Date()));

                // 调用接口
                AtlasSyncUriDTO syncUriDTO = new AtlasSyncUriDTO()
                        .setContractDefDTO(contractDefDTO)
                        .setDeliveryDefDTO(deliveryDefDTO)
                        .setTradeType(recordEntity.getTradeType())
                        .setOperationType(recordEntity.getOperationType());
                ResponseEntity<String> responseEntity = uriService.syncAtlasInfo(syncUriDTO);

                requestUri = syncUriDTO.getRequestUri();

                // 处理回调
                syncCallbackService.processSyncCallBack(recordEntity, responseEntity);
            } catch (Exception exception) {
                exception.printStackTrace();

                // 记录报错信息
                recordEntity.setAtlasResultsInfo(new Gson().toJson(exception.getMessage()));
            }
            // 记录日志
            operationLogService.saveOperationLogByRecord(recordEntity.setRequestUri(requestUri), AtlasOperationSourceEnum.INTERFACE_RETRY.getDesc());
        }

    }

    @Override
    public void reSyncByRequestId(Integer requestId) {
        AtlasSyncRequestEntity requestEntity = syncRequestDao.getById(requestId);

        if (null != requestEntity) {
            syncService.syncByRequestEntity(requestEntity.setOptionSource(AtlasOperationSourceEnum.MANUAL_RETRY.getDesc()));
        }

    }

    @Override
    public void rebuildRequestEntity(AtlasSyncRequestDTO atlasSyncRequestDTO) {
        // 保存request记录
        syncService.saveRequestInfo(atlasSyncRequestDTO);
    }
}
