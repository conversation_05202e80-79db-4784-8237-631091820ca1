package com.navigator.cuckoo.service.impl;

import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.cuckoo.dao.*;
import com.navigator.cuckoo.pojo.dto.importer.DepartmentMappingImportDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasSecondCostQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.entity.AtlasMappingDepartmentEntity;
import com.navigator.cuckoo.pojo.entity.AtlasMappingMarketEntity;
import com.navigator.cuckoo.pojo.entity.AtlasMappingSecondCostEntity;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.service.IAtlasMappingService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasMappingServiceImpl implements IAtlasMappingService {
    private final AtlasMappingMarketDao atlasMappingMarketDao;
    private final AtlasMappingDepartmentDao atlasMappingDepartmentDao;
    private final AtlasMappingInvoiceDao atlasMappingInvoiceDao;
    private final AtlasTradeRemoteService tradeRemoteService;
    private final AtlasMappingContractDao atlasMappingContractDao;
    private final AtlasMappingSecondCostDao atlasMappingSecondCostDao;

    @Override
    public String getMappingMarketByFutureLetter(String futureLetter) {
        List<AtlasMappingMarketEntity> marketEntityList = atlasMappingMarketDao.getListByFutureLetter(futureLetter);
        return !marketEntityList.isEmpty() ? marketEntityList.get(0).getFnoMarket() : "";
    }

    @Override
    public String getDepartmentCode(ContractEntity contractEntity, String syncAction) {

        try {
            String departmentCode = "";

            // 获取账套信息
            String siteCode = contractEntity.getSiteCode();

            // 拆分转厂的BusinessEntity
            if (syncAction.equals(AtlasSyncActionEnum.SPLIT.getValue()) && contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1) {
                ContractEntity parentContract = tradeRemoteService.getBasicContractById(contractEntity.getParentId());
                siteCode = parentContract.getSiteCode();
            }

            SiteEntity siteEntity = tradeRemoteService.getBusinessEntityBySiteCode(siteCode);

            // 获取商品信息
            String goodsMdmCode = tradeRemoteService.getSkuMdmId(contractEntity.getGoodsId(), contractEntity.getContractCategoryType());

            // 获取marketZone信息
            String marketZone = tradeRemoteService.getWarehouseMarketZone(contractEntity.getShipWarehouseId());

            AtlasMappingDepartmentEntity mappingDepartmentDTO = new AtlasMappingDepartmentEntity();
            mappingDepartmentDTO
                    .setBusinessEntity(siteEntity.getAtlasCode())
                    .setCommodityCode(goodsMdmCode)
                    .setContractCategoryType(contractEntity.getContractCategoryType());

            List<AtlasMappingDepartmentEntity> departmentEntityList = atlasMappingDepartmentDao.getByCondition(mappingDepartmentDTO);
            for (AtlasMappingDepartmentEntity mappingDepartment : departmentEntityList) {
                if (StringUtil.isBlank(mappingDepartment.getMarketZone())) {
                    departmentCode = mappingDepartment.getMdmCode();
                    break;
                }

                if (mappingDepartment.getMarketZone().equals(marketZone)) {
                    departmentCode = mappingDepartment.getMdmCode();
                }
            }

            return departmentCode != null ? departmentCode : "";
        } catch (Exception e) {
            log.error("获取部门代码失败", e);
            return "";
        }
    }

    @Override
    public String getTaxCodeMdmCode(String invoiceTypeMdmCode, BigDecimal taxRate, Integer contractCategoryType) {
        return atlasMappingInvoiceDao.getTaxCodeMdmCode(invoiceTypeMdmCode, taxRate, contractCategoryType);
    }

    @Override
    public AtlasMappingContractEntity getByNavContractCode(String contractCode) {
        List<AtlasMappingContractEntity> mappingContractEntityList = atlasMappingContractDao.getByNavContractCode(contractCode);
        return !mappingContractEntityList.isEmpty() ? mappingContractEntityList.get(0) : null;
    }

    @Override
    public boolean updateAtlasMappingContract(AtlasMappingContractEntity atlasMappingContractEntity) {
        // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07
        return atlasMappingContractDao.updateByIdWithVersionRetry(atlasMappingContractEntity);
    }

    @Override
    public List<String> getSecondCostPriceFields() {
        return atlasMappingSecondCostDao.getSecondCostPriceFields();
    }

    @Override
    public AtlasMappingSecondCostEntity getSecondCostByCondition(AtlasSecondCostQueryDTO atlasSecondCostQueryDTO) {
        return atlasMappingSecondCostDao.getByCondition(atlasSecondCostQueryDTO);
    }

    @Override
    public String importAtlasDepartmentMapping(MultipartFile file) {
        List<DepartmentMappingImportDTO> mappingImportDTOList = EasyPoiUtils.importExcel(file, 0, 1, DepartmentMappingImportDTO.class);
        Map<String, AtlasMappingDepartmentEntity> departmentMap = new HashMap<>();

        for (DepartmentMappingImportDTO dto : mappingImportDTOList) {
            List<String> businessEntities = Arrays.asList("h1_" + dto.getDepartmentForH1(), "ha_" + dto.getDepartmentForHA(), "he_" + dto.getDepartmentForHE());
            List<String> commodityCodes = new ArrayList<>();
            if (!"0".equals(dto.getSpotMdmId())) commodityCodes.add("1_" + dto.getSpotMdmId());
            if (!"0".equals(dto.getWarrantMdmId())) commodityCodes.add("2_" + dto.getWarrantMdmId());
            if (!"0".equals(dto.getSoybean2MdmId())) commodityCodes.add("3_" + dto.getSoybean2MdmId());

            for (String businessEntity : businessEntities) {
                for (String commodityCode : commodityCodes) {
                    String mdmCode = businessEntity.split("_")[1];
                    String key = businessEntity + "_" + commodityCode;

                    AtlasMappingDepartmentEntity mappingDepartment = new AtlasMappingDepartmentEntity()
                            .setMdmCode("null".equals(mdmCode) ? "" : mdmCode)
                            .setBusinessEntity(businessEntity.split("_")[0])
                            .setContractCategoryType(Integer.valueOf(commodityCode.split("_")[0]))
                            .setCommodityCode(commodityCode.split("_")[1])
                            .setMarketZone("CX")
                            .setStatus("null".equals(mdmCode) ? 0 : 1);

                    departmentMap.put(key, mappingDepartment);
                }
            }
        }

        departmentMap.forEach((key, value) -> atlasMappingDepartmentDao.saveOrUpdate(value));
        return "导入成功";
    }

}
