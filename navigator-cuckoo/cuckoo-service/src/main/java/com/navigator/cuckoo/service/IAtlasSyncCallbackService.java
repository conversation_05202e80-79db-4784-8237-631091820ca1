package com.navigator.cuckoo.service;

import com.navigator.cuckoo.pojo.dto.ack.AtlasFunctionAckDTO;
import com.navigator.cuckoo.pojo.dto.ack.AtlasQueryBlockedQuantityDTO;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import org.springframework.http.ResponseEntity;

/**
 * <p>
 * ATLAS回调记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IAtlasSyncCallbackService {

    /**
     * 处理ATLAS异步回调
     */
    void processAsyncCallBack(AtlasFunctionAckDTO atlasFunctionAckDTO);

    /**
     * 处理ATLAS同步回调
     */
    void processSyncCallBack(AtlasSyncRecordEntity recordEntity, ResponseEntity<String> responseEntity);

    /**
     * 处理ATLAS同步异常回调
     */
    void processSyncCallBackError(AtlasSyncRecordEntity recordEntity);

    /**
     * 查询ATLAS blocked数量
     */
    AtlasQueryBlockedQuantityDTO queryBlockedQuantity(AtlasQueryBlockedQuantityDTO atlasFunctionAckDTO);

    /**
     * DR合同执行情况更新信息
     */
    void ackContractExeUpdate(String  atlasDeliveryAckDTO);
}
