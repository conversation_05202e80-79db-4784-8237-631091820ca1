package com.navigator.cuckoo.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.cuckoo.dao.AtlasMappingContractDao;
import com.navigator.cuckoo.dao.AtlasSyncCallbackDao;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncCallbackEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.enums.AtlasSyncStatusEnum;
import com.navigator.cuckoo.pojo.vo.AtlasSyncRecordVO;
import com.navigator.cuckoo.service.IAtlasSyncQueryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 查询接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasSyncQueryServiceImpl implements IAtlasSyncQueryService {
    private final AtlasSyncRecordDao syncRecordDao;
    private final AtlasSyncCallbackDao syncCallbackDao;
    private final AtlasMappingContractDao mappingContractDao;

    @Override
    public IPage<AtlasSyncRecordVO> getSyncRecordList(QueryDTO<AtlasQueryDTO> queryDTO) {

        IPage<AtlasSyncRecordEntity> recordEntityIPage = syncRecordDao.pageSyncRecord(queryDTO);

        List<AtlasSyncRecordVO> syncRecordVOList = recordEntityIPage.getRecords().stream().map(atlasSyncRecordEntity -> {

            AtlasSyncRecordVO syncRecordVO = BeanUtil.toBean(atlasSyncRecordEntity, AtlasSyncRecordVO.class);

            // 未调用atlas的同步记录
            if (AtlasSyncStatusEnum.NOT_CALL_ATLAS.getValue().equals(atlasSyncRecordEntity.getSyncStatus())) {
                syncRecordVO.setCallbackEntity(new AtlasSyncCallbackEntity()
                        .setBusinessDocId(atlasSyncRecordEntity.getBizCode())
                        .setReturnMsg(atlasSyncRecordEntity.getAtlasResultsInfo()));
            } else {
                AtlasSyncCallbackEntity callbackEntity = syncCallbackDao.getByUuid(atlasSyncRecordEntity.getUuid());
                if (null != syncRecordVO && null != callbackEntity) {
                    syncRecordVO.setAckSequence(atlasSyncRecordEntity.getTryTimes() + 1);
                    syncRecordVO.setCallbackEntity(callbackEntity);
                }
            }

            return syncRecordVO;
        }).collect(Collectors.toList());

        return new Page<AtlasSyncRecordVO>()
                .setPages(recordEntityIPage.getPages())
                .setCurrent(recordEntityIPage.getCurrent())
                .setSize(recordEntityIPage.getSize())
                .setTotal(recordEntityIPage.getTotal())
                .setRecords(syncRecordVOList);
    }

    @Override
    public IPage<AtlasMappingContractEntity> getMappingContractList(QueryDTO<AtlasMappingQueryDTO> queryDTO) {
        return mappingContractDao.pageMappingContract(queryDTO);
    }

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    @Override
    public List<String> getOperationTypeList() {
        QueryWrapper<AtlasSyncRecordEntity> wrapper = new QueryWrapper<>();
        wrapper.select("DISTINCT operation_type");

        return syncRecordDao.list(wrapper).stream()
                .map(AtlasSyncRecordEntity::getOperationType)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End
}
