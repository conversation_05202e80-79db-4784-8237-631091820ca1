package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasMappingMarketMapper;
import com.navigator.cuckoo.pojo.entity.AtlasMappingMarketEntity;

import java.util.List;

@Dao
public class AtlasMappingMarketDao extends BaseDaoImpl<AtlasMappingMarketMapper, AtlasMappingMarketEntity> {

    public List<AtlasMappingMarketEntity> getListByFutureLetter(String futureLetter) {
        return list(Wrappers.<AtlasMappingMarketEntity>lambdaQuery()
                .eq(AtlasMappingMarketEntity::getFutureLetters, futureLetter));
    }
}
