package com.navigator.cuckoo.service.convert;

import cn.hutool.core.util.ObjectUtil;
import com.navigator.admin.pojo.entity.*;
import com.navigator.bisiness.enums.ContractNatureEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.cuckoo.pojo.entity.AtlasPaymentTermChangesEntity;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.service.IAtlasMappingService;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <p>
 * 主数据配置相关服务
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/29
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class AtlasSyncMDMService {
    private final AtlasTradeRemoteService tradeRemoteService;
    private final IAtlasMappingService mappingService;
    private final IAtlasOperationLogService operationLogService;

    @Value("${atlas.mdmCode.defaultPaymentTerm:100AD30}")
    private String defaultPaymentTerm;

    // 优化：所属商务默认值由WUZIJI改为WANGJA1 Author: Mr 2025-04-01
    @Value("${atlas.mdmCode.defaultTrader:WANGJA1}")
    private String defaultTrader;

    /**
     * 买/卖方主体的mdmID
     *
     * @param customerId 主体id
     */
    public String getCustomerMdmCode(Integer customerId) {

        String customerMdmCode = "";
        CustomerEntity customerEntity = tradeRemoteService.getBasicCustomerById(customerId);
        if (null != customerEntity) {
            customerMdmCode = customerEntity.getMdmCustomerCode();
        }
        return customerMdmCode == null ? "" : customerMdmCode;
    }

    /**
     * 获取提货权合同的mdmID
     *
     * @param dceContractNo   合同号
     * @param customerMdmCode 客户mdmID
     */
    public String getDceCustomerMdmCode(String dceContractNo, String customerMdmCode) {
        // 预分配 -> 提货权合同
        ContractEntity contractEntity = tradeRemoteService.getBasicContractByCode(dceContractNo);
        if (ObjectUtil.isNotEmpty(contractEntity) && ContractNatureEnum.WAREHOUSE_CARGO_RIGHTS.getValue().equals(contractEntity.getContractNature())) {
            // 找到父合同的仓单贸易合同
            ContractEntity traderContract = tradeRemoteService.getBasicContractById(contractEntity.getParentId());
            return getCustomerMdmCode(traderContract.getCustomerId());
        }

        if (ObjectUtil.isNotEmpty(contractEntity)) {
            return getCustomerMdmCode(contractEntity.getCustomerId());
        }
        return customerMdmCode;
    }

    /**
     * 货品代码的mdmID
     *
     * @param goodsId              货品代码
     * @param contractCategoryType 合同类别 1.现货 2.仓单 3.豆二
     */
    public String getGoodsMdmCode(Integer goodsId, Integer contractCategoryType) {

        String goodsMdmCode = tradeRemoteService.getSkuMdmId(goodsId, contractCategoryType);

        return goodsMdmCode == null ? "" : goodsMdmCode;
    }

    /**
     * 包装代码mdmCode
     *
     * @param goodsId 货品id
     */
    public String getPackageMdmCode(Integer goodsId) {

        String packageMdmCode = "";

        SkuEntity skuEntity = tradeRemoteService.getSkuBySkuId(goodsId);
        if (null != skuEntity) {
            AttributeValueEntity valueEntity = tradeRemoteService.getPackageById(skuEntity.getPackageId());
            if (null != valueEntity) {
                packageMdmCode = valueEntity.getMdmPackageCode();
            }
        }
        return packageMdmCode == null ? "" : packageMdmCode;
    }

    /**
     * 目的港的mdmID
     *
     * @param destinationId 目的港id
     */
    public String getDestinationMdmCode(Integer destinationId) {

        String destinationMdmCode = "";
        SystemRuleItemEntity itemEntity = tradeRemoteService.getSystemRuleById(destinationId);
        if (null != itemEntity) {
            destinationMdmCode = itemEntity.getMdmCode();
        }
        return destinationMdmCode == null ? "" : destinationMdmCode;
    }

    /**
     * 发货库点的ATLAS code
     *
     * @param shipWarehouseId 发货库点id
     */
    public String getShipWarehouseAtlasCode(Integer shipWarehouseId) {

        /*String shipWarehouseAtlasCode = "";
        WarehouseEntity warehouseEntity = tradeRemoteService.getShipWarehouseById(shipWarehouseId);
        if (null != warehouseEntity) {
            shipWarehouseAtlasCode = warehouseEntity.getAtlasWarehouseCode();
        }
        return shipWarehouseAtlasCode == null ? "" : shipWarehouseAtlasCode;*/
        // 接口保留这个字段（可扩展性）现阶段没有字段对应 send blank
        return "";
    }

    /**
     * 库点对应的terminal code
     *
     * @param warehouseId 库点id
     */
    public String getWarehouseTerminalCode(Integer warehouseId) {

        String atlasTerminalCode = "";
        WarehouseEntity warehouseEntity = tradeRemoteService.getWarehouseById(warehouseId);
        if (null != warehouseEntity) {
            atlasTerminalCode = warehouseEntity.getAtlasTerminalCode();
        }
        return atlasTerminalCode == null ? "" : atlasTerminalCode;
    }

    /**
     * 交货工厂的mdmID
     *
     * @param factoryCode 交货工厂code
     */
    public String getFactoryMdmCode(String factoryCode) {

        String factoryMdmCode = "";
        FactoryEntity factoryEntity = tradeRemoteService.getFactoryByCode(factoryCode);
        if (null != factoryEntity) {
            factoryMdmCode = factoryEntity.getMdmFactoryCode();
        }
        return factoryMdmCode == null ? "" : factoryMdmCode;
    }

    /**
     * 发票类型代码的mdmID
     *
     * @param invoiceTypeId 交货工厂id
     */

    public String getInvoiceTypeMdmCode(Integer invoiceTypeId) {

        String invoiceTypeMdmCode = "";
        SystemRuleItemEntity itemEntity = tradeRemoteService.getSystemRuleById(invoiceTypeId);
        if (null != itemEntity) {
            invoiceTypeMdmCode = itemEntity.getMdmCode();
        }

        return invoiceTypeMdmCode == null ? "" : invoiceTypeMdmCode;
    }

    /**
     * 付款方式代码的mdmID
     *
     * @param operationType  操作类型
     * @param contractEntity 合同实体
     * @return 付款方式代码的mdmID
     */
    public String getPayConditionMdmCode(String operationType, ContractEntity contractEntity) {
        // 获取支付条件
        PayConditionEntity payConditionEntity = tradeRemoteService.getPayConditionById(contractEntity.getPayConditionId());

        // 如果支付条件为空，则直接返回空字符串
        if (payConditionEntity == null) {
            return "";
        }

        // 如果是注销C的生成的销售、采购合同，且选择的Payment Term为预付时，取默认值
        if (operationType.equals(AtlasSyncActionEnum.CREATE.getValue()) &&
                contractEntity.getCreditDays() == 0 &&
                contractEntity.getTradeType() == ContractTradeTypeEnum.WRITE_OFF_C.getValue()) {

            // 记录paymentTerm变更记录
            operationLogService.savePaymentTermChanges(new AtlasPaymentTermChangesEntity()
                    .setContractCode(contractEntity.getContractCode())
                    .setTradeType(contractEntity.getTradeType())
                    .setCreditDays(contractEntity.getCreditDays())
                    .setPayConditionId(contractEntity.getPayConditionId())
                    .setOldPaymentTermCode(payConditionEntity.getMdmPayConditionCode())
                    .setNewPaymentTermCode(defaultPaymentTerm)
            );

            return defaultPaymentTerm;
        }

        // 返回payConditionMdmCode，如果为空返回空字符串
        return payConditionEntity.getMdmPayConditionCode() == null ? "" : payConditionEntity.getMdmPayConditionCode();
    }

    /**
     * 提货库点的mdmID
     *
     * @param deliveryWarehouseId 提货库点id
     */
    public String getDeliveryWarehouseMdmCode(Integer deliveryWarehouseId) {

        /*String deliveryWarehouseMdmCode = "";
        DeliveryWarehouseEntity warehouseEntity = tradeRemoteService.getDeliveryWarehouseById(deliveryWarehouseId);
        if (null != warehouseEntity) {
            deliveryWarehouseMdmCode = warehouseEntity.getCode();
        }
        return deliveryWarehouseMdmCode == null ? "" : deliveryWarehouseMdmCode;*/
        //  现有逻辑去掉, 数据结构可以保留，接口触发的时候数据为空
        return "";
    }

    /**
     * 主体的mdmID
     *
     * @param siteCode 账套code
     */
    public String getBusinessEntityBySiteCode(String siteCode) {

        String businessEntity = "";
        SiteEntity siteEntity = tradeRemoteService.getBusinessEntityBySiteCode(siteCode);
        if (null != siteEntity) {
            businessEntity = siteEntity.getAtlasCode();
        }
        return businessEntity == null ? "" : businessEntity;
    }

    /**
     * 根据条件查询主体信息
     *
     * @param companyId           主体ID
     * @param deliveryFactoryCode 工厂code
     * @return
     */
    public String getBusinessEntityByCondition(Integer companyId, String deliveryFactoryCode) {

        String businessEntity = "";
        SiteEntity siteEntity = tradeRemoteService.getBusinessEntityByCondition(companyId, deliveryFactoryCode);
        if (null != siteEntity) {
            businessEntity = siteEntity.getAtlasCode();
        }
        return businessEntity == null ? "" : businessEntity;
    }

    /**
     * 获取MarketZone的mdmID
     *
     * @param warehouseId 库点id
     * @return MarketZone的mdmID
     */
    public String getMarketZoneMdmCode(Integer warehouseId) {

        String marketZoneMdmCode = tradeRemoteService.getWarehouseMarketZone(warehouseId);

        return marketZoneMdmCode == null ? "" : marketZoneMdmCode;
    }

    /**
     * 是否是第三方交割库-非LDC库（不定库中的）
     *
     * @param warehouseId 库点id
     */
    public String getThirdPartyTerminalOnly(Integer warehouseId) {

        String thirdPartyTerminalOnly = "N";
        WarehouseEntity warehouseEntity = tradeRemoteService.getWarehouseById(warehouseId);
        if (null != warehouseEntity) {
            // 不定库中的非LDC库
            Integer isUnset = warehouseEntity.getIsUnset();
            Integer warehouseType = warehouseEntity.getWarehouseType();

            if (isUnset != null && warehouseType != null && isUnset == 1 && warehouseType != 1) {
                thirdPartyTerminalOnly = "Y";
            }
        }
        return thirdPartyTerminalOnly;
    }

    /**
     * 获取税率代码的mdmID
     *
     * @param invoiceType          发票类型
     * @param taxRate              税率
     * @param contractCategoryType 合同类别 1.现货 2.仓单 3.豆二
     * @return 税率代码的mdmID
     */
    public String getTaxCodeMdmCode(Integer invoiceType, BigDecimal taxRate, Integer contractCategoryType) {
        String invoiceTypeMdmCode = "";
        String taxCodeMdmCode = "";
        SystemRuleItemEntity itemEntity = tradeRemoteService.getInvoiceType(invoiceType);
        if (null != itemEntity) {
            invoiceTypeMdmCode = itemEntity.getMdmCode();
        }

        // 如果结果不为空，则去对应的中转表查询mdmCode
        if (invoiceTypeMdmCode != null && !invoiceTypeMdmCode.isEmpty()) {
            taxCodeMdmCode = mappingService.getTaxCodeMdmCode(invoiceTypeMdmCode, taxRate, contractCategoryType);
        }

        return taxCodeMdmCode == null ? "" : taxCodeMdmCode;
    }

    /**
     * 获取所属商务的mdmID
     *
     * @param ownerId 所属商务id
     * @return
     */
    public String getTraderMdmCode(Integer ownerId) {
        // 优化：所属商务默认值由WUZIJI改为WANGJA1 Author: Mr 2025-04-01
        // String traderMdmCode = "WUZIJI";
        String traderMdmCode = defaultTrader;

        EmployEntity traderEntity = tradeRemoteService.getEmployById(ownerId);
        if (null != traderEntity) {
            traderMdmCode = traderEntity.getNickName();
        }
        return traderMdmCode == null ? "" : traderMdmCode;
    }

    /**
     * 获取交货类型mdmID
     *
     * @param deliveryType 交货类型id
     * @return
     */
    public String getDeliveryTypeMdmCode(Integer deliveryType) {
        String deliveryTypeMdmCode = "";
        DeliveryTypeEntity deliveryTypeEntity = tradeRemoteService.getDeliveryTypeById(deliveryType);
        if (null != deliveryTypeEntity) {
            deliveryTypeMdmCode = deliveryTypeEntity.getAtlasCode();
        }
        return deliveryTypeMdmCode == null ? "" : deliveryTypeMdmCode;
    }

    /**
     * 获取袋皮扣重mdmID
     *
     * @param needPackageWeight 是否需要袋皮扣重
     * @param packageWeight     袋皮扣重
     */
    public String getPackingWeightMdmCode(Integer needPackageWeight, String packageWeight) {
        String packingWeight = "0";
        if (needPackageWeight == 1) {
            SystemRuleItemEntity itemEntity = tradeRemoteService.getSystemRuleById(Integer.parseInt(packageWeight));
            if (null != itemEntity) {
                if ("不扣皮".equals(itemEntity.getRuleValue())) {
                    packingWeight = "0";
                } else {
                    packingWeight = itemEntity.getRuleValue().toLowerCase().split("kg")[0];
                }
            }
        }
        return packingWeight == null ? "" : packingWeight;
    }
}
