package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasSyncCallbackMapper;
import com.navigator.cuckoo.pojo.entity.AtlasSyncCallbackEntity;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Dao
public class AtlasSyncCallbackDao extends BaseDaoImpl<AtlasSyncCallbackMapper, AtlasSyncCallbackEntity> {

    public AtlasSyncCallbackEntity getByUuid(String uuid) {
        List<AtlasSyncCallbackEntity> callbackEntityList = list(Wrappers.<AtlasSyncCallbackEntity>lambdaQuery()
                .eq(AtlasSyncCallbackEntity::getUuid, uuid)
                .orderByDesc(AtlasSyncCallbackEntity::getId));
        return CollectionUtils.isNotEmpty(callbackEntityList) ? callbackEntityList.get(0) : null;
    }

    public List<AtlasSyncCallbackEntity> getListByBusinessDocId(String businessDocID) {
        return list(Wrappers.<AtlasSyncCallbackEntity>lambdaQuery()
                .eq(AtlasSyncCallbackEntity::getBusinessDocId, businessDocID));
    }
}
