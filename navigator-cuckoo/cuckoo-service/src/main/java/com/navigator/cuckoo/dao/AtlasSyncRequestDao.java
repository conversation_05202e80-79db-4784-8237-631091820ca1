package com.navigator.cuckoo.dao;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasSyncRequestMapper;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRequestEntity;
import org.apache.commons.collections.CollectionUtils;

import java.util.Arrays;
import java.util.List;

@Dao
public class AtlasSyncRequestDao extends BaseDaoImpl<AtlasSyncRequestMapper, AtlasSyncRequestEntity> {

    public AtlasSyncRequestEntity getLatestByBizCode(String bizCode) {
        List<AtlasSyncRequestEntity> requestEntityList = list(Wrappers.<AtlasSyncRequestEntity>lambdaQuery()
                .eq(AtlasSyncRequestEntity::getBizCode, bizCode)
                .in(AtlasSyncRequestEntity::getTradeType, Arrays.asList(
                        ContractTradeTypeEnum.REVISE_NORMAL.getValue(),
                        ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue(),
                        ContractTradeTypeEnum.FIXED.getValue()))
                .orderByDesc(AtlasSyncRequestEntity::getTtId));

        return CollectionUtils.isNotEmpty(requestEntityList) ? requestEntityList.get(0) : null;
    }
}
