package com.navigator.cuckoo.remote;

import com.navigator.cuckoo.config.AtlasFeignConfiguration;
import com.navigator.cuckoo.pojo.dto.payload.AtlasContractDefDTO;
import com.navigator.cuckoo.pojo.dto.payload.AtlasDeliveryDefDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URI;

/**
 * <p>
 * ATLAS Remote 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@FeignClient(value = "navigator-cuckoo-service", url = "default_url", configuration = AtlasFeignConfiguration.class)
public interface AtlasRemoteFacade {

    @PostMapping
    ResponseEntity<String> syncAtlasCreateInfo(URI uri, @RequestBody AtlasContractDefDTO atlasContractDefDTO);

    @PutMapping
    ResponseEntity<String> syncAtlasModifyInfo(URI uri, @RequestBody AtlasContractDefDTO atlasContractDefDTO);

    @PostMapping(value = "/split")
    ResponseEntity<String> syncAtlasSplitInfo(URI uri, @RequestBody AtlasContractDefDTO atlasContractDefDTO);

    @PutMapping(value = "/pricing")
    ResponseEntity<String> syncAtlasPricingInfo(URI uri, @RequestBody AtlasContractDefDTO atlasContractDefDTO);

    @PostMapping
    ResponseEntity<String> syncAtlasDeliveryRequest(URI uri, @RequestBody AtlasDeliveryDefDTO atlasDeliveryDefDTO);

    @PutMapping
    ResponseEntity<String> syncAtlasDeliveryRequestCancel(URI uri, @RequestBody AtlasDeliveryDefDTO atlasDeliveryDefDTO);

    @PostMapping
    ResponseEntity<String> getDeliveryOpenQuantity(URI uri, @RequestBody AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO);

    @GetMapping
    ResponseEntity<String> getContractOpenQuantity(URI uri);

}
