package com.navigator.cuckoo.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.cuckoo.dao.AtlasMappingContractDao;
import com.navigator.cuckoo.dao.AtlasSyncCallbackDao;
import com.navigator.cuckoo.dao.AtlasSyncRecordDao;
import com.navigator.cuckoo.dao.AtlasSyncRequestDao;
import com.navigator.cuckoo.pojo.dto.ack.*;
import com.navigator.cuckoo.pojo.entity.*;
import com.navigator.cuckoo.pojo.enums.*;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import com.navigator.cuckoo.service.IAtlasSyncCallbackService;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import com.navigator.delivery.facade.DeliveryApplyAtlasFacade;
import com.navigator.delivery.pojo.dto.DeliveryAckContractDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.DeliveryApplyStatusEnum;
import com.navigator.delivery.pojo.enums.DeliveryBillStatusEnum;
import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Date;

/**
 * <p>
 * 回调接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasSyncCallbackServiceImpl implements IAtlasSyncCallbackService {
    private final AtlasSyncCallbackDao syncCallbackDao;
    private final AtlasSyncRequestDao syncRequestDao;
    private final AtlasSyncRecordDao syncRecordDao;
    private final AtlasMappingContractDao mappingContractDao;

    // service
    private final IAtlasOperationLogService operationLogService;
    private final AtlasSyncUriService syncUriService;

    // facade
    private final AtlasTradeRemoteService tradeRemoteService;
    private final DeliveryApplyAtlasFacade deliveryApplyAtlasFacade;

    // util
    private final RedisUtil redisUtil;

    @Override
    public void processAsyncCallBack(AtlasFunctionAckDTO atlasFunctionAckDTO) {

        try {
            String originalBusinessObject = atlasFunctionAckDTO.getFunctionalAck().getHeader().get(0).getOriginalBusinessObject();

            // process contract callback data
            if (originalBusinessObject.equals("Contract")) {
                processContractAsyncCallBack(atlasFunctionAckDTO);
            }

            // process delivery apply callback data
            if (originalBusinessObject.equals("Delivery")) {
                processDeliveryAsyncCallBack(atlasFunctionAckDTO);
            }

        } catch (Exception e) {
            log.error("=====[ATLAS#processAsyncCallBack:{}] 异常信息====={}", atlasFunctionAckDTO, e.getMessage());
        }

    }

    @Override
    public void processSyncCallBack(AtlasSyncRecordEntity recordEntity, ResponseEntity<String> responseEntity) {

        if (responseEntity.getStatusCode().is2xxSuccessful()) {
            // update record data
            recordEntity.setSyncStatus(AtlasSyncStatusEnum.SENT_SUCCESS.getValue())
                    .setAtlasResultsInfo(new Gson().toJson(responseEntity))
                    .setUpdatedAt(new Date());

            syncRecordDao.updateById(recordEntity);

            // update request data
            AtlasSyncRequestEntity requestEntity = syncRequestDao.getById(recordEntity.getRequestId());
            if (null != requestEntity) {
                requestEntity.setSyncStatus(AtlasSyncStatusEnum.SENT_SUCCESS.getValue())
                        .setUpdatedAt(new Date());

                syncRequestDao.updateById(requestEntity);
            }
        }
    }

    @Override
    public void processSyncCallBackError(AtlasSyncRecordEntity recordEntity) {
        if (null != recordEntity) {
            recordEntity.setSyncStatus(AtlasSyncStatusEnum.SENT_ERROR.getValue())
//                    .setTryTimes(recordEntity.getTryTimes() + 1)
                    .setUpdatedAt(new Date());

            syncRecordDao.updateById(recordEntity);

            // update request data
            AtlasSyncRequestEntity requestEntity = syncRequestDao.getById(recordEntity.getRequestId());
            if (null != requestEntity) {
                requestEntity.setSyncStatus(AtlasSyncStatusEnum.SENT_ERROR.getValue())
                        .setUpdatedAt(new Date());

                syncRequestDao.updateById(requestEntity);
            }
        }
    }

    @Override
    public AtlasQueryBlockedQuantityDTO queryBlockedQuantity(AtlasQueryBlockedQuantityDTO blockedQuantityDTO) {

        // 记录参数
        String beforeData = new Gson().toJson(blockedQuantityDTO);

        List<AtlasQueryBlockedQuantityDTO.ContractList> contractList = blockedQuantityDTO.getBlockedQuantityDetails().getContractList();

        if (CollectionUtil.isNotEmpty(contractList)) {
            contractList.removeIf(contract -> {
                BigDecimal blockedQuantity = syncUriService.getContractBlockedQuantity(contract.getContractNo());
                if (BigDecimalUtil.isGreaterThanZero(blockedQuantity)) {
                    contract.setBlockedQuantity(blockedQuantity.stripTrailingZeros().toPlainString());
                    return false;
                }
                return true;
            });
        }

        // 记录参数
        String afterData = new Gson().toJson(blockedQuantityDTO);

        //  save operation log
        operationLogService.saveCallBackOperationLog(new AtlasOperationLogEntity()
                .setRequestUrl("contract:/contract/blockedQuantity")
                .setRequestInfo(beforeData)
                .setResponseInfo(afterData)
                .setOperationType(AtlasSyncActionEnum.QUERY_BLOCKED_QUANTITY.getValue())
                .setOperationSource(AtlasOperationSourceEnum.ATLAS_SERVICE_CALL.getDesc()));

        return blockedQuantityDTO;
    }

    @Override
    public void ackContractExeUpdate(String atlasDeliveryAckDTO) {
        log.info("=====[ATLAS#ackContractExeUpdate:{}] 请求方法=====", atlasDeliveryAckDTO);

        String businessDocID = "";

        String errorMessage = "";

        try {

            AtlasDeliveryAckDTO atlasDeliveryAck = new AtlasDeliveryAckDTO();

            // 判断是json还是xml
            if (atlasDeliveryAckDTO.contains("<")) {
                // xml解析
                Map<String, Object> objectMap = XmlUtil.xmlToMap(atlasDeliveryAckDTO);
                // 将map转成DeliveryAckDTO对象
                AtlasDeliveryAckDTO.DeliveryAckDTO deliveryAckDTO = JSONUtil.toBean(JSONUtil.toJsonStr(objectMap), AtlasDeliveryAckDTO.DeliveryAckDTO.class);

                atlasDeliveryAck.setDelivery(deliveryAckDTO);
            } else {
                // 解析atlasDeliveryAckDTO
                atlasDeliveryAck = JSON.parseObject(atlasDeliveryAckDTO, AtlasDeliveryAckDTO.class);
            }

            businessDocID = atlasDeliveryAck.getDelivery().getFunctionalDocID().getBusinessDocID();
            // 去掉子编号的后缀
//            if (businessDocID.contains("-")) {
//                businessDocID = businessDocID.split("-")[0];
//            }

            List<DeliveryAckContractDTO.ContractDTO> contractList = new ArrayList<>();
            // 解析contractList
            atlasDeliveryAck.getDelivery().getDeliveryData().getDeliveryNoteList()
                    .stream().filter(deliveryNote -> deliveryNote.getContractNo() != null)
                    .forEach(deliveryNote -> {
                        DeliveryAckContractDTO.ContractDTO contractDTO = new DeliveryAckContractDTO.ContractDTO();
                        contractDTO.setDeliveryCode(deliveryNote.getDeliveryNo())
                                .setContractCode(deliveryNote.getContractNo())
                                .setPlannedQuantity(deliveryNote.getDeliveryNoteItemList().get(0).getPlannedQuantity())
                                .setWeightUnit(deliveryNote.getDeliveryNoteItemList().get(0).getWeightUnit());
                        contractList.add(contractDTO);
                    });

            DeliveryAckContractDTO deliveryAckContractDTO = new DeliveryAckContractDTO();
            deliveryAckContractDTO.setContractList(contractList);
            deliveryAckContractDTO.setDeliveryCode(businessDocID);
            deliveryAckContractDTO.setCreationStatus(atlasDeliveryAck.getDelivery().getDeliveryData().getCreationStatus());

            deliveryApplyAtlasFacade.saveDeliveryApplyAckContract(deliveryAckContractDTO);

            // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 start
            // 处理executed_num字段更新逻辑
            processContractExecuteUpdate(atlasDeliveryAck, contractList);
            // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 end

        } catch (Exception e) {
            log.error("=====[ATLAS#ackContractExeUpdate:{}] 异常信息====={}", atlasDeliveryAckDTO, e.getMessage());

            errorMessage = e.getMessage();
        }

        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();

        //  save operation log
        operationLogService.saveCallBackOperationLog(operationLogEntity
                .setBizCode(businessDocID)
                .setRequestUrl("DR:/DR/ackContractExeUpdate")
                .setRequestInfo(atlasDeliveryAckDTO)
                .setResponseInfo(errorMessage)
                .setOperationType(AtlasSyncActionEnum.DR_CONTRACT_EXE_UPDATE.getValue())
                .setOperationSource(AtlasOperationSourceEnum.ATLAS_SERVICE_CALL.getDesc()));
    }

    /**
     * 处理contract异步回调数据
     *
     * @param atlasFunctionAckDTO ATLAS回调数据
     */
    private void processContractAsyncCallBack(AtlasFunctionAckDTO atlasFunctionAckDTO) {
        log.info("=====[ATLAS#processCallBack:{}] 开始请求方法=====", atlasFunctionAckDTO);

        // process and save ack data
        AtlasSyncCallbackEntity callbackEntity = saveCallBackInfo(atlasFunctionAckDTO);

        // init operation log
        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();

        if (null != callbackEntity && StringUtils.isNotBlank(callbackEntity.getReturnMessageCode())) {
            String returnMessageCode = callbackEntity.getReturnMessageCode();

            // update record data
            updateSyncRequest(callbackEntity, returnMessageCode, operationLogEntity);

            // process contract callback data
            processContractCallBackData(atlasFunctionAckDTO, returnMessageCode, callbackEntity);
        }

        //  save operation log
        operationLogService.saveCallBackOperationLog(operationLogEntity
                .setRequestUrl("contract:/atlas/callBack")
                .setRequestInfo(new Gson().toJson(atlasFunctionAckDTO))
                .setOperationType(callbackEntity != null ? callbackEntity.getOperationType() : null)
                .setOperationSource(AtlasOperationSourceEnum.ATLAS_SERVICE_CALL.getDesc()));

        log.info("=====[ATLAS#processCallBack] 结束请求方法=====");
    }

    /**
     * 处理DR异步回调数据
     *
     * @param atlasFunctionAckDTO ATLAS回调数据
     */
    private void processDeliveryAsyncCallBack(AtlasFunctionAckDTO atlasFunctionAckDTO) {
        log.info("=====[ATLAS#processDeliveryAsyncCallBack:{}] 结束请求方法=====", atlasFunctionAckDTO);

        // process and save ack data
        AtlasSyncCallbackEntity callbackEntity = saveCallBackInfo(atlasFunctionAckDTO);

        // init operation log
        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();

        if (null != callbackEntity && StringUtils.isNotBlank(callbackEntity.getReturnMessageCode())) {
            String returnMessageCode = callbackEntity.getReturnMessageCode();

            // update record data
            updateSyncRequest(callbackEntity, returnMessageCode, operationLogEntity);

            // process delivery apply
            updateDeliveryApply(callbackEntity, returnMessageCode);

        }

        //  save operation log
        operationLogService.saveCallBackOperationLog(operationLogEntity
                .setRequestUrl("DR:/atlas/callBack")
                .setRequestInfo(new Gson().toJson(atlasFunctionAckDTO))
                .setOperationType(callbackEntity != null ? callbackEntity.getOperationType() : null)
                .setOperationSource(AtlasOperationSourceEnum.ATLAS_SERVICE_CALL.getDesc()));

        log.info("=====[ATLAS#processDeliveryAsyncCallBack] 结束请求方法=====");

    }

    /**
     * 更新delivery apply信息
     *
     * @param callbackEntity    回调数据
     * @param returnMessageCode 回调状态
     */
    private void updateDeliveryApply(AtlasSyncCallbackEntity callbackEntity, String returnMessageCode) {
        // 包含子编号的处理
        String ackBusinessDocId = callbackEntity.getAckBusinessDocId();
        String applyCode = ackBusinessDocId.split("-")[0];

        // ATLAS两个DR请求对应的是Navigator一条DR
        if (ackBusinessDocId.contains("-")) {
            // 定义审核状态的顺序
            Map<String, Integer> returnStatusOrder = new HashMap<>();
            returnStatusOrder.put(AtlasSyncDeliveryStatusEnum.REJECTED.getValue(), 1);
            returnStatusOrder.put(AtlasSyncDeliveryStatusEnum.BLOCKED.getValue(), 2);
            returnStatusOrder.put(AtlasSyncDeliveryStatusEnum.CONFIRMATION.getValue(), 3);

            // 定义当前状态的顺序
            Integer returnStatus = returnStatusOrder.get(returnMessageCode);

            // 查询当前apply状态
            String status = redisUtil.getString("deliveryApply:status:" + applyCode);
            if (StringUtils.isNotBlank(status)) {
                Integer currentStatus = returnStatusOrder.get(status);
                if (currentStatus != null && returnStatus != null && returnStatus > currentStatus) {
                    log.info("method:updateDeliveryApply \n delivery apply:{} status update from:{} to:{}",
                            applyCode, currentStatus, returnStatus);
                    return;
                }
            }
            redisUtil.set("deliveryApply:status:" + applyCode, returnMessageCode);
        }

        Result<DeliveryApplyEntity> result = deliveryApplyAtlasFacade.getDeliveryApplyByCode(applyCode);
        if (result.isSuccess() && result.getData() != null) {
            DeliveryApplyEntity deliveryApplyEntity = result.getData();

            // 更新atlas_delivery_apply的applyStatus和atlas_sub_status
            deliveryApplyEntity
                    .setAtlasApplyStatus(returnMessageCode)
                    .setAtlasSubStatus(callbackEntity.getSubStatus());

            // BUGFIX：Case-1003208-cukoo error 日志中有一些日期格式的异常， 请排查并修正 Author: Mr 2025-05-12 Start
            // 更新apply确认时间
            if (StringUtils.isNotBlank(callbackEntity.getApprovalTime())) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("dd-MM-yyyy hh:mm:ss");
                try {
                    Date date = dateFormat.parse(callbackEntity.getApprovalTime());
                    deliveryApplyEntity.setApprovalAt(date);
                } catch (ParseException e) {
                    log.error("method:updateDeliveryApply \n parse approvalTime error:{}", e.getMessage());
                }
            }
            // BUGFIX：Case-1003208-cukoo error 日志中有一些日期格式的异常， 请排查并修正 Author: Mr 2025-05-12 End

            // 更新approvalComments
            deliveryApplyEntity.setApprovalComments(callbackEntity.getApprovalComments());

            // 更新车辆状态
            deliveryApplyEntity.setQueueStatus(callbackEntity.getQueueStatus());

            // 根据返回状态更新apply状态
            boolean sendMsg = true;
            switch (AtlasSyncDeliveryStatusEnum.getByValue(returnMessageCode)) {
                case CONFIRMATION:
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.WAIT_APPROVE.getValue());
                    break;
                case BLOCKED:
                    // 更新apply状态为锁定
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.BLOCKED.getValue());
                    break;
                case CONFIRMED:
                    // 更新apply状态为已确认
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.APPROVED.getValue());
                    // 更新为已开单
                    deliveryApplyEntity.setBillStatus(DeliveryBillStatusEnum.BILLED.getValue());
                    break;
                case CANCELLATION:
                    // 更新apply状态为作废待审核
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.INVALID_WAIT_APPROVE.getValue());
                    break;
                case CANCELED:
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.INVALID.getValue());
                    break;
                case REJECTED:
                    // 更新apply状态为已拒绝
                    deliveryApplyEntity.setApplyStatus(DeliveryApplyStatusEnum.REJECTED.getValue());
                    break;
                default:
                    sendMsg = false;
                    break;
            }

            deliveryApplyAtlasFacade.updateDeliveryApply(deliveryApplyEntity);
            // 发送消息
            if (sendMsg) {
                log.info("提货回调发送消息===============================状态：{},=============申请单内容{}", returnMessageCode, FastJsonUtils.getBeanToJson(deliveryApplyEntity));
                deliveryApplyAtlasFacade.sendInMailMessage(deliveryApplyEntity.getId());
            }
        }
    }

    /**
     * 处理contract回调数据
     *
     * @param atlasFunctionAckDTO ATLAS回调数据
     * @param returnMessageCode   回调状态
     * @param callbackEntity      回调数据
     */
    private void processContractCallBackData(AtlasFunctionAckDTO atlasFunctionAckDTO, String returnMessageCode, AtlasSyncCallbackEntity callbackEntity) {
        try {
            String originalBusinessObject = atlasFunctionAckDTO.getFunctionalAck().getHeader().get(0).getOriginalBusinessObject();

            // 处理contract回调
            if (originalBusinessObject.equals("Contract")) {
                // save atlas mapping contract - Creation
                String operationType = atlasFunctionAckDTO.getFunctionalAck().getHeader().get(0).getOriginalBusinessOperation();
                if (returnMessageCode.equals("SUCCESS") && (operationType.equals("CREATE") || operationType.equals("SPLIT"))) {
                    AtlasMappingContractEntity mappingContractEntity = new AtlasMappingContractEntity();

                    ContractEntity contractEntity = tradeRemoteService.getBasicContractByCode(callbackEntity.getBizCode());
                    if (null != contractEntity) {
                        mappingContractEntity.setCompanyBusinessEntity(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                                        ? contractEntity.getSupplierName() : contractEntity.getCustomerName())
                                .setAtlasBusinessEntity(atlasFunctionAckDTO.getFunctionalAck().getFunctionalDocID().getBusinessEntity())
                                .setContractBusinessEntity(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                                        ? contractEntity.getCustomerName() : contractEntity.getSupplierName())
                                .setSalesType(contractEntity.getSalesType())
                                .setNavContractCode(contractEntity.getContractCode())
                                .setAtlasContractCode(callbackEntity.getAckBusinessDocId())
                                .setSplitFlag(0)
                                .setContractNum(contractEntity.getContractNum())
                                .setCallBackTime(callbackEntity.getAckTime())
                                .setUuid(callbackEntity.getUuid())
                                .setUpdatedAt(new Date())
                                .setCreatedAt(new Date());
                        mappingContractDao.save(mappingContractEntity);

                        // process parent contract num
                        if (operationType.equals("SPLIT")) {
                            ContractEntity parentContract = tradeRemoteService.getBasicContractById(contractEntity.getParentId());
                            if (null != parentContract) {
                                List<AtlasMappingContractEntity> mappingContractList = mappingContractDao.getByNavContractCode(parentContract.getContractCode());
                                for (AtlasMappingContractEntity mappingContract : mappingContractList) {
                                    mappingContractDao.updateById(mappingContract
                                            .setContractNum(mappingContract.getContractNum().subtract(contractEntity.getContractNum())));
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("method:processContractCallBackData \n save mapping contract error:{}", e.getMessage());
        }
    }

    /**
     * 保存回调数据
     *
     * @param atlasFunctionAckDTO ATLAS回调数据
     * @return AtlasSyncCallbackEntity 回调数据
     */
    private AtlasSyncCallbackEntity saveCallBackInfo(AtlasFunctionAckDTO atlasFunctionAckDTO) {

        if (null != atlasFunctionAckDTO && null != atlasFunctionAckDTO.getFunctionalAck()) {
            AtlasFunctionAckDTO.FunctionalAckDTO functionalAck = atlasFunctionAckDTO.getFunctionalAck();

            String bizCode = functionalAck.getFunctionalDocID().getBusinessDocID();

            String uuid = "";
            String returnMessageCode = "";
            StringBuilder returnMessageText = new StringBuilder();
            String ackBusinessDocId = "";
            String originalBusinessOperation = "";
            String subStatus = "";
            String approvalComments = "";
            String approvalTime = "";
            String queueStatus = "";

            for (AtlasAckHeaderDTO atlasAckHeaderDTO : functionalAck.header) {
                uuid = atlasAckHeaderDTO.getOrigMessageID();
                returnMessageCode = atlasAckHeaderDTO.getStatus();
                returnMessageText = new StringBuilder(atlasAckHeaderDTO.getStatus()).append(":");
                originalBusinessOperation = atlasAckHeaderDTO.getOriginalBusinessOperation();
            }

            for (AtlasAckItemDTO item : functionalAck.items) {
                ackBusinessDocId = item.getAckBusinessDocID();
                try {

                    AtlasAckItemDTO.LineItemAck lineItemAck = item.getLineItemAckList()
                            .getLineItemAck()
                            .get(0);

                    // 处理parameterList: subStatus Approval_comments Approval_time
                    AtlasAckItemDTO.ParameterList parameterList = lineItemAck.getParameterList();
                    if (CollectionUtil.isNotEmpty(parameterList.getParameter())) {
                        for (AtlasParameterDTO parameter : parameterList.getParameter()) {
                            if (parameter.getParameterName().equals("substatus")) {
                                subStatus = parameter.getValue();
                            }
                            if (parameter.getParameterName().equals("Approval_comments")) {
                                approvalComments = parameter.getValue();
                            }
                            if (parameter.getParameterName().equals("Approval_time")) {
                                approvalTime = parameter.getValue();
                            }
                            if (parameter.getParameterName().equals("Queue_status")) {
                                queueStatus = parameter.getValue();
                            }
                        }
                    }

                    // 处理return message
                    List<AtlasReturnMessageDTO> messageDTOList = lineItemAck.getReturnMessageList().getReturnMessage();

                    for (AtlasReturnMessageDTO atlasReturnMessageDTO : messageDTOList) {
                        returnMessageText.append("\n").append(atlasReturnMessageDTO.getReturnMessageText());
                    }

                } catch (Exception e) {
                    log.error("method:saveCallBackInfo \n get returnMsg error:{}", e.getMessage());
                }
            }

            // BUGFIX：Case-1003277-N081里Deliverycancel的请求没有显示回执信息 Author: Mr 2025-06-24 Start
            // 设置DR cancel的uuid
            boolean isCancelStatus = AtlasSyncDeliveryStatusEnum.CANCELED.getValue().equals(returnMessageCode) ||
                    AtlasSyncDeliveryStatusEnum.CANCELLATION.getValue().equals(returnMessageCode);

            if (isCancelStatus) {
                uuid = syncRecordDao
                        .getByBizCodeAndOperationType(bizCode, AtlasSyncActionEnum.DELIVERY_REQUEST_CANCEL.getValue())
                        .stream()
                        .map(AtlasSyncRecordEntity::getUuid)
                        .findFirst()
                        .orElse(null);
            }
            // BUGFIX：Case-1003277-N081里Deliverycancel的请求没有显示回执信息 Author: Mr 2025-06-24 End

            AtlasSyncCallbackEntity callbackEntity = new AtlasSyncCallbackEntity();
            callbackEntity.setUuid(uuid)
                    .setBizCode(bizCode)
                    .setTryTimes(0)
                    .setSyncType(AtlasSyncTypeEnum.ASYNC.getValue())
                    .setBusinessDocId(bizCode)
                    .setAckBusinessDocId(ackBusinessDocId)
                    .setAckData(new Gson().toJson(atlasFunctionAckDTO))
                    .setReturnMsg(returnMessageText.toString())
                    .setReturnMessageCode(returnMessageCode)
                    .setAckTime(new Date())
                    .setOperationType(originalBusinessOperation)
                    .setSubStatus(subStatus)
                    .setApprovalComments(approvalComments)
                    .setApprovalTime(approvalTime)
                    .setQueueStatus(queueStatus);

            syncCallbackDao.save(callbackEntity);
            return callbackEntity;
        }

        return null;
    }

    /**
     * 更新同步请求数据
     *
     * @param callbackEntity     回调数据
     * @param returnMessageCode  回调状态
     * @param operationLogEntity 操作日志
     */
    private void updateSyncRequest(AtlasSyncCallbackEntity callbackEntity, String returnMessageCode, AtlasOperationLogEntity operationLogEntity) {
        AtlasSyncRecordEntity recordEntity = syncRecordDao.getByUuid(callbackEntity.getUuid());
        if (null != recordEntity) {
            syncRecordDao.updateById(recordEntity
                    .setSyncStatus(returnMessageCode.equals("FAILED") ?
                            AtlasSyncStatusEnum.ACK_ERROR.getValue() : AtlasSyncStatusEnum.ACK_SUCCESS.getValue())
                    .setStatus(returnMessageCode)
                    .setAckTime(callbackEntity.getAckTime())
                    .setUpdatedAt(new Date())
                    .setSubStatus(callbackEntity.getSubStatus()));

            // update request data
            AtlasSyncRequestEntity requestEntity = syncRequestDao.getById(recordEntity.getRequestId());
            syncRequestDao.updateById(requestEntity
                    .setSyncStatus(returnMessageCode.equals("FAILED") ?
                            AtlasSyncStatusEnum.ACK_ERROR.getValue() : AtlasSyncStatusEnum.ACK_SUCCESS.getValue())
                    .setUpdatedAt(new Date()));

            //  process operation log
            operationLogEntity.setReferId(recordEntity.getUuid())
                    .setBizCode(recordEntity.getBizCode())
                    .setCreatedBy(recordEntity.getCreatedBy());
        }
    }

    // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 start
    /**
     * 处理合同执行数据更新
     * @param atlasDeliveryAck ATLAS回调数据
     * @param contractList 合同列表
     */
    private void processContractExecuteUpdate(AtlasDeliveryAckDTO atlasDeliveryAck,
                                            List<DeliveryAckContractDTO.ContractDTO> contractList) {
        try {
            String creationStatus = atlasDeliveryAck.getDelivery().getDeliveryData().getCreationStatus();

            // 只有当creationStatus=3时才处理executed_num更新
            if ("3".equals(creationStatus)) {
                log.info("=====[ATLAS#processContractExecuteUpdate] creationStatus=3，开始处理executed_num更新=====");

                for (DeliveryAckContractDTO.ContractDTO contractDTO : contractList) {
                    String contractCode = contractDTO.getContractCode();
                    String plannedQuantityStr = contractDTO.getPlannedQuantity();

                    if (StringUtils.isNotBlank(contractCode) && StringUtils.isNotBlank(plannedQuantityStr)) {
                        try {
                            // 检查是否为现货采购合同
                            if (isSpotProcurementContract(contractCode)) {
                                BigDecimal plannedQuantity = new BigDecimal(plannedQuantityStr);
                                updateExecutedNum(contractCode, plannedQuantity);
                                log.info("=====[ATLAS#processContractExecuteUpdate] 更新合同{}的executed_num，增加量：{}",
                                        contractCode, plannedQuantity);
                            } else {
                                log.info("=====[ATLAS#processContractExecuteUpdate] 合同{}不是现货采购合同，跳过executed_num更新",
                                        contractCode);
                            }
                        } catch (NumberFormatException e) {
                            log.error("=====[ATLAS#processContractExecuteUpdate] plannedQuantity格式错误：{}", plannedQuantityStr, e);
                        }
                    }
                }
            } else {
                log.info("=====[ATLAS#processContractExecuteUpdate] creationStatus={}，不处理executed_num更新", creationStatus);
            }
        } catch (Exception e) {
            log.error("=====[ATLAS#processContractExecuteUpdate] 处理executed_num更新异常", e);
        }
    }

    /**
     * 检查是否为现货采购合同
     * @param contractCode 合同编码
     * @return true-是现货采购合同，false-不是
     */
    private boolean isSpotProcurementContract(String contractCode) {
        try {
            ContractEntity contractEntity = tradeRemoteService.getBasicContractByCode(contractCode);
            if (contractEntity != null) {
                // 检查是否为现货(bu_code='ST')且采购(sales_type=1)
                return "ST".equals(contractEntity.getBuCode()) &&
                       Integer.valueOf(1).equals(contractEntity.getSalesType());
            }
        } catch (Exception e) {
            log.error("=====[ATLAS#isSpotProcurementContract] 查询合同信息异常，contractCode：{}", contractCode, e);
        }
        return false;
    }

    /**
     * 更新合同执行数量
     * @param contractCode 合同编码
     * @param plannedQuantity 计划数量
     */
    private void updateExecutedNum(String contractCode, BigDecimal plannedQuantity) {
        try {
            List<AtlasMappingContractEntity> mappingList = mappingContractDao.getByNavContractCode(contractCode);
            if (!mappingList.isEmpty()) {
                AtlasMappingContractEntity mappingEntity = mappingList.get(0);

                // 累加plannedQuantity到executed_num
                BigDecimal currentExecutedNum = mappingEntity.getExecutedNum() != null ?
                    mappingEntity.getExecutedNum() : BigDecimal.ZERO;
                BigDecimal newExecutedNum = currentExecutedNum.add(plannedQuantity);

                mappingEntity.setExecutedNum(newExecutedNum);
                mappingEntity.setUpdatedAt(new Date());

                mappingContractDao.updateById(mappingEntity);

                log.info("=====[ATLAS#updateExecutedNum] 更新合同{}执行数量: {} -> {}",
                        contractCode, currentExecutedNum, newExecutedNum);
            } else {
                log.warn("=====[ATLAS#updateExecutedNum] 未找到合同{}的ATLAS映射记录", contractCode);
            }
        } catch (Exception e) {
            log.error("=====[ATLAS#updateExecutedNum] 更新executed_num异常，contractCode：{}", contractCode, e);
        }
    }
    // 1003309 采购合同未开单量未到货量显示优化 changed by Jason Shi at 2025-6-27 end

}
