package com.navigator.cuckoo.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.gson.Gson;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.AtlasSyncRequestDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasDeliveryOpenQuantityDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.vo.AtlasSyncRecordVO;
import com.navigator.cuckoo.service.IAtlasMappingService;
import com.navigator.cuckoo.service.IAtlasSyncQueryService;
import com.navigator.cuckoo.service.IAtlasSyncRetryService;
import com.navigator.cuckoo.service.IAtlasSyncService;
import com.navigator.cuckoo.service.convert.AtlasSyncUriService;
import com.navigator.cuckoo.service.convert.AtlasTradeRemoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 对外暴露接口的实现
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */

@Slf4j
@RestController
@RequiredArgsConstructor
public class AtlasContractFacadeImpl implements AtlasContractFacade {
    private final IAtlasSyncService syncService;
    private final IAtlasSyncQueryService queryService;
    private final IAtlasSyncRetryService retryService;
    private final IAtlasMappingService configService;
    private final AtlasSyncUriService syncUriService;
    private final AtlasTradeRemoteService tradeRemoteService;

    @Override
    public Result syncContractRequest(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【trade】==》【cuckoo】\n method:syncContractRequest,params:{}", new Gson().toJson(syncRequestDTO));
        syncService.syncContractRequest(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result syncDeliveryRequest(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【trade】==》【cuckoo】\n method:syncDeliveryRequest,params:{}", new Gson().toJson(syncRequestDTO));
        syncService.syncContractRequest(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result<BigDecimal> getContractOpenQuantity(String businessEntity, String contractCode) {
        return Result.success(syncUriService.getContractOpenQuantity(businessEntity, contractCode));
    }

    @Override
    public Result<AtlasDeliveryOpenQuantityDTO> getDeliveryOpenQuantity(String counterpartyId, AtlasDeliveryOpenQuantityDTO deliveryOpenQuantityDTO) {
        return Result.success(syncUriService.getDeliveryOpenQuantity(counterpartyId, deliveryOpenQuantityDTO));
    }

    @Override
    public Result<AtlasMappingContractEntity> getByNavContractCode(String contractCode) {
        return Result.success(configService.getByNavContractCode(contractCode));
    }

    @Override
    public Result updateAtlasMappingContract(AtlasMappingContractEntity atlasMappingContractEntity) {
        return Result.success(configService.updateAtlasMappingContract(atlasMappingContractEntity));
    }

    @Override
    public Result<BigDecimal> getContractBlockedQuantity(String contractCode) {
        return Result.success(syncUriService.getContractBlockedQuantity(contractCode));
    }

    @Override
    public Result getSyncRecordList(QueryDTO<AtlasQueryDTO> queryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:getSyncRecordList,params:{}", new Gson().toJson(queryDTO));
        IPage<AtlasSyncRecordVO> recordVOIPage = queryService.getSyncRecordList(queryDTO);
        return Result.page(recordVOIPage);
    }

    @Override
    public Result reSyncContractRequest(AtlasRetryDTO atlasRetryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:reSyncContractRequest,params:{}", new Gson().toJson(atlasRetryDTO));
        retryService.reSyncContractRequest(atlasRetryDTO);
        return Result.success();
    }

    @Override
    public Result<List<String>> getBusinessEntityNameList() {
        log.info("【magellan】==》【cuckoo】\n method:getBusinessEntityNameList");
        return Result.success(tradeRemoteService.getBusinessEntityNameList());
    }

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    @Override
    public Result<List<String>> getOperationTypeList() {
        log.info("【magellan】==》【cuckoo】\n method:getOperationTypeList");
        return Result.success(queryService.getOperationTypeList());
    }
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End

    @Override
    public Result getMappingContractList(QueryDTO<AtlasMappingQueryDTO> queryDTO) {
        log.info("【magellan】==》【cuckoo】\n method:getSyncRecordList,params:{}", new Gson().toJson(queryDTO));
        IPage<AtlasMappingContractEntity> recordVOIPage = queryService.getMappingContractList(queryDTO);
        return Result.page(recordVOIPage);
    }

    @Override
    public Result rebuildRequestEntity(AtlasSyncRequestDTO syncRequestDTO) {
        log.info("【PostMan】\n method:reSyncContractRequest,params:{}", new Gson().toJson(syncRequestDTO));
        retryService.rebuildRequestEntity(syncRequestDTO);
        return Result.success();
    }

    @Override
    public Result reSyncByRequestId(Integer requestId) {
        log.info("【PostMan】\n method:reSyncByRequestId,requestId:{}", requestId);
        retryService.reSyncByRequestId(requestId);
        return Result.success();
    }

    @Override
    public Result reSyncByRecordId(Integer recordId) {
        log.info("【PostMan】\n method:reSyncByRecordId,recordId:{}", recordId);
        retryService.reSyncByRecordId(recordId);
        return Result.success();
    }

    @Override
    public Result<String> importAtlasDepartmentMapping(MultipartFile file) {
        return Result.success(configService.importAtlasDepartmentMapping(file));
    }
}
