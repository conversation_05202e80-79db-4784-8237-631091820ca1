package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasMappingInvoiceMapper;
import com.navigator.cuckoo.pojo.entity.AtlasMappingInvoiceEntity;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;

@Dao
public class AtlasMappingInvoiceDao extends BaseDaoImpl<AtlasMappingInvoiceMapper, AtlasMappingInvoiceEntity> {


    public String getTaxCodeMdmCode(String invoiceTypeMdmCode, BigDecimal taxRate, Integer contractCategoryType) {
        List<AtlasMappingInvoiceEntity> invoiceEntityList = list(Wrappers.<AtlasMappingInvoiceEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(invoiceTypeMdmCode), AtlasMappingInvoiceEntity::getInvoiceMdmCode, invoiceTypeMdmCode)
                .eq(taxRate != null, AtlasMappingInvoiceEntity::getTaxRate, taxRate)
                .eq(contractCategoryType != null, AtlasMappingInvoiceEntity::getContractCategoryType, contractCategoryType)
                .eq(AtlasMappingInvoiceEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
        return !invoiceEntityList.isEmpty() ? invoiceEntityList.get(0).getTaxCode() : null;
    }
}
