package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.mapper.AtlasMappingContractMapper;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
@Slf4j
public class AtlasMappingContractDao extends BaseDaoImpl<AtlasMappingContractMapper, AtlasMappingContractEntity> {

    public IPage<AtlasMappingContractEntity> pageMappingContract(QueryDTO<AtlasMappingQueryDTO> queryDTO) {
        LambdaQueryWrapper<AtlasMappingContractEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (null != queryDTO && null != queryDTO.getCondition()) {
            AtlasMappingQueryDTO atlasMappingQueryDTO = queryDTO.getCondition();
            queryWrapper = Wrappers.<AtlasMappingContractEntity>lambdaQuery()
                    .eq(StringUtils.isNotBlank(atlasMappingQueryDTO.getCompanyBusinessEntity()), AtlasMappingContractEntity::getCompanyBusinessEntity, atlasMappingQueryDTO.getCompanyBusinessEntity())
                    .eq(StringUtils.isNotBlank(atlasMappingQueryDTO.getAtlasBusinessEntity()), AtlasMappingContractEntity::getAtlasBusinessEntity, atlasMappingQueryDTO.getAtlasBusinessEntity())
                    .eq(StringUtils.isNotBlank(atlasMappingQueryDTO.getContractBusinessEntity()), AtlasMappingContractEntity::getContractBusinessEntity, atlasMappingQueryDTO.getContractBusinessEntity())
                    .eq(null != atlasMappingQueryDTO.getSalesType(), AtlasMappingContractEntity::getSalesType, atlasMappingQueryDTO.getSalesType())
                    .eq(null != atlasMappingQueryDTO.getSplitFlag(), AtlasMappingContractEntity::getSplitFlag, atlasMappingQueryDTO.getSplitFlag())
                    .like(StringUtils.isNotBlank(atlasMappingQueryDTO.getNavContractCode()), AtlasMappingContractEntity::getNavContractCode, StringUtils.isNotBlank(atlasMappingQueryDTO.getNavContractCode()) ? atlasMappingQueryDTO.getNavContractCode().trim() : null)
                    .gt(StringUtils.isNotBlank(atlasMappingQueryDTO.getCreateStartTime()), AtlasMappingContractEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(atlasMappingQueryDTO.getCreateStartTime()))
                    .lt(StringUtils.isNotBlank(atlasMappingQueryDTO.getCreateEndTime()), AtlasMappingContractEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(atlasMappingQueryDTO.getCreateEndTime()))
                    .orderByDesc(AtlasMappingContractEntity::getCallBackTime);
        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    public List<AtlasMappingContractEntity> getByNavContractCode(String contractCode) {
        return list(Wrappers.<AtlasMappingContractEntity>lambdaQuery()
                .eq(AtlasMappingContractEntity::getNavContractCode, contractCode));
    }

    // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07 start
    public boolean updateByIdWithVersionRetry(AtlasMappingContractEntity entity) {
        int maxRetries = 3;
        long delayMillis = 500;

        for (int i = 0; i < maxRetries; i++) {
            Integer currentVersion = entity.getVersion();
            log.info("Attempt {} to update entity with id={} and version={}", i + 1, entity.getId(), currentVersion);

            // 避免实体中对 version 重复赋值
            entity.setVersion(null);

            int rows = baseMapper.update(entity, Wrappers.<AtlasMappingContractEntity>lambdaUpdate()
                    .eq(AtlasMappingContractEntity::getId, entity.getId())
                    .eq(AtlasMappingContractEntity::getVersion, currentVersion)
                    .set(AtlasMappingContractEntity::getVersion, currentVersion + 1));

            if (rows > 0) {
                log.info("Update successful for entity id={}, new version={}", entity.getId(), currentVersion + 1);
                return true;
            } else {
                log.warn("Update failed for entity id={}, expected version={}", entity.getId(), currentVersion);
            }

            if (i < maxRetries - 1) {
                try {
                    log.info("Retrying after {}ms...", delayMillis);
                    Thread.sleep(delayMillis);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Thread interrupted during retry delay", e);
                    return false;
                }
            }
        }

        log.error("Update failed after {} attempts for entity id={}", maxRetries, entity.getId());
        return false;
    }
    // BUGFIX：case-1003175 1003134的负数问题再次出现 Author: Mr 2025-05-07 end
}
