package com.navigator.cuckoo.config;

import feign.RequestInterceptor;
import feign.auth.BasicAuthRequestInterceptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class AtlasFeignConfiguration {
    @Value("${atlas.authentication.type}")
    private String authType;

    @Value("${atlas.authentication.userName}")
    private String userName;

    @Value("${atlas.authentication.password}")
    private String password;

    @Value("${atlas.authentication.api-key}")
    private String apiKey;

    @Value("${atlas.authentication.api-value}")
    private String apiValue;

    @Bean
    public BasicAuthRequestInterceptor basicAuthRequestInterceptor() {
        return new BasicAuthRequestInterceptor(userName, password);
    }

    // API Key认证拦截器
    @Bean
    public RequestInterceptor apiKeyRequestInterceptor() {
        return template -> {
            // 将 API Key 添加到请求头
            template.header(apiKey, apiValue);

            // 添加 Accept 头
            template.header("Accept", "application/json");
        };
    }

    @Bean
    public RequestInterceptor authenticationRequestInterceptor() {
        if ("basic".equalsIgnoreCase(authType)) {
            log.info("Atlas Using Basic authentication");
            // 根据配置选择 Basic 认证
            return basicAuthRequestInterceptor();
        } else if ("api_key".equalsIgnoreCase(authType)) {
            log.info("Atlas Using API Key authentication");
            // 根据配置选择 API Key 认证
            return apiKeyRequestInterceptor();
        }
        throw new IllegalStateException("Unknown authentication type: " + authType);
    }
}
