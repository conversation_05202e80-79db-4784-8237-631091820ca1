package com.navigator.cuckoo.service.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.SettleType;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.pojo.dto.payload.*;
import com.navigator.cuckoo.pojo.dto.query.AtlasSecondCostQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.entity.AtlasMappingSecondCostEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRequestEntity;
import com.navigator.cuckoo.pojo.enums.AtlasSyncActionEnum;
import com.navigator.cuckoo.service.IAtlasMappingService;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.delivery.pojo.dto.DeliveryApplyCarpoolInfoDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyEntity;
import com.navigator.delivery.pojo.enums.DeliveryTransportWay;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 接口转换的实现类
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AtlasSyncConvertService {

    private final AtlasTradeRemoteService tradeRemoteService;
    private final AtlasSyncMDMService syncMDMService;
    private final IAtlasMappingService mappingService;

    /**
     * PROCESS CONTRACT SYNC DATA
     */
    public AtlasContractDefDTO processContractRequestData(AtlasSyncRequestEntity requestEntity) {
        // contract
        ContractEntity contractEntity = tradeRemoteService.getBasicContractById(requestEntity.getBizId());

        AtlasSyncActionEnum actionEnum = AtlasSyncActionEnum.getActionByTradeType(requestEntity.getTradeType());
        switch (actionEnum) {
            case CREATE:
            case BUY_BACK:
                AtlasContractHeaderDTO createHeaderDTO = getContractHeaderDTO(actionEnum.getValue(), contractEntity);
                AtlasContractDataDTO createDataDTO = getContractDataDTO(actionEnum.getValue(), contractEntity);
                AtlasContractCostsDefDTO contractCostsDTO = getContractCostsDTO(contractEntity, createDataDTO.getCounterpartyID(), actionEnum.getValue());
                AtlasContractPricingDTO pricingDTO = getContractPricingDTO(actionEnum.getValue(), contractEntity, requestEntity, contractCostsDTO.getExcludeFields());

                return new AtlasContractDefDTO().createDefDTO(createHeaderDTO, createDataDTO, pricingDTO, contractCostsDTO.getContractCosts());
            case MODIFY:
            case CLOSED:
            case WARRANT_WITHDRAW:
                ContractEntity modifyContract = preCheckModifyContract(contractEntity);
                AtlasContractHeaderDTO modifyHeaderDTO = getContractHeaderDTO(actionEnum.getValue(), modifyContract);
                AtlasContractDataDTO modifyDataDTO = getContractDataDTO(actionEnum.getValue(), modifyContract);
                AtlasContractCostsDefDTO modifyCostsDTO = getContractCostsDTO(modifyContract, modifyDataDTO.getCounterpartyID(), actionEnum.getValue());
                AtlasContractPricingDTO modifyPricingDTO = getContractPricingDTO(actionEnum.getValue(), modifyContract, requestEntity, modifyCostsDTO.getExcludeFields());

                return new AtlasContractDefDTO().modifyDefDTO(modifyHeaderDTO, modifyDataDTO, modifyPricingDTO, modifyCostsDTO.getContractCosts());
            case PRICE:
            case PRICE_UPDATE:
                AtlasContractHeaderDTO priceHeaderDTO = getContractHeaderDTO(actionEnum.getValue(), contractEntity);
                AtlasContractPricingDTO pricePricingDTO = getContractPricingDTO(actionEnum.getValue(), contractEntity, requestEntity, new ArrayList<>());

                return new AtlasContractDefDTO().priceDefDTO(priceHeaderDTO, pricePricingDTO);
            case SPLIT:
                AtlasContractHeaderDTO splitHeaderDTO = getContractHeaderDTO(actionEnum.getValue(), contractEntity);
                AtlasContractDataDTO splitDataDTO = getContractDataDTO(actionEnum.getValue(), contractEntity);
                AtlasContractCostsDefDTO splitCostsDTO = getContractCostsDTO(contractEntity, splitDataDTO.getCounterpartyID(), actionEnum.getValue());
                AtlasContractPricingDTO splitPricingDTO = getContractPricingDTO(actionEnum.getValue(), contractEntity, requestEntity, splitCostsDTO.getExcludeFields());
                AtlasContractSplitDTO splitDTO = getContractSplitDTO(actionEnum.getValue(), contractEntity);

                return new AtlasContractDefDTO().splitDefDTO(splitHeaderDTO, splitDataDTO, splitPricingDTO, splitDTO, splitCostsDTO.getContractCosts());
            case WASHOUT:
                TTAddEntity washoutEntity = tradeRemoteService.getWashoutDetailByTTId(requestEntity.getTtId());
                contractEntity.setTotalWashoutNum(washoutEntity.getContractNum());

                AtlasContractHeaderDTO washoutHeaderDTO = getContractHeaderDTO(actionEnum.getValue(), contractEntity);
                AtlasContractDataDTO washoutDataDTO = getContractDataDTO(actionEnum.getValue(), contractEntity);
                AtlasContractCostsDTO washoutCostsDTO = getWashoutContractCostsDTO(washoutEntity, contractEntity, washoutDataDTO);
                AtlasContractPricingDTO washoutPricingDTO = getContractPricingDTO(actionEnum.getValue(), contractEntity, requestEntity, new ArrayList<>());
                AtlasContractSplitDTO washoutSplitDTO = getContractSplitDTO(actionEnum.getValue(), contractEntity);

                return new AtlasContractDefDTO().splitDefDTO(washoutHeaderDTO, washoutDataDTO, washoutPricingDTO, washoutSplitDTO, washoutCostsDTO);
            default:
                throw new RuntimeException("not support operation type");
        }
    }

    /**
     * 修改合同前置检查
     *
     * @param contractEntity 合同实体
     * @return
     */
    private ContractEntity preCheckModifyContract(ContractEntity contractEntity) {

        // 转厂的更新 取原合同的信息
        if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1) {
            ContractHistoryEntity contractHistoryEntity = tradeRemoteService.getContractHistoryEntity(contractEntity.getId(), contractEntity.getMainVersion() - 1);
            if (ObjectUtil.isNotEmpty(contractHistoryEntity)) {
                // 原合同信息
                contractEntity = BeanUtil.copyProperties(contractHistoryEntity, ContractEntity.class);
                // 更新转厂字段
                contractEntity.setIsChangeFactory(1);
                // 价格信息
                ContractPriceEntity contractPriceEntity = JSON.parseObject(contractHistoryEntity.getPriceInfo(), ContractPriceEntity.class);
                contractEntity.setContractPriceEntity(contractPriceEntity);
            }
        }
        return contractEntity;
    }

    /**
     * PROCESS DELIVERY SYNC DATA
     */
    public List<AtlasDeliveryDefDTO> processDeliveryRequestData(AtlasSyncRequestEntity requestEntity) {
        List<AtlasDeliveryDefDTO> deliveryDefDTOList = new ArrayList<>();

        DeliveryApplyEntity deliveryApply = tradeRemoteService.getDeliveryApplyById(requestEntity.getBizId());

        // 根据申请id获取分配信息
        List<DeliveryApplyAllocateEntity> allocateEntityList = tradeRemoteService.getDeliveryRequestAllocateInfo(deliveryApply.getId());

        // 处理isFinal
        Integer isFinal = getIsFinal(deliveryApply, allocateEntityList);

        allocateEntityList.forEach(allocateEntity -> {
            // 处理header
            AtlasContractHeaderDTO deliveryHeaderDTO = getDeliveryHeaderDTO(deliveryApply, allocateEntity.getSubApplyCode());
            // 处理data
            AtlasDeliveryDataDTO deliveryDataDTO = getDeliveryDataDTO(deliveryApply, allocateEntity, isFinal);
            deliveryDefDTOList.add(new AtlasDeliveryDefDTO().deliveryDTO(deliveryHeaderDTO, deliveryDataDTO));
        });

        return deliveryDefDTOList;
    }

    /**
     * PROCESS DELIVERY CANCEL SYNC DATA
     */
    public List<AtlasDeliveryDefDTO> processDeliveryRequestCancelData(AtlasSyncRequestEntity requestEntity) {
        List<AtlasDeliveryDefDTO> deliveryDefDTOList = new ArrayList<>();

        DeliveryApplyEntity deliveryApply = tradeRemoteService.getDeliveryApplyById(requestEntity.getBizId());

        // 根据申请id获取分配信息
        List<DeliveryApplyAllocateEntity> allocateEntityList = tradeRemoteService.getDeliveryRequestAllocateInfo(deliveryApply.getId());

        allocateEntityList.forEach(allocateEntity -> {
            AtlasContractHeaderDTO deliveryHeaderDTO = getDeliveryHeaderDTO(deliveryApply, allocateEntity.getSubApplyCode());
            deliveryDefDTOList.add(new AtlasDeliveryDefDTO().deliveryDTO(deliveryHeaderDTO, null));
        });

        return deliveryDefDTOList;
    }

    /**
     * 计算最终拼车人数
     * Current check on ATLAS is to compare is final and validate that the same number if received
     * <p>
     * isFinal = Number of active DR already created for groupID
     * + Number of DR to be created for this counterparty
     * + (total number of customer expected in this groupID - number of customer which already issued DR in this sharedTruck -1)
     *
     * @param deliveryApply      提货申请
     * @param allocateEntityList 预分配信息
     * @return 拼车isFinal
     */
    private Integer getIsFinal(DeliveryApplyEntity deliveryApply, List<DeliveryApplyAllocateEntity> allocateEntityList) {

        // 异常处理
        if (CollectionUtils.isEmpty(allocateEntityList) || deliveryApply.getIsCarpool() == null) {
            log.error("DR编号：{} 预分配信息为空或未设置是否拼车，默认isFinal=1", deliveryApply.getCode());
            return 0;
        }

        int isFinal = 1;
        // 如果预分配结果只有一个，则判断是否是拼车
        if (allocateEntityList.size() == 1 && deliveryApply.getIsCarpool() == 0) {
            return isFinal;
        }

        // 如果预分配结果是多个，则默认为拼车
        if (allocateEntityList.size() > 1 && deliveryApply.getIsCarpool() == 0) {
            deliveryApply
                    .setIsCarpool(1)
                    .setCarpoolCount(allocateEntityList.size());

            // for循环从1开始，设置优先级
            for (int i = 0; i < allocateEntityList.size(); i++) {
                DeliveryApplyAllocateEntity allocateEntity = allocateEntityList.get(i);
                allocateEntity.setPrioritySort((i + 1) * 100);
            }

            return allocateEntityList.size();
        }

        // 获取DR拼车信息
        DeliveryApplyCarpoolInfoDTO carpoolInfoDTO = tradeRemoteService.getCarpoolDeliveryRequestInfo(deliveryApply.getPlateNumber(),
                DateTimeUtil.formatDate(deliveryApply.getPlanDeliveryTime()));

        // 预期拼车DR个数
        int carpoolCount = deliveryApply.getCarpoolCount();
        // 已拼车DR个数
        int carpoolDeliveryCount = carpoolInfoDTO.getCarpoolDeliveryCount() - 1;
        // 预分配个数
        int allocateCount = allocateEntityList.size();
        // 已分配的个数
        int assignedCount = carpoolInfoDTO.getAssignedCount();

        isFinal = assignedCount + allocateCount + (carpoolCount - carpoolDeliveryCount - 1);

        deliveryApply.setIsFinal(isFinal);
        tradeRemoteService.updateDeliveryApply(deliveryApply);

        // 先排序 1.是否是豆二  2.是否是DCE 3.现货 4.仓单号从小到大  然后设置排序字段
        allocateEntityList.sort(Comparator.comparingInt((DeliveryApplyAllocateEntity o) -> {
            Integer soy2 = o.getIsSoybean2();
            Integer isDce = o.getIsExchangeDelivery();

            // 排序依据优先级
            if (soy2 != null && soy2.equals(1)) {
                return -1000;
            } else if (isDce != null && isDce.equals(1)) {
                return -500;
            } else {
                return 0;
            }
        }).thenComparing((o1, o2) -> {
            String warrant1 = o1.getWarrantNumber();
            String warrant2 = o2.getWarrantNumber();

            return Comparator.nullsLast(String::compareTo)
                    .compare(warrant1, warrant2);
        }).thenComparing((o1, o2) -> {
            Integer isDce1 = o1.getIsExchangeDelivery();
            Integer isDce2 = o2.getIsExchangeDelivery();

            // 如果是DCE交割，按dceContractNo进行排序
            if (isDce1 != null && isDce1.equals(1) && isDce2 != null && isDce2.equals(1)) {
                String dceContractNo1 = o1.getDceContractNo();  // 获取第一个对象的DCE合同号
                String dceContractNo2 = o2.getDceContractNo();  // 获取第二个对象的DCE合同号

                // 如果DCE合同号都不为null，则进行排序
                if (dceContractNo1 != null && dceContractNo2 != null) {
                    return dceContractNo1.compareTo(dceContractNo2);  // 按dceContractNo升序排序
                } else if (dceContractNo1 != null) {
                    return -1;  // 如果第一个对象的dceContractNo不为null，则排在前面
                } else if (dceContractNo2 != null) {
                    return 1;  // 如果第二个对象的dceContractNo不为null，则排在前面
                }
            }
            // 如果不是DCE交割，或者DCE合同号为null，保持原样（返回0）
            return 0;
        }));

        // 按照排序字段设置prioritySort
        int priorityBase = deliveryApply.getLoadingPriority() * 100;
        for (int i = 0; i < allocateEntityList.size(); i++) {
            DeliveryApplyAllocateEntity allocateEntity = allocateEntityList.get(i);
            allocateEntity.setPrioritySort(priorityBase + i);
        }
        return isFinal;
    }


    private AtlasDeliveryDataDTO getDeliveryDataDTO(DeliveryApplyEntity deliveryApply, DeliveryApplyAllocateEntity deliveryApplyAllocate, Integer isFinal) {
        // picker MDM
        Integer customerId = deliveryApply.getCustomerId();
        String customerMdmCode = syncMDMService.getCustomerMdmCode(customerId);

        // counterpartyID
        String counterpartyID = customerMdmCode;
        if (deliveryApplyAllocate.getCpid() != null) {
            counterpartyID = syncMDMService.getCustomerMdmCode(deliveryApplyAllocate.getCpid());
        }

        // goods MDM
        int contractCategoryType = ContractCategoryTypeEnum.REGULAR.getValue();
        if (deliveryApplyAllocate.getIsExchangeDelivery() != null && deliveryApplyAllocate.getIsExchangeDelivery() == 1) {
            contractCategoryType = ContractCategoryTypeEnum.DCE.getValue();
        }
        if (deliveryApplyAllocate.getIsSoybean2() != null && deliveryApplyAllocate.getIsSoybean2() == 1) {
            contractCategoryType = ContractCategoryTypeEnum.SOYBEAN2.getValue();
        }
        String goodsMdmCode = syncMDMService.getGoodsMdmCode(deliveryApply.getGoodsId(), contractCategoryType);

        // deliveryWarehouse MDM
        String warehouseMdmCode = syncMDMService.getDeliveryWarehouseMdmCode(deliveryApply.getDeliveryWarehouseId());

        // Factory MDM
        // String factoryMdmCode = syncMDMService.getFactoryMdmCode(contractEntity.getDeliveryFactoryCode());

        // Terminal MDM
        String warehouseTerminalCode = syncMDMService.getWarehouseTerminalCode(deliveryApply.getDeliveryWarehouseId());

        // contractTerms 自提：EXW  配送：CNF
        String contractTerms = deliveryApply.getDeliveryType().equals(String.valueOf(DeliveryModeEnum.TAKE.getValue())) ? "EXW" : "CNF";

        // transportationMode  车提/船提 truck/barge
        String transportationMode = DeliveryTransportWay.getByValue(deliveryApply.getTransportWay()).getAtlasCode();

        AtlasDeliveryDataDTO deliveryDataDTO = new AtlasDeliveryDataDTO();

        return deliveryDataDTO.setCounterpartyID(counterpartyID)
                .setCommodityID(goodsMdmCode)
                .setDeliveryQuantity(deliveryApplyAllocate.getAllocationQty().stripTrailingZeros().toPlainString())
                .setDeliveryFactory(deliveryApply.getDeliveryFactoryCode())
                .setTerminal(warehouseTerminalCode)
                .setPlanDeliverydate(DateUtil.formatDate(deliveryApply.getPlanDeliveryTime()))
                .setWarehouseType("")
                .setDeliveryWarehouse(warehouseMdmCode)
                .setContractTerms(contractTerms)
                .setTruckNumber(StringUtils.isNotBlank(deliveryApply.getPlateNumber()) ? deliveryApply.getPlateNumber() : "*")
                .setTrailerNumber(deliveryApply.getTrailerNumber())
                .setDriver(StringUtils.isNotBlank(deliveryApply.getDriverName()) ? deliveryApply.getDriverName() : "*")
                .setDriverID(StringUtils.isNotBlank(deliveryApply.getDriverIdNumber()) ? deliveryApply.getDriverIdNumber() : "*")
                .setDriverTel(StringUtils.isNotBlank(deliveryApply.getOnboardPhone()) ? deliveryApply.getOnboardPhone() : "*")
                .setIsCarpool(String.valueOf(deliveryApply.getIsCarpool()))
                .setCarpoolInfo(deliveryApply.getCarpoolCustomer())
                .setCarpoolPriority(deliveryApplyAllocate.getPrioritySort() == null ? "0" : String.valueOf(deliveryApplyAllocate.getPrioritySort()))
                .setCreatedBy(deliveryApply.getCreatedBy())
                .setCreatedAt(DateUtil.formatDateTime(deliveryApply.getCreatedAt()))
                .setRemark(deliveryApply.getRemark())
                .setTransportationMode(transportationMode)
                .setIsExchangeDelivery(String.valueOf(deliveryApplyAllocate.getIsExchangeDelivery()))
                .setIsSoybean2(String.valueOf(deliveryApplyAllocate.getIsSoybean2()))
                .setWarrantNumber(deliveryApplyAllocate.getWarrantNumber())
                .setPicker(customerMdmCode)
                .setIsFinal(String.valueOf(isFinal))
                ;
    }

    private AtlasContractHeaderDTO getDeliveryHeaderDTO(DeliveryApplyEntity deliveryApply, String subApplyCode) {

        // referId =  主体id + factoryCode
        CustomerEntity customerEntity = tradeRemoteService.getBasicCustomerById(deliveryApply.getSupplierId());

        // 所属主体
        String businessEntity = syncMDMService.getBusinessEntityByCondition(customerEntity.getCompanyId(), deliveryApply.getDeliveryFactoryCode());

        AtlasContractHeaderDTO headerDTO = new AtlasContractHeaderDTO();
        headerDTO.setBusinessAppID("NVG")
                .setBusinessEntity(businessEntity)
                .setBusinessDocID(StringUtil.isNotBlank(subApplyCode) ? subApplyCode : deliveryApply.getCode())
                .setUuid(IdUtil.simpleUUID());

        return headerDTO;
    }


    // 获取合同头信息
    private AtlasContractHeaderDTO getContractHeaderDTO(String operationType, ContractEntity contractEntity) {
        // 所属主体
        String businessEntity = syncMDMService.getBusinessEntityBySiteCode(contractEntity.getSiteCode());

        AtlasContractHeaderDTO headerDTO = new AtlasContractHeaderDTO();
        headerDTO.setBusinessAppID("NVG")
                .setBusinessEntity(businessEntity)
                .setBusinessDocID(contractEntity.getContractCode())
                .setUuid(IdUtil.simpleUUID());

        // 拆分/回购合同 设置原始合同号
        if (operationType.equals(AtlasSyncActionEnum.SPLIT.getValue()) || operationType.equals(AtlasSyncActionEnum.BUY_BACK.getValue())) {
            ContractEntity parentContract = tradeRemoteService.getBasicContractById(contractEntity.getParentId());
            headerDTO.setOriginalBusinessDocID(parentContract.getContractCode());

            // 拆分转厂的BusinessEntity
            if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1) {
                headerDTO.setBusinessEntity(syncMDMService.getBusinessEntityBySiteCode(parentContract.getSiteCode()));
            }
        }

        // 解约定赔 设置原始合同号
        if (operationType.equals(AtlasSyncActionEnum.WASHOUT.getValue())) {
            headerDTO.setOriginalBusinessDocID(contractEntity.getContractCode());
        }

        return headerDTO;
    }

    // 获取合同数据信息
    private AtlasContractDataDTO getContractDataDTO(String syncAction, ContractEntity contractEntity) {
        // trader
        String traderMdmCode = syncMDMService.getTraderMdmCode(contractEntity.getOwnerId());

        // customer MDM
        Integer customerId = contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())
                ? contractEntity.getCustomerId() : contractEntity.getSupplierId();
        String customerMdmCode = syncMDMService.getCustomerMdmCode(customerId);

        // goods MDM
        Integer contractCategoryType = contractEntity.getContractCategoryType();
        String goodsMdmCode = syncMDMService.getGoodsMdmCode(contractEntity.getGoodsId(), contractCategoryType);

        // package MDM
        String packageMdmCode = syncMDMService.getPackageMdmCode(contractEntity.getGoodsId());

        // destination MDM
        String destinationMdmCode = StringUtils.isNotBlank(contractEntity.getDestination())
                ? syncMDMService.getDestinationMdmCode(Integer.valueOf(contractEntity.getDestination())) : "";

        // ShipWareHouse  AtlasCode
        String shipWarehouseAtlasCode = syncMDMService.getShipWarehouseAtlasCode(contractEntity.getShipWarehouseId());

        // Terminal MDM
        String warehouseTerminalCode = syncMDMService.getWarehouseTerminalCode(contractEntity.getShipWarehouseId());

        // comment
        String comment = getContractComment(syncAction, contractEntity);

        // InvoiceType MDM
        // String invoiceTypeMdmCode = syncMDMService.getInvoiceTypeMdmCode(contractEntity.getInvoiceType());

        // taxCode MDM
        String taxCode = getTaxMdmCode(contractEntity, syncAction, contractEntity.getTaxRate(), contractCategoryType);

        // Factory MDM
        // String factoryMdmCode = syncMDMService.getFactoryMdmCode(contractEntity.getDeliveryFactoryCode());

        // marketZone MDM
        String marketZoneMdmCode = syncMDMService.getMarketZoneMdmCode(contractEntity.getShipWarehouseId());

        // Third party Terminal Only
        String thirdPartyTerminalOnly = syncMDMService.getThirdPartyTerminalOnly(contractEntity.getShipWarehouseId());

        // packingWeight
        String packingWeight = syncMDMService.getPackingWeightMdmCode(contractEntity.getNeedPackageWeight(), contractEntity.getPackageWeight());

        // deliveryType
        String contractTerms = syncMDMService.getDeliveryTypeMdmCode(contractEntity.getDeliveryType());

        // department
        // String department = mappingService.getDepartmentCodeByReferId(null);
        String department = mappingService.getDepartmentCode(contractEntity, syncAction);

        // cashReleaseMethod
        Integer depositReleaseType = contractEntity.getDepositReleaseType();
        String depositReleaseMethod = depositReleaseType != null ? (depositReleaseType == 0 ? "2" : depositReleaseType.toString()) : null;

        // changeFactory Flag
        boolean changeFactory = contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1;

        // isClosed
        String isClosed = getContractCloseStatus(contractEntity, changeFactory, syncAction);

        // contractQuantity
        String contractQuantity = getContractQuantity(contractEntity, changeFactory, syncAction);

        // shipping status
        String shippingStatus = getShippingStatus(contractEntity, syncAction);

        // 采购合同的接口报文terminal字段处理将采用不定库逻辑(terminal="",MarketZone="CX")，范围：创建、修改、拆分
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {
            warehouseTerminalCode = "";
            marketZoneMdmCode = "CX";
        }

        AtlasContractDataDTO createDataDTO = new AtlasContractDataDTO();
        return createDataDTO.setDepartment(department)
                .setTrader(traderMdmCode)
                .setContractDate(DateUtil.formatDate(contractEntity.getSignDate()))
                .setIsPurchase(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? "Y" : "N")
                .setCounterpartyID(customerMdmCode)
                .setCommodityID(goodsMdmCode)
                .setContractQuantity(contractQuantity)
                .setQuantityUnit("MT")
                .setPackingCode(packageMdmCode)
                .setPackingWeight(packingWeight)
                .setContractTerms(contractTerms)
                .setDestination(destinationMdmCode)
                .setWarehouse(shipWarehouseAtlasCode)
                .setPeriodType("D")
                .setPeriodStart(DateUtil.formatDate(contractEntity.getDeliveryStartTime()))
                .setPeriodEnd(DateUtil.formatDate(contractEntity.getDeliveryEndTime()))
                .setTerminal(warehouseTerminalCode)
                .setComment(comment)
                .setTolerance(String.valueOf(contractEntity.getWeightTolerance()))
                .setTaxCode(taxCode)
                .setCashReleaseMethod(depositReleaseMethod)
                .setIsOEM(contractEntity.getOem() == 1 ? "Y" : "N")
                .setMarketZone(marketZoneMdmCode)
                .setThirdpartyTerminalOnly(thirdPartyTerminalOnly)
                .setIsApproved("Y")
                .setIsClosed(isClosed)
                .setIsExchangeContract(BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode()) ? "Y" : "N")
                .setWarrantNumber(contractEntity.getWarrantCode() == null ? "" : contractEntity.getWarrantCode())
                .setShippingStatus(shippingStatus);
    }

    /**
     * 获取税率编码
     *
     * @param contractEntity       合同实体
     * @param syncAction           操作类型
     * @param taxRate              税率
     * @param contractCategoryType 合同类型：现货、DCE、豆二
     * @return 税率编码
     */
    private String getTaxMdmCode(ContractEntity contractEntity, String syncAction, BigDecimal taxRate, Integer contractCategoryType) {
        // 判断DCE或者豆二的合同
        if (contractCategoryType.equals(ContractCategoryTypeEnum.DCE.getValue()) ||
                contractCategoryType.equals(ContractCategoryTypeEnum.SOYBEAN2.getValue())) {

            // 获取付款条件
            PayConditionEntity payConditionEntity = Optional.of(contractEntity)
                    .map(ContractEntity::getPayConditionId)
                    .map(tradeRemoteService::getPayConditionById)
                    .orElse(null);

            // 是否是线下交易所仓单合同&&自行结算
            boolean isOfflineTrade = Optional.of(contractEntity)
                    .filter(e -> WarrantTradeTypeEnum.OFFLINE_TRADE.getValue().equals(e.getWarrantTradeType()))
                    .map(ContractEntity::getSettleType)
                    .filter(settle -> SettleType.AUTO_SELF.getValue().toString().equals(settle))
                    .isPresent();

            // 检查paymentTerm 是否包含CASH
            boolean isCashPaymentTerm = Optional.ofNullable(payConditionEntity)
                    .map(PayConditionEntity::getName)
                    .map(name -> name.endsWith("CASH"))
                    .orElse(false);

            // 注销C的合同
            boolean isWriteOffC = AtlasSyncActionEnum.CREATE.getValue().equals(syncAction) &&
                    contractEntity.getCreditDays() == 0 &&
                    contractEntity.getTradeType() == ContractTradeTypeEnum.WRITE_OFF_C.getValue();

            // 采购合同是否包含DCE
            boolean isPurchaseDCE = contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) &&
                    Optional.ofNullable(payConditionEntity)
                            .map(PayConditionEntity::getName)
                            .map(name -> name.endsWith("DCE"))
                            .orElse(false);

            // 如果满足任何条件，返回现货的taxCode
            if (isOfflineTrade || isCashPaymentTerm || isWriteOffC || isPurchaseDCE) {
                contractCategoryType = ContractCategoryTypeEnum.REGULAR.getValue();
            }
        }
        return syncMDMService.getTaxCodeMdmCode(contractEntity.getInvoiceType(), taxRate, contractCategoryType);
    }

    /**
     * 获取ShippingStatus
     *
     * @param contractEntity 合同实体
     * @param syncAction     操作类型
     * @return ShippingStatus
     */
    private String getShippingStatus(ContractEntity contractEntity, String syncAction) {

        String shippingStatus = "";

        // 解约定赔 设置原始合同号
        if (syncAction.equals(AtlasSyncActionEnum.WASHOUT.getValue())) {
            shippingStatus = "WA";
        }

        // 尾量关闭
        if (BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())
                && ContractCloseTypeEnum.TAIL_CLOSE.getCode().equals(contractEntity.getContractCloseType())) {
            shippingStatus = "TQ";
        }

        // 取消尾量关闭
        if (BigDecimalUtil.isZero(contractEntity.getCloseTailNum())
                && ContractCloseTypeEnum.TAIL_CLOSE.getCode().equals(contractEntity.getContractCloseType())) {
            shippingStatus = "";
        }
        return shippingStatus;
    }

    /**
     * 获取合同关闭状态
     *
     * @param changeFactory 是否转厂
     * @param syncAction    操作类型
     * @return 合同关闭状态
     */
    private String getContractCloseStatus(ContractEntity contractEntity, boolean changeFactory, String syncAction) {
        String isClosed = "N";

        // 转厂isClosed字段至Y
        if (changeFactory && (AtlasSyncActionEnum.MODIFY.getValue().equals(syncAction)
                || AtlasSyncActionEnum.SPLIT.getValue().equals(syncAction))) {
            isClosed = "Y";
        }

        // 合同关闭状态
        if (syncAction.equals(AtlasSyncActionEnum.CLOSED.getValue())) {
            // 尾量关闭不修改isClosed字段
            if (!ContractCloseTypeEnum.TAIL_CLOSE.getCode().equals(contractEntity.getContractCloseType())) {
                isClosed = "Y";
            }
        }

        // 注销撤回isClosed字段至Y
        if (syncAction.equals(AtlasSyncActionEnum.WARRANT_WITHDRAW.getValue())) {
            isClosed = "Y";
        }

        return isClosed;
    }

    /**
     * 获取合同数量
     *
     * @param contractEntity 合同实体
     * @param changeFactory  是否转厂
     * @param syncAction     操作类型
     * @return 合同数量
     */
    private String getContractQuantity(ContractEntity contractEntity, boolean changeFactory, String syncAction) {
        // contractQuantity
        String contractQuantity = contractEntity.getContractNum().stripTrailingZeros().toPlainString();

        // add by zengshl 豆二合同修改的特殊处理【主合同是修改的动作】
        if (contractEntity.getIsSoybean2() != null && contractEntity.getIsSoybean2() == 1 && AtlasSyncActionEnum.MODIFY.getValue().equals(syncAction)
                && BuCodeEnum.WT.getValue().equals(contractEntity.getBuCode())) {
            contractQuantity = contractEntity.getContractNum()
                    .subtract(contractEntity.getWarrantCancelCount())
                    .subtract(contractEntity.getTotalBuyBackNum())
                    .stripTrailingZeros()
                    .toPlainString();
        }

        // 修改转厂更新合同数量为0
        if (changeFactory && AtlasSyncActionEnum.MODIFY.getValue().equals(syncAction)) {
            contractQuantity = "0";
        }

        // 解约定赔数量
        if (AtlasSyncActionEnum.WASHOUT.getValue().equals(syncAction)) {
            contractQuantity = contractEntity.getTotalWashoutNum().stripTrailingZeros().toPlainString();
        }

        // 注销撤回更新合同数量为0
        if (syncAction.equals(AtlasSyncActionEnum.WARRANT_WITHDRAW.getValue())) {
            contractQuantity = "0";
        }

        // 尾量关闭||取消尾量关闭 更新合同数量为尾量关闭数量
        if (ContractCloseTypeEnum.TAIL_CLOSE.getCode().equals(contractEntity.getContractCloseType())) {
            // 获取已提数量
            AtlasMappingContractEntity mappingContract = mappingService.getByNavContractCode(contractEntity.getContractCode());
            if (mappingContract != null && mappingContract.getExecutedNum() != null) {
                contractQuantity = contractEntity.getContractNum()
                        .subtract(mappingContract.getExecutedNum())
                        .stripTrailingZeros()
                        .toPlainString();
            }
        }
        return contractQuantity;
    }

    /**
     * 获取合同备注
     *
     * @param syncAction     操作类型
     * @param contractEntity 合同实体
     * @return 合同备注
     */
    private String getContractComment(String syncAction, ContractEntity contractEntity) {
        String comment = contractEntity.getMemo();
        switch (AtlasSyncActionEnum.getByValue(syncAction)) {
            case SPLIT:
                // 拆分转厂
                if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1) {
                    comment = "Contract transferred to " + syncMDMService.getBusinessEntityBySiteCode(contractEntity.getSiteCode());
                }
                break;
            case WASHOUT:
                comment = "Washout of contract [" + contractEntity.getContractCode() + "]";
                break;
            case BUY_BACK:
                ContractEntity parentContract = tradeRemoteService.getBasicContractById(contractEntity.getParentId());
                if (null != parentContract) {
                    comment = "Buyback of contract [" + parentContract.getContractCode() + "]";
                }
                break;
            case CLOSED:
                // 尾量关闭不修改备注
                if (!ContractCloseTypeEnum.TAIL_CLOSE.getCode().equals(contractEntity.getContractCloseType())) {
                    comment = "Cancellation of contract [" + contractEntity.getContractCode() + "]";
                }
                break;
            default:
                break;
        }
        return comment;
    }

    // 获取合同定价信息
    private AtlasContractPricingDTO getContractPricingDTO(String operationType, ContractEntity contractEntity, AtlasSyncRequestEntity requestEntity, List<String> excludeFields) {

        // paymentType MDM
        String paymentTypeMdmCode = syncMDMService.getPayConditionMdmCode(operationType, contractEntity);

        // market
        String marketName = mappingService.getMappingMarketByFutureLetter(contractEntity.getFutureCode());

        // domainCode YYYY-MM-32
        String prompt = formatPrompt(contractEntity.getDomainCode());

        // 点价截止日期
        String fixationDueDate = "";
        if (contractEntity.getPriceEndType().equals(ContractPriceEndTypeEnum.DATE.getValue()) && StringUtils.isNotBlank(contractEntity.getPriceEndTime())) {
            fixationDueDate = contractEntity.getPriceEndTime().split(" ")[0];
        }

        // 合同价格明细累加
        ContractPriceEntity priceEntity;
        // 转厂的价格明细去历史版本
        if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1 && operationType.equals(AtlasSyncActionEnum.MODIFY.getValue())) {
            priceEntity = contractEntity.getContractPriceEntity();
        } else {
            priceEntity = tradeRemoteService.getContractPriceDetail(contractEntity.getId());
        }

        BigDecimal premiumDiscount = getPremiumDiscount(priceEntity);

        BigDecimal unitPrice = getUnitPrice(priceEntity);

        // 转月处理
        String monthRollSpread = null;
        String originalPrompt = null;
        if (requestEntity.getTradeType().equals(ContractTradeTypeEnum.TRANSFER_PART.getValue())
                || requestEntity.getTradeType().equals(ContractTradeTypeEnum.TRANSFER_ALL.getValue())) {
            // 根据ttId获取转月合同信息
            TTTranferEntity ttTranferEntity = tradeRemoteService.getTransferContractByTtId(requestEntity.getTtId());
            if (null != ttTranferEntity) {
                prompt = formatPrompt(ttTranferEntity.getDomainCode());
                monthRollSpread = ttTranferEntity.getPrice().stripTrailingZeros().toPlainString();
                originalPrompt = formatPrompt(ttTranferEntity.getOriginalDomainCode());
            }
        }

        // contract pricing
        AtlasContractPricingDTO pricingDTO = new AtlasContractPricingDTO();
        // 是否是点价相关操作
        boolean isPriceRelated = operationType.equals(AtlasSyncActionEnum.PRICE.getValue()) || operationType.equals(AtlasSyncActionEnum.PRICE_UPDATE.getValue());
        if (!isPriceRelated) {
            String pricingMethod = "";
            String isDeferred = "";

            switch (ContractTypeEnum.getByValue(contractEntity.getContractType())) {
                case YI_KOU_JIA:
                    // 一口价拆一口价
                    pricingMethod = "N";
                    isDeferred = "N";

                    Integer originalContractType = tradeRemoteService.getOriginalContractType(contractEntity.getId());

                    if (null != originalContractType) {
                        // 一口价（基差全部定价）拆一口价
                        if (ContractTypeEnum.JI_CHA.getValue() == originalContractType) {
                            pricingMethod = "F";
                            isDeferred = "N";
                        }
                        // 一口价（基差暂定价全部定价）拆一口价
                        else if (ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() == originalContractType) {
                            pricingMethod = "F";
                            isDeferred = "Y";
                        }
                    }
                    break;
                case JI_CHA:
                    pricingMethod = "F";
                    isDeferred = "N";
                    break;
                case ZAN_DING_JIA:
                    pricingMethod = "N";
                    isDeferred = "Y";
                    break;
                case JI_CHA_ZAN_DING_JIA:
                    pricingMethod = "F";
                    isDeferred = "Y";
                    break;
                default:
                    break;
            }

            // BUGFIX：Case-1003202-回购场景一口价报文Pricing Method处理 Author: Mr 2025-05-12 Start
            // 如果是回购合同，直接返回
            if (AtlasSyncActionEnum.BUY_BACK.getValue().equals(operationType)) {
                pricingMethod = "N";
                isDeferred = "N";
            }
            // BUGFIX：Case-1003202-回购场景一口价报文Pricing Method处理 Author: Mr 2025-05-12 End

            pricingDTO.setPricingMethod(pricingMethod)
                    .setIsDeferred(isDeferred)
                    .setCurrency("CNY")
                    .setPriceCode("PMT")
                    .setPaymentTerm(paymentTypeMdmCode)
                    .setPriceInclVAT(unitPrice.stripTrailingZeros().toPlainString())
                    .setFixingType(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? "SEO" : "BEO")
                    .setFnoMarket(marketName)
                    .setMonthRollSpread(monthRollSpread)
                    .setOriginalPrompt(originalPrompt)
                    .setPrompt(prompt)
                    .setFixationDueDate(fixationDueDate)
                    .setPremiumDiscount(premiumDiscount.stripTrailingZeros().toPlainString());
        }

        // pricing + Split
        if (operationType.equals(AtlasSyncActionEnum.SPLIT.getValue())
                || operationType.equals(AtlasSyncActionEnum.PRICE.getValue())
                || operationType.equals(AtlasSyncActionEnum.PRICE_UPDATE.getValue())) {
            // 定价单列表
            List<AtlasContractPriceFixingDTO> priceFixingList = new ArrayList<>();
            List<ConfirmPriceDTO> confirmPriceDTOList = tradeRemoteService.getPriceFixingList(contractEntity.getId());
            //合同已定量部位0
            if (CollectionUtils.isNotEmpty(confirmPriceDTOList)) {
                String finalPrompt = prompt;
                priceFixingList = confirmPriceDTOList.stream().map(confirmPriceDTO -> {
                    AtlasContractPriceFixingDTO priceFixingDTO = new AtlasContractPriceFixingDTO();
                    return priceFixingDTO
                            .setFixingID(String.valueOf(confirmPriceDTO.getId()))
                            .setFixingDate(DateUtil.formatDate(confirmPriceDTO.getPriceTime()))
                            .setFixingPrompt(finalPrompt)
                            .setFixingPrice(confirmPriceDTO.getTransactionPrice().stripTrailingZeros().toPlainString())
                            .setFixingQuantity(confirmPriceDTO.getNum().stripTrailingZeros().toPlainString())
                            .setFixingCode(contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue()) ? "SEO" : "BEO");
                }).collect(Collectors.toList());
            }

            String fullyFixed = BigDecimalUtil.isEqual(contractEntity.getTotalPriceNum(), contractEntity.getContractNum()) ? "Y" : "N";

            // 点价不判断是否全部点价完成
            if (operationType.equals(AtlasSyncActionEnum.PRICE.getValue())) {
                fullyFixed = "N";
            }
            pricingDTO.setFullyFixed(fullyFixed)
                    .setPriceFixingList(CollectionUtils.isNotEmpty(priceFixingList) ? priceFixingList : null);
        }

        // 合同价格明细
        List<AtlasContractPriceDetailDTO> priceDetailList = getPriceDetailList(priceEntity, excludeFields);
        pricingDTO.setPriceDetailList(priceDetailList);

        return pricingDTO;
    }

    /**
     * 格式化prompt “YYYY-MM-32”
     *
     * @param doMainCode
     * @return
     */
    private String formatPrompt(String doMainCode) {
        String prompt = "";
        if (doMainCode.length() > 1) {
            prompt = String.format("20%s-%s-32", doMainCode.substring(0, 2), doMainCode.substring(2, 4));
        }
        return prompt;
    }

    // 获取合同价格明细
    private List<AtlasContractPriceDetailDTO> getPriceDetailList(ContractPriceEntity priceEntity, List<String> excludeFields) {

        List<AtlasContractPriceDetailDTO> priceDetailDTOList = new ArrayList<>();

        ContractPriceBaseEntity priceBaseEntity = BeanUtil.toBean(priceEntity, ContractPriceBaseEntity.class);

        // 获取对象的Class对象
        Class<?> clazz = priceBaseEntity.getClass();
        // 遍历对象的所有属性
        for (Field field : clazz.getDeclaredFields()) {
            // 设置属性可访问，以便获取私有属性的值
            field.setAccessible(true);

            try {
                // 判断属性的类型是否为BigDecimal
                if (field.getType().equals(BigDecimal.class) && !excludeFields.contains(field.getName())) {

                    // 获取属性的名称和值
                    BigDecimal price = (BigDecimal) field.get(priceBaseEntity);

                    if (!BigDecimalUtil.isZero(price)) {
                        AtlasContractPriceDetailDTO priceDetailDTO = new AtlasContractPriceDetailDTO();

                        switch (field.getName()) {
                            case "extraPrice":
                                priceDetailDTO.setNameCN("基差价")
                                        .setName("Basis price")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "forwardPrice":
                                priceDetailDTO.setNameCN("期货价格")
                                        .setName("Future price")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "proteinDiffPrice":
                                priceDetailDTO.setNameCN("蛋白价格")
                                        .setName("High protein price")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "compensationPrice":
                                priceDetailDTO.setNameCN("散粕补贴")
                                        .setName("Bulk premium")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "optionPrice":
                                priceDetailDTO.setNameCN("期权费")
                                        .setName("Option premium")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "transportPrice":
                                priceDetailDTO.setNameCN("运费")
                                        .setName("Freight")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("N");
                                break;
                            case "liftingPrice":
                                priceDetailDTO.setNameCN("起吊费")
                                        .setName("Loading fee")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("N");
                                break;
                            case "delayPrice":
                                priceDetailDTO.setNameCN("滞期费")
                                        .setName("Demurrage charge")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("N");
                                break;
                            case "temperaturePrice":
                                priceDetailDTO.setNameCN("高温费")
                                        .setName("High-temperature allowance")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("N");
                                break;
                            case "otherDeliveryPrice":
                                priceDetailDTO.setNameCN("其他物流费")
                                        .setName("Other logistics expenses")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("N");
                                break;
                            case "buyBackPrice":
                                priceDetailDTO.setNameCN("和解款折价")
                                        .setName("Washout fee")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "complaintDiscountPrice":
                                priceDetailDTO.setNameCN("客诉折价")
                                        .setName("Claim discount")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "transferFactoryPrice":
                                priceDetailDTO.setNameCN("转厂补贴")
                                        .setName("Plant transfer allowance")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "otherPrice":
                                priceDetailDTO.setNameCN("其他补贴")
                                        .setName("Other allowance")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "businessPrice":
                                priceDetailDTO.setNameCN("商务补贴")
                                        .setName("Marketing allowance")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "fee":
                                priceDetailDTO.setNameCN("手续费")
                                        .setName("Service charge")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            case "refineDiffPrice":
                                priceDetailDTO.setNameCN("精炼价差")
                                        .setName("Refinery premium")
                                        .setValue(price.stripTrailingZeros().toPlainString())
                                        .setIsFOB("Y");
                                break;
                            default:
                                break;
                        }

                        // 防止json中出现空对象
                        if (StringUtil.isNotBlank(priceDetailDTO.getName())) {
                            priceDetailDTOList.add(priceDetailDTO);
                        }
                    }
                }
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return priceDetailDTOList;
    }

    // 合同价格明细累加
    private BigDecimal getPremiumDiscount(ContractPriceEntity priceEntity) {

        PriceDetailBO priceDetailBO = BeanUtil.toBean(priceEntity, PriceDetailBO.class);
        BigDecimal totalPrice = tradeRemoteService.calculatePriceBo(priceDetailBO);

        // 价格明细
        return totalPrice.subtract(priceEntity.getForwardPrice());
    }

    // 含税单价
    private BigDecimal getUnitPrice(ContractPriceEntity priceEntity) {
        PriceDetailBO priceDetailBO = BeanUtil.toBean(priceEntity, PriceDetailBO.class);
        return tradeRemoteService.calculatePriceBo(priceDetailBO);
    }

    // 获取拆分的合同信息
    private AtlasContractSplitDTO getContractSplitDTO(String operationType, ContractEntity contractEntity) {
        AtlasContractSplitDTO splitDTO = new AtlasContractSplitDTO();

        // 父合同信息
        ContractEntity parentContract = AtlasSyncActionEnum.WASHOUT.getValue().equals(operationType)
                ? contractEntity : tradeRemoteService.getBasicContractById(contractEntity.getParentId());

        switch (ContractTypeEnum.getByValue(contractEntity.getContractType())) {
            case YI_KOU_JIA:
                // 一口价 → 一口价 flat
                if (parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    splitDTO.setSplitType("flat");

                    Integer originalContractType = tradeRemoteService.getOriginalContractType(contractEntity.getId());

                    if (null != originalContractType) {
                        // 一口价（基差拆出）||一口价（基差暂定价拆出） → 一口价 turnedFlat
                        if (originalContractType.equals(ContractTypeEnum.JI_CHA.getValue())
                                || originalContractType.equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                            splitDTO.setSplitType("turnedFlat");
                        }
                    }

                }

                // 基差  → 一口价 || 基差暂定价 → 一口价 pricedBasis
                if (parentContract.getContractType().equals(ContractTypeEnum.JI_CHA.getValue()) ||
                        parentContract.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                    splitDTO.setSplitType("pricedBasis");

                    // originalPriceFixingList
                    List<ConfirmPriceDTO> priceFixingList = tradeRemoteService.getPriceFixingList(contractEntity.getId());

                    List<AtlasContractSplitDTO.OriginalPriceFixingDTO> priceFixingDTOS = new ArrayList<>();

                    for (ConfirmPriceDTO confirmPriceDTO : priceFixingList) {
                        if (confirmPriceDTO.getSourceId() > 0) {
                            priceFixingDTOS.add(new AtlasContractSplitDTO
                                    .OriginalPriceFixingDTO()
                                    .setOriginalFixingID(String.valueOf(confirmPriceDTO.getSourceId())));
                        }
                    }
                    splitDTO.setOriginalPriceFixingList(CollectionUtils.isNotEmpty(priceFixingDTOS) ? priceFixingDTOS : null);
                }
                break;
            case JI_CHA:
                // 基差 → 基差 unpricedBasis
                if (parentContract.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
                    splitDTO.setSplitType("unpricedBasis");
                }

                // 一口价 → 基差 Depricing
                if (parentContract.getContractType().equals(ContractTypeEnum.YI_KOU_JIA.getValue())) {
                    splitDTO.setSplitType("Depricing");
                }
                break;
            case JI_CHA_ZAN_DING_JIA:
                // 基差暂定价 → 基差暂定价 || 基差 → 基差暂定价 unpricedBasis
                if (parentContract.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())
                        || parentContract.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue())) {
                    splitDTO.setSplitType("unpricedBasis");
                }
                break;
            default:
                break;
        }
        return splitDTO;
    }

    // 获取次级费用信息
    private AtlasContractCostsDefDTO getContractCostsDTO(ContractEntity contractEntity, String counterpartyID, String syncAction) {

        AtlasContractCostsDefDTO costsDefDTO = new AtlasContractCostsDefDTO();

        List<String> excludeFields = new ArrayList<>();
        AtlasContractCostsDTO costsDTO = new AtlasContractCostsDTO();
        List<AtlasContractCostsDTO.ContractCostDTO> costList = new ArrayList<>();

        // 仅针对于销售合同-新增 修改 拆分
        if (contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) &&
                Arrays.asList(AtlasSyncActionEnum.CREATE.getValue(), AtlasSyncActionEnum.MODIFY.getValue(), AtlasSyncActionEnum.SPLIT.getValue()).contains(syncAction)) {
            // 直接获取次级费用信息
            List<String> priceDetailFieldList = mappingService.getSecondCostPriceFields();

            ContractPriceEntity contractPriceEntity;
            // 转厂的价格明细去历史版本
            if (contractEntity.getIsChangeFactory() != null && contractEntity.getIsChangeFactory() == 1 && AtlasSyncActionEnum.MODIFY.getValue().equals(syncAction)) {
                contractPriceEntity = contractEntity.getContractPriceEntity();
            } else {
                contractPriceEntity = tradeRemoteService.getContractPriceDetail(contractEntity.getId());
            }

            for (String priceDetailField : priceDetailFieldList) {
                try {
                    Field field = ContractPriceBaseEntity.class.getDeclaredField(priceDetailField);
                    field.setAccessible(true);
                    BigDecimal priceDetailValue = (BigDecimal) field.get(contractPriceEntity);

                    // 只处理大于0的字段
                    if (priceDetailValue != null && priceDetailValue.signum() > 0) {

                        AtlasMappingSecondCostEntity costEntity = mappingService.getSecondCostByCondition(new AtlasSecondCostQueryDTO()
                                .setPriceDetailField(priceDetailField)
                                .setSyncAction(syncAction)
                                .setBuCode(contractEntity.getBuCode())
                                .setDeliveryType(contractEntity.getDeliveryType())
                                .setDeliveryTypeName(contractEntity.getDeliveryTypeValue()));
                        if (costEntity != null) {
                            AtlasContractCostsDTO.ContractCostDTO costDTO = new AtlasContractCostsDTO.ContractCostDTO();
                            costDTO.setCode(costEntity.getCode())
                                    .setType("R")
                                    .setRateInclVAT(priceDetailValue.stripTrailingZeros().toPlainString())
                                    .setCurrency("CNY")
                                    .setPriceCode("PMT")
                                    .setIsPayable(contractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue()) ? "Y" : "N")
                                    .setCounterpartyID("");
                            //.setCounterpartyID(counterpartyID);
                            costList.add(costDTO);

                            excludeFields.add(priceDetailField);
                        }
                    }
                } catch (Exception e) {
                    log.error("获取次级费用信息异常", e);
                }
            }

            if (!costList.isEmpty()) {
                costsDTO.setCostList(costList);
            }
        }

        costsDefDTO.setContractCosts(costsDTO)
                .setExcludeFields(excludeFields);
        return costsDefDTO;
    }

    // 解约定赔费用信息
    private AtlasContractCostsDTO getWashoutContractCostsDTO(TTAddEntity washoutEntity, ContractEntity contractEntity, AtlasContractDataDTO washoutDataDTO) {
        AtlasContractCostsDTO costsDTO = new AtlasContractCostsDTO();
        // 采销类型
        Integer salesType = contractEntity.getSalesType();

        try {
            // 价差 = 合同价格-市场价格
            BigDecimal diffPrice = getDiffPrice(washoutEntity, contractEntity);

            // 价差总额
            BigDecimal totalDiffAmount = diffPrice.multiply(washoutEntity.getContractNum()).abs();

            // 判断是增益还是损失
            boolean isGain = BigDecimalUtil.isGreaterThanZero(diffPrice);

            // 部分还是全部解约定赔
            // BUGFIX：Case-1003226 解约定赔全部、部分逻辑修复 Author: Mr 2025-06-06 Start
            // boolean isAllWashout = BigDecimalUtil.isEqual(washoutEntity.getContractNum(), contractEntity.getOrderNum());
            boolean isAllWashout = BigDecimalUtil.isZero(contractEntity.getContractNum()
                    .subtract(contractEntity.getTotalBuyBackNum())
                    .subtract(contractEntity.getWarrantCancelCount()));
            // BUGFIX：Case-1003226 解约定赔全部、部分逻辑修复 Author: Mr 2025-06-06 End

            // 税率编码
            BigDecimal taxRate = (isGain && !isAllWashout) ? contractEntity.getTaxRate() : BigDecimal.ZERO;
            String taxCode = getTaxMdmCode(contractEntity, AtlasSyncActionEnum.WASHOUT.getValue(), taxRate, contractEntity.getContractCategoryType());
            washoutDataDTO.setTaxCode(taxCode);

            // 费用编码
            String costCode = getCostCode(isGain, salesType, isAllWashout);

            // isPayable N-Rec Y-Pay
            String isPayable = (salesType.equals(ContractSalesTypeEnum.SALES.getValue())
                    ? (isGain ? "N" : "Y")
                    : (isGain ? "Y" : "N"));

            AtlasContractCostsDTO.ContractCostDTO costDTO = new AtlasContractCostsDTO.ContractCostDTO();
            costDTO.setCode(costCode)
                    .setType("A")
                    .setRateInclVAT(totalDiffAmount.stripTrailingZeros().toPlainString())
                    .setCurrency("CNY")
                    .setPriceCode("")
                    .setIsPayable(isPayable)
                    .setCounterpartyID(washoutDataDTO.getCounterpartyID());

            costsDTO.setCostList(Collections.singletonList(costDTO));
        } catch (Exception e) {
            log.error("getWashoutContractCostsDTO 方法中出现异常: {}", e.getMessage());
        }

        return costsDTO;
    }

    // 计算价差
    private BigDecimal getDiffPrice(TTAddEntity washoutEntity, ContractEntity contractEntity) {
        BigDecimal diffPrice = washoutEntity.getUnitPrice().subtract(washoutEntity.getWashoutUnitPrice());
        // 基差/基差暂定价特殊处理
        if ((contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) && BigDecimalUtil.isZero(contractEntity.getTotalPriceNum())) ||
                contractEntity.getContractType().equals(ContractTypeEnum.JI_CHA.getValue())) {
            diffPrice = diffPrice.subtract(washoutEntity.getForwardPrice());
        }
        return diffPrice;
    }

    // 获取费用编码
    private String getCostCode(boolean isGain, Integer salesType, boolean isAllWashout) {

        // 根据销售类型决定 "GAIN" 或 "LOSS"
        String gainOrLoss = (salesType.equals(ContractSalesTypeEnum.SALES.getValue()))
                ? (isGain ? "GAIN" : "LOSS")
                : (isGain ? "LOSS" : "GAIN");

        // 构建费用编码
        return isAllWashout ? "WSH" + gainOrLoss
                : (isGain ? (salesType.equals(ContractSalesTypeEnum.SALES.getValue()) ? "WSGANPS" : "WSLOSPP")
                : "WSH" + gainOrLoss);
    }

}
