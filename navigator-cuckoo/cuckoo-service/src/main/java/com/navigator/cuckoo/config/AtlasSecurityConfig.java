package com.navigator.cuckoo.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Slf4j
@EnableWebSecurity
public class AtlasSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${cuckoo.authentication.type}")
    private String authType;

    @Value("${cuckoo.authentication.userName}")
    private String basicUsername;

    @Value("${cuckoo.authentication.password}")
    private String basicPassword;

    @Value("${cuckoo.authentication.api-key}")
    private String apiKey;

    @Value("${cuckoo.authentication.api-value}")
    private String apiValue;

    @Value("${cuckoo.authentication.api-matchers}")
    private String apiMatchers;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 基础配置，允许访问 "/atlas/**" 的请求进行认证
        http.authorizeRequests()
                .antMatchers(apiMatchers).authenticated()
                .and()
                .csrf().disable();

        // 根据配置的认证类型来选择 Basic 认证或 API Key 认证
        if ("basic".equalsIgnoreCase(authType)) {
            log.info("Cuckoo Using Basic authentication");
            // 启用 HTTP Basic 认证
            http.httpBasic();
        } else if ("api_key".equalsIgnoreCase(authType)) {
            log.info("Cuckoo Using API Key authentication");
            // 启用 API Key 认证
            http.addFilterBefore(new ApiKeyAuthenticationFilter(apiKey, apiValue,apiMatchers), UsernamePasswordAuthenticationFilter.class);
        }
    }

    @Override
    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
        // Basic 认证的用户名和密码验证
        if ("basic".equalsIgnoreCase(authType)) {
            auth.inMemoryAuthentication()
                    .withUser(basicUsername)
                    .password(passwordEncoder().encode(basicPassword))
                    .roles("USER");
        }
    }

    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
