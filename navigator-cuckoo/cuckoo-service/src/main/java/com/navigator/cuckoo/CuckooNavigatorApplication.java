package com.navigator.cuckoo;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = "com.navigator")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade", "com.navigator.cuckoo.remote"})
public class CuckooNavigatorApplication {
    public static void main(String[] args) {
        SpringApplication.run(CuckooNavigatorApplication.class, args);
    }
}
