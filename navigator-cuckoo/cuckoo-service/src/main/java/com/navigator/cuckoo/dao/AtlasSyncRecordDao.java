package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.cuckoo.mapper.AtlasSyncRecordMapper;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.pojo.enums.AtlasReprocessTypeEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncRecordListTypeEnum;
import com.navigator.cuckoo.pojo.enums.AtlasSyncStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@Dao
public class AtlasSyncRecordDao extends BaseDaoImpl<AtlasSyncRecordMapper, AtlasSyncRecordEntity> {

    public AtlasSyncRecordEntity getByUuid(String uuid) {
        return getOne(Wrappers.<AtlasSyncRecordEntity>lambdaQuery()
                .eq(AtlasSyncRecordEntity::getUuid, uuid)
                .eq(AtlasSyncRecordEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public IPage<AtlasSyncRecordEntity> pageSyncRecord(QueryDTO<AtlasQueryDTO> queryDTO) {
        LambdaQueryWrapper<AtlasSyncRecordEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (null != queryDTO && null != queryDTO.getCondition()) {
            AtlasQueryDTO atlasQueryDTO = queryDTO.getCondition();
            queryWrapper = Wrappers.<AtlasSyncRecordEntity>lambdaQuery()
                    .eq(StringUtils.isNotBlank(atlasQueryDTO.getObjectType()), AtlasSyncRecordEntity::getObjectType, atlasQueryDTO.getObjectType())
                    .eq(StringUtils.isNotBlank(atlasQueryDTO.getOperationType()), AtlasSyncRecordEntity::getOperationType, atlasQueryDTO.getOperationType())
                    .gt(StringUtils.isNotBlank(atlasQueryDTO.getSyncStartTime()), AtlasSyncRecordEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(atlasQueryDTO.getSyncStartTime()))
                    .lt(StringUtils.isNotBlank(atlasQueryDTO.getSyncEndTime()), AtlasSyncRecordEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(atlasQueryDTO.getSyncEndTime()))
                    .eq(StringUtils.isNotBlank(atlasQueryDTO.getBusinessEntity()), AtlasSyncRecordEntity::getBusinessEntity, atlasQueryDTO.getBusinessEntity())
                    .like(StringUtils.isNotBlank(atlasQueryDTO.getBusinessDocId()), AtlasSyncRecordEntity::getBusinessDocId, atlasQueryDTO.getBusinessDocId().trim())
                    .eq(StringUtils.isNotBlank(atlasQueryDTO.getSyncStatus()), AtlasSyncRecordEntity::getSyncStatus, atlasQueryDTO.getSyncStatus())
                    .in(null != atlasQueryDTO.getRecordListType() && atlasQueryDTO.getRecordListType() == AtlasSyncRecordListTypeEnum.PENDING.getValue(),
                            // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-05-08
                            AtlasSyncRecordEntity::getSyncStatus, Arrays.asList(AtlasSyncStatusEnum.SENT_ERROR.getValue(), AtlasSyncStatusEnum.ACK_ERROR.getValue(), AtlasSyncStatusEnum.NOT_CALL_ATLAS.getValue()))
                    .notIn(null != atlasQueryDTO.getRecordListType() && atlasQueryDTO.getRecordListType() == AtlasSyncRecordListTypeEnum.PENDING.getValue(),
                            AtlasSyncRecordEntity::getReprocessed, Arrays.asList(AtlasReprocessTypeEnum.REPROCESSED.getName(), AtlasReprocessTypeEnum.CANCELLED.getName()))
                    .orderByDesc(AtlasSyncRecordEntity::getUpdatedAt);

        } else {
            queryDTO = new QueryDTO<>();
        }
        return this.baseMapper.selectPage(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), queryWrapper);
    }

    // BUGFIX：Case-1003277-N081里Deliverycancel的请求没有显示回执信息 Author: Mr 2025-06-24 Start
    public List<AtlasSyncRecordEntity> getByBizCodeAndOperationType(String bizCode, String operationType) {
        return list(Wrappers.<AtlasSyncRecordEntity>lambdaQuery()
                .eq(AtlasSyncRecordEntity::getBusinessDocId, bizCode)
                .eq(AtlasSyncRecordEntity::getOperationType, operationType)
                .eq(AtlasSyncRecordEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(AtlasSyncRecordEntity::getCreatedAt));
    }
    // BUGFIX：Case-1003277-N081里Deliverycancel的请求没有显示回执信息 Author: Mr 2025-06-24 End
}
