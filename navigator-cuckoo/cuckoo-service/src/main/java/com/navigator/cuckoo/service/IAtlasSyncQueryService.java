package com.navigator.cuckoo.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import com.navigator.cuckoo.pojo.entity.AtlasMappingContractEntity;
import com.navigator.cuckoo.pojo.vo.AtlasSyncRecordVO;

import java.util.List;

/**
 * <p>
 * 查询接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/18
 */
public interface IAtlasSyncQueryService {

    IPage<AtlasSyncRecordVO> getSyncRecordList(QueryDTO<AtlasQueryDTO> queryDTO);

    IPage<AtlasMappingContractEntity> getMappingContractList(QueryDTO<AtlasMappingQueryDTO> queryDTO);

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    List<String> getOperationTypeList();
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End
}
