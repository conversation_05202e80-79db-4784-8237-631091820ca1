package com.navigator.cuckoo.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.cuckoo.mapper.AtlasMappingDepartmentMapper;
import com.navigator.cuckoo.pojo.entity.AtlasMappingDepartmentEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class AtlasMappingDepartmentDao extends BaseDaoImpl<AtlasMappingDepartmentMapper, AtlasMappingDepartmentEntity> {

    public List<AtlasMappingDepartmentEntity> getByCondition(AtlasMappingDepartmentEntity mappingDepartment) {
        return list(Wrappers.<AtlasMappingDepartmentEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(mappingDepartment.getBusinessEntity()), AtlasMappingDepartmentEntity::getBusinessEntity, mappingDepartment.getBusinessEntity())
                .eq(StringUtils.isNotBlank(mappingDepartment.getCommodityCode()), AtlasMappingDepartmentEntity::getCommodityCode, mappingDepartment.getCommodityCode())
//                .eq(mappingDepartment.getContractCategoryType() != null, AtlasMappingDepartmentEntity::getContractCategoryType, mappingDepartment.getContractCategoryType())
                .eq(StringUtils.isNotBlank(mappingDepartment.getMarketZone()), AtlasMappingDepartmentEntity::getMarketZone, mappingDepartment.getMarketZone())
                .eq(AtlasMappingDepartmentEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
    }
}
