package com.navigator.cuckoo.service;

import com.navigator.cuckoo.pojo.entity.AtlasOperationLogEntity;
import com.navigator.cuckoo.pojo.entity.AtlasPaymentTermChangesEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;

/**
 * <p>
 * ATLAS操作记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
public interface IAtlasOperationLogService {


    /**
     * 记录record的记录
     *
     * @param syncRecordEntity record表
     * @param operationSource  操作来源
     */
    void saveOperationLogByRecord(AtlasSyncRecordEntity syncRecordEntity, String operationSource);

    void saveCallBackOperationLog(AtlasOperationLogEntity operationLogEntity);

    /**
     * 保存paymentTerm变更记录
     *
     * @param paymentTermChangesEntity  paymentTerm变更记录
     */
    void savePaymentTermChanges(AtlasPaymentTermChangesEntity paymentTermChangesEntity);

}
