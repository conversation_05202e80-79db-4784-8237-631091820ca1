package com.navigator.cuckoo.controller;

import com.navigator.cuckoo.pojo.dto.ack.AtlasFunctionAckDTO;
import com.navigator.cuckoo.pojo.dto.ack.AtlasQueryBlockedQuantityDTO;
import com.navigator.cuckoo.pojo.dto.result.AtlasResponseDTO;
import com.navigator.cuckoo.service.IAtlasSyncCallbackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class CallBackController {
    private final IAtlasSyncCallbackService callBackService;

    /**
     * 回执接口 合同+DR
     *
     * @param atlasFunctionAckDTO 回执信息
     * @return
     */
    @PostMapping("${cuckoo.endpoint.call-back-path}")
    public AtlasResponseDTO contractCallBack(@RequestBody AtlasFunctionAckDTO atlasFunctionAckDTO) {
        callBackService.processAsyncCallBack(atlasFunctionAckDTO);
        return AtlasResponseDTO.success();
    }

    /**
     * DR合同执行情况更新接口
     *
     * @param atlasDeliveryAckDTO DR合同执行情况更新信息
     * @return
     */
    @PostMapping("${cuckoo.endpoint.dr-contract-path}")
    public AtlasResponseDTO ackContractExeUpdate(@RequestBody String atlasDeliveryAckDTO) {
         callBackService.ackContractExeUpdate(atlasDeliveryAckDTO);
        return AtlasResponseDTO.success();
    }

    /**
     * 查询Navigator中block数量
     */
    @PostMapping("${cuckoo.endpoint.blocked-quantity-path}")
    public AtlasQueryBlockedQuantityDTO getBlockedQuantity(@RequestBody AtlasQueryBlockedQuantityDTO atlasFunctionAckDTO) {
        return callBackService.queryBlockedQuantity(atlasFunctionAckDTO);
    }

}
