package com.navigator.cuckoo.service.impl;

import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.cuckoo.dao.AtlasOperationLogDao;
import com.navigator.cuckoo.dao.AtlasPaymentTermChangesDao;
import com.navigator.cuckoo.pojo.entity.AtlasOperationLogEntity;
import com.navigator.cuckoo.pojo.entity.AtlasPaymentTermChangesEntity;
import com.navigator.cuckoo.pojo.entity.AtlasSyncRecordEntity;
import com.navigator.cuckoo.service.IAtlasOperationLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <p>
 * ATLAS操作记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Service
@RequiredArgsConstructor
public class AtlasOperationLogServiceImpl implements IAtlasOperationLogService {
    private final AtlasOperationLogDao operationLogDao;
    private final AtlasPaymentTermChangesDao paymentTermChangesDao;

    @Override
    public void saveOperationLogByRecord(AtlasSyncRecordEntity syncRecordEntity, String operationSource) {
        AtlasOperationLogEntity operationLogEntity = new AtlasOperationLogEntity();
        operationLogEntity.setReferId(String.valueOf(syncRecordEntity.getUuid()))
                .setBizCode(syncRecordEntity.getBizCode())
                .setRequestSystem(SystemEnum.CUCKOO.getName())
                .setOperationType(syncRecordEntity.getOperationType())
                .setTargetSystem(SystemEnum.ATLAS.getName())
                .setRequestUrl(syncRecordEntity.getRequestUri())
                .setRequestInfo(syncRecordEntity.getAtlasRequestInfo())
                .setResponseInfo(syncRecordEntity.getAtlasResultsInfo())
                .setOperationSource(operationSource)
                .setCreatedAt(new Date())
                .setCreatedBy(syncRecordEntity.getUpdatedBy());
        operationLogDao.save(operationLogEntity);
    }

    @Override
    public void saveCallBackOperationLog(AtlasOperationLogEntity operationLogEntity) {
        operationLogEntity
                .setRequestSystem(SystemEnum.ATLAS.getName())
                .setTargetSystem(SystemEnum.CUCKOO.getName())
                .setCreatedAt(new Date());
        operationLogDao.save(operationLogEntity);
    }

    @Override
    public void savePaymentTermChanges(AtlasPaymentTermChangesEntity paymentTermChangesEntity) {
        paymentTermChangesDao.save(paymentTermChangesEntity);
    }
}
