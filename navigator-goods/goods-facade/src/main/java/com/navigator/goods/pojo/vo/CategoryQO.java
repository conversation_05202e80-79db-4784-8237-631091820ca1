package com.navigator.goods.pojo.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 品类品种
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@Accessors(chain = true)
public class CategoryQO {
    @ApiModelProperty(value = "父编码")
    private Integer parentSerialNo;
    @ApiModelProperty(value = "层级")
    private Integer level;
    @ApiModelProperty(value = "是否交割")
    private Integer isDce;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "期货代码")
    private String futureCode;
    @ApiModelProperty(value = "是否查询DTO，默认：1")
    private Integer isDto;
}
