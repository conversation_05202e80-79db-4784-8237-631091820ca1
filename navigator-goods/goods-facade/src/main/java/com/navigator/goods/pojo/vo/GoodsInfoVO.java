package com.navigator.goods.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021-11-25 19:36
 */
@Data
public class GoodsInfoVO {
    /**
     * 商品ID
     */
    private Integer goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品编码
     */
    private String goodsCode;
    /**
     * 规格ID
     */
    private Integer specId;
    /**
     * 包装ID
     */
    private Integer packageId;
    /**
     * 品类ID
     */
    private Integer categoryId;
    /**
     * 油厂ID
     */
    private Integer supplierId;

    private BigDecimal taxRate;

    /**
     * 一级分类
     */
    private Integer category1;

    /**
     * 二级分类
     */
    private Integer category2;

    /**
     * 三级分类
     */
    private Integer category3;

}
