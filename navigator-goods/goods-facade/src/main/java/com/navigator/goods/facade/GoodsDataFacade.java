package com.navigator.goods.facade;

import com.navigator.common.dto.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 商品域数据处理 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Api(tags = "商品域数据处理")
@FeignClient(value = "navigator-goods-service")
public interface GoodsDataFacade {

    /**
     * 导入基础数据
     *
     * @param file
     * @return
     */
    @ApiOperation(value = "导入基础数据")
    @PostMapping(value = "/goodsData/doImport", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result doImport(@RequestPart("file") MultipartFile file,
                    @RequestParam(value = "processHistory", required = false) Boolean processHistory,
                    @RequestParam(value = "rangeList", required = false) List<String> rangeList);


}