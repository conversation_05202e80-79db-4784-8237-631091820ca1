package com.navigator.goods.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 品类品种
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@Accessors(chain = true)
public class CategoryDTO extends CategoryInfoDTO {

    @ApiModelProperty(value = "品类编码")
    private String code;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "主编码")
    private Integer serialNo;

    @ApiModelProperty(value = "是否交割")
    private Integer isDce;

    @ApiModelProperty(value = "交易所")
    private String exchange;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "状态")
    private Integer status;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "关键规格")
    private List<CategoryAttributeDTO> keyAttributeList;

    @ApiModelProperty(value = "销售规格")
    private List<CategoryAttributeDTO> saleAttributeList;

    @ApiModelProperty(value = "包装规格")
    private CategoryAttributeDTO packageAttribute;


}

