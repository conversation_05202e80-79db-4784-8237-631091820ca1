package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_sku_attribute_value")
@ApiModel(value = "skuAttributeValue对象", description = "")
public class SkuAttributeValueEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "SKU ID")
    private Integer skuId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "规格值ID")
    private Integer attributeValueId;

    @ApiModelProperty(value = "规格名称")
    private String attributeName;

    @ApiModelProperty(value = "规格值名称")
    private String attributeValueName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    private String memo;

    @ApiModelProperty(value = "创建人")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "规格类型")
    private Integer attributeType;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<SkuAttributeValueEntity> lqw(SkuAttributeValueQO condition) {
        LambdaQueryWrapper<SkuAttributeValueEntity> lqw = new LambdaQueryWrapper<SkuAttributeValueEntity>().eq(SkuAttributeValueEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SkuAttributeValueEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSkuId()), SkuAttributeValueEntity::getSkuId, condition.getSkuId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), SkuAttributeValueEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), SkuAttributeValueEntity::getCategory2, condition.getCategory2());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory3()), SkuAttributeValueEntity::getCategory3, condition.getCategory3());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeId()), SkuAttributeValueEntity::getAttributeId, condition.getAttributeId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeValueId()), SkuAttributeValueEntity::getAttributeValueId, condition.getAttributeValueId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeName()), SkuAttributeValueEntity::getAttributeName, condition.getAttributeName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeValueName()), SkuAttributeValueEntity::getAttributeValueName, condition.getAttributeValueName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSort()), SkuAttributeValueEntity::getSort, condition.getSort());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeType()), SkuAttributeValueEntity::getAttributeType, condition.getAttributeType());
            lqw.orderByDesc(SkuAttributeValueEntity::getId);
        }
        return lqw;
    }
}
