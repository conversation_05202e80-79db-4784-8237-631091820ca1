package com.navigator.goods.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SkuRefreshDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * SKU货品 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Api(tags = "SKU货品")
@FeignClient(value = "navigator-goods-service")
public interface SkuFacade {
    /**
     * 根据条件：获取SKU货品DTO分页
     *
     * @param queryDTO
     * @return
     */
    @ApiOperation(value = "根据条件：获取SKU货品DTO分页")
    @PostMapping("/sku/querySkuDTOPage")
    Page<SkuDTO> querySkuDTOPage(@RequestBody QueryDTO<SkuQO> queryDTO);

    /**
     * 根据条件：获取SKU货品DTO列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取SKU货品DTO列表")
    @PostMapping("/sku/querySkuDTOList")
    List<SkuDTO> querySkuDTOList(@RequestBody SkuQO condition);

    @GetMapping("/sku/getSkuListByIds")
    List<SkuEntity> getSkuListByIds(@RequestParam(value = "skuIdList") List<Integer> skuIdList);

    /**
     * 根据ID：获取SKU货品
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取SKU货品")
    @GetMapping("/sku/getSkuById")
    SkuEntity getSkuById(@RequestParam(value = "id") Integer id);

    /**
     * 根据ID：获取SKU货品详情
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取SKU货品详情")
    @GetMapping("/sku/getSkuDTOById")
    SkuDTO getSkuDTOById(@RequestParam(value = "id") Integer id);

    /**
     * 根据全称：获取SKU货品
     *
     * @param fullName
     * @return
     */
    @ApiOperation(value = "根据全称：获取SKU货品")
    @GetMapping("/sku/getSkuByFullName")
    Result<SkuEntity> getSkuByFullName(@RequestParam(value = "fullName") String fullName);

    /**
     * 根据编码：获取SKU货品
     *
     * @param skuNo
     * @return
     */
    @ApiOperation(value = "根据编码：获取SKU货品")
    @GetMapping("/sku/getSkuBySkuNo")
    SkuEntity getSkuBySkuNo(@RequestParam(value = "skuNo") String skuNo);

    /**
     * 刷新：SKU货品
     *
     * @param skuRefreshDTO
     * @return
     */
    @ApiOperation(value = "刷新：SKU货品")
    @PostMapping("/sku/refreshSku")
    Result<List<SkuAddDTO>> refreshSku(@RequestBody SkuRefreshDTO skuRefreshDTO);

    /**
     * 保存：SKU货品
     *
     * @param skuAddDTOList
     * @return
     */
    @ApiOperation(value = "保存：SKU货品")
    @PostMapping("/sku/saveSku")
    Result saveSku(@RequestBody List<SkuAddDTO> skuAddDTOList);

    /**
     * 查询不可提货的SKU货品ID
     *
     * @return
     */
    @ApiOperation(value = "查询不可提货的SKU货品ID")
    @GetMapping("/sku/queryCannotDeliverySkuIdList")
    List<Integer> queryCannotDeliverySkuIdList();

    /**
     * 根据关键规格ID获取
     *
     * @param specId
     * @return
     */
    @Deprecated
    @ApiOperation(value = "根据关键规格ID获取")
    @PostMapping("/sku/querySkuBySpecId")
    List<SkuEntity> querySkuBySpecId(@RequestParam("specId") Integer specId);


    @ApiModelProperty(value = "根据条件：获取SKU货品Mdm列表")
    @PostMapping("/sku/querySkuMdmList")
    Result<List<SkuMdmEntity>> querySkuMdmList(@RequestBody SkuMdmQO skuMdmQO);

    /**
     * 处理历史数据
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "处理历史数据")
    @GetMapping("/sku/processHistoryData")
    Result processHistoryData(@RequestParam(value = "id", required = false) Integer id);
}
