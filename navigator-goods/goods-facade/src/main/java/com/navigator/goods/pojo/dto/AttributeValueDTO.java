package com.navigator.goods.pojo.dto;

import com.navigator.goods.pojo.entity.AttributeValueEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 规格值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeValueDTO extends AttributeValueEntity {
    @ApiModelProperty(value = "规格名称")
    private String attributeName;
}
