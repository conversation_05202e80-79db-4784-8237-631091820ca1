package com.navigator.goods.facade;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeQO;
import com.navigator.goods.pojo.vo.GoodsAttributeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 规格属性服务
 *
 * <AUTHOR>
 * @since 2021-11-25 19:26
 */
@Api(tags = "规格")
@FeignClient(value = "navigator-goods-service")
public interface AttributeFacade {

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param attributeId 规格ID
     * @return 规格列表
     */
    @ApiOperation(value = "根据规格ID，获取规格值列表")
    @GetMapping("/getAttributeValueList")
    List<AttributeValueEntity> getAttributeValueList(@RequestParam("attributeId") Integer attributeId);

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param categoryId 品类ID
     * @return 规格列表
     */
    @Deprecated
    @ApiOperation(value = "旧接口，不适用")
    @GetMapping("/getSpecListByCategoryId")
    GoodsAttributeVO getSpecListByCategoryId(@RequestParam("categoryId") Integer categoryId);

    /**
     * 根据规格/包装ID获取信息
     *
     * @param attributeValueId 规格/包装ID
     * @return 规格/包装信息
     */
    @ApiOperation(value = "根据规格/包装ID获取信息")
    @GetMapping("/getAttributeValueById")
    AttributeValueEntity getAttributeValueById(@RequestParam("attributeValueId") Integer attributeValueId);

    /**
     * 根据条件：获取规格DTO分页
     *
     * @param queryDTO
     * @return
     */
    @ApiOperation(value = "根据条件：获取规格DTO分页")
    @PostMapping("/attribute/queryAttributeDTOPage")
    Page<AttributeDTO> queryAttributeDTOPage(@RequestBody QueryDTO<AttributeQO> queryDTO);

    /**
     * 根据条件：获取规格DTO列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取规格DTO列表")
    @PostMapping("/attribute/queryAttributeDTOList")
    List<AttributeDTO> queryAttributeDTOList(@RequestBody AttributeQO condition);

    /**
     * 根据ID：获取规格DTO
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取规格DTO")
    @GetMapping("/attribute/getAttributeDTOById")
    AttributeDTO getAttributeDTOById(@RequestParam(value = "id") Integer id);

    /**
     * 新增：规格
     *
     * @param addDTO
     * @return
     */
    @ApiOperation(value = "新增：规格")
    @PostMapping("/attribute/addAttribute")
    Result addAttribute(@RequestBody AttributeAddDTO addDTO);

    /**
     * 更新：规格
     *
     * @param updateDTO
     * @return
     */
    @ApiOperation(value = "更新：规格")
    @PostMapping("/attribute/updateAttribute")
    Result updateAttribute(@RequestBody AttributeUpdateDTO updateDTO);

    @GetMapping("/attribute/getAttributeValueListByType")
    List<AttributeValueEntity> getAttributeValueListByType(@RequestParam(value = "type") Integer type);
}
