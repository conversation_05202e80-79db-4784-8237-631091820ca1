package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.SpuQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SPU商品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_spu")
@ApiModel(value = "spu对象", description = "SPU商品")
public class SpuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "一级品类", required = true)
    private Integer category1;

    @ApiModelProperty(value = "二级品类", required = true)
    private Integer category2;

    @ApiModelProperty(value = "品种ID(3级品类)", required = true)
    private Integer category3;

    @ApiModelProperty(value = "spu编码（品种code+多个关键销售规格+包装值ID 下划线隔开（排序）:_11_222_12_）", required = true)
    private String spuNo;

    @ApiModelProperty(value = "品种+关键规格（豆粕,43%;）", required = true)
    private String spuName;

    @ApiModelProperty(value = "关键规格信息多个关键规格值信息(Json)", required = true)
    private String keyAttributeValues;

    @ApiModelProperty(value = "SPU商品状态(0禁用;1启用)", required = true)
    private Integer status;

    @ApiModelProperty(value = "备注")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty(value = "创建人")
    @TableField("CREATED_BY")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @TableField("CREATED_AT")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    @TableField("UPDATED_BY")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @TableField("UPDATED_AT")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "NAV SPU ID")
    private String navSpuId;


    /**
     * 查询条件
     *
     * @param condition
     * @param order
     * @return
     */
    public static final LambdaQueryWrapper<SpuEntity> lqw(SpuQO condition, boolean order) {
        LambdaQueryWrapper<SpuEntity> lqw = new LambdaQueryWrapper<SpuEntity>().eq(SpuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SpuEntity::getId, condition.getId());
            lqw.in(!CollectionUtils.isEmpty(condition.getSpuIdList()), SpuEntity::getId, condition.getSpuIdList());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), SpuEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), SpuEntity::getCategory2, condition.getCategory2());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory3()), SpuEntity::getCategory3, condition.getCategory3());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSpuNo()), SpuEntity::getSpuNo, condition.getSpuNo());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSpuName()), SpuEntity::getSpuName, condition.getSpuName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getStatus()), SpuEntity::getStatus, condition.getStatus());
        }
        if (order) {
            lqw.orderByDesc(SpuEntity::getId);
        }
        return lqw;
    }
}
