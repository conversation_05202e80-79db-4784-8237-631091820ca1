package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * SPU的关键规格信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Accessors(chain = true)
public class SpuAttributeValueQO {
    @ApiModelProperty(value = "ID")
    private Integer id;
    @ApiModelProperty(value = "spuId")
    private Integer spuId;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "三级品类")
    private Integer category3;
    @ApiModelProperty(value = "关键规格ID")
    private Integer attributeId;
    @ApiModelProperty(value = "关键规格值ID")
    private Integer attributeValueId;
    @ApiModelProperty(value = "关键规格值名称")
    private String attributeValueName;
    @ApiModelProperty(value = "排序")
    private Integer sort;
}
