package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 规格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeQO {
    @ApiModelProperty(value = "自增ID")
    private Integer id;
    @ApiModelProperty(value = "规格名称")
    private String name;
    @ApiModelProperty(value = "类型：1：包装规格，2：关键规格，3：销售规格")
    private Integer type;
    @ApiModelProperty(value = "显示名称")
    private String displayName;
}
