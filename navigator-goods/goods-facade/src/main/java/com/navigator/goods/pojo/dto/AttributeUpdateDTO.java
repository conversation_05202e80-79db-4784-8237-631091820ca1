package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * <p>
 * 规格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeUpdateDTO {
    @ApiModelProperty(value = "自增ID", required = true)
    private Integer id;
    @ApiModelProperty(value = "规格值名称", required = true)
    private Set<String> valueNameSet;
}
