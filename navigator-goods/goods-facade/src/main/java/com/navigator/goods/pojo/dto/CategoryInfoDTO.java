package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 品类信息
 *
 * <AUTHOR>
 */
@Data
public class CategoryInfoDTO {

    @ApiModelProperty(value = "品类ID")
    private Integer id;

    @ApiModelProperty(value = "品类名称")
    private String name;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类（品种）")
    private Integer category3;

    @ApiModelProperty(value = "一级品类名称")
    private String categoryName1;

    @ApiModelProperty(value = "二级品类名称")
    private String categoryName2;

    @ApiModelProperty(value = "三级品类（品种）名称")
    private String categoryName3;
}
