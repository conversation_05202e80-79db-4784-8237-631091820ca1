package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 规格值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_attribute_value")
@ApiModel(value = "attributeValue对象", description = "规格值")
public class AttributeValueEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "属性值")
    private String name;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注（lkg编码）")
    private String memo;

    @ApiModelProperty(value = "袋皮扣重(包装)")
    private BigDecimal packageWeight;

    @ApiModelProperty(value = "包装代码mdm编码")
    private String mdmPackageCode;

    @ApiModelProperty(value = "是否删除 （0不删除1删除）")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;


    /**
     * 查询条件
     *
     * @param condition
     * @param sort
     * @return
     */
    public static final LambdaQueryWrapper<AttributeValueEntity> lqw(AttributeValueQO condition, boolean sort) {
        LambdaQueryWrapper<AttributeValueEntity> lqw = new LambdaQueryWrapper<AttributeValueEntity>().eq(AttributeValueEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), AttributeValueEntity::getId, condition.getId());
            lqw.in(!CollectionUtils.isEmpty(condition.getFilterAttributeIdList()), AttributeValueEntity::getAttributeId, condition.getFilterAttributeIdList());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeId()), AttributeValueEntity::getAttributeId, condition.getAttributeId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getName()), AttributeValueEntity::getName, condition.getName());
        }
        if (sort) {
            lqw.orderByDesc(AttributeValueEntity::getId);
        }
        return lqw;
    }

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<AttributeValueEntity> lqw() {
        return lqw(null, false);
    }
}
