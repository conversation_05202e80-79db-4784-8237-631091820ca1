package com.navigator.goods.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 商品服务
 *
 * <AUTHOR>
 * @since 2021-11-25 19:43
 */
@Deprecated
@FeignClient(value = "navigator-goods-service")
public interface GoodsFacade {


    /**
     * 根据品类、包装、规格获取商品信息，无则创建
     *
     * @param goodsSpecDTO 商品品类包装规格-传参
     * @return 商品基本信息
     */
    @PostMapping("/acquireGoods")
    GoodsInfoVO acquireGoodsInfo(@RequestBody GoodsSpecDTO goodsSpecDTO);

    @PostMapping("/queryGoodsTaxRate")
    Result queryGoodsTaxRate(@RequestBody GoodsSpecDTO goodsSpecDTO);

    /**
     * 根据商品ID查询商品信息
     *
     * @param goodsId 商品ID
     * @return 商品基本信息
     */
    @GetMapping("/findGoodsById")
    GoodsInfoVO findGoodsById(@RequestParam(value = "goodsId") Integer goodsId);

    /**
     * 根据商品ID查询商品信息
     *
     * @param goodsId 商品ID
     * @return 商品基本信息
     */
    @GetMapping("/findGoodsDetail")
    GoodsDTO findGoodsDetail(@RequestParam(value = "goodsId") Integer goodsId);

    /**
     * 根据商品名称获取商品信息
     *
     * @param goodsName 商品名称
     * @return 商品基本信息
     */
    @GetMapping("/findGoodsInfo")
    GoodsDTO findGoodsInfo(@RequestParam(value = "goodsName") String goodsName);


    /**
     * 根据包装、规格查询商品集合
     *
     * @param packageId 包装
     * @param specId    规格
     * @return 商品结果集
     */
    @GetMapping("/queryGoodsListBySpec")
    List<GoodsInfoVO> queryGoodsListBySpec(@RequestParam(value = "categoryId") Integer categoryId,
                                           @RequestParam(value = "packageId") Integer packageId,
                                           @RequestParam(value = "specId") Integer specId);

    /**
     * 根据状态获取所有商品信息
     *
     * @param status 商品状态
     * @return 所有商品名称集合
     */
    @GetMapping("/getAllGoodsList")
    List<GoodsSearchDTO> getAllGoodsList(@RequestParam(value = "status", required = false) Integer status);

    @GetMapping("/getAllGoodsListByCategoryId")
    List<GoodsSearchDTO> getAllGoodsListByCategoryId(@RequestParam(value = "status", required = false) Integer status,
                                                     @RequestParam(value = "categoryId", required = false) Integer categoryId);

    @GetMapping("/getAllLkgGoodsList")
    List<GoodsSearchDTO> getAllLkgGoodsList(@RequestParam(value = "status", required = false) Integer status);

    /**
     * 根据条件分页查询商品信息
     *
     * @param goodsSearchDTO 商品查询条件
     * @return 查询的商品结果集合
     */
    @PostMapping("/queryGoodsList")
    Result queryGoodsList(@RequestBody QueryDTO<GoodsSearchDTO> goodsSearchDTO);

    /**
     * 禁用/启用商品
     *
     * @param goodsId 商品ID
     * @param status  商品状态
     * @param taxRate
     * @return 禁用/启用结果
     */
    @GetMapping("/invalidGoods")
    Result invalidGoods(@RequestParam(value = "goodsId") Integer goodsId,
                        @RequestParam(value = "status", required = false) Integer status,
                        @RequestParam(value = "futurePrefix", required = false) String futurePrefix,
                        @RequestParam(value = "futureSuffix", required = false) String futureSuffix,
                        @RequestParam(value = "taxRate", required = false) String taxRate);

    @GetMapping("/updateGoodsName")
    Result updateGoodsName();

    @GetMapping("/removeGoodsCache")
    void removeGoodsCache(@RequestParam(value = "goodsId") Integer id);

    @GetMapping("/removeAllGoodsCache")
    void removeAllGoodsCache();

    @PostMapping("/importGoodsInfo")
    Result importGoodsInfo(@RequestParam("file") MultipartFile uploadFile);

    /**
     * 根据商品ID更新商品提货状态
     *
     * @param goodsId        商品ID
     * @param deliveryStatus 提货状态
     * @return 更新结果
     */
    @GetMapping("/updateGoodsDeliveryStatus")
    Result updateGoodsDeliveryStatus(@RequestParam("goodsId") Integer goodsId, @RequestParam("deliveryStatus") Integer deliveryStatus);

    // 查询不可提货的商品id
    @GetMapping("/queryCannotDeliveryGoodsIdList")
    List<Integer> queryCannotDeliveryGoodsIdList();
}
