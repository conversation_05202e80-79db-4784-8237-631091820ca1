package com.navigator.goods.pojo.dto;

import com.navigator.goods.pojo.entity.SpuEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * SPU商品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Accessors(chain = true)
public class SpuDTO extends SpuEntity {

    @ApiModelProperty(value = "一级品类名称")
    private String categoryName1;

    @ApiModelProperty(value = "二级品类名称")
    private String categoryName2;

    @ApiModelProperty(value = "品种名称(3级品类)")
    private String categoryName3;

}
