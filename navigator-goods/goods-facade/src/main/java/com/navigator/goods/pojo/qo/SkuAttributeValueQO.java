package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Data
@Accessors(chain = true)
public class SkuAttributeValueQO {
    @ApiModelProperty(value = "主键ID")
    private Integer id;
    @ApiModelProperty(value = "SKU ID")
    private Integer skuId;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "三级品类")
    private Integer category3;
    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;
    @ApiModelProperty(value = "规格值ID")
    private Integer attributeValueId;
    @ApiModelProperty(value = "规格名称")
    private String attributeName;
    @ApiModelProperty(value = "规格值名称")
    private String attributeValueName;
    @ApiModelProperty(value = "排序")
    private Integer sort;
    @ApiModelProperty(value = "规格类型")
    private Integer attributeType;
}
