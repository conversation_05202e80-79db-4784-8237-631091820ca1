package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 品类品种
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class CategoryUpdateDTO {
    @ApiModelProperty(value = "ID", required = true)
    private Integer id;
    @ApiModelProperty(value = "状态", required = true)
    private Integer status;
}
