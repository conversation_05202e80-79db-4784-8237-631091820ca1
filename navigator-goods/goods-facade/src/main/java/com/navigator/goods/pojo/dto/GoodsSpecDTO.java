package com.navigator.goods.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021-11-25 19:32
 */
@Data
@Accessors(chain = true)
public class GoodsSpecDTO {
    /**
     * 包装ID（属性规格attributeValueID）
     * 50/100kg
     */
    private Integer packageId;
    /**
     * 规格ID（属性规格attributeValueID）
     * 43%、46%
     */
    private Integer specId;
    /**
     * 品类ID
     */
    private Integer categoryId;
    /**
     * 工厂ID
     */
    private Integer supplierId;

    private BigDecimal taxRate;
}
