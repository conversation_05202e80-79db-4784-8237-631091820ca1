package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * SKU-MDM
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@Accessors(chain = true)
public class SkuMdmQO {
    @ApiModelProperty(value = "主键")
    private Integer id;
    @ApiModelProperty(value = "类型")
    private Integer type;
    @ApiModelProperty(value = "SKU ID")
    private Integer skuId;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "SKU ID列表")
    private List<Integer> skuIdList;
    @ApiModelProperty(value = "MDM编码")
    private String mdmId;
}
