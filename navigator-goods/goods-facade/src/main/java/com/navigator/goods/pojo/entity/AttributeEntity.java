package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.AttributeQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 规格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_attribute")
@ApiModel(value = "attribute对象", description = "规格")
public class AttributeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "规格名称")
    private String name;

//    @ApiModelProperty(value = "分类ID")
//    private Integer categoryId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "类型：1：包装规格，2：关键规格，3：销售规格")
    /**
     * {@link com.navigator.goods.pojo.enums.AttributeType}
     */
    private Integer type;

    @ApiModelProperty(value = "是否支持搜索")
    private Integer canSearch;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "显示名称")
    private String displayName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;


    /**
     * 查询条件
     *
     * @param condition
     * @param order
     * @return
     */
    public static final LambdaQueryWrapper<AttributeEntity> lqw(AttributeQO condition, boolean order) {
        LambdaQueryWrapper<AttributeEntity> lqw = new LambdaQueryWrapper<AttributeEntity>().eq(AttributeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), AttributeEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getName()), AttributeEntity::getName, condition.getName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getType()), AttributeEntity::getType, condition.getType());
            lqw.eq(StringUtil.isNotNullBlank(condition.getDisplayName()), AttributeEntity::getDisplayName, condition.getDisplayName());
        }
        if (order) {
            lqw.orderByDesc(AttributeEntity::getUpdatedAt, AttributeEntity::getId);
        }
        return lqw;
    }

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<AttributeEntity> lqw() {
        return lqw(null, false);

    }
}
