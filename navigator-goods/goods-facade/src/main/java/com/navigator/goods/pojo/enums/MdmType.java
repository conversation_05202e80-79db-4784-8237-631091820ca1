package com.navigator.goods.pojo.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * MDM类型
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MdmType {
    /**
     * 现货
     */
    SPOT(1, "现货"),
    /**
     * 仓单
     */
    WARRANT(2, "仓单"),
    /**
     * 豆二
     */
    SOYBEAN2(3, "豆二"),
    ;

    private Integer value;
    private String desc;

    /**
     * 根据字典值获取枚举类
     *
     * @return
     */
    public static MdmType getByValue(Integer value) {
        for (MdmType item : MdmType.values()) {
            if (item.equals(value)) {
                return item;
            }
        }
        return null;
    }

    /**
     * 是否等于某个值
     *
     * @param value
     * @return
     */
    public boolean equals(Integer value) {
        return this.getValue().equals(value);
    }
}
