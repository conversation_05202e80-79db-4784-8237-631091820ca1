package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * SKU货品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@Accessors(chain = true)
public class SkuQO {
    @ApiModelProperty(value = "ID")
    private Integer id;
    @ApiModelProperty(value = "linkinage商品编码")
    private String linkageGoodsCode;
    @ApiModelProperty(value = "其他平台商品编码")
    private String mdmGoodsCode;
    @ApiModelProperty(value = "货品代码")
    private String code;
    @ApiModelProperty(value = "货品名称")
    private String name;
    @ApiModelProperty(value = "商品简称")
    private String shortName;
    @ApiModelProperty(value = "包装ID")
    private Integer packageId;
    @ApiModelProperty(value = "规格ID")
    private Integer specId;
    @ApiModelProperty(value = "商品种类")
    private Integer categoryId;
    @ApiModelProperty(value = "所属油厂")
    private String supplierId;
    @ApiModelProperty(value = "1.按标包结算 2.按磅单结算 3.按磅单扣袋皮")
    private Integer settleType;
    @ApiModelProperty(value = "条形码")
    private String barCode;
    @ApiModelProperty(value = "计量单位（1、千克 2、吨）")
    private Integer measureUnit;
    @ApiModelProperty(value = "状态 0-禁用1-启用")
    private Integer status;
    @ApiModelProperty(value = "linkinage商品名称")
    private String linkageGoodsName;
    @ApiModelProperty(value = "前缀")
    private String futurePrefix;
    @ApiModelProperty(value = "后缀")
    private String futureSuffix;
    @ApiModelProperty(value = "税率")
    private Double taxRate;
    @ApiModelProperty(value = "是否可提货")
    private Integer isDelivery;
    @ApiModelProperty(value = "SPU商品ID")
    private Integer spuId;
    @ApiModelProperty(value = "系统生成的sku货品全称")
    private String fullName;
    @ApiModelProperty(value = "品种code+多个关键+销售规格+包装值ID下划线隔开（排序）:_11_222_12_")
    private String skuNo;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "三级品类")
    private Integer category3;
    @ApiModelProperty(value = "三级品类列表")
    private List<Integer> category3List;
    @ApiModelProperty(value = "是否仓单标品(0否 1是)")
    private Integer isWarrant;
    @ApiModelProperty(value = "是否TT默认规格(新增字段)")
    private Integer isTtAttribute;
    @ApiModelProperty(value = "DTO接口：是否需要MDM，1：需要。")
    private Integer isNeedSkuMdmList;
    @ApiModelProperty(value = "账套编码")
    private String siteCode;
    @ApiModelProperty(value = "MDM ID")
    private String mdmId;
    @ApiModelProperty(value = "货品昵称")
    private String nickName;
    @ApiModelProperty(value = "期货代码")
    private String futureCode;
}
