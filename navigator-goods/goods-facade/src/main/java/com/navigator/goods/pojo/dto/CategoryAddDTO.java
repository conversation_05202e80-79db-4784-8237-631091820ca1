package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;

/**
 * <p>
 * 品类品种
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class CategoryAddDTO {
    @ApiModelProperty(value = "二级品类", required = true)
    private Integer parentId;
    @ApiModelProperty(value = "品类名称", required = true)
    private String name;
    @ApiModelProperty(value = "是否交割", required = true)
    private Integer isDce;
    @ApiModelProperty(value = "期货代码")
    private String futureCode;
    @ApiModelProperty(value = "交易所")
    private String exchange;
    @ApiModelProperty(value = "是否拆分合同", required = true)
    private Integer isSplitContract;
    @ApiModelProperty(value = "状态", required = true)
    private Integer status;
    @ApiModelProperty(value = "关键规格ID", required = true)
    private Set<Integer> keyAttributeIdSet;
    @ApiModelProperty(value = "销售规格ID")
    private Set<Integer> saleAttributeIdSet;
    @ApiModelProperty(value = "包装规格ID", required = true)
    private Integer packageAttributeId;
}
