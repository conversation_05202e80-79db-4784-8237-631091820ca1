package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 规格值
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeValueQO {
    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;
    @ApiModelProperty(value = "规格类型（1包装规格;2关键规格;3销售规格）")
    private Integer attributeType;
    @ApiModelProperty(value = "自增ID")
    private Integer id;
    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;
    @ApiModelProperty(value = "属性值")
    private String name;
    @ApiModelProperty(value = "规格过滤集合")
    private List<Integer> filterAttributeIdList;
}
