package com.navigator.goods.pojo.dto;

import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 规格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeDTO extends AttributeEntity {

    @ApiModelProperty(value = "规格值列表")
    private List<AttributeValueEntity> attributeValueList;

}
