package com.navigator.goods.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021-07-07 12:02
 */
@Getter
@AllArgsConstructor
public enum AttributeType {
    /**
     * 规格属性类型
     */
    PACKAGE(1, "包装规格"),
    KEY(2, "关键规格"),
    SALE(3, "销售规格"),
            ;
    private Integer value;
    private String desc;

    public static AttributeType getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(attributeType -> Objects.equals(value, attributeType.value))
                .findFirst()
                .orElse(SALE);
    }

    /**
     * 是否等于某个值
     * @param value
     * @return
     */
    public boolean equals(Integer value){
        return this.getValue().equals(value);
    }
}
