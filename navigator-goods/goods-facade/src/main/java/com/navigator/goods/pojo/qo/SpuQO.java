package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * SPU商品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@Accessors(chain = true)
public class SpuQO {
    @ApiModelProperty(value = "ID")
    private String id;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "品种ID(3级品类)")
    private Integer category3;
    @ApiModelProperty(value = "spu编码（品种code+多个关键销售规格+包装值ID 下划线隔开（排序）:_11_222_12_）")
    private String spuNo;
    @ApiModelProperty(value = "品种+关键规格（豆粕,43%;）")
    private String spuName;
    @ApiModelProperty(value = "SPU商品状态(0禁用;1启用)")
    private Integer status;
    @ApiModelProperty(value = "SpuIdList)")
    private List<Integer> spuIdList;
}
