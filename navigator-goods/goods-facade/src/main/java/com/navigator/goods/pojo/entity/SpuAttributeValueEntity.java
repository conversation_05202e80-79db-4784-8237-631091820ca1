package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.SpuAttributeValueQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SPU的关键规格信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_spu_attribute_value")
@ApiModel(value = "spuAttributeValue对象", description = "SPU的关键规格信息")
public class SpuAttributeValueEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "spuId")
    private Integer spuId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "关键规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "关键规格名称")
    private String attributeName;

    @ApiModelProperty(value = "关键规格值ID")
    private Integer attributeValueId;

    @ApiModelProperty(value = "关键规格值名称")
    private String attributeValueName;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "删除状态")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<SpuAttributeValueEntity> lqw(SpuAttributeValueQO condition) {
        LambdaQueryWrapper<SpuAttributeValueEntity> lqw = new LambdaQueryWrapper<SpuAttributeValueEntity>().eq(SpuAttributeValueEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SpuAttributeValueEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSpuId()), SpuAttributeValueEntity::getSpuId, condition.getSpuId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), SpuAttributeValueEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), SpuAttributeValueEntity::getCategory2, condition.getCategory2());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory3()), SpuAttributeValueEntity::getCategory3, condition.getCategory3());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeId()), SpuAttributeValueEntity::getAttributeId, condition.getAttributeId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeValueId()), SpuAttributeValueEntity::getAttributeValueId, condition.getAttributeValueId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeValueName()), SpuAttributeValueEntity::getAttributeValueName, condition.getAttributeValueName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSort()), SpuAttributeValueEntity::getSort, condition.getSort());
        }
        lqw.orderByDesc(SpuAttributeValueEntity::getId);
        return lqw;
    }
}
