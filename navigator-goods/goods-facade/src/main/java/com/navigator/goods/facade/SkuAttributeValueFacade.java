package com.navigator.goods.facade;

import com.navigator.goods.pojo.entity.SkuAttributeValueEntity;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Api(tags = "SKU货品规格值")
@FeignClient(value = "navigator-goods-service")
public interface SkuAttributeValueFacade {
    /**
     * 根据条件：获取SKU货品规格列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取SKU货品规格列表")
    @PostMapping("/skuAttributeValue/querySkuAttributeValueList")
    List<SkuAttributeValueEntity> querySkuAttributeValueList(@RequestBody SkuAttributeValueQO condition);

    /**
     * 根据ID：获取SKU货品规格值
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取SKU货品规格值")
    @GetMapping("/skuAttributeValue/getSkuAttributeValueById")
    SkuAttributeValueEntity getSkuAttributeValueById(@RequestParam(value = "id") Integer id);
}