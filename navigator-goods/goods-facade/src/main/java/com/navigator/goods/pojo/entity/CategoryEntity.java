package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.vo.CategoryQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 品类信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbg_category")
@ApiModel(value = "Category对象", description = "品类信息表")
public class CategoryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "分类父ID")
    private Integer parentId;

    @ApiModelProperty(value = "linkinage品类编码")
    private String linkageCategoryCode;

    @ApiModelProperty(value = "linkinage品类名称")
    private String linkageCategoryName;

    @ApiModelProperty(value = "linkinage货品大类名称")
    private String linkageParentCategoryName;

    @ApiModelProperty(value = "品类名称")
    private String name;

    @ApiModelProperty(value = "品类编码")
    private String code;

    @ApiModelProperty(value = "采销用途（如同时允许采购和销售，则拼接显示，以分号（；）隔开）")
    private String salesType;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "码头服务公司")
    private Integer port;

    @ApiModelProperty(value = "发货地点")
    private String deliveryAddress;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

//    @ApiModelProperty(value = "履约保证金比例 (系统默认客户属性配置中的比例修改时可输入其他比例，不同步至客户属性中, 两种方式：比例和固定金额)")
//    private Integer depositRate;

//    @ApiModelProperty(value = "追加履约保证金 一口价默认是5%【下跌】基差150 【下跌】 一口价暂定价5%【上涨也会收】延期定价 默认>0【上涨收】")
//    private BigDecimal addedDepositAmount;
//
//    @ApiModelProperty(value = "迟付款罚金(默认2元/天/吨)")
//    private BigDecimal delayPayFine;

    @ApiModelProperty(value = "状态  0:启用 1:禁用")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @TableField(exist = false)
    private List<Integer> categoryPriceList;

    @TableField(exist = false)
    private String parentCategoryName;

    @ApiModelProperty(value = "主编码")
    private Integer serialNo;

    @ApiModelProperty(value = "是否交割")
    private Integer isDce;

    @ApiModelProperty(value = "是否豆二")
    private Integer isSoybean2;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

//    @ApiModelProperty(value = "NAV品种ID")
//    private String navId;

    @ApiModelProperty(value = "是否拆分合同")
    private Integer isSplitContract;

    /**
     * 查询条件
     *
     * @param condition
     * @param order
     * @return
     */
    public static final LambdaQueryWrapper<CategoryEntity> lqw(CategoryQO condition, boolean order) {
        LambdaQueryWrapper<CategoryEntity> lqw = new LambdaQueryWrapper<CategoryEntity>().eq(CategoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsDce()), CategoryEntity::getIsDce, condition.getIsDce());
            lqw.eq(StringUtil.isNotNullBlank(condition.getLevel()), CategoryEntity::getLevel, condition.getLevel());
            lqw.eq(StringUtil.isNotNullBlank(condition.getStatus()), CategoryEntity::getStatus, condition.getStatus());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFutureCode()), CategoryEntity::getFutureCode, condition.getFutureCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getParentSerialNo()), CategoryEntity::getParentId, condition.getParentSerialNo());
        }
        if (order) {
            lqw.orderByDesc(CategoryEntity::getUpdatedAt);
        }
        return lqw;
    }

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<CategoryEntity> lqw() {
        return lqw(null, false);
    }
}
