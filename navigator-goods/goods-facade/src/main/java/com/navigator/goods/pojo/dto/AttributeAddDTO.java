package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.LinkedHashSet;

/**
 * <p>
 * 规格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Data
@Accessors(chain = true)
public class AttributeAddDTO {
    @ApiModelProperty(value = "类型：1：包装规格，2：关键规格，3：销售规格", required = true)
    private Integer type;
    @ApiModelProperty(value = "规格名称", required = true)
    private String name;
    @ApiModelProperty(value = "规格值名称", required = true)
    private LinkedHashSet<String> valueNameSet;
}
