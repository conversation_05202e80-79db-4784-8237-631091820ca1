package com.navigator.goods.pojo.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-03-29 22:21
 */
@Data
public class GoodsSearchDTO {
    /**
     * Nav商品ID
     */
    private Integer id;
    /**
     * Nav商品名称
     */
    @Excel(name = "名称", orderNum = "7")
    private String name;
    /**
     * Nav商品编码
     */
    private String code;
    /**
     * linkinage商品名称
     */
    @Excel(name = "Lkg名称", orderNum = "8")
    private String linkageGoodsName;
    /**
     * linkinage商品编码
     */
    @Excel(name = "Lkg编码", orderNum = "9")
    private String linkageGoodsCode;

    /**
     * 提货状态
     */
    private Integer isDelivery;
}
