package com.navigator.goods.pojo.qo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * 品类规格关联
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Accessors(chain = true)
@Data
public class CategoryAttributeQO {
    @ApiModelProperty(value = "ID")
    private Integer id;
    @ApiModelProperty(value = "一级品类")
    private Integer category1;
    @ApiModelProperty(value = "二级品类")
    private Integer category2;
    @ApiModelProperty(value = "品种ID(3级品类)")
    private Integer category3;
    @ApiModelProperty(value = "规格类型（1包装规格;2关键规格;3销售规格）")
    private Integer attributeType;
}
