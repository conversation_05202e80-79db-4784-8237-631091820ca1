package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 品类规格关联
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_category_attribute")
@ApiModel(value = "CategoryAttributeEntity对象", description = "品类规格关联")
public class CategoryAttributeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "ID", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "品种ID(3级品类)")
    private Integer category3;

    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "规格名")
    private String attributeName;

    @ApiModelProperty(value = "规格类型（1包装规格;2关键规格;3销售规格）")
    private Integer attributeType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "备注")
    @TableField("MEMO")
    private String memo;

    @ApiModelProperty(value = "删除状态")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * 查询条件
     *
     * @param condition
     * @param sort
     * @return
     */
    public static final LambdaQueryWrapper<CategoryAttributeEntity> lqw(CategoryAttributeQO condition, boolean sort) {
        LambdaQueryWrapper<CategoryAttributeEntity> lqw = new LambdaQueryWrapper<CategoryAttributeEntity>().eq(CategoryAttributeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), CategoryAttributeEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), CategoryAttributeEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), CategoryAttributeEntity::getCategory2, condition.getCategory2());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory3()), CategoryAttributeEntity::getCategory3, condition.getCategory3());
            lqw.eq(StringUtil.isNotNullBlank(condition.getAttributeType()), CategoryAttributeEntity::getAttributeType, condition.getAttributeType());
        }
        if (sort) {
            lqw.orderByAsc(CategoryAttributeEntity::getSort);
        }
        return lqw;
    }

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<CategoryAttributeEntity> lqw() {
        return lqw(null, false);
    }
}
