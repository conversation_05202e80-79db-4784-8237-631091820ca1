package com.navigator.goods.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-07-07 14:51
 */
@Data
@Accessors(chain = true)
public class GoodsAttributeVO {
    private Integer categoryId;

    private String categoryName;
    /**
     * 值解析(包装)
     */
    private List<AttributeValueVO> packageList;
    /**
     * 值解析(规格-蛋白含量)
     */
    private List<AttributeValueVO> specList;

}
