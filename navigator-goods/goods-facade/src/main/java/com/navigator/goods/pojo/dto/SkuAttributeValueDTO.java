package com.navigator.goods.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> NaNa
 * @since : 2025-01-22 15:17
 **/
@Data
public class SkuAttributeValueDTO {
    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "规格名")
    private String attributeName;

    /**
     * {@link com.navigator.goods.pojo.enums.AttributeType}
     */
    @ApiModelProperty(value = "规格类型（1包装规格;2关键规格;3销售规格）")
    private Integer attributeType;

    @ApiModelProperty(value = "规格值ID")
    private Integer attributeValueId;

    @ApiModelProperty(value = "规格值名")
    private String attributeValueName;
}
