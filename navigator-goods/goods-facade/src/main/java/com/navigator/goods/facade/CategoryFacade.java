package com.navigator.goods.facade;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.*;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 商品-品类管理
 *
 * <AUTHOR>
 * @since 2021-11-30 17:08
 */
@Api(tags = "品类品种")
@FeignClient(value = "navigator-goods-service")
public interface CategoryFacade {

    /**
     * 获取所有商品-品类信息
     *
     * @return 品类集合
     */
//    @GetMapping(value = "/getAllCategoryList",produces = MediaType.APPLICATION_JSON_VALUE)
    @GetMapping(value = "/getAllCategoryList")
    @ApiOperation("获取所有商品-品类信息")
    List<CategoryEntity> getAllCategoryList(@RequestParam(value = "level", required = false) Integer level);


    /**
     * 根据条件分页查询品种信息
     *
     * @param categorySearchDTO 品种查询条件
     * @return 查询的品种结果集合
     */
    @PostMapping("/queryCategoryList")
    @ApiOperation(value = "旧接口，不适用")
    Result queryCategoryList(@RequestBody QueryDTO<CategorySearchDTO> categorySearchDTO);

//    /**
//     * 根据ID：获取品类品种
//     *
//     * @param categoryId 品类
//     * @return 品类信息
//     */
//    @GetMapping("/getCategoryById")
//    @ApiOperation("根据ID：获取品类品种")
//    CategoryEntity getCategoryById(@RequestParam(value = "id") Integer categoryId);

    /**
     * 根据ID获取品类基本信息
     *
     * @param categoryCode 品类
     * @return 品类信息
     */
    @GetMapping("/getCategoryByCode")
    @ApiOperation(value = "旧接口，不适用")
    CategoryEntity getCategoryByCode(@RequestParam(value = "code") String categoryCode);

    /**
     * 更新品类信息
     *
     * @param categoryCreateDTO 品类信息
     * @return 更新结果
     */
    @PostMapping("/updateCategory")
    @ApiOperation(value = "旧接口，不适用")
    Result updateCategoryStatus(@RequestBody CategoryCreateDTO categoryCreateDTO);

    /**
     * 禁用/启用品种
     *
     * @param categoryId 品种ID
     * @param status     品种状态
     * @return 禁用/启用结果
     */
    @GetMapping("/invalidCategory")
    @ApiOperation(value = "旧接口，不适用")
    Result invalidCategory(@RequestParam(value = "categoryId") Integer categoryId,
                           @RequestParam(value = "status") Integer status);


    // 新

    /**
     * 根据条件：获取品类品种DTO分页
     *
     * @param queryDTO
     * @return
     */
    @ApiOperation(value = "根据条件：获取品类品种DTO分页")
    @PostMapping("/category/queryCategoryDTOPage")
    Page<CategoryDTO> queryCategoryDTOPage(@RequestBody QueryDTO<CategoryQO> queryDTO);

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取品类品种列表")
    @PostMapping("/category/queryCategoryList")
    List<CategoryEntity> queryCategoryList(@RequestBody CategoryQO condition);

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取品类品种DTO列表")
    @PostMapping("/category/queryCategoryDTOList")
    List<CategoryDTO> queryCategoryDTOList(@RequestBody CategoryQO condition);

    /**
     * 根据条件：获取品类品种ID列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取品类品种ID列表")
    @PostMapping("/category/queryCategoryIdList")
    List<Integer> queryCategoryIdList(@RequestBody CategoryQO condition);

    /**
     * 根据条件：获取品类品种主编码列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取品类品种主编码列表")
    @PostMapping("/category/queryCategorySerialNoList")
    List<Integer> queryCategorySerialNoList(@RequestBody CategoryQO condition);

    /**
     * 根据ID：获取品类品种
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取品类品种DTO")
    @GetMapping("/category/getCategoryDTOById")
    CategoryDTO getCategoryDTOById(@RequestParam(value = "id") Integer id);

    @GetMapping("/category/getBasicCategoryBySerialNo")
    CategoryEntity getBasicCategoryBySerialNo(@RequestParam(value = "serialNo") Integer serialNo);

    /**
     * 根据主编码：获取品类品种
     *
     * @param serialNo
     * @return
     */
    @ApiOperation(value = "根据主编码：获取品类品种DTO")
    @GetMapping("/category/getCategoryDTOBySerialNo")
    CategoryDTO getCategoryDTOBySerialNo(@RequestParam(value = "serialNo") Integer serialNo);

    /**
     * 新增：品类品种
     *
     * @param addDTO
     * @return
     */
    @ApiOperation(value = "新增：品类品种")
    @PostMapping("/category/addCategory")
    Result addCategory(@RequestBody CategoryAddDTO addDTO);

    /**
     * 更新：品类品种
     *
     * @param updateDTO
     * @return
     */
    @ApiOperation(value = "更新：品类品种")
    @PostMapping("/category/updateCategoryStatus")
    Result updateCategoryStatus(@RequestBody CategoryUpdateDTO updateDTO);

    /**
     * 获取菜单结构
     *
     * @param systemId
     * @param customerId
     * @return
     */
    @ApiOperation(value = "获取菜单结构")
    @PostMapping("/category/queryCategoryMenu")
    List<Tree<Integer>> queryCategoryMenu(@RequestParam(value = "systemId") Integer systemId,
                                          @RequestParam(value = "customerId", required = false) Integer customerId);

    /**
     * 获取树形结构
     *
     * @return
     */
    @ApiOperation(value = "获取树形结构")
    @PostMapping("/category/queryCategoryTree")
    List<Tree<Integer>> queryCategoryTree();

    /**
     * 根据二级品类：获取期货代码列表
     *
     * @param category2
     * @return
     */
    @ApiOperation(value = "根据二级品类：获取期货代码列表")
    @PostMapping("/category/queryFutureCodeList")
    List<String> queryFutureCodeList(@RequestParam(value = "category2") Integer category2);

    /**
     * 根据主编吗：获取上一级品类
     *
     * @param serialNo
     * @return
     */
    @ApiOperation(value = "根据主编吗：获取上一级品类")
    @PostMapping("/category/getParentCategoryBySerialNo")
    CategoryEntity getParentCategoryBySerialNo(@RequestParam(value = "serialNo") Integer serialNo);

    /**
     * 根据主编吗：获取品类名称
     *
     * @param serialNoList
     * @return
     */
    @ApiOperation(value = "根据主编吗：获取品类名称")
    @GetMapping("/category/getCategoryNameBySerialNoList")
    List<CategoryEntity> getCategoryNameBySerialNoList(@RequestParam(value = "serialNoList") List<Integer> serialNoList);

    @ApiOperation(value = "根据主编吗：获取品类名称")
    @GetMapping("/category/assemblyCategoryNames")
    String assemblyCategoryNames(@RequestParam(value = "serialNoList") List<Integer> categorySerialNoList);

    /**
     * 根据主编吗：获取上一级品类
     *
     * @param name
     * @return
     */
    @ApiOperation(value = "根据主编吗：获取上一级品类")
    @PostMapping("/category/getCategoryByName")
    CategoryEntity getCategoryByName(@RequestParam(value = "name") String name,
                                               @RequestParam(value = "level") Integer level);

    /**
     * 根据主编吗：获取上一级品类
     *
     * @param name
     * @return
     */
    @ApiOperation(value = "根据主编吗：获取上一级品类")
    @GetMapping("/category/getSerialNoListByCategoryNames")
    List<Integer> getSerialNoListByCategoryNames(@RequestParam(value = "nameList") List<String> nameList,
                                     @RequestParam(value = "level") Integer level);
}
