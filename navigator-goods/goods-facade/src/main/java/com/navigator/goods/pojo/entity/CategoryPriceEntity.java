package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2022-03-29 19:42
 */
@Data
@Accessors(chain = true)
@TableName("dbg_category_price")
@ApiModel(value = "CategoryPrice对象", description = "品类报价关系表")
public class CategoryPriceEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "品类ID")
    private Integer categoryId;


    @ApiModelProperty(value = "报价类型")
    private Integer contractPriceType;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
