package com.navigator.goods.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * SKU-MDM
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_sku_mdm")
@ApiModel(value = "skuMdm对象", description = "SKU-MDM")
public class SkuMdmEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型，1：现货，2：仓单，3：豆二")
    private Integer type;

    @ApiModelProperty(value = "SKU ID")
    private Integer skuId;

    @ApiModelProperty(value = "MDM编码")
    private String mdmId;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "删除状态")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


    /**
     * 查询条件
     *
     * @param condition
     * @return
     */
    public static final LambdaQueryWrapper<SkuMdmEntity> lqw(SkuMdmQO condition) {
        LambdaQueryWrapper<SkuMdmEntity> lqw = new LambdaQueryWrapper<SkuMdmEntity>().eq(SkuMdmEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SkuMdmEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getType()), SkuMdmEntity::getType, condition.getType());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSkuId()), SkuMdmEntity::getSkuId, condition.getSkuId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getMdmId()), SkuMdmEntity::getMdmId, condition.getMdmId());
            lqw.in(CollUtil.isNotEmpty(condition.getSkuIdList()), SkuMdmEntity::getSkuId, condition.getSkuIdList());
        }
        lqw.orderByDesc(SkuMdmEntity::getId);
        return lqw;
    }
}
