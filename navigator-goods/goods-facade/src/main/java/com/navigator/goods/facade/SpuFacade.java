package com.navigator.goods.facade;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.dto.SpuRefreshDTO;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.goods.pojo.qo.SpuQO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <p>
 * SPU商品 Facade
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Api(tags = "SPU商品")
@FeignClient(value = "navigator-goods-service")
public interface SpuFacade {
    /**
     * 根据条件：获取SPU商品DTO分页
     *
     * @param queryDTO
     * @return
     */
    @ApiOperation(value = "根据条件：获取SPU商品DTO分页")
    @PostMapping("/spu/querySpuDTOPage")
    Page<SpuDTO> querySpuDTOPage(@RequestBody QueryDTO<SpuQO> queryDTO);

    /**
     * 根据条件：获取SPU商品DTO列表
     *
     * @param condition
     * @return
     */
    @ApiOperation(value = "根据条件：获取SPU商品DTO列表")
    @PostMapping("/spu/querySpuDTOList")
    List<SpuDTO> querySpuDTOList(@RequestBody SpuQO condition);

    /**
     * 根据ID：获取SPU商品
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "根据ID：获取SPU商品")
    @GetMapping("/spu/getSpuById")
    SpuEntity getSpuById(@RequestParam(value = "id") Integer id);

    /**
     * 根据编码：获取SPU商品
     *
     * @param spuNo
     * @return
     */
    @ApiOperation(value = "根据编码：获取SPU商品")
    @GetMapping("/spu/getSpuBySpuNo")
    SpuEntity getSpuByNo(@RequestParam(value = "spuNo") String spuNo);

    /**
     * 刷新：SPU商品
     *
     * @param spuRefreshDTO
     * @return
     */
    @ApiOperation(value = "刷新：SPU商品")
    @PostMapping("/spu/refreshSpu")
    Result<List<SpuDTO>> refreshSpu(@RequestBody SpuRefreshDTO spuRefreshDTO);

    /**
     * 保存：SPU商品
     *
     * @param spuDTOList
     * @return
     */
    @ApiOperation(value = "保存：SPU商品")
    @PostMapping("/spu/saveSpu")
    Result saveSpu(@RequestBody List<SpuDTO> spuDTOList);
}