package com.navigator.goods.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 商品基础表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@Accessors(chain = true)
@TableName("dbg_goods")
@ApiModel(value = "Goods对象", description = "商品基础表")
public class GoodsEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "linkinage商品编码")
    private String linkageGoodsCode;

    @ApiModelProperty(value = "linkinage商品名称")
    private String linkageGoodsName;

    @ApiModelProperty(value = "其他平台商品编码")
    private String mdmGoodsCode;

    @ApiModelProperty(value = "货品代码")
    private String code;

    @ApiModelProperty(value = "货品名称")
    private String name;

    @ApiModelProperty(value = "商品简称")
    private String shortName;

    @ApiModelProperty(value = "包装ID")
    private Integer packageId;

    @ApiModelProperty(value = "规格ID")
    private Integer specId;

    @ApiModelProperty(value = "商品种类")
    private Integer categoryId;

    @ApiModelProperty(value = "商品图片")
    private String picture;

    @ApiModelProperty(value = "所属油厂")
    private Integer supplierId;

    @ApiModelProperty(value = "1.按标包结算 2.按磅单结算 3.按磅单扣袋皮")
    private Integer settleType;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "计量单位（1、千克 2、吨）")
    private Integer measureUnit;

    @ApiModelProperty(value = "状态 0-禁用1-启用")
    @TableField(value = "status", fill = FieldFill.INSERT)
    private Integer status;

    @ApiModelProperty(value = "状态 0-禁用1-启用")
    @TableField(value = "is_deleted", fill = FieldFill.INSERT)
    @TableLogic
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /**
     * Nav品类名
     */
    @TableField(exist = false)
    private String categoryName;
    /**
     * 所属货品大类
     */
    @TableField(exist = false)
    private String parentCategoryName;
    /**
     * 规格名
     */
    @TableField(exist = false)
    private String specName;
    /**
     * 包装名
     */
    @TableField(exist = false)
    private String packageName;

    @ApiModelProperty(value = "前缀")
    private String futurePrefix;

    @ApiModelProperty(value = "后缀")
    private String futureSuffix;

    @ApiModelProperty(value = "是否可提货")
    private Integer isDelivery;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "SPU商品ID")
    private Integer spuId;

    @ApiModelProperty(value = "系统生成的sku货品全称")
    private String fullName;

    @ApiModelProperty(value = "品种code+多个关键+销售规格+包装值ID 下划线隔开（排序）:_11_222_12_")
    private String skuNo;

    @ApiModelProperty(value = "多个关键规格值+销售规格信息(Json,例：\n[{\"attributeId\":\"20\",\"attributeValueId\":\"20\",\"attributeValueName\":\"43%\"}])")
    private String keyAttributeValues;

    @ApiModelProperty(value = "一级分类")
    private Integer category1;

    @ApiModelProperty(value = "二级分类")
    private Integer category2;

    @ApiModelProperty(value = "三级分类")
    private Integer category3;

    @ApiModelProperty(value = "是否仓单标品(0否 1是)")
    private Integer isWarrant;
}
