package com.navigator.goods.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2021-07-08 19:57
 */
@Data
@Accessors(chain = true)
public class AttributeValueVO {
    private Integer attributeId;
    private Integer attributeValueId;
    private String attributeName;
    private String attributeValue;
    /**
     * 袋皮扣重(包装)
     */
    private BigDecimal packageWeight;

}