package com.navigator.goods.pojo.dto;

import com.navigator.goods.pojo.entity.AttributeValueEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 品种规格
 *
 * <AUTHOR>
 */
@Data
public class CategoryAttributeDTO {
    @ApiModelProperty(value = "规格ID")
    private Integer attributeId;

    @ApiModelProperty(value = "规格名")
    private String attributeName;

    @ApiModelProperty(value = "规格类型（1包装规格;2关键规格;3销售规格）")
    private Integer attributeType;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "规格值")
    private List<AttributeValueEntity> attributeValueList;
}
