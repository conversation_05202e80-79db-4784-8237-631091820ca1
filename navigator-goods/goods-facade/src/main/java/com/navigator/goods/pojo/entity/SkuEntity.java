package com.navigator.goods.pojo.entity;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.pojo.qo.SkuQO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * SKU货品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbg_goods")
@ApiModel(value = "sku对象", description = "SKU货品")
public class SkuEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "linkinage商品编码", required = true)
    private String linkageGoodsCode;

    @ApiModelProperty(value = "其他平台商品编码")
    private String mdmGoodsCode;

    @ApiModelProperty(value = "货品代码")
    private String code;

    @ApiModelProperty(value = "货品名称")
    private String name;

    @ApiModelProperty(value = "商品简称")
    private String shortName;

    @ApiModelProperty(value = "包装ID")
    private Integer packageId;

    @ApiModelProperty(value = "规格ID")
    private Integer specId;

    @ApiModelProperty(value = "商品种类")
    private Integer categoryId;

    @ApiModelProperty(value = "所属油厂")
    private String supplierId;

    @ApiModelProperty(value = "商品图片")
    private String picture;

    @ApiModelProperty(value = "1.按标包结算 2.按磅单结算 3.按磅单扣袋皮")
    private Integer settleType;

    @ApiModelProperty(value = "条形码")
    private String barCode;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "计量单位（1、千克 2、吨）")
    private Integer measureUnit;

    @ApiModelProperty(value = "状态 0-禁用1-启用")
    private Integer status;

    @ApiModelProperty(value = "是否删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "linkinage商品名称", required = true)
    private String linkageGoodsName;

    @ApiModelProperty(value = "前缀")
    private String futurePrefix;

    @ApiModelProperty(value = "后缀")
    private String futureSuffix;

    @ApiModelProperty(value = "税率", required = true)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "是否可提货")
    private Integer isDelivery;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "SPU商品ID")
    private Integer spuId;

    @ApiModelProperty(value = "系统生成的sku货品全称")
    private String fullName;

    @ApiModelProperty(value = "品种code+多个关键+销售规格+包装值ID下划线隔开（排序）:_11_222_12_")
    private String skuNo;

    @ApiModelProperty(value = "多个关键规格值+销售规格信息(Json)")
    private String keyAttributeValues;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "是否仓单标品(0否 1是)")
    private Integer isWarrant;

    @ApiModelProperty(value = "是否TT默认规格(新增字段)")
    private Integer isTtAttribute;

    @ApiModelProperty(value = "NAV SKU ID")
    private String navSkuId;

    @ApiModelProperty(value = "货品昵称")
    private String nickName;

    @TableField(exist = false)
    @ApiModelProperty(value = "SPU商品名称")
    private String spuName;

    @ApiModelProperty(value = "是否Global Oil(0否 1是)")
    private Integer isGlobalOil;

    /**
     * 查询条件
     *
     * @param condition
     * @param order
     * @return
     */
    public static final LambdaQueryWrapper<SkuEntity> lqw(SkuQO condition, boolean order) {
        LambdaQueryWrapper<SkuEntity> lqw = new LambdaQueryWrapper<SkuEntity>().eq(SkuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
        if (condition != null) {
            lqw.eq(StringUtil.isNotNullBlank(condition.getId()), SkuEntity::getId, condition.getId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getLinkageGoodsCode()), SkuEntity::getLinkageGoodsCode, StringUtil.trim(condition.getLinkageGoodsCode()));
            lqw.eq(StringUtil.isNotNullBlank(condition.getMdmGoodsCode()), SkuEntity::getMdmGoodsCode, StringUtil.trim(condition.getMdmGoodsCode()));
            lqw.eq(StringUtil.isNotNullBlank(condition.getCode()), SkuEntity::getCode, StringUtil.trim(condition.getCode()));
            lqw.like(StringUtil.isNotNullBlank(condition.getName()), SkuEntity::getFullName, StringUtil.trim(condition.getName()));
            lqw.like(StringUtil.isNotNullBlank(condition.getNickName()), SkuEntity::getNickName, StringUtil.trim(condition.getNickName()));
            lqw.eq(StringUtil.isNotNullBlank(condition.getShortName()), SkuEntity::getShortName, StringUtil.trim(condition.getShortName()));
            lqw.eq(StringUtil.isNotNullBlank(condition.getPackageId()), SkuEntity::getPackageId, condition.getPackageId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSpecId()), SkuEntity::getSpecId, condition.getSpecId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategoryId()), SkuEntity::getCategoryId, condition.getCategoryId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSupplierId()), SkuEntity::getSupplierId, condition.getSupplierId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSettleType()), SkuEntity::getSettleType, condition.getSettleType());
            lqw.eq(StringUtil.isNotNullBlank(condition.getBarCode()), SkuEntity::getBarCode, condition.getBarCode());
            lqw.eq(StringUtil.isNotNullBlank(condition.getMeasureUnit()), SkuEntity::getMeasureUnit, condition.getMeasureUnit());
            lqw.eq(StringUtil.isNotNullBlank(condition.getStatus()), SkuEntity::getStatus, condition.getStatus());
            lqw.eq(StringUtil.isNotNullBlank(condition.getLinkageGoodsName()), SkuEntity::getLinkageGoodsName, condition.getLinkageGoodsName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFuturePrefix()), SkuEntity::getFuturePrefix, condition.getFuturePrefix());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFutureSuffix()), SkuEntity::getFutureSuffix, condition.getFutureSuffix());
            lqw.eq(StringUtil.isNotNullBlank(condition.getTaxRate()), SkuEntity::getTaxRate, condition.getTaxRate());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsDelivery()), SkuEntity::getIsDelivery, condition.getIsDelivery());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSpuId()), SkuEntity::getSpuId, condition.getSpuId());
            lqw.eq(StringUtil.isNotNullBlank(condition.getFullName()), SkuEntity::getFullName, condition.getFullName());
            lqw.eq(StringUtil.isNotNullBlank(condition.getSkuNo()), SkuEntity::getSkuNo, condition.getSkuNo());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory1()), SkuEntity::getCategory1, condition.getCategory1());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory2()), SkuEntity::getCategory2, condition.getCategory2());
            lqw.eq(StringUtil.isNotNullBlank(condition.getCategory3()), SkuEntity::getCategory3, condition.getCategory3());
            lqw.in(CollUtil.isNotEmpty(condition.getCategory3List()), SkuEntity::getCategory3, condition.getCategory3List());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsWarrant()), SkuEntity::getIsWarrant, condition.getIsWarrant());
            lqw.eq(StringUtil.isNotNullBlank(condition.getIsTtAttribute()), SkuEntity::getIsTtAttribute, condition.getIsTtAttribute());
        }
        if (order) {
            lqw.orderByDesc(SkuEntity::getId);
        }
        return lqw;
    }

    @Deprecated
    public String getPackageName() {
        return null;
    }

    @Deprecated
    public String getSpecName() {
        return null;
    }

    /**
     * 查询条件
     *
     * @return
     */
    public static final LambdaQueryWrapper<SkuEntity> lqw() {
        return new LambdaQueryWrapper<SkuEntity>().eq(SkuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
    }
}
