package com.navigator.goods.pojo.dto;

import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * SKU货品
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@Accessors(chain = true)
public class SkuDTO extends SkuEntity {

    @ApiModelProperty(value = "一级品类名称")
    private String categoryName1;

    @ApiModelProperty(value = "二级品类名称")
    private String categoryName2;

    @ApiModelProperty(value = "品种名称(3级品类)")
    private String categoryName3;

    @ApiModelProperty(value = "MDM ID")
    private List<SkuMdmEntity> skuMdmList;

    @ApiModelProperty(value = "关键规格值集合")
    List<SkuAttributeValueDTO> specAttributeValueList;

    @ApiModelProperty(value = "包装规格值集合")
    List<SkuAttributeValueDTO> packageAttributeValueList;
}
