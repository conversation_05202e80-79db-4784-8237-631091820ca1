package com.navigator.goods.pojo.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.admin.pojo.dto.systemrule.InvoiceTypeDTO;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2022-05-11 09:51
 **/
@Data
public class CategoryCreateDTO {
    private Integer id;
    /**
     * 分类父ID
     */
    private Integer parentId;

    private String parentCategoryName;
    /**
     * linkinage品类编码
     */
    private String linkageCategoryCode;
    /**
     * linkinage品类名称
     */
    private String linkageCategoryName;
    /**
     * Lkg货品大类
     */
    private String linkageParentCategoryName;
    /**
     * 品类名称
     */
    private String name;
    /**
     * 品类编码
     */
    private String code;

    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;
    /**
     * 类别报价类型
     */
    private List<Integer> categoryPriceList;
    /**
     * 发票信息
     */
    private List<InvoiceTypeDTO> invoiceTypeDTOList;

}
