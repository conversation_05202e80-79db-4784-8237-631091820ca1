<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>navigator-goods</artifactId>
        <groupId>com.navigator</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>goods-facade</artifactId>

    <dependencies>
    <!--swagger 文档注释-->
    <dependency>
        <groupId>io.swagger</groupId>
        <artifactId>swagger-annotations</artifactId>
    </dependency>
    <!--swagger-->

    <!-- MyBatis Plus 相关依赖-->
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>3.2.0</version>
    </dependency>
    <dependency>
        <groupId>org.mybatis.spring.boot</groupId>
        <artifactId>mybatis-spring-boot-starter</artifactId>
        <version>2.0.1</version>
    </dependency>
    <dependency>
        <groupId>com.navigator</groupId>
        <artifactId>navigator-commons</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>admin-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>admin-facade</artifactId>
        </dependency>
    </dependencies>

</project>