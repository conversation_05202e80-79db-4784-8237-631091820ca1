apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-goods-int
  namespace: int
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-goods-int
  template:
    metadata:
      labels:
        app: ldc-navigator-goods-int
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-goods-int:#{Build.BuildId}#
        name: ldc-navigator-goods-int
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "int" 
        volumeMounts:
        - name: azure
          mountPath: /logs
      volumes:
      - name: azure
        csi:
          driver: file.csi.azure.com
          readOnly: false
          volumeAttributes:
            secretName: storageaccount-csm4nnvgsto001-secret  # required
            shareName: logs-int  # required
            server: csm4nnvgsto001.privatelink.file.core.chinacloudapi.cn
            mountOptions: "dir_mode=0777,file_mode=0777,cache=strict,actimeo=30,nosharesock"  # optiona
---

apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-goods-int
  namespace: int
spec:
  type: ClusterIP
  ports:
  - port: 9107
    protocol: TCP
    targetPort: 80
  selector:
    app: ldc-navigator-goods-int