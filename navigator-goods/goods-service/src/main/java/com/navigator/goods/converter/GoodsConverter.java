package com.navigator.goods.converter;

import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.goods.pojo.vo.AttributeValueVO;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface GoodsConverter {
    GoodsConverter INSTANCE = Mappers.getMapper(GoodsConverter.class);


    @Mappings({
            @Mapping(source = "id", target = "goodsId"),
            @Mapping(source = "code", target = "goodsCode"),
            @Mapping(source = "name", target = "goodsName"),
    })
    GoodsInfoVO convertEntity2Vo(GoodsEntity goodsEntity);

    @Mappings({
            @Mapping(source = "id", target = "attributeValueId"),
            @Mapping(source = "name", target = "attributeValue"),
    })
    AttributeValueVO convertEntity2Vo(AttributeValueEntity attributeValueEntity);
}
