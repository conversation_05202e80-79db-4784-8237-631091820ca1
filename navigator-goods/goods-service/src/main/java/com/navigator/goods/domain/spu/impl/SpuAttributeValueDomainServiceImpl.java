package com.navigator.goods.domain.spu.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.goods.dao.SpuAttributeValueDao;
import com.navigator.goods.domain.spu.ISpuAttributeValueDomainService;
import com.navigator.goods.mapper.SpuAttributeValueMapper;
import com.navigator.goods.pojo.entity.SpuAttributeValueEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * SPU的关键规格信息 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Service

public class SpuAttributeValueDomainServiceImpl extends ServiceImpl<SpuAttributeValueMapper, SpuAttributeValueEntity> implements ISpuAttributeValueDomainService {

    @Resource
    private SpuAttributeValueDao spuAttributeValueDao;

    @Override
    public SpuAttributeValueEntity getSpuAttributeValueById(Integer id) {
        return spuAttributeValueDao.getSpuAttributeValueById(id);
    }
}