package com.navigator.goods.domain.category;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeQO;

import java.util.List;

/**
 * <p>
 * 规格 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface IAttributeDomainService extends IService<AttributeEntity> {
    /**
     * 根据条件：获取规格DTO分页
     *
     * @param queryDTO
     * @return
     */
    Page<AttributeDTO> queryAttributeDTOPage(QueryDTO<AttributeQO> queryDTO);

    /**
     * 根据条件：获取规格DTO列表
     *
     * @param condition
     * @return
     */
    List<AttributeDTO> queryAttributeDTOList(AttributeQO condition);

    /**
     * 根据ID：获取规格DTO
     *
     * @param id
     * @return
     */
    AttributeDTO getAttributeDTOById(Integer id);

    /**
     * 新增：规格
     *
     * @param addDTO
     * @return
     */
    AttributeEntity addAttribute(AttributeAddDTO addDTO);

    /**
     * 更新：规格
     *
     * @param updateDTO
     * @return
     */
    AttributeEntity updateAttribute(AttributeUpdateDTO updateDTO);

    List<AttributeValueEntity> getAttributeValueListByType(Integer type);

}
