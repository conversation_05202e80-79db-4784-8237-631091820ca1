package com.navigator.goods.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.navigator.admin.facade.SequenceFacade;
import com.navigator.common.annotation.Dao;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.PageUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.mapper.CategoryMapper;
import com.navigator.goods.pojo.dto.CategoryAddDTO;
import com.navigator.goods.pojo.dto.CategorySearchDTO;
import com.navigator.goods.pojo.dto.CategoryUpdateDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2021-12-03 14:06
 */
@Slf4j
@Dao
public class CategoryDao extends BaseDaoImpl<CategoryMapper, CategoryEntity> {

    @Resource
    private SequenceFacade sequenceFacade;

    /**
     * 获取所有商品-品类信息
     *
     * @return 品类集合
     */
    public List<CategoryEntity> getAllCategoryList(Integer level) {
        level = null == level ? 2 : level;
        return this.list(Wrappers.<CategoryEntity>lambdaQuery()
                .eq(CategoryEntity::getLevel, level)
                .eq(CategoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据品类编码获取品类信息
     *
     * @param categoryCode 品类编码
     * @return 品类信息
     */
    public CategoryEntity getCategoryByCode(String categoryCode) {
        List<CategoryEntity> categoryEntities = this.list(Wrappers.<CategoryEntity>lambdaQuery()
                .eq(CategoryEntity::getCode, categoryCode)
                .eq(CategoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(categoryEntities) ? null : categoryEntities.get(0);
    }

    @Deprecated
    public IPage<CategoryEntity> queryCategoryListOld(QueryDTO<CategorySearchDTO> categoryqueryDTO) {
        CategorySearchDTO categorySearchDTO = categoryqueryDTO.getCondition();
        return this.page(new Page<>(categoryqueryDTO.getPageNo(), categoryqueryDTO.getPageSize()), new LambdaQueryWrapper<CategoryEntity>()
                .eq(null != categorySearchDTO.getCategoryId(), CategoryEntity::getId, categorySearchDTO.getCategoryId())
                .eq(null != categorySearchDTO.getParentCategoryId(), CategoryEntity::getParentId, categorySearchDTO.getParentCategoryId())
                .eq(StrUtil.isNotBlank(categorySearchDTO.getCategoryName()), CategoryEntity::getName, categorySearchDTO.getCategoryName())
                .eq(CategoryEntity::getLevel, 2)
                .eq(CategoryEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(CategoryEntity::getId)
        );
    }

    // 新

    /**
     * 根据条件：获取品类品种分页
     *
     * @param queryDTO
     * @return
     */
    public Page<CategoryEntity> queryCategoryPage(QueryDTO<CategoryQO> queryDTO) {
        IPage<CategoryEntity> page = this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), this.getLqwByCondition(queryDTO.getCondition()));
        return PageUtil.convertPage(page);
    }

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    public List<CategoryEntity> queryCategoryList(CategoryQO condition) {
        return this.list(this.getLqwByCondition(condition));
    }

    /**
     * 根据ID：获取品类品种
     *
     * @param id
     * @return
     */
    public CategoryEntity getCategoryById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据主编吗：获取品类品种
     *
     * @param serialNo
     * @return
     */
    public CategoryEntity getCategoryBySerialNo(Integer serialNo) {
        List<CategoryEntity> categoryEntityList = this.list(CategoryEntity.lqw(null, true).eq(CategoryEntity::getSerialNo, serialNo));
        return CollectionUtils.isEmpty(categoryEntityList) ? null : categoryEntityList.get(0);
    }

    public List<CategoryEntity> getCategoryBySerialNoList(List<Integer> serialNoList) {
        if (CollectionUtils.isEmpty(serialNoList)) {
            return new ArrayList<>();
        }
        return this.list(CategoryEntity.lqw(null, true).in(CategoryEntity::getSerialNo, serialNoList));
    }

    /**
     * 获取三级品类
     *
     * @param serialNo
     * @return
     */
    public CategoryEntity[] getThreeCategoryBySerialNo(Integer serialNo) {
        CategoryEntity category3 = this.getCategoryBySerialNo(serialNo);
        CategoryEntity category2 = this.getCategoryById(category3.getParentId());
        CategoryEntity category1 = this.getCategoryById(category2.getParentId());
        CategoryEntity[] arr = new CategoryEntity[3];
        arr[0] = category1;
        arr[1] = category2;
        arr[2] = category3;
        return arr;
    }

    /**
     * 新增：品类品种
     *
     * @param addDTO
     * @param parent
     * @return
     */
    public CategoryEntity addCategory(CategoryAddDTO addDTO, CategoryEntity parent) {
        // 复制
        CategoryEntity entity = BeanUtil.copyProperties(addDTO, CategoryEntity.class);
        // 新增
        entity.setLevel(3);
        entity.setCode(parent.getCode());
//        entity.setNavId(sequenceFacade.generate(SequenceEnum.NAV_ID.getValue(), 5));
        entity.setIsSplitContract(0);
        entity.setCreatedAt(new Date());
        entity.setUpdatedAt(new Date());
        entity.setCreatedBy(JwtUtils.getCurrentUserId());
        entity.setUpdatedBy(JwtUtils.getCurrentUserId());
        // 验证合法性
        this.checkLegal(entity);
        boolean flag = save(entity);
        if (flag) {
            // 更新主编码
            entity.setSerialNo(entity.getId());
            IdNameConverter.setName(IdNameType.category_id_name, entity.getId().toString(), entity.getName());
            IdNameConverter.setName(IdNameType.category_serialNo_name, entity.getSerialNo().toString(), entity.getName());
            IdNameConverter.setName(IdNameType.category_id_serialNo, entity.getId().toString(), entity.getSerialNo().toString());
            this.updateById(entity);
            return entity;
        }
        throw new BusinessException("新增失败！");
    }

    /**
     * 更新：品类品种
     *
     * @param updateDTO
     * @return
     */
    public CategoryEntity updateCategory(CategoryUpdateDTO updateDTO) {
        if (StringUtil.isNullBlank(updateDTO.getId())) {
            throw new BusinessException("没有记录ID，无法更新！");
        }
        CategoryEntity entity = this.getById(updateDTO.getId());
        if (entity == null) {
            throw new BusinessException("找不到记录！");
        }
        // 更新字段
        BeanUtil.copyProperties(updateDTO, entity);
        // 保存
        entity.setUpdatedAt(new Date());
        entity.setUpdatedBy(JwtUtils.getCurrentUserId());
        boolean flag = updateById(entity);
        if (flag) {
            return entity;
        }
        throw new BusinessException("更新失败！");
    }

    /**
     * 菜单
     *
     * @param isAdmin
     * @param category2List
     * @return
     */
    public List<Tree<Integer>> queryCategoryMenu(Boolean isAdmin, LinkedHashSet<Integer> category2List) {
        List<CategoryEntity> nodes = new ArrayList<>();
        if (isAdmin || category2List.contains(0)) {
            // 管理员或非管理员不区分品类查询全部二级品类
            log.info("{}：查询全部二级品类", isAdmin ? "管理员" : "非管理员");
            nodes = this.list(CategoryEntity.lqw(null, true)
                    .eq(CategoryEntity::getStatus, 1)
                    .le(CategoryEntity::getLevel, 2)
            );
        } else if (CollUtil.isNotEmpty(category2List)) {
            log.info("非管理员：查询指定二级品类");
            // 非管理员
            // 二级品类
            List<CategoryEntity> list = this.list(CategoryEntity.lqw(null, true)
                    .eq(CategoryEntity::getStatus, 1)
                    .le(CategoryEntity::getLevel, 2)
                    .in(CategoryEntity::getSerialNo, category2List)
            );
            // 一级品类
            Map<Integer, CategoryEntity> parentMap = new HashMap<>();
            for (CategoryEntity item : list) {
                if (parentMap.get(item.getParentId()) == null) {
                    log.info("查询指定二级品类 {} 的一级品类 {}", item.getSerialNo(), item.getParentId());
                    CategoryEntity parent = this.getById(item.getParentId());
                    parentMap.put(parent.getId(), parent);
                    nodes.add(parent);
                }
            }
            nodes.addAll(list);
        }
        if (CollUtil.isEmpty(nodes)) {
            log.info("未授权任何品类菜单");
            return new ArrayList<>();
        }
        log.info("授权品类菜单个数：{}", nodes.size());
        return TreeUtil.build(nodes, 0, (entity, tree) -> this.toTree(entity, tree));
    }

    /**
     * 树形
     *
     * @return
     */
    public List<Tree<Integer>> queryCategoryTree() {
        List<CategoryEntity> nodes = this.list(CategoryEntity.lqw(null, true).eq(CategoryEntity::getStatus, 1));
        return TreeUtil.build(nodes, 0, (entity, tree) -> this.toTree(entity, tree));
    }

    /**
     * 获取所有数据的map
     *
     * @return
     */
    public Map<Integer, CategoryEntity> queryAllCategoryMap() {
        List<CategoryEntity> list = this.list(CategoryEntity.lqw(null, false));
        Map<Integer, CategoryEntity> map = new HashMap<>();
        list.forEach(item -> map.put(item.getId(), item));
        return map;
    }

    /**
     * 验证合法性
     *
     * @param entity
     */
    private void checkLegal(CategoryEntity entity) {
        int count = this.count(
                CategoryEntity.lqw(null, false)
                        .eq(CategoryEntity::getParentId, entity.getParentId())
                        .eq(CategoryEntity::getName, entity.getName())
                        .ne(StringUtil.isNotNullBlank(entity.getId()), CategoryEntity::getId, entity.getId())
        );
        if (count > 0) {
            throw new BusinessException("品种名称重复，请重新填写！");
        }
    }

    /**
     * 转Tree
     *
     * @param entity
     * @param tree
     */
    private void toTree(CategoryEntity entity, Tree<Integer> tree) {
        tree.setId(entity.getId());
        tree.setParentId(entity.getParentId());
        tree.setName(entity.getName());
        tree.putExtra("serialNo", entity.getSerialNo());
        tree.putExtra("domainCode", entity.getFutureCode());
        tree.putExtra("level", entity.getLevel());
        tree.putExtra("isSplitContract", entity.getIsSplitContract());
    }


    /**
     * 获取查询条件
     *
     * @param condition
     * @return
     */
    private LambdaQueryWrapper<CategoryEntity> getLqwByCondition(CategoryQO condition) {
        if (condition == null) {
            condition = new CategoryQO();
        }
        // 默认查品种
        if (StringUtil.isNullBlank(condition.getLevel())) {
            condition.setLevel(3);
        }
        // 查品种
        if (condition.getLevel() == 3) {
            // 父Id
            Integer parentSerialNo = condition.getParentSerialNo();
            // 获取基础查询条件
            LambdaQueryWrapper<CategoryEntity> lqw = CategoryEntity.lqw(condition.setParentSerialNo(null), true);
            // 父Id不为空时
            if (StringUtil.isNotNullBlank(parentSerialNo)) {
                CategoryEntity categoryEntity = this.getCategoryBySerialNo(parentSerialNo);
                if (categoryEntity == null) {
                    return lqw;
                }
                if (categoryEntity.getLevel() == 1) {
                    List<CategoryEntity> category2EntityList = this.list(CategoryEntity.lqw(null, false).eq(CategoryEntity::getParentId, categoryEntity.getSerialNo()));
                    List<Integer> category2List = new ArrayList<>();
                    category2EntityList.forEach(item -> category2List.add(item.getId()));
                    lqw.in(CategoryEntity::getParentId, category2List);
                } else if (categoryEntity.getLevel() == 2) {
                    lqw.eq(CategoryEntity::getParentId, categoryEntity.getSerialNo());
                } else if (categoryEntity.getLevel() == 3) {
                    lqw.eq(CategoryEntity::getSerialNo, categoryEntity.getSerialNo());
                }
            }
            return lqw;
        } else {
            return CategoryEntity.lqw(condition, true);
        }
    }

    /**
     * 获取主编码列表
     *
     * @param condition
     * @return
     */
    public List<Integer> querySerialNoList(CategoryQO condition) {
        List<CategoryEntity> list = this.list(CategoryEntity.lqw(condition, false));
        List<Integer> result = new ArrayList<>();
        list.forEach(item -> result.add(item.getSerialNo()));
        return result;
    }

    public CategoryEntity getCategoryByNameAndLevel(String categoryName, Integer level, Integer parentId) {
        List<CategoryEntity> categoryEntityList = this.list(CategoryEntity.lqw()
                .eq(StringUtils.isNotBlank(categoryName), CategoryEntity::getName, categoryName)
                .eq(null != parentId, CategoryEntity::getParentId, parentId)
                .eq(null != level, CategoryEntity::getLevel, level));
        return CollectionUtils.isEmpty(categoryEntityList) ? null : categoryEntityList.get(0);
    }

    public List<CategoryEntity> getByCategoryNamesAndLevel(List<String> categoryNameList, Integer level) {
        return this.list(CategoryEntity.lqw()
                .in(!CollectionUtils.isEmpty(categoryNameList), CategoryEntity::getName, categoryNameList)
                .eq(null != level, CategoryEntity::getLevel, level)
                .orderByAsc(CategoryEntity::getSerialNo)
        );
    }

    public boolean saveCategoryWithId(CategoryEntity categoryEntity) {
        return SqlHelper.retBool(this.getBaseMapper().saveCategoryWithId(categoryEntity));
    }
}
