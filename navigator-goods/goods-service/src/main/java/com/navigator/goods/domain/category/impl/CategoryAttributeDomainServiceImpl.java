package com.navigator.goods.domain.category.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.dao.CategoryAttributeDao;
import com.navigator.goods.domain.category.ICategoryAttributeDomainService;
import com.navigator.goods.mapper.CategoryAttributeMapper;
import com.navigator.goods.pojo.dto.CategoryAttributeDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.CategoryAttributeEntity;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 品类规格关联 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@Service
public class CategoryAttributeDomainServiceImpl extends ServiceImpl<CategoryAttributeMapper, CategoryAttributeEntity> implements ICategoryAttributeDomainService {

    @Resource
    private CategoryAttributeDao categoryAttributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;


    @Override
    public List<CategoryAttributeDTO> queryCategoryAttributeList(CategoryAttributeQO condition) {
        List<CategoryAttributeEntity> list = categoryAttributeDao.queryCategoryAttributeList(condition);
        List<CategoryAttributeDTO> result = new ArrayList<>();
        list.forEach(item -> {
            CategoryAttributeDTO dto = BeanUtil.copyProperties(item, CategoryAttributeDTO.class);
            dto.setAttributeId(item.getAttributeId());
            dto.setAttributeName(item.getAttributeName());
            dto.setAttributeType(item.getAttributeType());
            dto.setAttributeValueList(attributeValueDao.queryAttributeValueList(new AttributeValueQO().setAttributeId(item.getAttributeId())));
            result.add(dto);
        });
        return result;
    }


    @Override
    public CategoryAttributeEntity getCategoryAttributeById(Integer id) {
        return categoryAttributeDao.getCategoryAttributeById(id);
    }


    @Override
    public void addCategoryAttribute(Integer serialNo1, Integer serialNo2, Integer serialNo3, List<AttributeEntity> keyAttributeEntityList, List<AttributeEntity> saleAttributeEntityList, AttributeEntity packageAttributeEntity) {
        categoryAttributeDao.addCategoryAttribute(serialNo1, serialNo2, serialNo3, keyAttributeEntityList, saleAttributeEntityList, packageAttributeEntity);
    }
}