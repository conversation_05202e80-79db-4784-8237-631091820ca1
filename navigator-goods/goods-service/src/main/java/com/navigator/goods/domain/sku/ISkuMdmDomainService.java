package com.navigator.goods.domain.sku;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;

import java.util.List;

/**
 * <p>
 * SKU-MDM Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
public interface ISkuMdmDomainService extends IService<SkuMdmEntity> {
    /**
     * 根据ID：获取SKU-MDM
     *
     * @param id
     * @return
     */
    SkuMdmEntity getSkuMdmById(Integer id);

    /**
     * 根据条件：获取SKU-MDM列表
     *
     * @param skuMdmQO
     * @return
     */
    List<SkuMdmEntity> querySkuMdmList(SkuMdmQO skuMdmQO);
}
