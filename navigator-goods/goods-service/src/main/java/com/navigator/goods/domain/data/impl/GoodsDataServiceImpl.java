package com.navigator.goods.domain.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SequenceFacade;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.SequenceEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.MapUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.dao.*;
import com.navigator.goods.domain.data.IGoodsDataService;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.entity.*;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.enums.MdmType;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;

/**
 * 数据导入
 *
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Slf4j
@Service
public class GoodsDataServiceImpl implements IGoodsDataService {

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private AttributeDao attributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;

    @Resource
    private CategoryAttributeDao categoryAttributeDao;

    @Resource
    private SpuDao spuDao;

    @Resource
    private SpuAttributeValueDao spuAttributeValueDao;

    @Resource
    private SkuDao skuDao;

    @Resource
    private SkuAttributeValueDao skuAttributeValueDao;

    @Resource
    private SkuMdmDao skuMdmDao;

    @Resource
    private SequenceFacade sequenceFacade;

    private static String SEPARATOR = "\\$\\$";

    @SneakyThrows
    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Result doImport(MultipartFile multipartFile, Boolean processHistory, List<String> rangeList) {
        // 导入数据
        File excel = new File(System.getProperty("user.dir") + File.separator + "基础数据导入.xlsx");
        multipartFile.transferTo(excel);
        String errorMsg = "";
        if (CollectionUtils.isEmpty(rangeList)) {
            // 一级品类,二级品类,三级品种,规格,规格值,品种规格,商品SPU,货品SKU
            rangeList = new ArrayList<>();
        }
        this.processCategory1(excel, errorMsg, rangeList);
        this.processCategory2(excel, errorMsg, rangeList);
        this.processCategory3(excel, errorMsg, rangeList);
        this.processAttribute(excel, errorMsg, rangeList);
        this.processAttributeValue(excel, errorMsg, rangeList);
        this.processCategoryAttribute(excel, errorMsg, rangeList);
        this.processSpu(excel, errorMsg, rangeList);
        this.processSku(excel, errorMsg, rangeList);
        // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04
        this.processSkuAttribute(excel, errorMsg, rangeList);
        log.info("全部处理完成！！！！！！！！！！！！！！");
        return Result.success("货品主数据导入完成！！！");
    }

    /**
     * 一级品类
     *
     * @param excel
     */
    private void processCategory1(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("一级品类")) {
            return;
        }
        log.info("货品主数据导入==========================：1、导入一级品类");
        for (Map map : this.getSheetList(excel, "一级品类")) {
            String name = MapUtil.getString(map, "name");
            Integer serialNo = MapUtil.getInt(map, "serialNo");
            Integer status = MapUtil.getInt(map, "status");
            CategoryEntity category1 = new CategoryEntity();
            if (StringUtils.isBlank(name)) {
                continue;
            }
            List<CategoryEntity> category1EntityList = categoryDao.list(CategoryEntity.lqw().eq(CategoryEntity::getName, name).eq(CategoryEntity::getLevel, 1));
            if (CollectionUtils.isEmpty(category1EntityList)) {
                category1.setId(serialNo)
                        .setParentId(0);
                category1.setName(name);
                category1.setSerialNo(serialNo);
                category1.setIsDeleted(0);
                category1.setCreatedBy("sys");
                category1.setCreatedAt(new Date());
                category1.setUpdatedBy("sys");
                category1.setUpdatedAt(new Date());
                category1.setLevel(1)
                        .setIsDce(0)
                        .setIsSoybean2(0)
                        .setIsSplitContract(0)
                        .setStatus(null == status ? 1 : status);
                categoryDao.saveCategoryWithId(category1);
            } else {
                category1 = category1EntityList.get(0);
                category1.setUpdatedBy("sys");
                category1.setUpdatedAt(new Date());
                category1.setName(name)
                        .setLevel(1)
                        .setIsDce(0)
                        .setIsSoybean2(0)
                        .setIsSplitContract(0)
                        .setStatus(null == status ? DisableStatusEnum.ENABLE.getValue() : status)
                        .setSerialNo(null == serialNo ? category1.getId() : serialNo)
                ;
                categoryDao.updateById(category1);
            }
        }
    }

    /**
     * 二级品类
     *
     * @param excel
     */
    private void processCategory2(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("二级品类")) {
            return;
        }
        log.info("货品主数据导入==========================：2、导入二级品类");
        for (Map map : this.getSheetList(excel, "二级品类")) {
            String category1Name = MapUtil.getString(map, "category1Name");
            String name = MapUtil.getString(map, "name");
            Integer serialNo = getInteger(map, "serialNo", 11);
            String code = MapUtil.getString(map, "code");
            Integer isSplitContract = getInteger(map, "isSplitContract", 0);
            Integer status = MapUtil.getInt(map, "status");
            if (StringUtils.isBlank(category1Name) || StringUtils.isBlank(name) || StringUtils.isBlank(code)) {
                continue;
            }
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(category1Name, 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(name, 2, category1.getSerialNo());
            if (category2 == null) {
                category2 = new CategoryEntity();
                category2.setParentId(category1.getSerialNo());
                category2.setId(serialNo)
                        .setSerialNo(serialNo);
                category2.setName(name);
                category2.setCode(code);
                category2.setIsDce(0);
                category2.setIsSoybean2(0);
                category2.setIsSplitContract(isSplitContract)
                        .setStatus(null == status ? DisableStatusEnum.ENABLE.getValue() : status);
                category2.setIsDeleted(0);
                category2.setCreatedBy("Sys");
                category2.setCreatedAt(new Date());
                category2.setUpdatedBy("Sys");
                category2.setUpdatedAt(new Date());
                category2.setLevel(2);
                categoryDao.saveCategoryWithId(category2);
                // 主编码默认为id
//                category2.setSerialNo(category2.getId());
//                categoryDao.saveCategoryWithId(category2);
            } else {
                category2.setCode(code);
                if (category2.getIsDce() == null) {
                    category2.setIsDce(0);
                }
                if (category2.getIsSoybean2() == null) {
                    category2.setIsSoybean2(isSplitContract);
                }
                if (null != serialNo) {
                    category2.setSerialNo(serialNo);
                }
                category2.setIsSoybean2(isSplitContract == null ? 0 : isSplitContract)
                        .setIsSplitContract(isSplitContract == null ? 0 : isSplitContract)
                        .setStatus(null == status ? DisableStatusEnum.ENABLE.getValue() : status)
                        .setCode(code)
                        .setName(name);
                category2.setUpdatedBy("Sys");
                category2.setUpdatedAt(new Date());
                category2.setLevel(2);
                categoryDao.updateById(category2);
            }
        }
    }

    /**
     * 三级品类
     *
     * @param excel
     */
    private void processCategory3(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("三级品种")) {
            return;
        }
        log.info("货品主数据导入==========================：3、导入三级品种");
        List<Map> category3MapList = this.getSheetList(excel, "三级品种");
        log.info("三级品种解析数据：{}", FastJsonUtils.getBeanToJson(category3MapList));
        for (Map category3Map : category3MapList) {
            List<String> categoryList = MapUtil.getStringList(category3Map, "category1Category2", SEPARATOR);
            String category3Name = MapUtil.getString(category3Map, "name");
            Integer serialNo3 = getInteger(category3Map, "serialNo", 23);
            Integer isSplitContract = getInteger(category3Map, "isSplitContract", 0);
            Integer isDce = getInteger(category3Map, "isDce", 0);
            String futureCode = MapUtil.getString(category3Map, "futureCode");
            Integer status = MapUtil.getInt(category3Map, "status");
            log.info("货品主数据导入品种==========================：3、导入三级品种" + category3Name);
            if (StringUtils.isBlank(category3Name)) {
                return;
            }
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(categoryList.get(0), 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(categoryList.get(1), null, category1.getSerialNo());
            CategoryEntity category3 = categoryDao.getCategoryByNameAndLevel(category3Name, null, category2.getSerialNo());
            if (category3 == null) {
                category3 = new CategoryEntity();
                category3.setParentId(category2.getSerialNo());
                category3.setId(serialNo3)
                        .setName(category3Name)
                        .setSerialNo(null == serialNo3 ? 0 : serialNo3)
                        .setIsSplitContract(null == isSplitContract ? 0 : isSplitContract)
                        .setIsSoybean2(null == isSplitContract ? 0 : isSplitContract)
                        .setIsDce(null == isDce ? 0 : isDce)
                        .setFutureCode(StringUtils.isBlank(futureCode) ? "" : futureCode)
                        .setStatus(null == status ? DisableStatusEnum.ENABLE.getValue() : status);
                category3.setIsDeleted(0);
                category3.setCreatedBy("Sys");
                category3.setCreatedAt(new Date());
                category3.setUpdatedBy("Sys");
                category3.setUpdatedAt(new Date());
                category3.setLevel(3);
                categoryDao.saveCategoryWithId(category3);
                // 主编码默认为id
                if (null == serialNo3) {
                    category3.setSerialNo(category3.getId());
                    categoryDao.updateById(category3);
                }
            } else {
                category3.setSerialNo(null == serialNo3 ? category3.getId() : serialNo3)
                        .setIsSplitContract(null == isSplitContract ? 0 : isSplitContract)
                        .setIsSoybean2(null == isSplitContract ? 0 : isSplitContract)
                        .setIsDce(null == isDce ? 0 : isDce)
                        .setFutureCode(StringUtils.isBlank(futureCode) ? "" : futureCode)
                        .setStatus(null == status ? DisableStatusEnum.ENABLE.getValue() : status)
                        .setUpdatedBy("Sys")
                        .setUpdatedAt(new Date())
                        .setLevel(3);
                categoryDao.updateById(category3);
            }
        }
    }

    /**
     * 规格
     *
     * @param excel
     */
    private void processAttribute(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("规格")) {
            return;
        }
        log.info("货品主数据导入==========================：4、导入规格");
        for (Map map : this.getSheetList(excel, "规格")) {
            String name = MapUtil.getString(map, "name");
            String displayName = MapUtil.getString(map, "displayName");
            Integer sort = MapUtil.getInt(map, "sort");
            Integer attributeId = MapUtil.getInt(map, "attributeId");
            Integer type = getInteger(map, "type", null);
            String description = MapUtil.getString(map, "description");
            AttributeEntity entity = attributeDao.getAttributeByNameAndType(name, type);
            if (null != attributeId) {
                entity = attributeDao.getAttributeById(attributeId);
            }
            if (entity == null) {
                entity = new AttributeEntity();
                entity.setName(name);
                entity.setDisplayName(displayName);
//                entity.setCategoryId(0);
                entity.setSort(sort);
                entity.setType(type);
                entity.setCanSearch(1);
                entity.setDescription(description);
                entity.setIsDeleted(0);
                entity.setCreatedBy("Sys");
                entity.setCreatedAt(new Date());
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                attributeDao.save(entity);
            } else {
                entity.setName(name)
                        .setDisplayName(displayName)
                        .setSort(sort);
                entity.setType(type);
                entity.setDescription(description);
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                attributeDao.updateById(entity);
            }
        }
    }

    /**
     * 规格值
     *
     * @param excel
     */
    private void processAttributeValue(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("规格值")) {
            return;
        }
        log.info("货品主数据导入==========================：5、导入规格值");
        for (Map map : this.getSheetList(excel, "规格值")) {
            String attributeName = MapUtil.getString(map, "attributeName");
            String name = MapUtil.getString(map, "name");
            if (StringUtil.isNullBlank(attributeName)) {
                continue;
            }
            Integer sort = MapUtil.getInt(map, "sort");
            String memo = MapUtil.getString(map, "memo");
            String mdmId = MapUtil.getString(map, "mdmId");
            AttributeEntity attribute = attributeDao.getAttributeByNameAndType(attributeName, null);
            AttributeValueEntity entity = attributeValueDao.getByAttributeAndValue(attribute.getId(), name);
            if (entity == null) {
                entity = new AttributeValueEntity()
                        .setAttributeId(attribute.getId())
                        .setName(name)
                        .setSort(sort)
                        .setMemo(memo)
                        .setMdmPackageCode(mdmId)
                        .setIsDeleted(0)
                        .setCreatedBy("1")
                        .setCreatedAt(new Date())
                        .setUpdatedBy("1")
                        .setUpdatedAt(new Date());
                attributeValueDao.save(entity);
            } else {
                entity.setMdmPackageCode(mdmId)
                        .setIsDeleted(0)
                        .setSort(sort)
                        .setUpdatedBy("1")
                        .setUpdatedAt(new Date());
                attributeValueDao.updateById(entity);
            }
        }
    }

    /**
     * 品种规格
     *
     * @param excel
     */
    private void processCategoryAttribute(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("品种规格")) {
            return;
        }
        log.info("货品主数据导入==========================：6、导入品种规格");
        for (Map map : this.getSheetList(excel, "品种规格")) {
            List<String> categoryList = MapUtil.getStringList(map, "categoryName", SEPARATOR);
            List<String> attributeList = MapUtil.getStringList(map, "attributeName", SEPARATOR);
            Integer type = Integer.parseInt(attributeList.get(0).split("#")[0]);
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(categoryList.get(0), 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(categoryList.get(1), null, category1.getSerialNo());
            CategoryEntity category3 = categoryDao.getCategoryByNameAndLevel(categoryList.get(2), null, category2.getSerialNo());
            AttributeEntity attribute = attributeDao.getAttributeByNameAndType(attributeList.get(1), type);
            CategoryAttributeEntity entity = categoryAttributeDao.getAttributeByCategoryAndType(category1.getSerialNo(), category2.getSerialNo(), category3.getSerialNo(), attribute.getId());
            int sort = 1;
            if (AttributeType.SALE.equals(type)) {
                sort = 100;
            } else if (AttributeType.PACKAGE.equals(type)) {
                sort = 200;
            }
            List<CategoryAttributeEntity> tempList = categoryAttributeDao.list(CategoryAttributeEntity.lqw()
                    .eq(CategoryAttributeEntity::getCategory1, category1.getSerialNo())
                    .eq(CategoryAttributeEntity::getCategory2, category2.getSerialNo())
                    .eq(CategoryAttributeEntity::getCategory3, category3.getSerialNo())
                    .eq(CategoryAttributeEntity::getAttributeType, type)
                    .orderByDesc(CategoryAttributeEntity::getSort)
            );
            if (CollUtil.isNotEmpty(tempList)) {
                CategoryAttributeEntity temp = tempList.get(0);
                sort = temp.getSort() == null ? sort : sort + 1;
            }
            if (entity == null) {
                entity = new CategoryAttributeEntity();
                entity.setCategory1(category1.getSerialNo());
                entity.setCategory2(category2.getSerialNo());
                entity.setCategory3(category3.getSerialNo());
                entity.setAttributeId(attribute.getId());
                entity.setAttributeName(attribute.getName());
                entity.setAttributeType(attribute.getType());
                entity.setSort(sort);
                entity.setIsDeleted(0);
                entity.setCreatedBy("Sys");
                entity.setCreatedAt(new Date());
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                categoryAttributeDao.save(entity);
            } else {
                if (StringUtil.isNullBlank(entity.getSort())) {
                    entity.setSort(sort);
                }
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                categoryAttributeDao.updateById(entity);
            }
        }
    }

    /**
     * 商品SPU
     *
     * @param excel
     */
    private void processSpu(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("商品SPU")) {
            return;
        }
        log.info("货品主数据导入==========================：7、导入商品SPU");
        for (Map map : this.getSheetList(excel, "商品SPU")) {
            List<String> categoryList = MapUtil.getStringList(map, "categoryName", SEPARATOR);
            List<String> attributeValueList = new ArrayList<>();
            attributeValueList.add(MapUtil.getString(map, "attributeValue1"));
            attributeValueList.add(MapUtil.getString(map, "attributeValue2"));
            attributeValueList.add(MapUtil.getString(map, "attributeValue3"));
            attributeValueList.add(MapUtil.getString(map, "attributeValue4"));
            Integer status = getInteger(map, "status", 0);
            // spuNo
            List<Integer> spuNoList = new ArrayList<>();
            // spuName
            List<String> spuNameList = new ArrayList<>();
            // 关键规格
            List<Map<String, Object>> keyAttributeValues = new ArrayList<>();
            // 品类品种
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(categoryList.get(0), 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(categoryList.get(1), null, category1.getSerialNo());
            CategoryEntity category3 = categoryDao.getCategoryByNameAndLevel(categoryList.get(2), null, category2.getSerialNo());
            spuNoList.add(category3.getSerialNo());
            spuNameList.add(category3.getName());

            // 关键规格
            for (String str : attributeValueList) {
                if (StringUtil.isNullBlank(str)) {
                    continue;
                }
                // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
                Map<String, Object> keyMap = new HashMap<>(16);
                List<String> itemList = StringUtil.split(str, SEPARATOR);
                AttributeEntity attribute = attributeDao.getAttributeByNameAndType(itemList.get(0).trim(), AttributeType.KEY.getValue());
                keyMap.put("attributeId", attribute.getId());
                keyMap.put("attributeName", attribute.getName());
                keyMap.put("attributeType", attribute.getType());
                //如果关键规格是其他 则为空
                String attributeValueStr = " ";
                if (itemList.size() > 1) {
                    attributeValueStr = itemList.get(1);
                    if (StringUtils.isNotBlank(attributeValueStr) && !"其他".equals(attribute.getName())) {
                        attributeValueStr = attributeValueStr.trim();
                    }
                }
                AttributeValueEntity attributeValue = attributeValueDao.getByAttributeAndValue(attribute.getId(), attributeValueStr);
                spuNoList.add(attributeValue.getId());
                spuNameList.add(attributeValue.getName());
                keyMap.put("attributeValueId", attributeValue.getId());
                keyMap.put("attributeValueName", attributeValue.getName());
                //}
                keyAttributeValues.add(keyMap);
            }
            log.info("SPU导入的关键规格信息：：{}", FastJsonUtils.getBeanToJson(keyAttributeValues));
            // spuNo
            String spuNo = CollUtil.join(spuNoList, "_");
            // spuName
            String spuName = CollUtil.join(spuNameList, ",") + ";";
            // 商品SPU
            SpuEntity entity = spuDao.getSpuBySpuNo(spuNo);
            if (entity == null) {
                entity = new SpuEntity();
                entity.setCategory1(category1.getSerialNo());
                entity.setCategory2(category2.getSerialNo());
                entity.setCategory3(category3.getSerialNo());
                entity.setSpuNo(spuNo);
                entity.setSpuName(spuName);
                entity.setKeyAttributeValues(FastJsonUtils.getBeanToJson(keyAttributeValues));
                entity.setStatus(status);
                entity.setNavSpuId(sequenceFacade.generate(SequenceEnum.NAV_SPU_ID.getValue(), 5));
                entity.setIsDeleted(0);
                entity.setCreatedBy("Sys");
                entity.setCreatedAt(new Date());
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                spuDao.save(entity);
            } else {
                entity.setKeyAttributeValues(FastJsonUtils.getBeanToJson(keyAttributeValues));
                entity.setStatus(status);
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                spuDao.updateById(entity);
            }

            // SPU规格值
            SpuDTO spuDTO = BeanUtil.copyProperties(entity, SpuDTO.class);
            for (Map<String, Object> item : keyAttributeValues) {
                spuAttributeValueDao.saveSpuAttributeValue(spuDTO, (Integer) item.get("attributeId"), (String) item.get("attributeName"), (Integer) item.get("attributeValueId"), (String) item.get("attributeValueName"));
            }
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end
        }
    }

    /**
     * 货品SKU
     *
     * @param excel
     */
    private void processSku(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("货品SKU")) {
            return;
        }
        log.info("货品主数据导入==========================：8、导入货品SKU");
        for (Map map : this.getSheetList(excel, "货品SKU")) {
            List<String> spuList = MapUtil.getStringList(map, "spu", SEPARATOR);
            log.info(spuList.size() + "");
            log.info(spuList.toString());
            List<String> packageAttributeList = MapUtil.getStringList(map, "packageAttribute", SEPARATOR);
            Integer status = MapUtil.getInt(map, "status");
            Integer goodsId = MapUtil.getInt(map, "goodsId");
            String nickName = MapUtil.getString(map, "nickName");
//            List<String> nickNameList = new ArrayList<>();
//            nickNameList.add(MapUtil.getString(map, "nickName1"));
//            nickNameList.add(MapUtil.getString(map, "nickName2"));
//            nickNameList.add(MapUtil.getString(map, "nickName3"));
//            String nickName = CollUtil.join(CollUtil.removeBlank(nickNameList), SEPARATOR);
            BigDecimal taxRate = MapUtil.getBigDecimal(map, "taxRate");
            String linkageGoodsCode = MapUtil.getString(map, "linkageGoodsCode");
            String linkageGoodsName = MapUtil.getString(map, "linkageGoodsName");
            String mdmId1 = MapUtil.getString(map, "mdmId1");
            String mdmId2 = MapUtil.getString(map, "mdmId2");
            String mdmId3 = MapUtil.getString(map, "mdmId3");
            Integer isGlobalOil = getInteger(map, "isGlobalOil", 0);
            Integer isDelivery = getInteger(map, "isDelivery", 0);
            Integer isWarrant = getInteger(map, "isWarrant", 0);
            Integer isTtAttribute = getInteger(map, "isTtAttribute", 0);
            // spuNo
            List<Integer> spuNoList = new ArrayList<>();
            // 品类品种
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(spuList.get(0).trim(), 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(spuList.get(1).trim(), null, category1.getSerialNo());
            CategoryEntity category3 = categoryDao.getCategoryByNameAndLevel(spuList.get(2).trim(), null, category2.getSerialNo());
            spuNoList.add(category3.getSerialNo());
            List<String[]> attributeValueList = new ArrayList<>();
            // 关键规格-关键规格值
            attributeValueList.add(new String[]{spuList.get(3), spuList.get(4)});
            if (spuList.size() > 5) {
                attributeValueList.add(new String[]{spuList.get(5), spuList.get(6)});
            }
            if (spuList.size() > 7) {
                attributeValueList.add(new String[]{spuList.get(7), spuList.get(8)});
            }
            if (spuList.size() > 9) {
                attributeValueList.add(new String[]{spuList.get(9), spuList.get(10)});
            }
            log.info("sku货品导入关键规格信息============================={}", FastJsonUtils.getBeanToJson(attributeValueList));
            for (String[] strings : attributeValueList) {
                if (StringUtil.isNullBlank(strings[0]) || StringUtil.isNullBlank(strings[1])) {
                    continue;
                }
                AttributeEntity attribute = attributeDao.getAttributeByNameAndType(strings[0], AttributeType.KEY.getValue());
                AttributeValueEntity attributeValue = attributeValueDao.getByAttributeAndValue(attribute.getId(), strings[1]);
                spuNoList.add(attributeValue.getId());
            }

            // 包装规格
            AttributeEntity packageAttribute = attributeDao.getAttributeByNameAndType(packageAttributeList.get(0), AttributeType.PACKAGE.getValue());
            AttributeValueEntity packageAttributeValue = attributeValueDao.getByAttributeAndValue(packageAttribute.getId(), packageAttributeList.get(1));
            // spuNo
            String spuNo = CollUtil.join(spuNoList, "_");
            SpuEntity spuEntity = spuDao.getSpuBySpuNo(spuNo);
            String skuNo = spuEntity.getSpuNo() + "_" + packageAttributeValue.getId();
            String skuName = spuEntity.getSpuName() + packageAttributeValue.getName();
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(spuEntity.getKeyAttributeValues());
            Map<String, Object> packageMap = new HashMap<>();
            packageMap.put("attributeId", packageAttribute.getId());
            packageMap.put("attributeName", packageAttribute.getName());
            packageMap.put("attributeType", packageAttribute.getType());
            packageMap.put("attributeValueId", packageAttributeValue.getId());
            packageMap.put("attributeValueName", packageAttributeValue.getName());
            keyAttributeValues.add(packageMap);
            // 货品SKU
            SkuEntity entity = skuDao.getSkuBySkuNo(skuNo);
            if (null != goodsId) {
                entity = skuDao.getSkuById(goodsId);
            }
            if (StringUtil.isNullBlank(nickName)) {
                nickName = skuName;
            }
            if (entity == null && null == goodsId) {
                entity = new SkuEntity();
                entity.setCategory1(category1.getSerialNo());
                entity.setCategory2(category2.getSerialNo());
                entity.setCategory3(category3.getSerialNo());
                entity.setCategoryId(entity.getCategory2());
                entity.setSpuId(spuEntity.getId());
                entity.setSpuName(spuEntity.getSpuName());
                entity.setName(skuName)
                        .setFullName(skuName)
                        .setNickName(nickName)
                        .setSkuNo(skuNo);
                entity.setTaxRate(null == taxRate ? new BigDecimal(9) : taxRate.multiply(new BigDecimal(100)));
                entity.setLinkageGoodsCode(linkageGoodsCode);
                entity.setLinkageGoodsName(linkageGoodsName);
                entity.setStatus(null == status ? DisableStatusEnum.DISABLE.getValue() : status);
                entity.setIsDelivery(null == isDelivery ? 0 : isDelivery);
                entity.setIsGlobalOil(isGlobalOil);
                entity.setIsWarrant(null == isWarrant ? 0 : isWarrant);
                entity.setIsTtAttribute(null == isTtAttribute ? 0 : isTtAttribute);
                entity.setKeyAttributeValues(JSON.toJSONString(keyAttributeValues));
                entity.setNavSkuId(sequenceFacade.generate(SequenceEnum.NAV_SKU_ID.getValue(), 5));
                entity.setIsDeleted(0);
                entity.setCreatedAt(new Date());
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                entity.setPackageId(packageAttributeValue.getId());
                skuDao.save(entity);
            } else {
                entity.setCategoryId(entity.getCategory2());
                entity.setSkuNo(skuNo)
                        .setPackageId(packageAttributeValue.getId())
                        .setCategory1(category1.getSerialNo())
                        .setCategory2(category2.getSerialNo())
                        .setCategory3(category3.getSerialNo())
                        .setNickName(nickName)
                        .setFullName(skuName)
                        .setTaxRate(null == taxRate ? new BigDecimal(9) : taxRate.multiply(new BigDecimal(100)))
                        .setLinkageGoodsCode(StringUtils.isNotBlank(entity.getLinkageGoodsCode()) ? entity.getLinkageGoodsCode() : linkageGoodsCode)
                        .setLinkageGoodsName(StringUtils.isNotBlank(entity.getLinkageGoodsName()) ? entity.getLinkageGoodsName() : linkageGoodsName)
                        .setStatus(null == status ? DisableStatusEnum.DISABLE.getValue() : status)
                        .setIsDelivery(null == isDelivery ? 0 : isDelivery)
                        .setIsGlobalOil(isGlobalOil)
                        .setIsWarrant(null == isWarrant ? 0 : isWarrant)
                        .setIsTtAttribute(null == isTtAttribute ? 0 : isTtAttribute)
                        .setKeyAttributeValues(JSON.toJSONString(keyAttributeValues))
                        .setUpdatedBy("Sys")
                        .setUpdatedAt(new Date());

                skuDao.updateById(entity);
            }

            // SKU规格值
            SkuAddDTO skuAddDTO = BeanUtil.copyProperties(entity, SkuAddDTO.class);
            for (int i = 0; i < keyAttributeValues.size(); i++) {
                Map<String, Object> item = keyAttributeValues.get(i);
                skuAttributeValueDao.saveSkuAttributeValue(skuAddDTO, item);
            }

            // SKU MDM
            skuAddDTO.setSkuMdmList(new ArrayList<>());
            if (StringUtil.isNotNullBlank(mdmId1)) {
                skuAddDTO.getSkuMdmList().add(new SkuMdmEntity().setType(MdmType.SPOT.getValue()).setMdmId(mdmId1));
            }
            if (StringUtil.isNotNullBlank(mdmId2)) {
                skuAddDTO.getSkuMdmList().add(new SkuMdmEntity().setType(MdmType.WARRANT.getValue()).setMdmId(mdmId2));
            }
            if (StringUtil.isNotNullBlank(mdmId3)) {
                skuAddDTO.getSkuMdmList().add(new SkuMdmEntity().setType(MdmType.SOYBEAN2.getValue()).setMdmId(mdmId3));
            }
            skuMdmDao.saveSkuMdm(entity.getId(), skuAddDTO.getSkuMdmList());
        }
    }


    // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
    /**
     * 货品SKU
     *
     * @param excel
     */
    private void processSkuAttribute(File excel, String errorMsg, List<String> rangeList) {
        if (!CollectionUtils.isEmpty(rangeList) && !rangeList.contains("货品SKU规格")) {
            return;
        }
        log.info("货品主数据导入==========================：8、导入货品SKU");
        for (Map map : this.getSheetList(excel, "货品SKU")) {
            List<String> spuList = MapUtil.getStringList(map, "spu", SEPARATOR);
            log.info(spuList.size() + "");
            log.info(spuList.toString());
            List<String> packageAttributeList = MapUtil.getStringList(map, "packageAttribute", SEPARATOR);
            Integer goodsId = MapUtil.getInt(map, "goodsId");
            String nickName = MapUtil.getString(map, "nickName");
//            List<String> nickNameList = new ArrayList<>();
//            nickNameList.add(MapUtil.getString(map, "nickName1"));
//            nickNameList.add(MapUtil.getString(map, "nickName2"));
//            nickNameList.add(MapUtil.getString(map, "nickName3"));
//            String nickName = CollUtil.join(CollUtil.removeBlank(nickNameList), SEPARATOR);
            // spuNo
            List<Integer> spuNoList = new ArrayList<>();
            // 品类品种
            CategoryEntity category1 = categoryDao.getCategoryByNameAndLevel(spuList.get(0).trim(), 1, null);
            CategoryEntity category2 = categoryDao.getCategoryByNameAndLevel(spuList.get(1).trim(), null, category1.getSerialNo());
            CategoryEntity category3 = categoryDao.getCategoryByNameAndLevel(spuList.get(2).trim(), null, category2.getSerialNo());
            spuNoList.add(category3.getSerialNo());
            List<String[]> attributeValueList = new ArrayList<>();
            // 关键规格-关键规格值
            attributeValueList.add(new String[]{spuList.get(3), spuList.get(4)});
            if (spuList.size() > 5) {
                attributeValueList.add(new String[]{spuList.get(5), spuList.get(6)});
            }
            if (spuList.size() > 7) {
                attributeValueList.add(new String[]{spuList.get(7), spuList.get(8)});
            }
            if (spuList.size() > 9) {
                attributeValueList.add(new String[]{spuList.get(9), spuList.get(10)});
            }
            log.info("sku货品导入关键规格信息============================={}", FastJsonUtils.getBeanToJson(attributeValueList));
            for (String[] strings : attributeValueList) {
                if (StringUtil.isNullBlank(strings[0])) {
                    continue;
                }
                AttributeEntity attribute = attributeDao.getAttributeByNameAndType(strings[0], AttributeType.KEY.getValue());
                AttributeValueEntity attributeValue = attributeValueDao.getByAttributeAndValue(attribute.getId(), strings[1]);
                spuNoList.add(attributeValue.getId());
            }

            // 包装规格
            AttributeEntity packageAttribute = attributeDao.getAttributeByNameAndType(packageAttributeList.get(0), AttributeType.PACKAGE.getValue());
            AttributeValueEntity packageAttributeValue = attributeValueDao.getByAttributeAndValue(packageAttribute.getId(), packageAttributeList.get(1));
            // spuNo
            String spuNo = CollUtil.join(spuNoList, "_");
            SpuEntity spuEntity = spuDao.getSpuBySpuNo(spuNo);
            String skuNo = spuEntity.getSpuNo() + "_" + packageAttributeValue.getId();
            String skuName = spuEntity.getSpuName() + packageAttributeValue.getName();
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(spuEntity.getKeyAttributeValues());
            Map<String, Object> packageMap = new HashMap<>();
            packageMap.put("attributeId", packageAttribute.getId());
            packageMap.put("attributeName", packageAttribute.getName());
            packageMap.put("attributeType", packageAttribute.getType());
            packageMap.put("attributeValueId", packageAttributeValue.getId());
            packageMap.put("attributeValueName", packageAttributeValue.getName());
            keyAttributeValues.add(packageMap);
            // 货品SKU
            SkuEntity entity = skuDao.getSkuBySkuNo(skuNo);
            if (null != goodsId) {
                entity = skuDao.getSkuById(goodsId);
            }
            if (StringUtil.isNullBlank(nickName)) {
                nickName = skuName;
            }
            if (entity == null && null == goodsId) {
                entity = new SkuEntity();
                entity.setCategory1(category1.getSerialNo());
                entity.setCategory2(category2.getSerialNo());
                entity.setCategory3(category3.getSerialNo());
                entity.setCategoryId(entity.getCategory2());
                entity.setSpuId(spuEntity.getId());
                entity.setSpuName(spuEntity.getSpuName());
                entity.setName(skuName)
                        .setFullName(skuName)
                        .setNickName(nickName)
                        .setSkuNo(skuNo);
                entity.setKeyAttributeValues(JSON.toJSONString(keyAttributeValues));
                entity.setNavSkuId(sequenceFacade.generate(SequenceEnum.NAV_SKU_ID.getValue(), 5));
                entity.setIsDeleted(0);
                entity.setCreatedAt(new Date());
                entity.setUpdatedBy("Sys");
                entity.setUpdatedAt(new Date());
                entity.setPackageId(packageAttributeValue.getId());
                skuDao.save(entity);
            } else {
                entity.setCategoryId(entity.getCategory2())
                        .setKeyAttributeValues(JSON.toJSONString(keyAttributeValues))
                        .setUpdatedBy("Sys")
                        .setUpdatedAt(new Date());
                skuDao.updateById(entity);
            }
            // SKU规格值
            SkuAddDTO skuAddDTO = BeanUtil.copyProperties(entity, SkuAddDTO.class);
            for (int i = 0; i < keyAttributeValues.size(); i++) {
                Map<String, Object> item = keyAttributeValues.get(i);
                skuAttributeValueDao.saveSkuAttributeValue(skuAddDTO, item);
            }
        }
    }
    // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end


    /**
     * 获取数据
     *
     * @param excel
     * @param sheetName
     * @return
     */
    private List<Map> getSheetList(File excel, String sheetName) {
        ExcelReader excelReader = null;
        try {
            log.info("{}===》开始。", sheetName);
            excelReader = ExcelUtil.getReader(excel, sheetName);
            List<Map> list = excelReader.read(1, 2, Map.class);
            list.forEach(item -> log.info(item.toString()));
            log.info("{}===》完成。", sheetName);
            return list;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new BusinessException("导入错误！错误信息：" + e.getMessage());
        } finally {
            if (excelReader != null) {
                excelReader.close();
            }
        }
    }

    /**
     * 获取值
     *
     * @param map
     * @param key
     * @param defaultValue
     * @return
     */
    private Integer getInteger(Map map, String key, Integer defaultValue) {
        String value = MapUtil.getString(map, key);
        if (StringUtil.isNullBlank(value)) {
            return defaultValue;
        }
        return Integer.parseInt(value.split("#")[0]);
    }
}
