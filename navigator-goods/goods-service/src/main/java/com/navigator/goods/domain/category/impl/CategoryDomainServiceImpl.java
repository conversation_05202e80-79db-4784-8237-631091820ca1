package com.navigator.goods.domain.category.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.StringUtil;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.entity.TradingConfigEntity;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.domain.category.IAttributeDomainService;
import com.navigator.goods.domain.category.ICategoryAttributeDomainService;
import com.navigator.goods.domain.category.ICategoryDomainService;
import com.navigator.goods.mapper.CategoryMapper;
import com.navigator.goods.pojo.dto.CategoryAddDTO;
import com.navigator.goods.pojo.dto.CategoryAttributeDTO;
import com.navigator.goods.pojo.dto.CategoryDTO;
import com.navigator.goods.pojo.dto.CategoryUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 品类品种 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Slf4j
@Service
public class CategoryDomainServiceImpl extends ServiceImpl<CategoryMapper, CategoryEntity> implements ICategoryDomainService {

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private IAttributeDomainService attributeDomainService;

    @Resource
    private ICategoryAttributeDomainService categoryAttributeDomainService;

    @Resource
    private TradingConfigFacade tradingConfigFacade;

    @Resource
    private RoleFacade roleFacade;

    @Resource
    private CRoleFacade cRoleFacade;

    @Resource
    private OperationLogFacade operationLogFacade;

    @Override
    public Page<CategoryDTO> queryCategoryDTOPage(QueryDTO<CategoryQO> queryDTO) {
        Page<CategoryEntity> page = categoryDao.queryCategoryPage(queryDTO);
        Page<CategoryDTO> result = new Page<>();
        result.setPages(page.getPages());
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        result.setRecords(this.toCategoryDTOList(page.getRecords(), queryDTO.getCondition().getIsDto()));
        return result;
    }


    @Override
    public List<CategoryDTO> queryCategoryDTOList(CategoryQO condition) {
        return this.toCategoryDTOList(categoryDao.queryCategoryList(condition), condition.getIsDto());
    }


    @Override
    public List<CategoryEntity> queryCategoryList(CategoryQO condition) {
        return categoryDao.queryCategoryList(condition);
    }


    @Override
    public List<Integer> queryCategoryIdList(CategoryQO condition) {
        List<CategoryEntity> list = categoryDao.queryCategoryList(condition);
        List<Integer> idList = new ArrayList<>();
        list.forEach(item -> idList.add(item.getId()));
        return idList;
    }

    @Override
    public List<Integer> queryCategorySerialNoList(CategoryQO condition) {
        List<CategoryEntity> list = categoryDao.queryCategoryList(condition);
        List<Integer> serialNoList = new ArrayList<>();
        list.forEach(item -> serialNoList.add(item.getSerialNo()));
        return serialNoList;
    }


    @Override
    public CategoryDTO getCategoryDTOById(Integer id) {
        return this.toCategoryDTO(null, categoryDao.getCategoryById(id), 1);
    }

    @Override
    public CategoryEntity getBasicCategoryBySerialNo(Integer serialNo) {
        return categoryDao.getCategoryBySerialNo(serialNo);
    }


    @Override
    public CategoryDTO getCategoryDTOBySerialNo(Integer serialNo) {
        return this.toCategoryDTO(null, categoryDao.getCategoryBySerialNo(serialNo), 1);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryEntity addCategory(CategoryAddDTO addDTO) {
        CategoryEntity categoryEntity2 = categoryDao.getCategoryById(addDTO.getParentId());
        CategoryEntity categoryEntity1 = categoryDao.getCategoryById(categoryEntity2.getParentId());
        CategoryEntity categoryEntity3 = categoryDao.addCategory(addDTO, categoryEntity2);
        // 关键规格
        List<AttributeEntity> keyAttributeEntityList = attributeDomainService.list(AttributeEntity.lqw(null, true)
                .eq(AttributeEntity::getType, AttributeType.KEY.getValue())
                .in(AttributeEntity::getId, addDTO.getKeyAttributeIdSet())
        );


        // 销售规格
        List<AttributeEntity> saleAttributeEntityList = null;
        if (CollUtil.isNotEmpty(addDTO.getSaleAttributeIdSet())) {
            saleAttributeEntityList = attributeDomainService.list(AttributeEntity.lqw(null, true)
                    .eq(AttributeEntity::getType, AttributeType.SALE.getValue())
                    .in(AttributeEntity::getId, addDTO.getSaleAttributeIdSet())
            );
        }

        // 包装规格
        AttributeEntity packageAttributeEntity = null;
        List<AttributeEntity> attributeEntityList = attributeDomainService.list(AttributeEntity.lqw(null, true)
                .eq(AttributeEntity::getType, AttributeType.PACKAGE.getValue())
                .eq(AttributeEntity::getId, addDTO.getPackageAttributeId())
        );
        packageAttributeEntity = CollectionUtils.isEmpty(attributeEntityList) ? null : attributeEntityList.get(0);
        categoryAttributeDomainService.addCategoryAttribute(categoryEntity1.getSerialNo(), categoryEntity2.getSerialNo(), categoryEntity3.getSerialNo(), keyAttributeEntityList, saleAttributeEntityList, packageAttributeEntity);
        //记录日志
        recordMagellanOperationDetail(JSON.toJSONString(addDTO), OperationActionEnum.ADD_GOODS_CATEGORY, categoryEntity3.getId());
        return categoryEntity3;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CategoryEntity updateCategory(CategoryUpdateDTO updateDTO) {
        //记录日志
        recordMagellanOperationDetail(JSON.toJSONString(updateDTO), OperationActionEnum.ADD_GOODS_CATEGORY, updateDTO.getId());
        return categoryDao.updateCategory(updateDTO);
    }


    @Override
    public List<Tree<Integer>> queryCategoryMenu(Integer systemId, Integer customerId) {
        Result<Boolean> isAdminResult;
        Result<LinkedHashSet<Integer>> category2ListResult = null;
        if (SystemEnum.MAGELLAN.equals(systemId)) {
            isAdminResult = roleFacade.isAdmin(null);
            if (!isAdminResult.getData()) {
                category2ListResult = roleFacade.queryCategory2List(null);
            }
        } else {
            isAdminResult = cRoleFacade.isAdmin(null, customerId);
            if (!isAdminResult.getData()) {
                category2ListResult = cRoleFacade.queryCategory2List(null, customerId);
            }
        }
        return categoryDao.queryCategoryMenu(isAdminResult.getData(), category2ListResult != null ? category2ListResult.getData() : null);
    }


    @Override
    public List<Tree<Integer>> queryCategoryTree() {
        return categoryDao.queryCategoryTree();
    }

    /**
     * 转DTO集合
     *
     * @param list
     * @param isDto
     * @return
     */
    private List<CategoryDTO> toCategoryDTOList(List<CategoryEntity> list, Integer isDto) {
        List<CategoryDTO> result = new ArrayList<>();
        if (CollUtil.isNotEmpty(list)) {
            Map<Integer, CategoryEntity> idNameMap = categoryDao.queryAllCategoryMap();
            list.forEach(item -> {
                result.add(toCategoryDTO(idNameMap, item, isDto));
            });
        }
        return result;
    }

    /**
     * 转DTO
     *
     * @param idNameMap
     * @param categoryEntity
     * @param isDto
     * @return
     */
    private CategoryDTO toCategoryDTO(Map<Integer, CategoryEntity> idNameMap, CategoryEntity categoryEntity, Integer isDto) {
        if (isDto == null) {
            isDto = 1;
        }
        if (idNameMap == null) {
            idNameMap = categoryDao.queryAllCategoryMap();
        }
        CategoryDTO categoryDTO = BeanUtil.copyProperties(categoryEntity, CategoryDTO.class);
        if (isDto == 1) {
            // 去期货代码-交易所关联表找
            if (StringUtil.isNotNullBlank(categoryEntity.getFutureCode())) {
                List<TradingConfigEntity> tradingConfigEntityList = tradingConfigFacade.getDomainTypeEntityByCategoryCode(categoryEntity.getFutureCode());
                if (CollUtil.isNotEmpty(tradingConfigEntityList)) {
                    categoryDTO.setExchange(tradingConfigEntityList.get(0).getExchange());
                }
            }
            // 关键规格
            categoryDTO.setKeyAttributeList(new ArrayList<>());
            // 销售规格
            categoryDTO.setSaleAttributeList(new ArrayList<>());
            List<CategoryAttributeDTO> categoryAttributeDTOList = categoryAttributeDomainService.queryCategoryAttributeList(new CategoryAttributeQO().setCategory3(categoryEntity.getId()));
            categoryAttributeDTOList.forEach(item -> {
                if (AttributeType.KEY.equals(item.getAttributeType())) {
                    categoryDTO.getKeyAttributeList().add(item);
                } else if (AttributeType.SALE.equals(item.getAttributeType())) {
                    categoryDTO.getSaleAttributeList().add(item);
                } else if (AttributeType.PACKAGE.equals(item.getAttributeType())) {
                    categoryDTO.setPackageAttribute(item);
                }
            });
        }
        // 品类品种
        if (categoryEntity.getLevel() == 1) {
            categoryDTO.setCategory1(categoryEntity.getSerialNo());
            categoryDTO.setCategoryName1(categoryEntity.getName());
        } else if (categoryEntity.getLevel() == 2) {
            categoryDTO.setCategory2(categoryEntity.getSerialNo());
            categoryDTO.setCategoryName2(categoryEntity.getName());
            CategoryEntity categoryEntity1 = idNameMap.get(categoryEntity.getParentId());
            categoryDTO.setCategory1(categoryEntity1.getSerialNo());
            categoryDTO.setCategoryName1(categoryEntity1.getName());
        } else if (categoryEntity.getLevel() == 3) {
            categoryDTO.setCategory3(categoryEntity.getSerialNo());
            categoryDTO.setCategoryName3(categoryEntity.getName());
            CategoryEntity categoryEntity2 = idNameMap.get(categoryEntity.getParentId());
            categoryDTO.setCategory2(categoryEntity2.getSerialNo());
            categoryDTO.setCategoryName2(categoryEntity2.getName());
            CategoryEntity categoryEntity1 = idNameMap.get(categoryEntity2.getParentId());
            categoryDTO.setCategory1(categoryEntity1.getSerialNo());
            categoryDTO.setCategoryName1(categoryEntity1.getName());
        }
        categoryDTO.setUpdatedBy(IdNameConverter.getName(IdNameType.user_id_name, categoryDTO.getUpdatedBy()));
        return categoryDTO;
    }

    @Override
    public List<String> queryFutureCodeList(Integer category2) {
        List<CategoryEntity> entityList = categoryDao.queryCategoryList(new CategoryQO().setParentSerialNo(category2));
        List<String> futureCodList = new ArrayList<>();
        entityList.forEach(entity -> {
            if (StringUtil.isNotBlank(entity.getFutureCode())) {
                futureCodList.add(entity.getFutureCode());
            }
        });
        return futureCodList;
    }

    @Override
    public CategoryEntity getParentCategoryBySerialNo(Integer serialNo) {
        CategoryEntity entity = categoryDao.getCategoryBySerialNo(serialNo);
        return categoryDao.getCategoryBySerialNo(entity.getParentId());
    }

    @Override
    public List<CategoryEntity> getCategoryNameBySerialNoList(List<Integer> serialNoList) {
        return categoryDao.getCategoryBySerialNoList(serialNoList);
    }

    @Override
    public CategoryEntity getCategoryByName(String name, Integer level) {
        return categoryDao.getCategoryByNameAndLevel(name, level, null);
    }

    @Override
    public List<CategoryEntity> getByCategoryNames(List<String> nameList, Integer level) {
        nameList = nameList.stream().map(String::trim).collect(Collectors.toList());
        return categoryDao.getByCategoryNamesAndLevel(nameList, level);
    }

    //记录系统日志
    private void recordMagellanOperationDetail(String dtoData, OperationActionEnum operationActionEnum, Integer referBizId) {
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(dtoData)
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(operationActionEnum)
                    .setReferBizId(referBizId)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}