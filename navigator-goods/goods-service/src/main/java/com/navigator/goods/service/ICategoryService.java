package com.navigator.goods.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.CategoryCreateDTO;
import com.navigator.goods.pojo.dto.CategorySearchDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;

/**
 * <p>
 * 品类信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface ICategoryService {
    CategoryEntity getCategoryByCode(String categoryCode);

    /**
     * 根据条件分页查询品种信息
     *
     * @param categorySearchDTO 品种查询条件
     * @return 查询的品种结果集合
     */
    Result queryCategoryList(QueryDTO<CategorySearchDTO> categorySearchDTO);

    /**
     * 更新品类信息
     *
     * @param categoryCreateDTO 品类信息
     * @return 更新结果
     */
    boolean updateCategory(CategoryCreateDTO categoryCreateDTO);

    /**
     * 禁用/启用品种
     *
     * @param categoryId 品种ID
     * @param status     品种状态
     * @return 禁用/启用结果
     */
    boolean invalidCategory(Integer categoryId, Integer status);

}
