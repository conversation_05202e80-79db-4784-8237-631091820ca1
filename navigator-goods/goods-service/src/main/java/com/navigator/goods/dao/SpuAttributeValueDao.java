package com.navigator.goods.dao;

import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.goods.mapper.SpuAttributeValueMapper;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.entity.SpuAttributeValueEntity;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * SPU的关键规格信息 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Dao
public class SpuAttributeValueDao extends BaseDaoImpl<SpuAttributeValueMapper, SpuAttributeValueEntity> {
    /**
     * 根据ID：获取SPU的关键规格信息
     *
     * @param id
     * @return
     */
    public SpuAttributeValueEntity getSpuAttributeValueById(Integer id) {
        return this.getById(id);
    }

    /**
     * 保存
     *
     * @param spuDTO
     * @param attributeId
     * @param attributeValueId
     * @param attributeValueName
     */
    // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
    public void saveSpuAttributeValue(SpuDTO spuDTO, Integer attributeId,String attributeName, Integer attributeValueId, String attributeValueName) {
        SpuAttributeValueEntity entity = null;
        List<SpuAttributeValueEntity> attributeValueEntityList = this.list(
                SpuAttributeValueEntity.lqw(null)
                        .eq(SpuAttributeValueEntity::getSpuId, spuDTO.getId())
                        .eq(SpuAttributeValueEntity::getAttributeId, attributeId)
                        .eq(SpuAttributeValueEntity::getAttributeValueId, attributeValueId)
        );
        if (CollectionUtils.isEmpty(attributeValueEntityList)) {
            entity = new SpuAttributeValueEntity();
            entity.setSpuId(spuDTO.getId());
            entity.setCategory1(spuDTO.getCategory1());
            entity.setCategory2(spuDTO.getCategory2());
            entity.setCategory3(spuDTO.getCategory3());
            entity.setAttributeId(attributeId);
            entity.setAttributeName(attributeName);
            entity.setAttributeValueId(attributeValueId);
            entity.setAttributeValueName(attributeValueName);
            entity.setIsDeleted(0);
            entity.setCreatedBy(JwtUtils.getCurrentUserId());
            entity.setCreatedAt(new Date());
            entity.setUpdatedBy(JwtUtils.getCurrentUserId());
            entity.setUpdatedAt(new Date());
            this.save(entity);
        } else {
            entity = attributeValueEntityList.get(0);
            entity.setAttributeId(attributeId);
            entity.setAttributeName(attributeName);
            // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end
            entity.setAttributeValueId(attributeValueId);
            entity.setAttributeValueName(attributeValueName);
            entity.setUpdatedBy(JwtUtils.getCurrentUserId());
            entity.setUpdatedAt(new Date());
            this.updateById(entity);
        }
    }
}
