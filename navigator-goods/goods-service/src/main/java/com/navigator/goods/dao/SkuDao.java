package com.navigator.goods.dao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.PageUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.mapper.SkuMapper;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.enums.MdmType;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * SKU货品 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Dao
public class SkuDao extends BaseDaoImpl<SkuMapper, SkuEntity> {

    @Resource
    private SkuMdmDao skuMdmDao;

    @Resource
    private CategoryDao categoryDao;

    /**
     * 根据条件：获取SKU货品分页
     *
     * @param queryDTO
     * @return
     */
    public Page<SkuEntity> querySkuPage(QueryDTO<SkuQO> queryDTO) {
        LambdaQueryWrapper<SkuEntity> lqw = this.getLqw(queryDTO.getCondition());
        if (lqw == null) {
            IPage<SkuEntity> page = new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize());
            return PageUtil.convertPage(page);
        }
        return PageUtil.convertPage(this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), lqw));
    }

    public List<SkuEntity> getSkuListByIds(List<Integer> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<SkuEntity>lambdaQuery()
                .in(!CollectionUtils.isEmpty(skuIdList), SkuEntity::getId, skuIdList)
                .eq(SkuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据条件：获取SKU货品列表
     *
     * @param condition
     * @return
     */
    public List<SkuEntity> querySkuList(SkuQO condition) {
        LambdaQueryWrapper<SkuEntity> lqw = this.getLqw(condition);
        if (lqw == null) {
            return new ArrayList<>();
        }
        return this.list(lqw);
    }

    /**
     * 根据ID：获取SKU货品
     *
     * @param id
     * @return
     */
    public SkuEntity getSkuById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据全称：获取SKU货品
     *
     * @param fullName
     * @return
     */
    public SkuEntity getSkuByFullName(String fullName) {
        List<SkuEntity> list = this.list(SkuEntity.lqw(null, true).eq(SkuEntity::getFullName, fullName).eq(SkuEntity::getStatus, 1));
        if (CollUtil.isNotEmpty(list)) {
            if (list.size() > 1) {
                throw new BusinessException("存在多条相同名称的货品");
            }
            return list.get(0);
        }
        return null;
    }

    /**
     * 根据编码：获取SKU货品
     *
     * @param skuNo
     * @return
     */
    public SkuEntity getSkuBySkuNo(String skuNo) {
        List<SkuEntity> skuEntityList = this.list(SkuEntity.lqw(null, true).eq(SkuEntity::getSkuNo, skuNo));
        return CollectionUtils.isEmpty(skuEntityList) ? null : skuEntityList.get(0);
    }

    /**
     * 查询不可提货的SKU货品ID
     *
     * @return
     */
    public List<Integer> queryCannotDeliverySkuIdList() {
        // 禁用 或者 未启用 或者 已删除
        List<SkuEntity> list = this.list(SkuEntity.lqw(null, true)
                .eq(SkuEntity::getStatus, DisableStatusEnum.DISABLE.getValue())
                .or()
                .eq(SkuEntity::getIsDelivery, DisableStatusEnum.DISABLE.getValue()));
        List<Integer> idList = new ArrayList<>();
        list.forEach(item -> idList.add(item.getId()));
        return idList;
    }

    /**
     * 获取查询条件
     *
     * @param condition
     * @return
     */
    public LambdaQueryWrapper<SkuEntity> getLqw(SkuQO condition) {
        LambdaQueryWrapper<SkuEntity> lqw = SkuEntity.lqw(condition, true);
        if (StringUtil.isNotNullBlank(condition.getName())) {
            lqw.and(StrUtil.isNotBlank(condition.getName()),
                    bizWrapper -> bizWrapper.like(SkuEntity::getName, condition.getName().trim())
                            .or().like(SkuEntity::getFullName, condition.getName().trim())
                            .or().like(SkuEntity::getNickName, condition.getName().trim()));

        }
        if (StringUtil.isNotNullBlank(condition.getMdmId())) {
            List<Integer> skuIdList = skuMdmDao.querySkuIdList(new SkuMdmQO().setType(MdmType.SPOT.getValue()).setMdmId(condition.getMdmId()));
            if (CollUtil.isNotEmpty(skuIdList)) {
                lqw.in(SkuEntity::getId, skuIdList);
            } else {
                // 查询不到应返回空
                return null;
            }
        }
        if (StringUtil.isNotNullBlank(condition.getFutureCode())) {
            List<Integer> serialNoList = categoryDao.querySerialNoList(new CategoryQO().setIsDce(1).setLevel(3).setFutureCode(condition.getFutureCode()));
            if (CollUtil.isNotEmpty(serialNoList)) {
                lqw.in(CollUtil.isNotEmpty(serialNoList), SkuEntity::getCategory3, serialNoList);
            } else {
                // 查询不到应返回空
                return null;
            }
        }
        return lqw;
    }
}
