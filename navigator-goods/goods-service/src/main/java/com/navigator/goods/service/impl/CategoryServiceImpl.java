package com.navigator.goods.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.InvoiceTypeDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.dao.CategoryPriceDao;
import com.navigator.goods.pojo.dto.CategoryCreateDTO;
import com.navigator.goods.pojo.dto.CategorySearchDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.CategoryPriceEntity;
import com.navigator.goods.service.ICategoryService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 品类信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class CategoryServiceImpl implements ICategoryService {

    @Resource
    private CategoryDao categoryDao;
    @Resource
    private CategoryPriceDao categoryPriceDao;
    @Resource
    private SystemRuleFacade systemRuleFacade;

    @Override
    public CategoryEntity getCategoryByCode(String categoryCode) {
        return categoryDao.getCategoryByCode(categoryCode);
    }

    @Override
    public Result queryCategoryList(QueryDTO<CategorySearchDTO> categorySearchDTO) {
        IPage<CategoryEntity> categoryIpage = categoryDao.queryCategoryListOld(categorySearchDTO);
        List<CategoryCreateDTO> categoryCreateDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(categoryIpage.getRecords())) {
            categoryCreateDTOList = categoryIpage.getRecords().stream().map(categoryEntity -> {
                        CategoryCreateDTO categoryCreateDTO = BeanConvertUtils.convert(CategoryCreateDTO.class, categoryEntity);
                        if (categoryEntity.getParentId() != 0) {
                            CategoryEntity parentCategory = categoryDao.getById(categoryEntity.getParentId());
                            categoryCreateDTO.setParentCategoryName(parentCategory.getName());
                        }
                        List<CategoryPriceEntity> categoryPriceEntityList = categoryPriceDao.getPriceByCategoryId(categoryEntity.getId());
                        if (!CollectionUtils.isEmpty(categoryPriceEntityList)) {
                            categoryCreateDTO.setCategoryPriceList(categoryPriceEntityList.stream().map(CategoryPriceEntity::getContractPriceType).collect(Collectors.toList()));
                        }
                        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(new SystemRuleDTO().setCategoryId(categoryEntity.getId())
                                .setRuleCode(SystemCodeConfigEnum.INVOICE_TYPE.getRuleCode()).setStatus(DisableStatusEnum.ENABLE.getValue()));
                        if (null != systemRuleVO && !CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList())) {
                            List<InvoiceTypeDTO> invoiceTypeDTOList = systemRuleVO.getSystemRuleItemVOList().stream()
                                    .map(ruleItemVO -> {
                                        return new InvoiceTypeDTO().setInvoiceType(Integer.valueOf(ruleItemVO.getRuleItemKey()))
                                                .setTaxRate(Integer.valueOf(ruleItemVO.getRuleItemValue()))
                                                .setId(ruleItemVO.getRuleItemId())
                                                .setLkgCode(ruleItemVO.getLkgCode())
                                                .setName(ruleItemVO.getMemo());
                                    })
                                    .collect(Collectors.toList());
                            categoryCreateDTO.setInvoiceTypeDTOList(invoiceTypeDTOList);
                        }
                        return categoryCreateDTO;
                    }
            ).collect(Collectors.toList());
//            categoryIpage.getRecords().forEach(categoryEntity -> {
//                if (categoryEntity.getParentId() != 0) {
//                    CategoryEntity parentCategory = categoryDao.getById(categoryEntity.getParentId());
//                    categoryEntity.setParentCategoryName(parentCategory.getName());
//                }
//                List<CategoryPriceEntity> categoryPriceEntityList = categoryPriceDao.getPriceByCategoryId(categoryEntity.getId());
//                if (!CollectionUtils.isEmpty(categoryPriceEntityList)) {
//                    categoryEntity.setCategoryPriceList(categoryPriceEntityList.stream().map(CategoryPriceEntity::getContractPriceType).collect(Collectors.toList()));
//                }
//            });
        }
        return Result.page(categoryIpage, categoryCreateDTOList);
    }

    @Override
    public boolean updateCategory(CategoryCreateDTO categoryCreateDTO) {
        CategoryEntity categoryInfoEntity = categoryDao.getById(categoryCreateDTO.getId());
        if (null == categoryInfoEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        categoryInfoEntity.setStatus(categoryCreateDTO.getStatus())
                .setLinkageCategoryCode(categoryCreateDTO.getLinkageCategoryCode())
                .setLinkageCategoryName(categoryCreateDTO.getLinkageCategoryName());
        boolean upResult = categoryDao.updateById(categoryInfoEntity);
        categoryPriceDao.bindCategoryPrice(categoryCreateDTO.getId(), categoryCreateDTO.getCategoryPriceList());
        systemRuleFacade.updateInvoiceType(categoryInfoEntity.getId(), categoryCreateDTO.getInvoiceTypeDTOList());
        return upResult;
    }

    @Override
    public boolean invalidCategory(Integer categoryId, Integer status) {
        CategoryEntity categoryEntity = categoryDao.getById(categoryId);
        if (null == categoryEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        categoryEntity.setStatus(status);
        return categoryDao.updateById(categoryEntity);
    }
}