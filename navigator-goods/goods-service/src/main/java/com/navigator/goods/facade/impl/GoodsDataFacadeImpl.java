package com.navigator.goods.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.goods.domain.data.IGoodsDataService;
import com.navigator.goods.facade.GoodsDataFacade;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品域数据处理 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@RestController
public class GoodsDataFacadeImpl implements GoodsDataFacade {

    @Resource
    private IGoodsDataService goodsDataService;

    @Override
    public Result doImport(MultipartFile file, Boolean processHistory, List<String> rangeList) {
        goodsDataService.doImport(file, processHistory,rangeList);
        return Result.success();
    }


}