package com.navigator.goods.dao;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.mapper.SkuMdmMapper;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * SKU-MDM DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Dao
public class SkuMdmDao extends BaseDaoImpl<SkuMdmMapper, SkuMdmEntity> {

    @Resource
    private SkuDao skuDao;

    /**
     * 根据ID：获取SKU-MDM
     *
     * @param id
     * @return
     */
    public SkuMdmEntity getSkuMdmById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据条件：获取SKU-MDM列表
     *
     * @param condition
     * @return
     */
    public List<SkuMdmEntity> querySkuMdmList(SkuMdmQO condition) {
        if (StringUtil.isNotNullBlank(condition.getCategory2()) && CollUtil.isEmpty(condition.getSkuIdList())) {
            List<SkuEntity> skuList = skuDao.querySkuList(new SkuQO().setCategory2(condition.getCategory2()).setStatus(1));
            List<Integer> skuIdList = new ArrayList<>();
            skuList.forEach(item -> skuIdList.add(item.getId()));
            condition.setSkuIdList(skuIdList);
        }
        return this.list(SkuMdmEntity.lqw(condition));
    }

    /**
     * 根据条件：获取SKU-MDM列表
     *
     * @param condition
     * @return
     */
    public List<Integer> querySkuIdList(SkuMdmQO condition) {
        List<SkuMdmEntity> list = this.list(SkuMdmEntity.lqw(condition));
        List<Integer> skuIdList = new ArrayList<>();
        list.forEach(item -> skuIdList.add(item.getSkuId()));
        return skuIdList;
    }

    /**
     * 保存
     *
     * @param skuId
     * @param skuMdmList
     */
    public void saveSkuMdm(Integer skuId, List<SkuMdmEntity> skuMdmList) {
        if (CollUtil.isNotEmpty(skuMdmList)) {
            for (SkuMdmEntity item : skuMdmList) {
                SkuMdmEntity entity = null;
                List<SkuMdmEntity> skuMdmEntityList = this.list(SkuMdmEntity.lqw(null)
                        .eq(SkuMdmEntity::getSkuId, skuId)
                        .eq(SkuMdmEntity::getType, item.getType())
                );
                if (CollectionUtils.isEmpty(skuMdmEntityList)) {
                    entity = BeanUtil.copyProperties(item, SkuMdmEntity.class);
                    entity.setSkuId(skuId);
                    entity.setIsDeleted(0);
                    entity.setCreatedBy(JwtUtils.getCurrentUserId());
                    entity.setCreatedAt(new Date());
                    entity.setUpdatedBy(JwtUtils.getCurrentUserId());
                    entity.setUpdatedAt(new Date());
                    this.save(entity);
                } else {
                    entity = skuMdmEntityList.get(0);
                    entity.setMdmId(item.getMdmId());
                    entity.setUpdatedBy(JwtUtils.getCurrentUserId());
                    entity.setUpdatedAt(new Date());
                    this.updateById(entity);
                }
            }
        }
    }
}
