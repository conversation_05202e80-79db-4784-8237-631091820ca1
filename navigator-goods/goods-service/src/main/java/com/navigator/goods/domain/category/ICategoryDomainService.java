package com.navigator.goods.domain.category;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.goods.pojo.dto.CategoryAddDTO;
import com.navigator.goods.pojo.dto.CategoryDTO;
import com.navigator.goods.pojo.dto.CategoryUpdateDTO;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;

import java.util.List;

/**
 * <p>
 * 品类品种 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
public interface ICategoryDomainService extends IService<CategoryEntity> {
    /**
     * 根据条件：获取品类品种分页
     *
     * @param queryDTO
     * @return
     */
    Page<CategoryDTO> queryCategoryDTOPage(QueryDTO<CategoryQO> queryDTO);

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    List<CategoryDTO> queryCategoryDTOList(CategoryQO condition);

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    List<CategoryEntity> queryCategoryList(CategoryQO condition);

    /**
     * 根据条件：获取品类品种ID列表
     *
     * @param condition
     * @return
     */
    List<Integer> queryCategoryIdList(CategoryQO condition);

    /**
     * 根据条件：获取品类品种主编码列表
     *
     * @param condition
     * @return
     */
    List<Integer> queryCategorySerialNoList(CategoryQO condition);

    /**
     * 根据ID：获取品类品种
     *
     * @param id
     * @return
     */
    CategoryDTO getCategoryDTOById(Integer id);

    /**
     * 获取品类基本信息
     *
     * @param serialNo
     * @return
     */
    CategoryEntity getBasicCategoryBySerialNo(Integer serialNo);

    /**
     * 根据主编码：获取品类品种
     *
     * @param serialNo
     * @return
     */
    CategoryDTO getCategoryDTOBySerialNo(Integer serialNo);

    /**
     * 新增：品类品种
     *
     * @param addDTO
     * @return
     */
    CategoryEntity addCategory(CategoryAddDTO addDTO);

    /**
     * 更新：品类品种
     *
     * @param updateDTO
     * @return
     */
    CategoryEntity updateCategory(CategoryUpdateDTO updateDTO);

    /**
     * 获取菜单结构
     *
     * @param systemId
     * @param customerId
     * @return
     */
    List<Tree<Integer>> queryCategoryMenu(Integer systemId, Integer customerId);


    /**
     * 获取树形结构
     *
     * @return
     */
    List<Tree<Integer>> queryCategoryTree();

    /**
     * 获取期货代码
     *
     * @param category2
     * @return
     */
    List<String> queryFutureCodeList(Integer category2);

    /**
     * 根据主编吗：获取上一级品类
     *
     * @param serialNo
     * @return
     */
    CategoryEntity getParentCategoryBySerialNo(Integer serialNo);

    /**
     * 根据主编吗：获取品类名称
     *
     * @param serialNoList
     * @return
     */
    List<CategoryEntity> getCategoryNameBySerialNoList(List<Integer> serialNoList);

    /**
     * 根据品类名称、等级查询品类信息
     *
     * @param name
     * @param level
     * @return
     */
    CategoryEntity getCategoryByName(String name, Integer level);

    List<CategoryEntity> getByCategoryNames(List<String> nameList, Integer level);

}
