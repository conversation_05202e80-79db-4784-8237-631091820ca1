package com.navigator.goods.domain.spu;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.dto.SpuRefreshDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.goods.pojo.qo.SpuQO;

import java.util.List;

/**
 * <p>
 * SPU商品 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface ISpuDomainService extends IService<SpuEntity> {
    /**
     * 根据条件：获取SPU商品DTO分页
     *
     * @param queryDTO
     * @return
     */
    Page<SpuDTO> querySpuDTOPage(QueryDTO<SpuQO> queryDTO);

    /**
     * 根据条件：获取SPU商品DTO列表
     *
     * @param condition
     * @return
     */
    List<SpuDTO> querySpuDTOList(SpuQO condition);

    /**
     * 根据ID：获取SPU商品
     *
     * @param id
     * @return
     */
    SpuEntity getSpuById(Integer id);

    /**
     * 根据编码：获取SPU商品
     *
     * @param spuNo
     * @return
     */
    SpuEntity getSpuBySpuNo(String spuNo);

    /**
     * 刷新：SPU商品
     *
     * @param refreshSpuDTO
     * @return
     */
    List<SpuDTO> refreshSpu(SpuRefreshDTO refreshSpuDTO);

    /**
     * 保存：SPU商品
     *
     * @param spuDTOList
     */
    Result saveSpu(List<SpuDTO> spuDTOList);

    /**
     * 保存：SPU商品
     *
     * @param category1
     * @param category2
     * @param category3
     * @param attributeValueEntity
     * @param status
     * @return
     */
    SpuEntity addSpu(CategoryEntity category1, CategoryEntity category2, CategoryEntity category3, AttributeValueEntity attributeValueEntity, Integer status);
}
