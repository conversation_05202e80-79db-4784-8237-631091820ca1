package com.navigator.goods.facade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.domain.category.IAttributeDomainService;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeQO;
import com.navigator.goods.pojo.vo.GoodsAttributeVO;
import com.navigator.goods.service.IAttributeValueService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-11-25 19:27
 */
@RestController
public class AttributeFacadeImpl implements AttributeFacade {

    @Resource
    private AttributeValueDao attributeValueDao;

    @Resource
    private IAttributeValueService attributeValueService;

    @Resource
    private IAttributeDomainService attributeDomainService;

    @Override
    public List<AttributeValueEntity> getAttributeValueList(Integer attributeId) {

        return attributeValueDao.getAttributeValueList(attributeId);
    }

    @Override
    public GoodsAttributeVO getSpecListByCategoryId(Integer categoryId) {
        return attributeValueService.getSpecListByCategoryId(categoryId);
    }

    @Override
    public AttributeValueEntity getAttributeValueById(Integer attributeValueId) {
        return attributeValueService.getAttributeValueById(attributeValueId);
    }

    @Override
    public Page<AttributeDTO> queryAttributeDTOPage(QueryDTO<AttributeQO> queryDTO) {
        return attributeDomainService.queryAttributeDTOPage(queryDTO);
    }

    @Override
    public List<AttributeDTO> queryAttributeDTOList(AttributeQO condition) {
        return attributeDomainService.queryAttributeDTOList(condition);
    }

    @Override
    public AttributeDTO getAttributeDTOById(Integer id) {
        return attributeDomainService.getAttributeDTOById(id);
    }

    @Override
    public Result addAttribute(AttributeAddDTO addDTO) {
        attributeDomainService.addAttribute(addDTO);
        return Result.success();
    }

    @Override
    public Result updateAttribute(AttributeUpdateDTO updateDTO) {
        attributeDomainService.updateAttribute(updateDTO);
        return Result.success();
    }

    @Override
    public List<AttributeValueEntity> getAttributeValueListByType(Integer type) {
        return attributeDomainService.getAttributeValueListByType(type);
    }
}
