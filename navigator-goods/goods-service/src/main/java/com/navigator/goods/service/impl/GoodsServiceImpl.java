package com.navigator.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.converter.GoodsConverter;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.dao.GoodsDao;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.goods.service.IAttributeValueRelationService;
import com.navigator.goods.service.ICategoryService;
import com.navigator.goods.service.IGoodsService;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品基础表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
@AllArgsConstructor
public class GoodsServiceImpl implements IGoodsService {

    @Resource
    private GoodsDao goodsDao;
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private ICategoryService categoryService;
    @Resource
    private AttributeValueDao attributeValueDao;
    @Resource
    private IAttributeValueRelationService attributeValueRelationService;
    @Resource
    private EmployFacade employFacade;

    /**
     * 根据品类、包装、规格获取商品信息，无则创建
     *
     * @param goodsSpecDTO 商品品类包装规格-传参
     * @return 商品基本信息
     */
    @Override
    public GoodsInfoVO acquireGoodsInfo(GoodsSpecDTO goodsSpecDTO) {
        //1、查询商品是否存在（规格 包装 品类）
//        GoodsEntity goodsEntity = goodsRedisManager.findGoodsBySpecId(goodsSpecDTO);
        GoodsEntity goodsEntity = goodsDao.findGoodsBySpecId(goodsSpecDTO);
        if (null == goodsEntity) {
            //2、若不存在则创建
            goodsEntity = this.createGoods(goodsSpecDTO);
        }
        GoodsInfoVO goodsInfoVO = GoodsConverter.INSTANCE.convertEntity2Vo(goodsEntity);
        goodsInfoVO.setCategory1(goodsEntity.getCategory1());
        goodsInfoVO.setCategory2(goodsEntity.getCategory2());
        goodsInfoVO.setCategory3(goodsEntity.getCategory3());
        return goodsInfoVO;
    }

    /**
     * 校验商品是否配置税率
     *
     * @param goodsSpecDTO
     * @return
     */
    @Override
    public Result queryGoodsTaxRate(GoodsSpecDTO goodsSpecDTO) {
        List<GoodsEntity> goodsEntities = goodsDao.findGoodsList(goodsSpecDTO.getCategoryId(), goodsSpecDTO.getSpecId(), goodsSpecDTO.getPackageId());

        if (goodsEntities.isEmpty()) {
            return Result.failure("匹配不到该货品配置，请联系IT");
        }
        GoodsEntity goodsEntity = goodsEntities.get(0);
        if (DisableStatusEnum.DISABLE.getValue().equals(goodsEntity.getStatus())) {
            return Result.failure("该货品配置已禁用，请联系IT");
        }

        if (null == goodsEntity.getTaxRate()) {
            return Result.failure("匹配不到该货品配置，请联系IT");
        }

        return Result.success(true);
    }


    @Override
    public GoodsInfoVO findGoodsById(Integer goodsId) {
        GoodsEntity goodsEntity = goodsDao.getById(goodsId);
        return GoodsConverter.INSTANCE.convertEntity2Vo(goodsEntity);
    }

    @Override
    public GoodsDTO findGoodsDetail(Integer goodsId) {
        GoodsDTO goodsDTO = null;
        GoodsEntity goodsEntity = goodsDao.getById(goodsId);

        //TODO NANA packageName和SpecName应该放到DTO中，然后统一处理
        //1、goodsEntity = goodsDao.getById(goodsId)
        //2、根据情况转换为goodsDTO（获取其他的值，如PackageName）

        if (null != goodsEntity.getPackageId()) {
            //2、包装
            AttributeValueEntity packageEntity = attributeValueDao.getById(goodsEntity.getPackageId());
            goodsEntity.setPackageName(null == packageEntity ? "" : packageEntity.getName());
        }
        if (null != goodsEntity.getSpecId()) {
            //3、规格
            AttributeValueEntity specEntity = attributeValueDao.getById(goodsEntity.getSpecId());
            goodsEntity.setSpecName(null == specEntity ? "" : specEntity.getName());
        }

        goodsDTO = BeanConvertUtils.convert(GoodsDTO.class, goodsEntity);

        System.out.println("==============findGoodsDetail===============");
        System.out.println(JSON.toJSONString(goodsDTO));

        return goodsDTO;
    }


    @Override
    public GoodsDTO findGoodsDetail(String goodsName) {
        List<GoodsEntity> goodsByName = goodsDao.getGoodsByName(goodsName);
        GoodsDTO goodsDTO = null;
        if (CollUtil.isNotEmpty(goodsByName)) {
            GoodsEntity goodsEntity = goodsByName.get(0);
            if (null != goodsEntity.getPackageId()) {
                //2、包装
                AttributeValueEntity packageEntity = attributeValueDao.getById(goodsEntity.getPackageId());
                goodsEntity.setPackageName(null == packageEntity ? "" : packageEntity.getName());
            }
            if (null != goodsEntity.getSpecId()) {
                //3、规格
                AttributeValueEntity specEntity = attributeValueDao.getById(goodsEntity.getSpecId());
                goodsEntity.setSpecName(null == specEntity ? "" : specEntity.getName());
            }

            goodsDTO = BeanConvertUtils.convert(GoodsDTO.class, goodsEntity);
        }
        return goodsDTO;
    }

    @Override
    public List<GoodsInfoVO> queryGoodsListBySpec(Integer categoryId, Integer packageId, Integer specId) {
        List<GoodsEntity> goodsEntityList = goodsDao.findGoodsList(categoryId, specId, packageId);
        return goodsEntityList.stream()
                .map(GoodsConverter.INSTANCE::convertEntity2Vo)
                .collect(Collectors.toList());
    }

    @Override
    public List<GoodsSearchDTO> getAllGoodsList(Integer status) {
        List<GoodsEntity> goodsList = goodsDao.getAllGoodsList(status);
        return BeanConvertUtils.convert2List(GoodsSearchDTO.class, goodsList);
    }

    @Override
    public List<GoodsSearchDTO> getAllGoodsListByCategoryId(Integer status, Integer categoryId) {
        List<GoodsEntity> goodsList = goodsDao.getAllGoodsListByCategoryId(status, categoryId);
        return BeanConvertUtils.convert2List(GoodsSearchDTO.class, goodsList);
    }


    @Override
    public Result queryGoodsList(QueryDTO<GoodsSearchDTO> goodsSearchDTO) {
        IPage<GoodsEntity> goodsEntityIPage = goodsDao.queryGoodsList(goodsSearchDTO);
        if (!CollectionUtils.isEmpty(goodsEntityIPage.getRecords())) {
            goodsEntityIPage.getRecords().forEach(goodsEntity -> {
                CategoryEntity categoryEntity = categoryDao.getById(goodsEntity.getCategoryId());
                if (null != categoryEntity) {
                    CategoryEntity parentCategoryEntity = categoryDao.getById(categoryEntity.getParentId());
                    goodsEntity.setCategoryName(categoryEntity.getName())
                            .setParentCategoryName(null != parentCategoryEntity ? parentCategoryEntity.getName() : "");
                }
                if (null != goodsEntity.getPackageId()) {
                    //2、包装
                    AttributeValueEntity packageEntity = attributeValueDao.getById(goodsEntity.getPackageId());
                    goodsEntity.setPackageName(null == packageEntity ? "" : packageEntity.getName());
                }
                if (null != goodsEntity.getSpecId()) {
                    //3、规格
                    AttributeValueEntity specEntity = attributeValueDao.getById(goodsEntity.getSpecId());
                    goodsEntity.setSpecName(null == specEntity ? "" : specEntity.getName());
                }
            });
        }
        return Result.page(goodsEntityIPage);
    }

    @Override
    public boolean invalidGoods(Integer goodsId, Integer status, String futurePrefix, String futureSuffix, String taxRate) {
        GoodsEntity goodsEntity = goodsDao.getById(goodsId);
        if (null == goodsEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        goodsEntity.setStatus(status)
                .setFuturePrefix(StringUtil.isNotEmpty(futurePrefix) ? futurePrefix : goodsEntity.getFuturePrefix())
                .setFutureSuffix(StringUtil.isNotEmpty(futureSuffix) ? futurePrefix : goodsEntity.getFutureSuffix())
                .setTaxRate(StringUtil.isNotEmpty(taxRate) ? new BigDecimal(taxRate) : goodsEntity.getTaxRate())
                .setUpdatedAt(new Date())
                .setUpdatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        return goodsDao.updateById(goodsEntity);
    }

    /**
     * 此包装规格的油厂货品无，则默认创建
     *
     * @param goodsSpecDTO 商品包装规格信息
     * @return 创建后的商品信息
     */
    private GoodsEntity createGoods(GoodsSpecDTO goodsSpecDTO) {
        GoodsEntity goodsEntity = BeanConvertUtils.convert(GoodsEntity.class, goodsSpecDTO);
        //TODO：商品编码生成-1126-nana
        this.jointGoodsName(goodsSpecDTO, goodsEntity);
        goodsDao.save(goodsEntity);
        attributeValueRelationService.bindGoodsAttributeValue(goodsEntity.getId(), Arrays.asList(goodsSpecDTO.getPackageId(), goodsSpecDTO.getSpecId()));
        return goodsEntity;
    }

    /**
     * 拼接商品名称（豆粕-50KG,43%）
     *
     * @param goodsSpecDTO 商品规格信息
     * @return 商品名
     */
    private String jointGoodsName(GoodsSpecDTO goodsSpecDTO, GoodsEntity goodsEntity) {
        //1、品类
        CategoryEntity categoryEntity = categoryDao.getById(goodsSpecDTO.getCategoryId());
        String name = categoryEntity.getName();
        String lkgName = "";
        String lkgCode = "";
        if (GoodsCategoryEnum.OSM_OIL.getValue().equals(goodsSpecDTO.getCategoryId())) {
            AttributeValueEntity specEntity = attributeValueDao.getById(goodsSpecDTO.getSpecId());
            name = null != specEntity ? specEntity.getName() : name;
            lkgName = name;
            lkgCode = null != specEntity ? specEntity.getMemo() : "";
        } else if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(goodsSpecDTO.getCategoryId())) {
            if (null != goodsSpecDTO.getPackageId()) {
                //2、包装
                AttributeValueEntity packageEntity = attributeValueDao.getById(goodsSpecDTO.getPackageId());
                name = null != packageEntity ? name + "-" + packageEntity.getName() : name;
                lkgName = name;
                lkgCode = null != packageEntity ? packageEntity.getMemo() : "";
            }
            if (null != goodsSpecDTO.getSpecId()) {
                //3、规格
                AttributeValueEntity specEntity = attributeValueDao.getById(goodsSpecDTO.getSpecId());
                name = name + "," + specEntity.getName();
            }
        }
        goodsEntity.setName(name).setLinkageGoodsCode(lkgCode)
                .setLinkageGoodsName(lkgName).setCode("N." + lkgCode).setSupplierId(0);
        return name;
    }

    @Override
    public Result updateGoodsName() {
        List<GoodsEntity> goodsEntities = goodsDao.findGoodsList(GoodsCategoryEnum.OSM_OIL.getValue(), null, null);
        for (GoodsEntity goodsEntity : goodsEntities) {
            //3、规格
            AttributeValueEntity specEntity = attributeValueDao.getById(goodsEntity.getSpecId());
            String name = specEntity.getName();
            goodsDao.updateById(goodsEntity.setName(name));
        }
        List<GoodsEntity> goodsCodeEntityList = goodsDao.list();
        for (GoodsEntity goodsEntity : goodsCodeEntityList) {
            goodsEntity.setCode(StringUtils.isNotBlank(goodsEntity.getLinkageGoodsCode()) ? "N." + goodsEntity.getLinkageGoodsCode() : "");
            if (GoodsCategoryEnum.OSM_OIL.getValue().equals(goodsEntity.getCategoryId())) {
                //3、规格
                AttributeValueEntity specEntity = attributeValueDao.getById(goodsEntity.getSpecId());
                goodsEntity.setName(specEntity.getName());
            }
            goodsDao.updateById(goodsEntity);
        }
        return Result.success(goodsEntities.size());
    }

    @Override
    public Result importGoodsInfo(MultipartFile uploadFile) {
        return null;
    }

    @Override
    public boolean updateGoodsDeliveryStatus(Integer goodsId, Integer deliveryStatus) {
        GoodsEntity goodsEntity = goodsDao.getById(goodsId);
        if (null == goodsEntity) {
            throw new BusinessException(ResultCodeEnum.RECORD_NOT_EXIST);
        }
        goodsEntity
                .setIsDelivery(deliveryStatus)
                .setUpdatedAt(new Date())
                .setUpdatedBy(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())));
        return goodsDao.updateById(goodsEntity);
    }

    @Override
    public List<GoodsEntity> queryCannotDeliveryGoodsList() {
        return goodsDao.queryCannotDeliveryGoodsList();
    }
}
