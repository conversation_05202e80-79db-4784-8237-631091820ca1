package com.navigator.goods.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.goods.mapper.AttributeValueMapper;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import org.springframework.util.CollectionUtils;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-12-03 11:45
 */
@Dao
public class AttributeValueDao extends BaseDaoImpl<AttributeValueMapper, AttributeValueEntity> {

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param attributeId 规格ID
     * @return 规格列表
     */
    public List<AttributeValueEntity> getAttributeValueList(Integer attributeId) {
        return this.list(Wrappers.<AttributeValueEntity>lambdaQuery()
                        .eq(null != attributeId, AttributeValueEntity::getAttributeId, attributeId)
                        .eq(AttributeValueEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()))
                .stream()
                .sorted(Comparator.comparing(AttributeValueEntity::getSort).thenComparing(AttributeValueEntity::getId))
                .collect(Collectors.toList());
    }

    /**
     * 新增记录
     *
     * @param attributeId
     * @param name
     * @param sort
     * @param memo
     * @return
     */
    public AttributeValueEntity addAttributeValueEntity(Integer attributeId, String name, Integer sort, String memo) {
        AttributeValueEntity entity = this.getByAttributeAndValue(attributeId, name);
        if (entity == null) {
            entity = new AttributeValueEntity();
            entity.setAttributeId(attributeId);
            entity.setName(name);
            entity.setSort(sort);
            entity.setMemo(memo);
            entity.setIsDeleted(0);
            entity.setCreatedAt(new Date());
            entity.setUpdatedAt(new Date());
            this.save(entity);
        }
        return entity;
    }

    /**
     * 获取记录
     *
     * @param attributeId
     * @param name
     * @return
     */
    public AttributeValueEntity getByAttributeAndValue(Integer attributeId, String name) {
        List<AttributeValueEntity> attributeValueEntityList = this.list(Wrappers.<AttributeValueEntity>lambdaQuery()
                .eq(AttributeValueEntity::getAttributeId, attributeId)
                .eq(AttributeValueEntity::getName, name)
                .eq(AttributeValueEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(attributeValueEntityList) ? null : attributeValueEntityList.get(0);
    }

    /**
     * 根据ID：获取规格值
     *
     * @param id
     * @return
     */
    public AttributeValueEntity getAttributeValueById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据条件：获取规格值列表
     *
     * @param condition
     * @return
     */
    public List<AttributeValueEntity> queryAttributeValueList(AttributeValueQO condition) {
        return this.list(AttributeValueEntity.lqw(condition, true));
    }

    /**
     * 新增：规格值
     *
     * @param attributeId
     * @param valueNameSet
     */
    public void addAttributeValue(Integer attributeId, Set<String> valueNameSet) {
        // 验证合法性
        List<AttributeValueEntity> list = this.queryAttributeValueList(new AttributeValueQO().setAttributeId(attributeId));
        list.forEach(item -> {
            if (valueNameSet.contains(item.getName())) {
                throw new BusinessException("提交失败，取值不可重复！");
            }
        });

        for (String name : valueNameSet) {
            AttributeValueEntity item = new AttributeValueEntity();
            item.setAttributeId(attributeId);
            item.setName(name.trim());
            item.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
            this.save(item);
            IdNameConverter.setName(IdNameType.attributeValue_id_name, item.getId().toString(), item.getName());
        }
    }
}
