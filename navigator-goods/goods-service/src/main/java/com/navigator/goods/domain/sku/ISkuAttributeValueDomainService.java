package com.navigator.goods.domain.sku;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.goods.pojo.entity.SkuAttributeValueEntity;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;

import java.util.List;

/**
 * <p>
 * Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
public interface ISkuAttributeValueDomainService extends IService<SkuAttributeValueEntity> {
    /**
     * 根据条件：获取SKU货品规格列表
     *
     * @param condition
     * @return
     */
    List<SkuAttributeValueEntity> querySkuAttributeValueList(SkuAttributeValueQO condition);

    /**
     * 根据ID：获取SKU货品规格值
     *
     * @param id
     * @return
     */
    SkuAttributeValueEntity getSkuAttributeValueById(Integer id);
}
