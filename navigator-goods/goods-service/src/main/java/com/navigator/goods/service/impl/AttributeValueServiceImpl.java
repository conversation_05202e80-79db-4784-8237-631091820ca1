package com.navigator.goods.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.goods.converter.GoodsConverter;
import com.navigator.goods.dao.AttributeDao;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.vo.GoodsAttributeVO;
import com.navigator.goods.service.IAttributeValueService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 规格值信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class AttributeValueServiceImpl implements IAttributeValueService {

    @Resource
    private AttributeValueDao attributeValueDao;
    @Resource
    private AttributeDao attributeDao;
    @Resource
    private CategoryDao categoryDao;

    @Override
    public GoodsAttributeVO getSpecListByCategoryId(Integer categoryId) {
        CategoryEntity categoryEntity = categoryDao.getById(categoryId);
        GoodsAttributeVO goodsAttributeVO = new GoodsAttributeVO()
                .setCategoryId(categoryId)
                .setCategoryName(categoryEntity.getName());
        AttributeEntity packageEntity = attributeDao.findByCategoryIdAndType(categoryId, AttributeType.PACKAGE.getValue());
        if (null != packageEntity) {
            List<AttributeValueEntity> packageList = attributeValueDao.getAttributeValueList(packageEntity.getId());
            if (!CollectionUtils.isEmpty(packageList)) {
                goodsAttributeVO.setPackageList(packageList.stream().map(GoodsConverter.INSTANCE::convertEntity2Vo).collect(Collectors.toList()));
            }
        }
        AttributeEntity specEntity = attributeDao.findByCategoryIdAndType(categoryId, AttributeType.KEY.getValue());
        if (null != specEntity) {
            List<AttributeValueEntity> specList = attributeValueDao.getAttributeValueList(specEntity.getId());
            if (!CollectionUtils.isEmpty(specList)) {
                goodsAttributeVO.setSpecList(specList.stream().map(GoodsConverter.INSTANCE::convertEntity2Vo).collect(Collectors.toList()));
            }
        }
        return goodsAttributeVO;
    }

    @Override
    public AttributeValueEntity getAttributeValueById(Integer attributeValueId) {
        return attributeValueDao.getById(attributeValueId);
    }
}
