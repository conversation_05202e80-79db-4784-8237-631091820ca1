package com.navigator.goods.service.impl;

import com.navigator.goods.dao.AttributeDao;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.dao.AttributeValueRelationDao;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.AttributeValueRelationEntity;
import com.navigator.goods.service.IAttributeValueRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 商品规格关系表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Service
public class AttributeValueRelationServiceImpl implements IAttributeValueRelationService {

    @Resource
    private AttributeValueDao attributeValueDao;
    @Resource
    private AttributeDao attributeDao;
    @Resource
    private AttributeValueRelationDao attributeValueRelationDao;

    @Override
    public void bindGoodsAttributeValue(Integer goodsId, List<Integer> attributeValueIdList) {
        attributeValueIdList.forEach(attributeValueId -> {
            //属性值查询
            AttributeValueEntity attributeValueEntity = attributeValueDao.getById(attributeValueId);
            //属性名查询
            AttributeEntity attributeEntity = attributeDao.getById(attributeValueEntity.getAttributeId());
            attributeValueRelationDao.save(new AttributeValueRelationEntity()
                    .setGoodsId(goodsId)
                    .setAttributeId(attributeValueEntity.getAttributeId())
                    .setAttributeName(attributeEntity.getName())
                    .setAttributeValueId(attributeValueId)
                    .setAttributeValue(attributeValueEntity.getName())
                    .setSort(0));
        });
    }

    @Override
    public List<Integer> getGoodsIdListByValueId(Integer attributeValueId) {
        List<AttributeValueRelationEntity> valueRelationEntityList = attributeValueRelationDao.getValueRelationListByValueId(attributeValueId);
        return valueRelationEntityList.stream()
                .map(AttributeValueRelationEntity::getGoodsId)
                .distinct().collect(Collectors.toList());
    }

}
