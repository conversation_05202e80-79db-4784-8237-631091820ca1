package com.navigator.goods.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.goods.mapper.CategoryPriceMapper;
import com.navigator.goods.pojo.entity.CategoryPriceEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-30 00:27
 */
@Dao
public class CategoryPriceDao extends BaseDaoImpl<CategoryPriceMapper, CategoryPriceEntity> {

    public void bindCategoryPrice(Integer categoryId, List<Integer> contractPriceTypeList) {
        //删除之前的报价信息
        this.dropCategoryPrice(categoryId);
        //品类报价关系非空 重新绑定
        if (!CollectionUtils.isEmpty(contractPriceTypeList)) {
            contractPriceTypeList.forEach(contractPriceType -> {
                CategoryPriceEntity categoryPriceEntity = new CategoryPriceEntity()
                        .setCategoryId(categoryId)
                        .setContractPriceType(contractPriceType)
                        .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                this.save(categoryPriceEntity);
            });
        }
    }

    /**
     * 删除该品类的报价信息
     *
     * @param categoryId 品类ID
     */
    private void dropCategoryPrice(Integer categoryId) {
        new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(CategoryPriceEntity::getCategoryId, categoryId)
                .eq(CategoryPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .set(CategoryPriceEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }

    public List<CategoryPriceEntity> getPriceByCategoryId(Integer categoryId) {
        return this.list(Wrappers.<CategoryPriceEntity>lambdaQuery()
                .eq(CategoryPriceEntity::getCategoryId, categoryId)
                .eq(CategoryPriceEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

}
