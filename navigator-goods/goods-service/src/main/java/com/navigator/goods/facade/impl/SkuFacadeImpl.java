package com.navigator.goods.facade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.domain.sku.ISkuDomainService;
import com.navigator.goods.domain.sku.ISkuMdmDomainService;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SkuRefreshDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * SKU货品 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@RestController
public class SkuFacadeImpl implements SkuFacade {

    @Resource
    private ISkuDomainService skuDomainService;
    @Resource
    private ISkuMdmDomainService skuMdmDomainService;

    @Override
    public Page<SkuDTO> querySkuDTOPage(QueryDTO<SkuQO> queryDTO) {
        return skuDomainService.querySkuDTOPage(queryDTO);
    }

    @Override
    public List<SkuDTO> querySkuDTOList(SkuQO condition) {
        return skuDomainService.querySkuDTOList(condition);
    }

    @Override
    public List<SkuEntity> getSkuListByIds(List<Integer> skuIdList) {
        return skuDomainService.getSkuListByIds(skuIdList);
    }

    @Override
    public SkuEntity getSkuById(Integer id) {
        return skuDomainService.getSkuById(id);
    }

    @Override
    public SkuDTO getSkuDTOById(Integer id) {
        return skuDomainService.getSkuDTOById(id);
    }

    @Override
    public Result<SkuEntity> getSkuByFullName(String fullName) {
        return Result.success(skuDomainService.getSkuByFullName(fullName));
    }

    @Override
    public SkuEntity getSkuBySkuNo(String skuNo) {
        return skuDomainService.getSkuBySkuNo(skuNo);
    }

    @Override
    public Result<List<SkuAddDTO>> refreshSku(SkuRefreshDTO skuRefreshDTO) {
        return Result.success(skuDomainService.refreshSku(skuRefreshDTO));
    }

    @Override
    public Result saveSku(List<SkuAddDTO> skuAddDTOList) {
        return skuDomainService.saveSku(skuAddDTOList);
    }

    @Override
    public List<Integer> queryCannotDeliverySkuIdList() {
        return skuDomainService.queryCannotDeliverySkuIdList();
    }

    @Override
    public Result processHistoryData(Integer id) {
        skuDomainService.processHistoryData(id);
        return Result.success();
    }

    @Override
    public List<SkuEntity> querySkuBySpecId(Integer specId) {
        return skuDomainService.list(SkuEntity.lqw().eq(SkuEntity::getSpecId, specId));
    }

    @Override
    public Result querySkuMdmList(SkuMdmQO skuMdmQO) {
        return Result.success(skuMdmDomainService.querySkuMdmList(skuMdmQO));
    }
}
