package com.navigator.goods.domain.spu.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SequenceFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.SequenceEnum;
import com.navigator.common.util.CartesianProductUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.dao.*;
import com.navigator.goods.domain.spu.ISpuDomainService;
import com.navigator.goods.mapper.SpuMapper;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.dto.SpuRefreshDTO;
import com.navigator.goods.pojo.entity.*;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import com.navigator.goods.pojo.qo.SpuQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * SPU商品 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Slf4j
@Service
public class SpuDomainDomainServiceImpl extends ServiceImpl<SpuMapper, SpuEntity> implements ISpuDomainService {

    @Resource
    private SpuDao spuDao;

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private CategoryAttributeDao categoryAttributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;
    @Resource
    private AttributeDao attributeDao;

    @Resource
    private SpuAttributeValueDao spuAttributeValueDao;

    @Resource
    private SequenceFacade sequenceFacade;
    @Resource
    private OperationLogFacade operationLogFacade;


    @Override
    public Page<SpuDTO> querySpuDTOPage(QueryDTO<SpuQO> queryDTO) {
        SpuQO condition = queryDTO.getCondition();
        Page<SpuEntity> page = spuDao.querySpuPage(queryDTO);
        Page result = new Page<SkuDTO>();
        result.setPages(page.getPages());
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        List<SpuDTO> records = new ArrayList<>();
        page.getRecords().forEach(item -> {
            records.add(this.toDTO(item));
        });
        result.setRecords(records);
        return result;
    }


    @Override
    public List<SpuDTO> querySpuDTOList(SpuQO condition) {
        List<SpuEntity> list = spuDao.querySpuList(condition);
        List<SpuDTO> result = new ArrayList<>();
        list.forEach(item -> {
            result.add(this.toDTO(item));
        });
        return result;
    }


    @Override
    public SpuEntity getSpuById(Integer id) {
        return spuDao.getSpuById(id);
    }


    @Override
    public SpuEntity getSpuBySpuNo(String spuNo) {
        return spuDao.getSpuBySpuNo(spuNo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SpuDTO> refreshSpu(SpuRefreshDTO spuRefreshDTO) {
        String userName = IdNameConverter.getName(IdNameType.user_id_name, JwtUtils.getCurrentUserId());
        // 品种
        CategoryEntity[] categories = categoryDao.getThreeCategoryBySerialNo(spuRefreshDTO.getCategory3());
        // 结果集
        List<SpuDTO> resultList = new ArrayList<>();
        // 已有SPU
        List<SpuEntity> existList = spuDao.querySpuList(new SpuQO().setCategory3(spuRefreshDTO.getCategory3()));
        Map<String, SpuEntity> spuMap = new HashMap<>();
        existList.forEach(spu -> spuMap.put(spu.getSpuNo(), spu));
        // 关键规格
        List<CategoryAttributeEntity> categoryAttributeList = categoryAttributeDao.queryCategoryAttributeList(
                new CategoryAttributeQO()
                        .setCategory3(spuRefreshDTO.getCategory3())
                        .setAttributeType(AttributeType.KEY.getValue())
        );
        //// BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
        // 规格值
        List<List<AttributeValueEntity>> attributeValueListList = new ArrayList<>();
        for (CategoryAttributeEntity categoryAttributeEntity : categoryAttributeList) {
            Integer attributeId = categoryAttributeEntity.getAttributeId();
            attributeValueListList.add(attributeValueDao.queryAttributeValueList(new AttributeValueQO().setAttributeId(attributeId)));
        }
        // 生成规格值矩阵
        List<List<AttributeValueEntity>> attributeValueResultList = CartesianProductUtil.cartesianProduct(attributeValueListList);
        // 遍历规格值矩阵
        for (List<AttributeValueEntity> attributeValueList : attributeValueResultList) {
            String attributeName = IdNameConverter.getName(IdNameType.attribute_id_name, attributeValueList.get(0).getAttributeId().toString());
            List<Integer> spuNoList = new ArrayList<>();
            spuNoList.add(categories[2].getSerialNo());
            List<String> spuNameList = new ArrayList<>();
            spuNameList.add(categories[2].getName());
            spuNameList.add("其他".equals(attributeName) ? "" : categories[2].getName());
            List<Map<String, Object>> keyAttributeValues = new ArrayList<>();
            for (int i = 0, resultSize = attributeValueList.size(); i < resultSize; i++) {
                AttributeValueEntity attributeValue = attributeValueList.get(i);
                spuNoList.add(attributeValue.getId());
                if (!"其他".equals(attributeName)) {
                    spuNameList.add(attributeValue.getName());
                }
                Map<String, Object> map = new HashMap<>();
                map.put("attributeId", attributeValue.getAttributeId());
                map.put("attributeName", attributeName);
                map.put("attributeType", AttributeType.KEY.getValue());
                map.put("attributeValueId", attributeValue.getId());
                map.put("attributeValueName", attributeValue.getName());
                keyAttributeValues.add(map);
            }
            resultList.add(refreshSpuDTO(userName, categories, spuMap, spuNoList, spuNameList, keyAttributeValues));
        }
        // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end
        return resultList;
    }

    /**
     * 组合Spu DTO
     *
     * @param userName
     * @param categories
     * @param spuMap
     * @param spuNoList
     * @param spuNameList
     * @param keyAttributeValues
     * @return
     */
    private SpuDTO refreshSpuDTO(String userName, CategoryEntity[] categories, Map<String, SpuEntity> spuMap, List<Integer> spuNoList, List<String> spuNameList, List<Map<String, Object>> keyAttributeValues) {
        String spuNo = CollUtil.join(spuNoList, "_");
        String spuName = CollUtil.join(spuNameList, ",") + ";";
        SpuEntity spu = spuMap.get(spuNo);
        SpuDTO spuDTO;
        if (spu == null) {
            spuDTO = new SpuDTO();
            spuDTO.setCategory1(categories[0].getSerialNo());
            spuDTO.setCategoryName1(categories[0].getName());
            spuDTO.setCategory2(categories[1].getSerialNo());
            spuDTO.setCategoryName2(categories[1].getName());
            spuDTO.setCategory3(categories[2].getSerialNo());
            spuDTO.setCategoryName3(categories[2].getName());
            spuDTO.setSpuNo(spuNo);
            spuDTO.setSpuName(spuName);
            spuDTO.setKeyAttributeValues(FastJsonUtils.getBeanToJson(keyAttributeValues));
            spuDTO.setStatus(0);
            spuDTO.setUpdatedBy(userName);
            spuDTO.setUpdatedAt(new Date());
            spuDTO.setIsDeleted(0);
        } else {
            spuDTO = BeanUtil.copyProperties(spu, SpuDTO.class);
            spuDTO.setCategoryName1(categories[0].getName());
            spuDTO.setCategoryName2(categories[1].getName());
            spuDTO.setCategoryName3(categories[2].getName());
            spuDTO.setUpdatedBy(IdNameConverter.getName(IdNameType.user_id_name, spuDTO.getUpdatedBy()));
        }
        return spuDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveSpu(List<SpuDTO> spuDTOList) {
        for (SpuDTO spuDTO : spuDTOList) {

            // SPU
            SpuEntity entity;
            if (spuDTO.getId() == null) {
                entity = BeanUtil.copyProperties(spuDTO, SpuEntity.class);
                entity.setNavSpuId(sequenceFacade.generate(SequenceEnum.NAV_SPU_ID.getValue(), 5));
                if (spuDao.count(SpuEntity.lqw(null, false).eq(SpuEntity::getSpuNo, entity.getSpuNo())) > 0) {
                    return Result.failure("提交失败，SPU NO不可重复！");
                }
                entity.setUpdatedAt(new Date());
                entity.setUpdatedBy(JwtUtils.getCurrentUserId());
                this.save(entity);
                IdNameConverter.setName(IdNameType.spu_id_name, entity.getId().toString(), entity.getSpuName());
            } else {
                entity = spuDao.getSpuById(spuDTO.getId());
                if (StringUtil.isBlank(entity.getNavSpuId())) {
                    entity.setNavSpuId(sequenceFacade.generate(SequenceEnum.NAV_SPU_ID.getValue(), 5));
                }
                entity.setStatus(spuDTO.getStatus());
                entity.setUpdatedAt(new Date());
                entity.setUpdatedBy(JwtUtils.getCurrentUserId());
                this.updateById(entity);
            }

            // 主键
            spuDTO.setId(entity.getId());

            // SPU规格值
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(entity.getKeyAttributeValues());
            for (int i = 0; i < keyAttributeValues.size(); i++) {
                Map<String, Object> item = keyAttributeValues.get(i);
                //// BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04
                Integer attributeId = (Integer) item.get("attributeId");
                AttributeEntity attribute = attributeDao.getAttributeById(attributeId);
                spuAttributeValueDao.saveSpuAttributeValue(spuDTO, (Integer) item.get("attributeId"), attribute.getDisplayName(), (Integer) item.get("attributeValueId"), (String) item.get("attributeValueName"));
            }
        }

        //记录日志
        //记录日志
        recordMagellanOperationDetail(JSON.toJSONString(spuDTOList), OperationActionEnum.UPDATE_GOODS_SPU_CATEGORY, null);
        return Result.success();
    }

    @Override
    public SpuEntity addSpu(CategoryEntity category1, CategoryEntity category2, CategoryEntity category3, AttributeValueEntity attributeValueEntity, Integer status) {
        // spuNo
        List<Integer> spuNoList = new ArrayList<>();
        spuNoList.add(category3.getSerialNo());
        spuNoList.add(attributeValueEntity.getId());
        String spuNo = CollUtil.join(spuNoList, "_");

        SpuEntity spuEntity = spuDao.getSpuBySpuNo(spuNo);
        if (spuEntity == null) {
            spuEntity = new SpuEntity();
        }

        // spuName
        List<String> spuNameList = new ArrayList<>();
        spuNameList.add(category3.getName());
        spuNameList.add(attributeValueEntity.getName());
        String spuName = CollUtil.join(spuNameList, ",") + ";";

        // 关键规格
        List<Map<String, Object>> keyAttributeValues = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("attributeId", attributeValueEntity.getAttributeId());
        map.put("attributeName", IdNameConverter.getName(IdNameType.attribute_id_name, attributeValueEntity.getAttributeId().toString()));
        map.put("attributeType", AttributeType.KEY.getValue());
        map.put("attributeValueId", attributeValueEntity.getId());
        map.put("attributeValueName", attributeValueEntity.getName());
        keyAttributeValues.add(map);

        spuEntity.setCategory1(category1.getSerialNo());
        spuEntity.setCategory2(category2.getSerialNo());
        spuEntity.setCategory3(category3.getSerialNo());
        spuEntity.setSpuNo(spuNo);
        spuEntity.setSpuName(spuName);
        spuEntity.setKeyAttributeValues(JSON.toJSONString(keyAttributeValues));
        spuEntity.setStatus(status);
        spuEntity.setMemo("history");
        spuEntity.setUpdatedBy(JwtUtils.getCurrentUserId());
        spuEntity.setUpdatedAt(new Date());
        spuEntity.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
        if (spuEntity.getId() == null) {
            spuEntity.setNavSpuId(sequenceFacade.generate(SequenceEnum.NAV_SPU_ID.getValue(), 5));
            spuEntity.setCreatedBy(JwtUtils.getCurrentUserId());
            spuEntity.setCreatedAt(new Date());
            spuDao.save(spuEntity);
        } else {
            spuDao.updateById(spuEntity);
        }
        // SPU规格值
        SpuDTO spuDTO = BeanUtil.copyProperties(spuEntity, SpuDTO.class);
        //// BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
        AttributeEntity attribute = attributeDao.getAttributeById(attributeValueEntity.getAttributeId());
        spuAttributeValueDao.saveSpuAttributeValue(spuDTO, attributeValueEntity.getAttributeId(), attribute.getDisplayName(), attributeValueEntity.getId(), attributeValueEntity.getName());
        //// BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 end
        return spuEntity;
    }

    /**
     * 转DTO
     *
     * @param entity
     * @return
     */
    private SpuDTO toDTO(SpuEntity entity) {
        SpuDTO dto = BeanUtil.copyProperties(entity, SpuDTO.class);
        dto.setCategoryName1(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory1())));
        dto.setCategoryName2(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory2())));
        dto.setCategoryName3(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory3())));
        IdNameConverter.toName(IdNameType.user_id_name, dto);
        return dto;
    }

    //记录系统日志
    private void recordMagellanOperationDetail(String dtoData, OperationActionEnum operationActionEnum, Integer referBizId) {
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(dtoData)
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(operationActionEnum)
                    .setReferBizId(referBizId)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}