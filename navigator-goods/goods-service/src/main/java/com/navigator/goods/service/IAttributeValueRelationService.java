package com.navigator.goods.service;

import java.util.List;

/**
 * <p>
 * 商品规格关系表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IAttributeValueRelationService {

    /**
     * 绑定-商品属性规格信息
     *
     * @param goodsId              商品ID
     * @param attributeValueIdList 属性规格值
     */
    void bindGoodsAttributeValue(Integer goodsId, List<Integer> attributeValueIdList);

    /**
     * 根据规格属性值ID，获取商品集合
     *
     * @param attributeValueId 规格属性值ID
     * @return 商品ID集合
     */
    List<Integer> getGoodsIdListByValueId(Integer attributeValueId);
}
