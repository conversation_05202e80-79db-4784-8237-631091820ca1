package com.navigator.goods.domain.category.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.dao.CategoryAttributeDao;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.domain.category.IAttributeValueDomainService;
import com.navigator.goods.mapper.AttributeValueMapper;
import com.navigator.goods.pojo.dto.AttributeValueDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.entity.CategoryAttributeEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 规格值 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service

public class AttributeValueDomainServiceImpl extends ServiceImpl<AttributeValueMapper, AttributeValueEntity> implements IAttributeValueDomainService {

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private CategoryAttributeDao categoryAttributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;

    @Override
    public List<AttributeValueDTO> queryAttributeValueDTOList(AttributeValueQO condition) {
        CategoryEntity categoryEntity = categoryDao.getCategoryById(condition.getCategoryId());
        List<CategoryAttributeEntity> categoryAttributeEntityList = categoryAttributeDao.queryCategoryAttributeList(
                new CategoryAttributeQO()
                        .setAttributeType(condition.getAttributeType())
                        .setCategory3(categoryEntity.getSerialNo()
                        ));
        LambdaQueryWrapper<AttributeValueEntity> lqw = AttributeValueEntity.lqw(null,true);
        Map<Integer, String> attributeMap = new HashMap<>();
        // 查询指定规格的规格值
        if (CollUtil.isNotEmpty(categoryAttributeEntityList)) {
            List<Integer> attributeIdList = new ArrayList<>();
            categoryAttributeEntityList.forEach(item -> {
                attributeIdList.add(item.getAttributeId());
                attributeMap.put(item.getAttributeId(), item.getAttributeName());
            });
            lqw.in(AttributeValueEntity::getAttributeId, attributeIdList);
        }
        List<AttributeValueEntity> attributeValueEntityList = this.list(lqw);
        List<AttributeValueDTO> attributeValueDTOList = new ArrayList<>();
        attributeValueEntityList.forEach(item -> {
            AttributeValueDTO attributeValueDTO = BeanUtil.copyProperties(item, AttributeValueDTO.class);
            attributeValueDTO.setAttributeName(attributeMap.get(item.getAttributeId()));
            attributeValueDTOList.add(attributeValueDTO);
        });
        return attributeValueDTOList;
    }

    @Override
    public AttributeValueEntity getAttributeValueById(Integer id) {
        return attributeValueDao.getAttributeValueById(id);
    }
}