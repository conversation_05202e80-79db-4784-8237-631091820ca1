package com.navigator.goods.domain.sku.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.goods.dao.SkuAttributeValueDao;
import com.navigator.goods.domain.sku.ISkuAttributeValueDomainService;
import com.navigator.goods.mapper.SkuAttributeValueMapper;
import com.navigator.goods.pojo.entity.SkuAttributeValueEntity;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Service
public class SkuAttributeValueDomainServiceImpl extends ServiceImpl<SkuAttributeValueMapper, SkuAttributeValueEntity> implements ISkuAttributeValueDomainService {

    @Resource
    private SkuAttributeValueDao skuAttributeValueDao;


    @Override
    public List<SkuAttributeValueEntity> querySkuAttributeValueList(SkuAttributeValueQO condition) {
        return skuAttributeValueDao.querySkuAttributeValueList(condition);
    }


    @Override
    public SkuAttributeValueEntity getSkuAttributeValueById(Integer id) {
        return skuAttributeValueDao.getSkuAttributeValueById(id);
    }
}