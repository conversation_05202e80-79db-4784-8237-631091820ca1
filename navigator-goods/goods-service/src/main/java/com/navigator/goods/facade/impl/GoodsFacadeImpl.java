package com.navigator.goods.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import com.navigator.goods.service.IGoodsService;
import com.navigator.goods.service.manager.GoodsRedisManager;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 商品服务
 *
 * <AUTHOR>
 * @since 2021-11-25 19:27
 */
@RestController
public class GoodsFacadeImpl implements GoodsFacade {

    @Resource
    private IGoodsService goodsService;
    @Resource
    private GoodsRedisManager goodsRedisManager;

    @Override
    public GoodsInfoVO acquireGoodsInfo(GoodsSpecDTO goodsSpecDTO) {
        return goodsService.acquireGoodsInfo(goodsSpecDTO);
    }

    @Override
    public Result queryGoodsTaxRate(GoodsSpecDTO goodsSpecDTO) {
        return goodsService.queryGoodsTaxRate(goodsSpecDTO);
    }

    @Override
    public GoodsInfoVO findGoodsById(Integer goodsId) {
        return goodsService.findGoodsById(goodsId);
    }

    @Override
    public GoodsDTO findGoodsDetail(Integer goodsId) {
        return goodsService.findGoodsDetail(goodsId);
    }

    @Override
    public GoodsDTO findGoodsInfo(String goodsName) {
        return goodsService.findGoodsDetail(goodsName);
    }

    @Override
    public List<GoodsInfoVO> queryGoodsListBySpec(Integer categoryId, Integer packageId, Integer specId) {
        return goodsService.queryGoodsListBySpec(categoryId, packageId, specId);
    }

    @Override
    public List<GoodsSearchDTO> getAllGoodsList(Integer status) {
        return goodsService.getAllGoodsList(status);
    }

    @Override
    public List<GoodsSearchDTO> getAllGoodsListByCategoryId(Integer status, Integer categoryId) {
        return goodsService.getAllGoodsListByCategoryId(status, categoryId);
    }

    @Override
    public List<GoodsSearchDTO> getAllLkgGoodsList(Integer status) {
        List<GoodsSearchDTO> allGoodsList = goodsService.getAllGoodsList(status);

        // 按照linkageGoodsName分组
        return allGoodsList.stream()
                .collect(Collectors.groupingBy(GoodsSearchDTO::getLinkageGoodsName))
                .values().stream()
                .map(goodsSearchDTOS -> goodsSearchDTOS.get(0))
                .collect(Collectors.toList());
    }

    @Override
    public Result queryGoodsList(QueryDTO<GoodsSearchDTO> goodsSearchDTO) {
        return goodsService.queryGoodsList(goodsSearchDTO);
    }

    @Override
    public Result invalidGoods(Integer goodsId, Integer status, String futurePrefix, String futureSuffix, String taxRate) {
        return Result.judge(goodsService.invalidGoods(goodsId, status, futurePrefix, futureSuffix, taxRate));
    }

    @Override
    public Result updateGoodsName() {
        return goodsService.updateGoodsName();
    }

    @Override
    public void removeGoodsCache(Integer id) {
        goodsRedisManager.removeGoodsCache(id);
    }

    @Override
    public void removeAllGoodsCache() {
        goodsRedisManager.removeAllGoodsCache();
    }

    @Override
    public Result importGoodsInfo(MultipartFile uploadFile) {
        return goodsService.importGoodsInfo(uploadFile);
    }

    @Override
    public Result updateGoodsDeliveryStatus(Integer goodsId, Integer deliveryStatus) {
        return Result.success(goodsService.updateGoodsDeliveryStatus(goodsId, deliveryStatus));
    }

    @Override
    public List<Integer> queryCannotDeliveryGoodsIdList() {
        return goodsService.queryCannotDeliveryGoodsList().stream().map(GoodsEntity::getId).collect(Collectors.toList());
    }
}
