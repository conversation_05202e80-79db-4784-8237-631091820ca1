package com.navigator.goods.facade.impl;

import com.navigator.goods.facade.GoodsIdNameFacade;
import com.navigator.goods.service.IIdNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 商品域Id-Name服务接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
public class GoodsIdNameFacadeImpl implements GoodsIdNameFacade {

    @Resource
    private IIdNameService idNameService;

    @Override
    public void refreshCache() {
        idNameService.refreshCache();
    }
}
