package com.navigator.goods.dao;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.goods.mapper.CategoryAttributeMapper;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.CategoryAttributeEntity;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 品类规格关联 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
@Dao
public class CategoryAttributeDao extends BaseDaoImpl<CategoryAttributeMapper, CategoryAttributeEntity> {

    /**
     * 列表
     *
     * @return
     */
    public List<CategoryAttributeEntity> queryCategoryAttributeList(CategoryAttributeQO queryDTO) {
        return this.list(CategoryAttributeEntity.lqw(queryDTO, true));
    }

    /**
     * 根据ID：获取品类规格关联
     *
     * @param id
     * @return
     */
    public CategoryAttributeEntity getCategoryAttributeById(Integer id) {
        return this.getById(id);
    }

    /**
     * 新增
     *
     * @param category1
     * @param category2
     * @param category3
     * @param attributeEntity
     * @param sort
     * @return
     */
    public CategoryAttributeEntity addCategoryAttributeEntity(Integer category1, Integer category2, Integer category3, AttributeEntity attributeEntity, int sort) {
        List<CategoryAttributeEntity> categoryAttributeEntityList = this.list(Wrappers.<CategoryAttributeEntity>lambdaQuery()
                .eq(CategoryAttributeEntity::getCategory1, category1)
                .eq(CategoryAttributeEntity::getCategory2, category2)
                .eq(CategoryAttributeEntity::getCategory3, category3)
                .eq(CategoryAttributeEntity::getAttributeId, attributeEntity.getId())
        );
        if (!CollectionUtils.isEmpty(categoryAttributeEntityList)) {
            return categoryAttributeEntityList.get(0);
        }

        CategoryAttributeEntity categoryAttributeEntity = new CategoryAttributeEntity()
                .setCategory1(category1)
                .setCategory2(category2)
                .setCategory3(category3)
                .setAttributeId(attributeEntity.getId())
                .setAttributeName(attributeEntity.getName())
                .setAttributeType(attributeEntity.getType())
                .setSort(sort)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date());
        this.save(categoryAttributeEntity);
        return categoryAttributeEntity;
    }

    /**
     * 新增
     *
     * @param serialNo1
     * @param serialNo2
     * @param serialNo3
     * @param keyAttributeEntityList
     * @param saleAttributeEntityList
     * @param packageAttributeEntity
     */
    public void addCategoryAttribute(Integer serialNo1, Integer serialNo2, Integer serialNo3, List<AttributeEntity> keyAttributeEntityList, List<AttributeEntity> saleAttributeEntityList, AttributeEntity packageAttributeEntity) {
        // 关键规格
        int sort = 1;
        for (AttributeEntity item : keyAttributeEntityList) {
            this.addCategoryAttributeEntity(serialNo1, serialNo2, serialNo3, item, sort++);
        }
        // 销售规格
        sort = 100;
        if (CollUtil.isNotEmpty(saleAttributeEntityList)) {
            for (AttributeEntity item : saleAttributeEntityList) {
                this.addCategoryAttributeEntity(serialNo1, serialNo2, serialNo3, item, sort++);
            }
        }
        // 包装规格
        sort = 200;
        this.addCategoryAttributeEntity(serialNo1, serialNo2, serialNo3, packageAttributeEntity, sort++);
    }

    public CategoryAttributeEntity getCategoryAttributeByType(Integer category3, Integer attributeType) {
        List<CategoryAttributeEntity> categoryAttributeEntityList = this.list(
                CategoryAttributeEntity
                        .lqw(null, true)
                        .eq(CategoryAttributeEntity::getCategory3, category3)
                        .eq(CategoryAttributeEntity::getAttributeType, attributeType)
        );
        return CollectionUtils.isEmpty(categoryAttributeEntityList) ? null : categoryAttributeEntityList.get(0);
    }

    public CategoryAttributeEntity getAttributeByCategoryAndType(Integer category1, Integer category2,
                                                                 Integer category3, Integer attributeId) {
        List<CategoryAttributeEntity> categoryAttributeEntityList = this.list(CategoryAttributeEntity.lqw()
                .eq(null != category1, CategoryAttributeEntity::getCategory1, category1)
                .eq(null != category2, CategoryAttributeEntity::getCategory2, category2)
                .eq(null != category3, CategoryAttributeEntity::getCategory3, category3)
                .eq(null != attributeId, CategoryAttributeEntity::getAttributeId, attributeId)
        );
        return CollectionUtils.isEmpty(categoryAttributeEntityList) ? null : categoryAttributeEntityList.get(0);
    }

}
