package com.navigator.goods.service.manager;

import com.navigator.common.constant.RedisConstants;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.goods.dao.GoodsDao;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.GoodsEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-05-03 23:56
 */
@Service
@Slf4j
public class GoodsRedisManager {
    @Resource
    private GoodsDao goodsDao;
    @Resource
    private RedisUtil redisUtil;

    /**
     * 根据规格包装信息查询商品
     *
     * @param goodsSpecDTO 包装规格条件信息
     * @return 商品信息
     */
    public GoodsEntity findGoodsBySpecId(GoodsSpecDTO goodsSpecDTO) {
        String redisItem = goodsSpecDTO.getCategoryId() + "_" +
                goodsSpecDTO.getPackageId() + "_" + goodsSpecDTO.getSpecId() + "_" + goodsSpecDTO.getSupplierId();
        String goodsEntityJson = (String) redisUtil.hget(RedisConstants.GOODS_CATEGORY_INFO, redisItem);
        if (!StringUtils.isBlank(goodsEntityJson)) {
            return FastJsonUtils.getJsonToBean(goodsEntityJson, GoodsEntity.class);
        }
        GoodsEntity goodsEntity = goodsDao.findGoodsBySpecId(goodsSpecDTO);
        if (null != goodsEntity) {
            redisUtil.hset(RedisConstants.GOODS_CATEGORY_INFO, redisItem, FastJsonUtils.getBeanToJson(goodsEntity));
        }
        return goodsEntity;
    }

    public void removeGoodsCache(Integer id) {
        log.info("清空GoodsId缓存......" + id);
        if (null != id) {
            GoodsEntity goodsEntity = goodsDao.getById(id);
            String redisItem = goodsEntity.getCategoryId() + "_" +
                    goodsEntity.getPackageId() + "_" + goodsEntity.getSpecId() + "_" + goodsEntity.getSupplierId();
            redisUtil.hdel(RedisConstants.GOODS_CATEGORY_INFO, redisItem);
        } else {
            List<GoodsEntity> goodsEntities = goodsDao.getAllGoodsList(null);
            for (GoodsEntity goodsEntity : goodsEntities) {
                String redisItem = goodsEntity.getCategoryId() + "_" +
                        goodsEntity.getPackageId() + "_" + goodsEntity.getSpecId() + "_" + goodsEntity.getSupplierId();
                redisUtil.hdel(RedisConstants.GOODS_CATEGORY_INFO, redisItem);
            }
        }
    }

    public void removeAllGoodsCache() {
        log.info("清空Goods缓存......");
        redisUtil.del(RedisConstants.GOODS_CATEGORY_INFO);
    }
}
