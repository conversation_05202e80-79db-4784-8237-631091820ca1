package com.navigator.goods.facade.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.domain.spu.ISpuDomainService;
import com.navigator.goods.facade.SpuFacade;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.dto.SpuRefreshDTO;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.goods.pojo.qo.SpuQO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * SPU商品 Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@RestController
public class SpuFacadeImpl implements SpuFacade {

    @Resource
    private ISpuDomainService spuDomainService;

    @Override
    public Page<SpuDTO> querySpuDTOPage(QueryDTO<SpuQO> queryDTO) {
        return spuDomainService.querySpuDTOPage(queryDTO);
    }

    @Override
    public List<SpuDTO> querySpuDTOList(SpuQO condition) {
        return spuDomainService.querySpuDTOList(condition);
    }

    @Override
    public SpuEntity getSpuById(Integer id) {
        return spuDomainService.getSpuById(id);
    }

    @Override
    public SpuEntity getSpuByNo(String spuNo) {
        return spuDomainService.getSpuBySpuNo(spuNo);
    }

    @Override
    public Result<List<SpuDTO>> refreshSpu(SpuRefreshDTO spuRefreshDTO) {
        return Result.success(spuDomainService.refreshSpu(spuRefreshDTO));
    }

    @Override
    public Result saveSpu(List<SpuDTO> spuDTOList) {
        return spuDomainService.saveSpu(spuDTOList);
    }

}