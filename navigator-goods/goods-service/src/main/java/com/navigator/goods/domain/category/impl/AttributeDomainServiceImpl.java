package com.navigator.goods.domain.category.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.goods.dao.AttributeDao;
import com.navigator.goods.dao.AttributeValueDao;
import com.navigator.goods.domain.category.IAttributeDomainService;
import com.navigator.goods.mapper.AttributeMapper;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeQO;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 规格 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 */
@Service
public class AttributeDomainServiceImpl extends ServiceImpl<AttributeMapper, AttributeEntity> implements IAttributeDomainService {

    @Resource
    private AttributeDao attributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;
    @Resource
    private OperationLogFacade operationLogFacade;


    @Override
    public Page<AttributeDTO> queryAttributeDTOPage(QueryDTO<AttributeQO> queryDTO) {
        Page<AttributeEntity> page = attributeDao.queryAttributePage(queryDTO);
        Page result = new Page<AttributeDTO>();
        result.setPages(page.getPages());
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        List<AttributeDTO> records = new ArrayList<>();
        page.getRecords().forEach(item -> records.add(this.toAttributeDTO(item)));
        result.setRecords(records);
        return result;
    }


    @Override
    public List<AttributeDTO> queryAttributeDTOList(AttributeQO condition) {
        List<AttributeDTO> records = new ArrayList<>();
        List<AttributeEntity> list = attributeDao.queryAttributeList(condition);
        list.forEach(item -> records.add(this.toAttributeDTO(item)));
        return records;
    }


    @Override
    public AttributeDTO getAttributeDTOById(Integer id) {
        return this.toAttributeDTO(attributeDao.getAttributeById(id));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttributeEntity addAttribute(AttributeAddDTO addDTO) {
        // 保存规格
        AttributeEntity attributeEntity = attributeDao.addAttribute(addDTO);
        // 保存规格值
        attributeValueDao.addAttributeValue(attributeEntity.getId(), addDTO.getValueNameSet());

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(addDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.ADD_GOODS_ATTRIBUTE)
                    .setReferBizId(attributeEntity.getId())
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return attributeEntity;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public AttributeEntity updateAttribute(AttributeUpdateDTO updateDTO) {
        // 保存规格
        AttributeEntity attributeEntity = attributeDao.updateAttribute(updateDTO);
        // 保存规格值
        attributeValueDao.addAttributeValue(attributeEntity.getId(), updateDTO.getValueNameSet());

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(updateDTO))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.MODIFY_GOODS_ATTRIBUTE)
                    .setReferBizId(attributeEntity.getId())
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return attributeEntity;
    }

    @Override
    public List<AttributeValueEntity> getAttributeValueListByType(Integer type) {
        List<AttributeEntity> attributeList = attributeDao.queryAttributeList(new AttributeQO().setType(type));
        List<Integer> filterAttributeIdList = attributeList.stream().map(AttributeEntity::getId).collect(Collectors.toList());
        return attributeValueDao.queryAttributeValueList(new AttributeValueQO().setFilterAttributeIdList(filterAttributeIdList));
    }

    /**
     * 转换
     *
     * @param attributeEntity
     * @return
     */
    private AttributeDTO toAttributeDTO(AttributeEntity attributeEntity) {
        AttributeDTO attributeDTO = BeanUtil.copyProperties(attributeEntity, AttributeDTO.class);
        List<AttributeValueEntity> attributeValueList = attributeValueDao.queryAttributeValueList(new AttributeValueQO().setAttributeId(attributeEntity.getId()));
        attributeDTO.setAttributeValueList(attributeValueList);
        attributeDTO.setUpdatedBy(IdNameConverter.getName(IdNameType.user_id_name, attributeDTO.getUpdatedBy()));
        return attributeDTO;
    }
}