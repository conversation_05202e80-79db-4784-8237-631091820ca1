package com.navigator.goods.domain.spu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.goods.pojo.entity.SpuAttributeValueEntity;

/**
 * <p>
 * SPU的关键规格信息 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
public interface ISpuAttributeValueDomainService extends IService<SpuAttributeValueEntity> {
    /**
     * 根据ID：获取SPU的关键规格信息
     *
     * @param id
     * @return
     */
    SpuAttributeValueEntity getSpuAttributeValueById(Integer id);
}
