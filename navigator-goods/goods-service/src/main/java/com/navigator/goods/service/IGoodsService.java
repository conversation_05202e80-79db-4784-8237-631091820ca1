package com.navigator.goods.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.goods.pojo.dto.GoodsDTO;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.GoodsEntity;
import com.navigator.goods.pojo.vo.GoodsInfoVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 商品基础表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IGoodsService {


    /**
     * 根据品类、包装、规格获取商品信息，无则创建
     *
     * @param goodsSpecDTO 商品品类包装规格-传参
     * @return 商品基本信息
     */
    GoodsInfoVO acquireGoodsInfo(GoodsSpecDTO goodsSpecDTO);

    Result queryGoodsTaxRate(GoodsSpecDTO goodsSpecDTO);

    /**
     * 根据商品ID查询商品信息
     *
     * @param goodsId 商品ID
     * @return 商品基本信息
     */
    GoodsInfoVO findGoodsById(Integer goodsId);

    GoodsDTO findGoodsDetail(Integer goodsId);

    GoodsDTO findGoodsDetail(String goodsName);

    /**
     * 根据包装、规格查询商品集合
     *
     * @param packageId 包装
     * @param specId    规格
     * @return 商品结果集
     */
    List<GoodsInfoVO> queryGoodsListBySpec(Integer categoryId, Integer packageId, Integer specId);

    /**
     * 根据状态获取所有商品信息
     *
     * @param status 商品状态
     * @return 所有商品名称集合
     */
    List<GoodsSearchDTO> getAllGoodsList(Integer status);


    List<GoodsSearchDTO> getAllGoodsListByCategoryId(Integer status, Integer categoryId);

    /**
     * 根据条件分页查询商品信息
     *
     * @param goodsSearchDTO 商品查询条件
     * @return 查询的商品结果集合
     */
    Result queryGoodsList(QueryDTO<GoodsSearchDTO> goodsSearchDTO);

    /**
     * 禁用/启用商品
     *
     * @param goodsId 商品ID
     * @param status  商品状态
     * @param taxRate
     * @return 禁用/启用结果
     */
    boolean invalidGoods(Integer goodsId, Integer status, String futurePrefix, String futureSuffix, String taxRate);

    Result updateGoodsName();

    Result importGoodsInfo(MultipartFile uploadFile);

    boolean updateGoodsDeliveryStatus(Integer goodsId, Integer deliveryStatus);

    List<GoodsEntity> queryCannotDeliveryGoodsList();
}
