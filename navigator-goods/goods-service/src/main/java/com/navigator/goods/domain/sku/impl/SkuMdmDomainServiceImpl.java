package com.navigator.goods.domain.sku.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.goods.dao.SkuDao;
import com.navigator.goods.dao.SkuMdmDao;
import com.navigator.goods.domain.sku.ISkuMdmDomainService;
import com.navigator.goods.mapper.SkuMdmMapper;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.entity.SkuMdmEntity;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * SKU-MDM Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Service

public class SkuMdmDomainServiceImpl extends ServiceImpl<SkuMdmMapper, SkuMdmEntity> implements ISkuMdmDomainService {

    @Resource
    private SkuMdmDao skuMdmDao;

    @Override
    public SkuMdmEntity getSkuMdmById(Integer id) {
        return skuMdmDao.getSkuMdmById(id);
    }

    @Override
    public List<SkuMdmEntity> querySkuMdmList(SkuMdmQO skuMdmQO) {
        return skuMdmDao.querySkuMdmList(skuMdmQO);
    }
}
