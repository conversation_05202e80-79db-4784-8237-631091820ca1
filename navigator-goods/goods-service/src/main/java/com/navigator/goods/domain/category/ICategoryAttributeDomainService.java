package com.navigator.goods.domain.category;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.goods.pojo.dto.CategoryAttributeDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.entity.CategoryAttributeEntity;
import com.navigator.goods.pojo.qo.CategoryAttributeQO;

import java.util.List;

/**
 * <p>
 * 品类规格关联 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
public interface ICategoryAttributeDomainService extends IService<CategoryAttributeEntity> {
    /**
     * 根据条件：获取品类规格关联列表
     *
     * @param condition
     * @return
     */
    List<CategoryAttributeDTO> queryCategoryAttributeList(CategoryAttributeQO condition);

    /**
     * 根据ID：获取品类规格关联
     *
     * @param id
     * @return
     */
    CategoryAttributeEntity getCategoryAttributeById(Integer id);

    /**
     * 新增
     *
     * @param serialNo1
     * @param serialNo2
     * @param serialNo3
     * @param keyAttributeEntityList
     * @param saleAttributeEntityList
     * @param packageAttributeEntity
     */
    void addCategoryAttribute(Integer serialNo1, Integer serialNo2, Integer serialNo3, List<AttributeEntity> keyAttributeEntityList, List<AttributeEntity> saleAttributeEntityList, AttributeEntity packageAttributeEntity);
}
