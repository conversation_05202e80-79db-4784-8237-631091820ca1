package com.navigator.goods.domain.sku;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SkuRefreshDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.qo.SkuQO;

import java.util.List;

/**
 * <p>
 * SKU货品 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
public interface ISkuDomainService extends IService<SkuEntity> {
    /**
     * 根据条件：获取SKU货品DTO分页
     *
     * @param queryDTO
     * @return
     */
    Page<SkuDTO> querySkuDTOPage(QueryDTO<SkuQO> queryDTO);

    /**
     * 根据条件：获取SKU货品DTO列表
     *
     * @param condition
     * @return
     */
    List<SkuDTO> querySkuDTOList(SkuQO condition);

    List<SkuEntity> getSkuListByIds(List<Integer> skuIdList);

    SkuDTO getSkuDTOById(Integer id);

    /**
     * 根据ID：获取SKU货品
     *
     * @param id
     * @return
     */
    SkuEntity getSkuById(Integer id);

    /**
     * 根据全称：获取SKU货品
     *
     * @param fullName
     * @return
     */
    SkuEntity getSkuByFullName(String fullName);

    /**
     * 根据编码：获取SKU货品
     *
     * @param code
     * @return
     */
    SkuEntity getSkuBySkuNo(String code);

    /**
     * 刷新：SKU货品
     *
     * @param skuRefreshDTO
     * @return
     */
    List<SkuAddDTO> refreshSku(SkuRefreshDTO skuRefreshDTO);

    /**
     * 保存：SKU货品
     *
     * @param skuAddDTOList
     * @return
     */
    Result saveSku(List<SkuAddDTO> skuAddDTOList);

    /**
     * 查询不可提货的SKU货品ID
     *
     * @return
     */
    List<Integer> queryCannotDeliverySkuIdList();

    /**
     * 处理历史数据
     *
     * @param id
     */
    void processHistoryData(Integer id);
}
