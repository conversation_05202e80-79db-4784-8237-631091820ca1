package com.navigator.goods.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.goods.mapper.GoodsMapper;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import com.navigator.goods.pojo.entity.GoodsEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 14:12
 */
@Dao
public class GoodsDao extends BaseDaoImpl<GoodsMapper, GoodsEntity> {

    /**
     * 根据规格包装信息查询商品
     *
     * @param goodsSpecDTO 包装规格条件信息
     * @return 商品信息
     */
    public GoodsEntity findGoodsBySpecId(GoodsSpecDTO goodsSpecDTO) {
        List<GoodsEntity> goodsEntityList = this.list(Wrappers.<GoodsEntity>lambdaQuery()
//                .eq(null != goodsSpecDTO.getSupplierId(), GoodsEntity::getSupplierId, goodsSpecDTO.getSupplierId())
                        .eq(null != goodsSpecDTO.getCategoryId(), GoodsEntity::getCategoryId, goodsSpecDTO.getCategoryId())
                        .eq(null != goodsSpecDTO.getPackageId(), GoodsEntity::getPackageId, goodsSpecDTO.getPackageId())
                        .eq(null != goodsSpecDTO.getSpecId(), GoodsEntity::getSpecId, goodsSpecDTO.getSpecId())
                        .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .eq(GoodsEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
        );
        return CollectionUtil.isEmpty(goodsEntityList) ? null : goodsEntityList.get(0);
    }

    /**
     * 根据规格包装信息查询商品
     *
     * @return 商品信息
     */
    public List<GoodsEntity> findGoodsList(Integer categoryId, Integer specId, Integer packageId) {
        return this.list(Wrappers.<GoodsEntity>lambdaQuery()
                .eq(null != categoryId, GoodsEntity::getCategoryId, categoryId)
                .eq(null != packageId, GoodsEntity::getPackageId, packageId)
                .eq(null != specId, GoodsEntity::getSpecId, specId)
                .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据状态获取所有商品信息
     *
     * @param status 商品状态
     * @return 所有商品名称集合
     */
    public List<GoodsEntity> getAllGoodsList(Integer status) {
        return this.list(Wrappers.<GoodsEntity>lambdaQuery()
                .eq(null != status, GoodsEntity::getStatus, status)
                .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }


    public List<GoodsEntity> getAllGoodsListByCategoryId(Integer status, Integer categoryId) {
        return this.list(Wrappers.<GoodsEntity>lambdaQuery()
                .eq(null != status, GoodsEntity::getStatus, status)
                .eq(null != categoryId, GoodsEntity::getCategoryId, categoryId)
                .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    /**
     * 根据商品名称获取所有商品信息
     *
     * @param goodsName 商品名称
     * @return 所有商品名称集合
     */
    public List<GoodsEntity> getGoodsByName(String goodsName) {
        return this.list(Wrappers.<GoodsEntity>lambdaQuery()
                .eq(StrUtil.isNotBlank(goodsName), GoodsEntity::getName, goodsName)
                .eq(GoodsEntity::getStatus, DisableStatusEnum.ENABLE.getValue())
                .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue()));
    }

    public IPage<GoodsEntity> queryGoodsList(QueryDTO<GoodsSearchDTO> goodsQueryDTO) {
        GoodsSearchDTO goodsSearchDTO = goodsQueryDTO.getCondition();
        return this.page(new Page<>(goodsQueryDTO.getPageNo(), goodsQueryDTO.getPageSize()), new LambdaQueryWrapper<GoodsEntity>()
                .eq(null != goodsSearchDTO.getId(), GoodsEntity::getId, goodsSearchDTO.getId())
                .eq(StrUtil.isNotBlank(goodsSearchDTO.getCode()), GoodsEntity::getCode, goodsSearchDTO.getCode())
                .eq(StrUtil.isNotBlank(goodsSearchDTO.getLinkageGoodsCode()), GoodsEntity::getLinkageGoodsCode, goodsSearchDTO.getLinkageGoodsCode())
                .like(StrUtil.isNotBlank(goodsSearchDTO.getName()), GoodsEntity::getName, "%" + goodsSearchDTO.getName() + "%")
                .like(StrUtil.isNotBlank(goodsSearchDTO.getLinkageGoodsName()), GoodsEntity::getLinkageGoodsName, "%" + goodsSearchDTO.getLinkageGoodsName() + "%")
                .eq(null != goodsSearchDTO.getIsDelivery(), GoodsEntity::getIsDelivery, goodsSearchDTO.getIsDelivery())
                .eq(GoodsEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(GoodsEntity::getId)
        );
    }

    public List<GoodsEntity> queryCannotDeliveryGoodsList() {
        // 禁用 或者 未启用 或者 已删除
        return this.list(Wrappers.<GoodsEntity>lambdaQuery()
                .eq(GoodsEntity::getStatus, DisableStatusEnum.DISABLE.getValue())
                .or()
                .eq(GoodsEntity::getIsDelivery, DisableStatusEnum.DISABLE.getValue()));
    }
}
