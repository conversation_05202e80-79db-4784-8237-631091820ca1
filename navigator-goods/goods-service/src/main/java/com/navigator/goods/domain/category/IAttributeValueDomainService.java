package com.navigator.goods.domain.category;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.goods.pojo.dto.AttributeValueDTO;
import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.qo.AttributeValueQO;

import java.util.List;

/**
 * <p>
 * 规格值 Service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
public interface IAttributeValueDomainService extends IService<AttributeValueEntity> {

    /**
     * 根据条件：获取规格值DTO列表
     *
     * @param condition
     * @return
     */
    List<AttributeValueDTO> queryAttributeValueDTOList(AttributeValueQO condition);

    /**
     * 根据ID：获取规格值
     *
     * @param id
     * @return
     */
    AttributeValueEntity getAttributeValueById(Integer id);

}
