package com.navigator.goods.facade.impl;

import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.dao.CategoryDao;
import com.navigator.goods.domain.category.ICategoryDomainService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.dto.*;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import com.navigator.goods.service.ICategoryService;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021-11-30 17:09
 */
@RestController
public class CategoryFacadeImpl implements CategoryFacade {
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private ICategoryService categoryService;
    @Resource
    private ICategoryDomainService categoryDomainService;

    @Override
    public List<CategoryEntity> getAllCategoryList(Integer level) {
        return categoryDao.getAllCategoryList(level);
    }

    @Override
    public Result queryCategoryList(QueryDTO<CategorySearchDTO> categorySearchDTO) {
        return categoryService.queryCategoryList(categorySearchDTO);
    }

//    @Override
//    public CategoryEntity getCategoryById(Integer categoryId) {
//        return categoryDao.getById(categoryId);
//    }

    @Override
    public CategoryEntity getCategoryByCode(String categoryCode) {
        return categoryService.getCategoryByCode(categoryCode);
    }

    @Override
    public Result updateCategoryStatus(CategoryCreateDTO categoryCreateDTO) {
        return Result.judge(categoryService.updateCategory(categoryCreateDTO));
    }

    @Override
    public Result invalidCategory(Integer categoryId, Integer status) {
        return Result.judge(categoryService.invalidCategory(categoryId, status));
    }

    // 新
    @Override
    public Page<CategoryDTO> queryCategoryDTOPage(QueryDTO<CategoryQO> queryDTO) {
        return categoryDomainService.queryCategoryDTOPage(queryDTO);
    }

    @Override
    public List<CategoryEntity> queryCategoryList(CategoryQO condition) {
        return categoryDomainService.queryCategoryList(condition);
    }

    @Override
    public List<CategoryDTO> queryCategoryDTOList(CategoryQO condition) {
        return categoryDomainService.queryCategoryDTOList(condition);
    }

    @Override
    public List<Integer> queryCategoryIdList(CategoryQO condition) {
        return categoryDomainService.queryCategoryIdList(condition);
    }

    @Override
    public List<Integer> queryCategorySerialNoList(CategoryQO condition) {
        return categoryDomainService.queryCategorySerialNoList(condition);
    }

    @Override
    public CategoryDTO getCategoryDTOById(Integer id) {
        return categoryDomainService.getCategoryDTOById(id);
    }

    @Override
    public CategoryEntity getBasicCategoryBySerialNo(Integer serialNo) {
        return categoryDomainService.getBasicCategoryBySerialNo(serialNo);
    }

    @Override
    public CategoryDTO getCategoryDTOBySerialNo(Integer serialNo) {
        return categoryDomainService.getCategoryDTOBySerialNo(serialNo);
    }

    @Override
    public Result addCategory(CategoryAddDTO addDTO) {
        categoryDomainService.addCategory(addDTO);
        return Result.success();
    }

    @Override
    public Result updateCategoryStatus(CategoryUpdateDTO updateDTO) {
        categoryDomainService.updateCategory(updateDTO);
        return Result.success();
    }

    @Override
    public List<Tree<Integer>> queryCategoryMenu(Integer systemId, Integer customerId) {
        return categoryDomainService.queryCategoryMenu(systemId, customerId);
    }

    @Override
    public List<Tree<Integer>> queryCategoryTree() {
        return categoryDomainService.queryCategoryTree();
    }

    @Override
    public List<String> queryFutureCodeList(Integer category2) {
        return categoryDomainService.queryFutureCodeList(category2);
    }

    @Override
    public CategoryEntity getParentCategoryBySerialNo(Integer serialNo) {
        return categoryDomainService.getParentCategoryBySerialNo(serialNo);
    }

    @Override
    public List<CategoryEntity> getCategoryNameBySerialNoList(List<Integer> serialNoList) {
        return categoryDomainService.getCategoryNameBySerialNoList(serialNoList);
    }

    @Override
    public String assemblyCategoryNames(List<Integer> serialNoList) {
        if (CollectionUtils.isEmpty(serialNoList)) {
            return "";
        }
        List<CategoryEntity> categoryEntityList = categoryDomainService.getCategoryNameBySerialNoList(serialNoList);
        return categoryEntityList.stream().map(CategoryEntity::getName).collect(Collectors.joining(","));
    }

    @Override
    public CategoryEntity getCategoryByName(String name, Integer level) {
        return categoryDomainService.getCategoryByName(name, level);
    }

    @Override
    public List<Integer> getSerialNoListByCategoryNames(List<String> nameList, Integer level) {
        List<CategoryEntity> categoryEntityList = categoryDomainService.getByCategoryNames(nameList, level);
        return categoryEntityList.stream().map(CategoryEntity::getSerialNo).collect(Collectors.toList());
    }
}
