package com.navigator.goods.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.additional.update.impl.LambdaUpdateChainWrapper;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.goods.mapper.AttributeValueRelationMapper;
import com.navigator.goods.pojo.entity.AttributeValueRelationEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 13:55
 */
@Dao
public class AttributeValueRelationDao extends BaseDaoImpl<AttributeValueRelationMapper, AttributeValueRelationEntity> {

    /**
     * 根据attributeValueId 获取属性值绑定关系
     *
     * @param attributeValueId 属性值ID
     * @return 绑定关系结果
     */
    public List<AttributeValueRelationEntity> getValueRelationListByValueId(Integer attributeValueId) {
        return this.list(Wrappers.<AttributeValueRelationEntity>lambdaQuery()
                .eq(AttributeValueRelationEntity::getAttributeValueId, attributeValueId)
                .eq(AttributeValueRelationEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    /**
     * 批量删除某商品的所有规格属性
     *
     * @param goodsId 商品ID
     * @return
     */
    private boolean deleteAttributeValueRelation(Integer goodsId) {
        return new LambdaUpdateChainWrapper<>(this.baseMapper)
                .eq(AttributeValueRelationEntity::getGoodsId, goodsId)
                .set(AttributeValueRelationEntity::getIsDeleted, IsDeletedEnum.DELETED.getValue())
                .update();
    }
}
