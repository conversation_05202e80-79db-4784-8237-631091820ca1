package com.navigator.goods.service.impl;

import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.goods.domain.category.IAttributeDomainService;
import com.navigator.goods.domain.category.IAttributeValueDomainService;
import com.navigator.goods.domain.category.ICategoryDomainService;
import com.navigator.goods.domain.sku.ISkuDomainService;
import com.navigator.goods.domain.spu.ISpuDomainService;
import com.navigator.goods.pojo.entity.*;
import com.navigator.goods.service.IIdNameService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * Id-Name服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class IdNameServiceImpl implements IIdNameService {

    @Resource
    private ISpuDomainService spuDomainService;

    @Resource
    private ISkuDomainService skuDomainService;

    @Resource
    private ICategoryDomainService categoryDomainService;

    @Resource
    private IAttributeDomainService attributeDomainService;

    @Resource
    private IAttributeValueDomainService attributeValueDomainService;

    @Override
    public void refreshCache() {
        log.info("加载缓存开始。");
        IdNameConverter.setCache(IdNameType.category_id_name, categoryDomainService.list(CategoryEntity.lqw(null, true).select(CategoryEntity::getId, CategoryEntity::getName)));
        IdNameConverter.setCache(IdNameType.category_id_serialNo, categoryDomainService.list(CategoryEntity.lqw(null, true).select(CategoryEntity::getId, CategoryEntity::getSerialNo)), "id", "serialNo");
        IdNameConverter.setCache(IdNameType.category_serialNo_name, categoryDomainService.list(CategoryEntity.lqw(null, true).select(CategoryEntity::getSerialNo, CategoryEntity::getName)), "serialNo", "name");
        IdNameConverter.setCache(IdNameType.attribute_id_name, attributeDomainService.list(AttributeEntity.lqw(null, true).select(AttributeEntity::getId, AttributeEntity::getName)));
        IdNameConverter.setCache(IdNameType.attributeValue_id_name, attributeValueDomainService.list(AttributeValueEntity.lqw(null, true).select(AttributeValueEntity::getId, AttributeValueEntity::getName)));
        IdNameConverter.setCache(IdNameType.spu_id_name, spuDomainService.list(SpuEntity.lqw(null, true).select(SpuEntity::getId, SpuEntity::getSpuName)), "id", "spuName");
        IdNameConverter.setCache(IdNameType.sku_id_name, skuDomainService.list(SkuEntity.lqw(null, true).select(SkuEntity::getId, SkuEntity::getFullName)), "id", "fullName");
        log.info("加载缓存完成。");
    }
}