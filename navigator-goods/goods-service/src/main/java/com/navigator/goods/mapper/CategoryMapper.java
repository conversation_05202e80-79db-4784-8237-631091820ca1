package com.navigator.goods.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.goods.pojo.entity.CategoryEntity;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 品类信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface CategoryMapper extends BaseMapper<CategoryEntity> {

    @Insert("<script>" +
            "SET IDENTITY_INSERT  [dbo].[dbg_category]  ON;" +
            "INSERT INTO [dbo].[dbg_category] ([id], [parent_id], [name], [code], [sales_type], [level], [port], [delivery_address], [is_deleted], [created_at], [updated_at], [tax_rate],  [status], [linkage_category_code], [linkage_category_name], [linkage_parent_category_name], [serial_no], [is_dce], [is_soybean2], [future_code], [is_split_contract], [created_by], [updated_by]) VALUES " +
            "(#{categoryEntity.id}, #{categoryEntity.parentId}, #{categoryEntity.name}, #{categoryEntity.code}, #{categoryEntity.salesType}, #{categoryEntity.level}, #{categoryEntity.port}, #{categoryEntity.deliveryAddress}, #{categoryEntity.isDeleted}, #{categoryEntity.createdAt}, #{categoryEntity.updatedAt}, #{categoryEntity.taxRate}, #{categoryEntity.status}, #{categoryEntity.linkageCategoryCode}, #{categoryEntity.linkageCategoryName}, #{categoryEntity.linkageParentCategoryName}, #{categoryEntity.serialNo}, #{categoryEntity.isDce}, #{categoryEntity.isSoybean2}, #{categoryEntity.futureCode}, #{categoryEntity.isSplitContract}, #{categoryEntity.createdBy}, #{categoryEntity.updatedBy}) " +
            "SET IDENTITY_INSERT  [dbo].[dbg_category]  OFF;" +
            "</script>")
    int saveCategoryWithId(@Param("categoryEntity") CategoryEntity categoryEntity);

}
