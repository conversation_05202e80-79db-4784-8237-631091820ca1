package com.navigator.goods.dao;

import cn.hutool.core.map.MapUtil;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.goods.mapper.SkuAttributeValueMapper;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.entity.SkuAttributeValueEntity;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@Dao
public class SkuAttributeValueDao extends BaseDaoImpl<SkuAttributeValueMapper, SkuAttributeValueEntity> {
    /**
     * 根据条件：获取SKU货品规格列表
     *
     * @param condition
     * @return
     */
    public List<SkuAttributeValueEntity> querySkuAttributeValueList(SkuAttributeValueQO condition) {
        return this.list(SkuAttributeValueEntity.lqw(condition));
    }

    /**
     * 根据ID：获取SKU货品规格值
     *
     * @param id
     * @return
     */
    public SkuAttributeValueEntity getSkuAttributeValueById(Integer id) {
        return this.getById(id);
    }

    /**
     * 保存
     *
     * @param skuAddDTO
     * @param attributeValueMap
     */
    public void saveSkuAttributeValue(SkuAddDTO skuAddDTO, Map<String, Object> attributeValueMap) {
        Integer attributeId = MapUtil.getInt(attributeValueMap, "attributeId");
        String attributeName = MapUtil.getStr(attributeValueMap, "attributeName");
        Integer attributeType = MapUtil.getInt(attributeValueMap, "attributeType");
        Integer attributeValueId = MapUtil.getInt(attributeValueMap, "attributeValueId");
        String attributeValueName = MapUtil.getStr(attributeValueMap, "attributeValueName");

        List<SkuAttributeValueEntity> skuAttributeValueEntityList = this.list(
                SkuAttributeValueEntity.lqw(null)
                        .eq(SkuAttributeValueEntity::getSkuId, skuAddDTO.getId())
                        .eq(SkuAttributeValueEntity::getAttributeId, attributeId)
                        .eq(SkuAttributeValueEntity::getAttributeValueId, attributeValueId)
        );
        SkuAttributeValueEntity entity = null;
        if (CollectionUtils.isEmpty(skuAttributeValueEntityList)) {
            entity = new SkuAttributeValueEntity();
            entity.setSkuId(skuAddDTO.getId());
            entity.setCategory1(skuAddDTO.getCategory1());
            entity.setCategory2(skuAddDTO.getCategory2());
            entity.setCategory3(skuAddDTO.getCategory3());
            entity.setAttributeId(attributeId);
            entity.setAttributeName(attributeName);
            entity.setAttributeType(attributeType);
            entity.setAttributeValueId(attributeValueId);
            entity.setAttributeValueName(attributeValueName);
            entity.setIsDeleted(0);
            entity.setCreatedBy(JwtUtils.getCurrentUserId());
            entity.setCreatedAt(new Date());
            entity.setUpdatedBy(JwtUtils.getCurrentUserId());
            entity.setUpdatedAt(new Date());
            this.save(entity);
        } else {
            entity = skuAttributeValueEntityList.get(0);
            entity.setAttributeId(attributeId);
            entity.setAttributeName(attributeName);
            entity.setAttributeType(attributeType);
            entity.setAttributeValueId(attributeValueId);
            entity.setAttributeValueName(attributeValueName);
            entity.setUpdatedBy(JwtUtils.getCurrentUserId());
            entity.setUpdatedAt(new Date());
            this.updateById(entity);
        }
    }
}
