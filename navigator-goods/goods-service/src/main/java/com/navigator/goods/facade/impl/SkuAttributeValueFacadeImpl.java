package com.navigator.goods.facade.impl;

import com.navigator.goods.domain.sku.ISkuAttributeValueDomainService;
import com.navigator.goods.facade.SkuAttributeValueFacade;
import com.navigator.goods.pojo.entity.SkuAttributeValueEntity;
import com.navigator.goods.pojo.qo.SkuAttributeValueQO;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Facade实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-21
 */
@RestController
public class SkuAttributeValueFacadeImpl implements SkuAttributeValueFacade {

    @Resource
    private ISkuAttributeValueDomainService skuAttributeValueDomainService;

    @Override
    public List<SkuAttributeValueEntity> querySkuAttributeValueList(SkuAttributeValueQO condition) {
        return skuAttributeValueDomainService.querySkuAttributeValueList(condition);
    }

    @Override
    public SkuAttributeValueEntity getSkuAttributeValueById(Integer id) {
        return skuAttributeValueDomainService.getSkuAttributeValueById(id);
    }
}