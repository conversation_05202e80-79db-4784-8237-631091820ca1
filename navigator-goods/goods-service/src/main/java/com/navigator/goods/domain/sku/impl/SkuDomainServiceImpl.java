package com.navigator.goods.domain.sku.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.SequenceFacade;
import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.SequenceEnum;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.goods.dao.*;
import com.navigator.goods.domain.sku.ISkuDomainService;
import com.navigator.goods.domain.spu.ISpuDomainService;
import com.navigator.goods.mapper.SkuMapper;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SkuAttributeValueDTO;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SkuRefreshDTO;
import com.navigator.goods.pojo.entity.*;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.qo.AttributeValueQO;
import com.navigator.goods.pojo.qo.SkuMdmQO;
import com.navigator.goods.pojo.qo.SkuQO;
import com.navigator.goods.pojo.qo.SpuQO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * SKU货品 Service实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Service
public class SkuDomainServiceImpl extends ServiceImpl<SkuMapper, SkuEntity> implements ISkuDomainService {

    @Resource
    private SkuDao skuDao;

    @Resource
    private SkuAttributeValueDao skuAttributeValueDao;

    @Resource
    private SkuMdmDao skuMdmDao;

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private CategoryAttributeDao categoryAttributeDao;

    @Resource
    private AttributeDao attributeDao;

    @Resource
    private AttributeValueDao attributeValueDao;

    @Resource
    private SpuDao spuDao;

    @Resource
    private SiteFacade siteFacade;

    @Resource
    private ISpuDomainService spuDomainService;

    @Resource
    private SequenceFacade sequenceFacade;
    @Resource
    private OperationLogFacade operationLogFacade;

    @Override
    public Page<SkuDTO> querySkuDTOPage(QueryDTO<SkuQO> queryDTO) {
        SkuQO condition = queryDTO.getCondition();
        Integer isNeedSkuMdmList = condition.getIsNeedSkuMdmList();
        if (StringUtil.isNotNullBlank(condition.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(condition.getSiteCode());
            if (siteEntity != null) {
                condition.setCategory3List(siteEntity.getCategory3SerialNoList());
            }
        }
        Page<SkuEntity> page = skuDao.querySkuPage(queryDTO);
        Page result = new Page<SkuDTO>();
        result.setPages(page.getPages());
        result.setTotal(page.getTotal());
        result.setSize(page.getSize());
        result.setCurrent(page.getCurrent());
        List<SkuDTO> records = new ArrayList<>();
        page.getRecords().forEach(item -> {
            records.add(this.toDTO(item, isNeedSkuMdmList));
        });
        result.setRecords(records);
        return result;
    }

    @Override
    public List<SkuDTO> querySkuDTOList(SkuQO condition) {
        if (StringUtil.isNotNullBlank(condition.getSiteCode())) {
            SiteEntity siteEntity = siteFacade.getSiteDetailByCode(condition.getSiteCode());
            if (siteEntity != null) {
                condition.setCategory3List(siteEntity.getCategory3SerialNoList());
            }
        }
        List<SkuEntity> list = skuDao.querySkuList(condition);
        List<SkuDTO> result = new ArrayList<>();
        list.forEach(item -> {
            result.add(this.toDTO(item, condition.getIsNeedSkuMdmList()));
        });
        return result;
    }

    @Override
    public List<SkuEntity> getSkuListByIds(List<Integer> skuIdList) {
        return skuDao.getSkuListByIds(skuIdList);
    }

    @Override
    public SkuDTO getSkuDTOById(Integer id) {
        SkuEntity skuEntity = this.getSkuById(id);
        SkuDTO skuDTO = this.toDTO(skuEntity, 1);
        if (StringUtils.isNotBlank(skuEntity.getKeyAttributeValues())) {
            List<SkuAttributeValueDTO> keyAttributeValueList = FastJsonUtils.getJsonToList(skuEntity.getKeyAttributeValues(), SkuAttributeValueDTO.class);
            List<SkuAttributeValueDTO> specAttributeValueList = keyAttributeValueList
                    .stream()
                    .filter(it -> AttributeType.KEY.getValue().equals(it.getAttributeType()))
                    .collect(Collectors.toList());
            List<SkuAttributeValueDTO> packageAttributeValueList = keyAttributeValueList
                    .stream()
                    .filter(it -> AttributeType.PACKAGE.getValue().equals(it.getAttributeType()))
                    .collect(Collectors.toList());
            skuDTO.setSpecAttributeValueList(specAttributeValueList)
                    .setPackageAttributeValueList(packageAttributeValueList);
        }
        return skuDTO;
    }

    @Override
    public SkuEntity getSkuById(Integer id) {
        return skuDao.getSkuById(id);
    }

    @Override
    public SkuEntity getSkuByFullName(String fullName) {
        return skuDao.getSkuByFullName(fullName);
    }

    @Override
    public SkuEntity getSkuBySkuNo(String serialNo) {
        return skuDao.getSkuBySkuNo(serialNo);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SkuAddDTO> refreshSku(SkuRefreshDTO skuRefreshDTO) {
        String userName = IdNameConverter.getName(IdNameType.user_id_name, JwtUtils.getCurrentUserId());
        // 品种
        CategoryEntity[] categories = categoryDao.getThreeCategoryBySerialNo(skuRefreshDTO.getCategory3());
        // 结果集
        List<SkuAddDTO> resultList = new ArrayList<>();
        // 已有SKU
        List<SkuEntity> existList = skuDao.querySkuList(new SkuQO().setCategory3(skuRefreshDTO.getCategory3()));
        Map<String, SkuEntity> skuMap = new HashMap<>();
        existList.forEach(sku -> skuMap.put(sku.getSkuNo(), sku));
        // SPU
        List<SpuEntity> spuList = spuDao.querySpuList(new SpuQO().setCategory3(skuRefreshDTO.getCategory3()).setStatus(1));
        // 包装规格
        CategoryAttributeEntity packageCategoryAttribute = categoryAttributeDao.getCategoryAttributeByType(
                skuRefreshDTO.getCategory3(), AttributeType.PACKAGE.getValue());
        // 规格
        AttributeEntity attributeEntity = attributeDao.getAttributeById(packageCategoryAttribute.getAttributeId());
        // BUGFIX：case-1003024 SPU和SKU的规格数据导入有问题 Author: NaNa 2025-03-04 start
        List<AttributeValueEntity> attributeValueListList = attributeValueDao.queryAttributeValueList(new AttributeValueQO().setAttributeId(packageCategoryAttribute.getAttributeId()));
        // 生成SKU矩阵
        for (SpuEntity spu : spuList) {
            if ("其他".equals(attributeEntity.getName())) {
                resultList.add(refreshSkuAddDTO(userName, spu, skuMap, categories, packageCategoryAttribute, attributeValueListList.get(0)));
            } else {
                // 规格值
                for (AttributeValueEntity attributeValue : attributeValueListList) {
                    resultList.add(refreshSkuAddDTO(userName, spu, skuMap, categories, packageCategoryAttribute, attributeValue));
                }
            }
        }
        return resultList;
    }

    /**
     * 刷新Sku
     *
     * @param userName
     * @param spu
     * @param skuMap
     * @param categories
     * @param packageCategoryAttribute
     * @param attributeValue
     * @return
     */
    private SkuAddDTO refreshSkuAddDTO(String userName, SpuEntity spu, Map<String, SkuEntity> skuMap, CategoryEntity[] categories, CategoryAttributeEntity packageCategoryAttribute, AttributeValueEntity attributeValue) {
        SkuAddDTO skuAddDTO;
        String skuNo = spu.getSpuNo();
        String skuName = spu.getSpuName();
        if (attributeValue != null) {
            skuNo += "_" + attributeValue.getId();
            skuName += attributeValue.getName().trim();
        }
        SkuEntity sku = skuMap.get(skuNo);
        if (sku == null) {
            skuAddDTO = new SkuAddDTO();
            skuAddDTO.setCategory1(categories[0].getSerialNo());
            skuAddDTO.setCategoryName1(categories[0].getName());
            skuAddDTO.setCategory2(categories[1].getSerialNo());
            skuAddDTO.setCategoryName2(categories[1].getName());
            skuAddDTO.setCategory3(categories[2].getSerialNo());
            skuAddDTO.setCategoryName3(categories[2].getName());
            skuAddDTO.setCategoryId(skuAddDTO.getCategory2());
            skuAddDTO.setSpuId(spu.getId());
            skuAddDTO.setSpuName(spu.getSpuName());
            // 期货代码
            skuAddDTO.setFuturePrefix(categories[2].getFutureCode());
            skuAddDTO.setSkuNo(skuNo);
            skuAddDTO.setFullName(skuName);
            skuAddDTO.setName(skuName);
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(spu.getKeyAttributeValues());
            Map<String, Object> map = new HashMap<>();
            map.put("attributeId", packageCategoryAttribute.getAttributeId());
            map.put("attributeName", packageCategoryAttribute.getAttributeName());
            map.put("attributeType", packageCategoryAttribute.getAttributeType());
            if (attributeValue != null) {
                map.put("attributeValueId", attributeValue.getId());
                map.put("attributeValueName", attributeValue.getName());
            }
            keyAttributeValues.add(map);
            skuAddDTO.setKeyAttributeValues(FastJsonUtils.getBeanToJson(keyAttributeValues));
            skuAddDTO.setStatus(0);
            skuAddDTO.setUpdatedBy(userName);
            skuAddDTO.setUpdatedAt(new Date());
            skuAddDTO.setIsDeleted(0);
            if (attributeValue != null) {
                skuAddDTO.setPackageId(attributeValue.getId());
            }
        } else {
            skuAddDTO = BeanUtil.copyProperties(sku, SkuAddDTO.class);
            if (attributeValue != null) {
                skuAddDTO.setPackageId(attributeValue.getId());
            }
            skuAddDTO.setSpuId(spu.getId());
            skuAddDTO.setSpuName(spu.getSpuName());
            skuAddDTO.setCategoryName1(categories[0].getName());
            skuAddDTO.setCategoryName2(categories[1].getName());
            skuAddDTO.setCategoryName3(categories[2].getName());
            skuAddDTO.setUpdatedBy(IdNameConverter.getName(IdNameType.user_id_name, skuAddDTO.getUpdatedBy()));
            // 期货代码
            skuAddDTO.setFuturePrefix(categories[2].getFutureCode());
            List<SkuMdmEntity> skuMdmList = skuMdmDao.querySkuMdmList(new SkuMdmQO().setSkuId(sku.getId()));
            skuAddDTO.setSkuMdmList(skuMdmList);
        }
        return skuAddDTO;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result saveSku(List<SkuAddDTO> skuAddDTOList) {

        // 是否TT默认规格只有一个
        String ttSkuNo = null;
        for (SkuAddDTO skuAddDTO : skuAddDTOList) {
            if (skuAddDTO.getIsTtAttribute() == null) {
                skuAddDTO.setIsTtAttribute(0);
            } else if (skuAddDTO.getIsTtAttribute() == 1) {
                ttSkuNo = skuAddDTO.getSkuNo();
            }
        }

        if (ttSkuNo != null) {
            // 同品种只有一个默认规格
            SkuAddDTO skuAddDTO = skuAddDTOList.get(0);
            LambdaUpdateWrapper luw = new LambdaUpdateWrapper<SkuEntity>()
                    .set(SkuEntity::getIsTtAttribute, 0)
                    .eq(SkuEntity::getCategory1, skuAddDTO.getCategory1())
                    .eq(SkuEntity::getCategory2, skuAddDTO.getCategory2())
                    .eq(SkuEntity::getCategory3, skuAddDTO.getCategory3())
                    .eq(SkuEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue());
            skuDao.update(luw);
        }

        // 循环
        for (SkuAddDTO skuAddDTO : skuAddDTOList) {

            // 只允许一个TT默认规格
            skuAddDTO.setIsTtAttribute(skuAddDTO.getSkuNo().equalsIgnoreCase(ttSkuNo) ? 1 : 0);

            // SKU
            SkuEntity entity;
            if (skuAddDTO.getId() == null) {
                entity = BeanUtil.copyProperties(skuAddDTO, SkuEntity.class);
                entity.setCategoryId(entity.getCategory2());
                entity.setNavSkuId(sequenceFacade.generate(SequenceEnum.NAV_SKU_ID.getValue(), 5));
                if (skuDao.count(SkuEntity.lqw(null, false).eq(SkuEntity::getSkuNo, entity.getSkuNo())) > 0) {
                    return Result.failure("提交失败，SKU NO不可重复！");
                }
                entity.setCreatedAt(DateTimeUtil.now())
                        .setUpdatedAt(DateTimeUtil.now())
                        .setUpdatedBy(JwtUtils.getCurrentUserId());
                entity.setIsDeleted(0);
                this.save(entity);
                IdNameConverter.setName(IdNameType.sku_id_name, entity.getId().toString(), entity.getFullName());
            } else {
                entity = skuDao.getSkuById(skuAddDTO.getId());
                if (entity.getSpuId() == null) {
                    entity.setSpuId(skuAddDTO.getSpuId());
                }
                if (StringUtil.isBlank(entity.getNavSkuId())) {
                    entity.setNavSkuId(sequenceFacade.generate(SequenceEnum.NAV_SKU_ID.getValue(), 5));
                }
                entity.setCategoryId(entity.getCategory2());
                entity.setName(skuAddDTO.getName());
                entity.setLinkageGoodsCode(skuAddDTO.getLinkageGoodsCode());
                entity.setLinkageGoodsName(skuAddDTO.getLinkageGoodsName());
                entity.setTaxRate(skuAddDTO.getTaxRate());
                entity.setNickName(skuAddDTO.getNickName());
                entity.setIsWarrant(skuAddDTO.getIsWarrant());
                entity.setIsTtAttribute(skuAddDTO.getIsTtAttribute());
                entity.setIsDelivery(skuAddDTO.getIsDelivery());
                entity.setStatus(skuAddDTO.getStatus());
                entity.setUpdatedAt(new Date());
                entity.setUpdatedBy(JwtUtils.getCurrentUserId());
                entity.setIsDeleted(0);
                this.updateById(entity);
            }

            // 主键
            skuAddDTO.setId(entity.getId());

            // SKU规格值
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(entity.getKeyAttributeValues());
            for (int i = 0; i < keyAttributeValues.size(); i++) {
                Map<String, Object> item = keyAttributeValues.get(i);
                skuAttributeValueDao.saveSkuAttributeValue(skuAddDTO, item);
            }

            // SKU MDM
            skuMdmDao.saveSkuMdm(entity.getId(), skuAddDTO.getSkuMdmList());
        }
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(skuAddDTOList))
                    .setBeforeData(null)
                    .setAfterData(null)
                    .setOperationActionEnum(OperationActionEnum.MODIFY_GOODS_STRUCTURE)
            ;
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return Result.success();
    }

    @Override
    public List<Integer> queryCannotDeliverySkuIdList() {
        return skuDao.queryCannotDeliverySkuIdList();
    }

    /**
     * 转DTO
     *
     * @param entity
     * @param isNeedSkuMdmList
     * @return
     */
    private SkuDTO toDTO(SkuEntity entity, Integer isNeedSkuMdmList) {
        SkuDTO dto = BeanUtil.copyProperties(entity, SkuDTO.class);
        dto.setCategoryName1(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory1())));
        dto.setCategoryName2(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory2())));
        dto.setCategoryName3(IdNameConverter.getName(IdNameType.category_serialNo_name, String.valueOf(dto.getCategory3())));
        // 是否需要MDM
        if (Integer.valueOf(1).equals(isNeedSkuMdmList)) {
            List<SkuMdmEntity> skuMdmList = skuMdmDao.querySkuMdmList(new SkuMdmQO().setSkuId(dto.getId()));
            dto.setSkuMdmList(skuMdmList);
        }
        IdNameConverter.toName(IdNameType.user_id_name, dto);
        IdNameConverter.toName(IdNameType.spu_id_name, dto);
        return dto;
    }

    @Override
    public void processHistoryData(Integer id) {
        LambdaQueryWrapper<SkuEntity> lqw = SkuEntity.lqw(null, false);
        if (id != null) {
            lqw.eq(SkuEntity::getId, id);
        } else {
            lqw.isNull(SkuEntity::getSpuId);
        }
        List<SkuEntity> skuEntityList = skuDao.list(lqw);
        for (SkuEntity skuEntity : skuEntityList) {
            CategoryEntity category1 = categoryDao.getCategoryBySerialNo(skuEntity.getCategory1());
            CategoryEntity category2 = categoryDao.getCategoryBySerialNo(skuEntity.getCategory2());
            CategoryEntity category3 = categoryDao.getCategoryBySerialNo(skuEntity.getCategory3());
            // SPU：品种+关键规格
            AttributeValueEntity keyAttributeValue = attributeValueDao.getAttributeValueById(skuEntity.getSpecId());
            if (keyAttributeValue == null) {
                continue;
            }
            SpuEntity spuEntity = spuDomainService.addSpu(category1, category2, category3, keyAttributeValue, skuEntity.getStatus());

            // SKU：SPU+销售规格
            AttributeValueEntity packageAttributeValue = attributeValueDao.getAttributeValueById(skuEntity.getPackageId());
            if (packageAttributeValue == null) {
                continue;
            }
            AttributeEntity packageAttribute = attributeDao.getAttributeById(packageAttributeValue.getAttributeId());
            String skuNo = spuEntity.getSpuNo() + "_" + packageAttributeValue.getId();
            String skuName = spuEntity.getSpuName() + packageAttributeValue.getName();
            List<Map<String, Object>> keyAttributeValues = FastJsonUtils.getJsonToListMap(spuEntity.getKeyAttributeValues());
            Map<String, Object> map = new HashMap<>();
            map.put("attributeId", packageAttribute.getId());
            map.put("attributeName", packageAttribute.getName());
            map.put("attributeType", packageAttribute.getType());
            map.put("attributeValueId", packageAttributeValue.getId());
            map.put("attributeValueName", packageAttributeValue.getName());
            keyAttributeValues.add(map);

            skuEntity.setSpuId(spuEntity.getId());
            skuEntity.setFullName(skuName);
            skuEntity.setSkuNo(skuNo);
            skuEntity.setKeyAttributeValues(JSON.toJSONString(keyAttributeValues));
            if (StringUtil.isBlank(skuEntity.getNavSkuId())) {
                skuEntity.setNavSkuId(sequenceFacade.generate(SequenceEnum.NAV_SKU_ID.getValue(), 5));
            }
            skuEntity.setUpdatedBy(JwtUtils.getCurrentUserId());
            skuEntity.setUpdatedAt(new Date());
            this.updateById(skuEntity);

            // SKU规格值
            SkuAddDTO skuAddDTO = BeanUtil.copyProperties(skuEntity, SkuAddDTO.class);
            for (int i = 0; i < keyAttributeValues.size(); i++) {
                Map<String, Object> item = keyAttributeValues.get(i);
                skuAttributeValueDao.saveSkuAttributeValue(skuAddDTO, item);
            }
        }
    }
}