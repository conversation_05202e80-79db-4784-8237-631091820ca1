package com.navigator.goods.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.PageUtil;
import com.navigator.goods.mapper.SpuMapper;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.goods.pojo.qo.SpuQO;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <p>
 * SPU商品 DAO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@Dao
public class SpuDao extends BaseDaoImpl<SpuMapper, SpuEntity> {
    /**
     * 根据条件：获取SPU商品DTO分页
     *
     * @param queryDTO
     * @return
     */
    public Page<SpuEntity> querySpuPage(QueryDTO<SpuQO> queryDTO) {
        return PageUtil.convertPage(this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), SpuEntity.lqw(queryDTO.getCondition(), true)));
    }

    /**
     * 根据条件：获取SPU商品DTO列表
     *
     * @param condition
     * @return
     */
    public List<SpuEntity> querySpuList(SpuQO condition) {
        return this.list(SpuEntity.lqw(condition, true));
    }

    /**
     * 根据ID：获取SPU商品
     *
     * @param id
     * @return
     */
    public SpuEntity getSpuById(Integer id) {
        return this.getById(id);
    }

    /**
     * 根据编码：获取SPU商品
     *
     * @param spuNo
     * @return
     */
    public SpuEntity getSpuBySpuNo(String spuNo) {
        List<SpuEntity> spuEntityList = this.list(SpuEntity.lqw(null, true).eq(SpuEntity::getSpuNo, spuNo));
        return CollectionUtils.isEmpty(spuEntityList) ? null : spuEntityList.get(0);
    }

}
