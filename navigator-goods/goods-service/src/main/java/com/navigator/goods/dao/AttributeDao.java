package com.navigator.goods.dao;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.PageUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.goods.mapper.AttributeMapper;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.enums.AttributeType;
import com.navigator.goods.pojo.qo.AttributeQO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-03 13:52
 */
@Dao
public class AttributeDao extends BaseDaoImpl<AttributeMapper, AttributeEntity> {

    /**
     * @param categoryId    品种ID
     *                      {@link AttributeType}
     * @param attributeType 规格类型
     * @return 规格信息
     */
    public AttributeEntity findByCategoryIdAndType(Integer categoryId, Integer attributeType) {
        List<AttributeEntity> attributeEntityList = this.list(Wrappers.<AttributeEntity>lambdaQuery()
//                .eq(null != categoryId, AttributeEntity::getCategoryId, categoryId)
                        .eq(AttributeEntity::getType, attributeType)
                        .eq(AttributeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
        return CollectionUtils.isEmpty(attributeEntityList) ? null : attributeEntityList.get(0);
    }

    /**
     * 根据条件：获取规格分页
     *
     * @param queryDTO
     * @return
     */
    public Page<AttributeEntity> queryAttributePage(QueryDTO<AttributeQO> queryDTO) {
        return PageUtil.convertPage(this.page(new Page<>(queryDTO.getPageNo(), queryDTO.getPageSize()), AttributeEntity.lqw(queryDTO.getCondition(), true)));
    }

    /**
     * 根据条件：获取规格列表
     *
     * @param condition
     * @return
     */
    public List<AttributeEntity> queryAttributeList(AttributeQO condition) {
        return this.list(AttributeEntity.lqw(condition, true));
    }

    /**
     * 根据ID：获取规格
     *
     * @param id
     * @return
     */
    public AttributeEntity getAttributeById(Integer id) {
        return this.getById(id);
    }

    /**
     * 新增：规格
     *
     * @param addDTO
     * @return
     */
    public AttributeEntity addAttribute(AttributeAddDTO addDTO) {
        // 复制
        AttributeEntity entity = BeanUtil.copyProperties(addDTO, AttributeEntity.class);
        // 验证合法性
        this.checkLegal(entity);
        // 新增
        entity.setCreatedAt(new Date());
        entity.setCreatedBy(JwtUtils.getCurrentUserId());
        entity.setUpdatedAt(entity.getCreatedAt());
        entity.setUpdatedBy(entity.getCreatedBy());
        entity.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
        boolean flag = save(entity);
        if (flag) {
            IdNameConverter.setName(IdNameType.attribute_id_name, entity.getId().toString(), entity.getName());
            return entity;
        }
        throw new BusinessException("新增失败！");
    }

    /**
     * 更新：规格
     *
     * @param updateDTO
     * @return
     */
    public AttributeEntity updateAttribute(AttributeUpdateDTO updateDTO) {
        if (StringUtil.isNullBlank(updateDTO.getId())) {
            throw new BusinessException("没有记录ID，无法更新！");
        }
        AttributeEntity entity = this.getById(updateDTO.getId());
        if (entity == null) {
            throw new BusinessException("找不到记录！");
        }
        // 更新字段
        BeanUtil.copyProperties(updateDTO, entity);
        // 验证合法性
        this.checkLegal(entity);
        // 更新
        entity.setUpdatedAt(new Date());
        entity.setUpdatedBy(JwtUtils.getCurrentUserId());
        boolean flag = updateById(entity);
        if (flag) {
            return entity;
        }
        throw new BusinessException("更新失败！");
    }

    /**
     * 验证合法性
     *
     * @param entity
     */
    private void checkLegal(AttributeEntity entity) {
        int count = this.count(
                AttributeEntity.lqw(null, false)
                        .eq(AttributeEntity::getName, entity.getName())
                        .ne(StringUtil.isNotNullBlank(entity.getId()), AttributeEntity::getId, entity.getId())
        );
        if (count > 0) {
            throw new BusinessException("提交失败，名称不可重复！");
        }
    }

    public AttributeEntity getAttributeByNameAndType(String attributeName, Integer attributeType) {
        List<AttributeEntity> attributeEntityList = this.list(AttributeEntity.lqw()
                .eq(StringUtils.isNotBlank(attributeName), AttributeEntity::getName, attributeName)
                .eq(null != attributeType, AttributeEntity::getType, attributeType)
        );
        return CollectionUtils.isEmpty(attributeEntityList) ? null : attributeEntityList.get(0);
    }
}
