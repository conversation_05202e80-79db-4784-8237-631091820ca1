package com.navigator.goods.service;

import com.navigator.goods.pojo.entity.AttributeValueEntity;
import com.navigator.goods.pojo.vo.GoodsAttributeVO;


/**
 * <p>
 * 规格值信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
public interface IAttributeValueService {
    /**
     * 根据分类Id获取规格包装信息
     *
     * @param categoryId 分类ID
     * @return 品类的规格包装
     */
    GoodsAttributeVO getSpecListByCategoryId(Integer categoryId);

    /**
     * 根据规格/包装ID获取信息
     *
     * @param attributeValueId 规格/包装ID
     * @return 规格/包装信息
     */
    AttributeValueEntity getAttributeValueById(Integer attributeValueId);
}
