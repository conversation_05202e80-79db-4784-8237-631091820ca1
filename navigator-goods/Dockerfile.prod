FROM openjdk:11.0
RUN mkdir /config
COPY  navigator-goods/goods-service/src/main/resources/bootstrap-dev.yml /config
COPY  navigator-goods/goods-service/src/main/resources/bootstrap.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/goods-service/*.jar /navigator-goods-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-goods-1.0-SNAPSHOT.jar
