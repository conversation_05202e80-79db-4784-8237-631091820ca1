navigator_cloud
|
├── navigator-activiti                 -- 工作流服务
         ├── activiti-facade
         └── activiti-service
|
├── navigator-admin                    -- 员工角色权限、系统基础服务
         ├── admin-facade
         └── admin-service
├── navigator-commons                  -- 工具类及通用代码模块
        
├── navigator-customer                 -- 客户、供应商管理
         ├── customer-facade
         └── customer-service
        
├── navigator-dagama                   -- 消息中心
         ├── dagama-facade
         └── dagama-service
        
├── navigator-gateway                  -- 网关转发
├── navigator-generator                -- 代码自动生成工具
├── navigator-goods                    -- 品类、货品管理
         ├── goods-facade
|          └── goods-service
|
├── navigator-pay                      -- 支付账单管理
|          ├── pay-facade
|          └── pay-service
|
├── navigator-sparrow                  -- 对外对接（易企签、linkinage对接）
         ├── sparrow-facade
         └── sparrow-service
|
├── navigator-trade                    -- 交易中心（TT、合同、提货管理）
         ├── trade-facade
         └── trade-service
│
└── navigator-web                      -- Web端
|   ├── navigator-columbus-web         -- 哥伦布系统（客户端）
|   ├── navigator-magellan-web         -- 麦哲伦系统（业务端）
|   └── navigator-pigeon-web           -- 信鸽系统（对外对接中心）
|──────────────────────────────其他──────────────────────────────────────────────────────
└── deploy                      -- maven打包后项目jar将会被复制到此目录


# Introduction 
TODO: Give a short introduction of your project. Let this section explain the objectives or the motivation behind this project. 

# Getting Started
TODO: Guide users through getting your code up and running on their own system. In this section you can talk about:
1.	Installation process
2.	Software dependencies
3.	Latest releases
4.	API references

# Build and Test
TODO: Describe and show how to build your code and run the tests. 

# Contribute
TODO: Explain how other users and developers can contribute to make your code better. 

If you want to learn more about creating good readme files then refer the following [guidelines](https://docs.microsoft.com/en-us/azure/devops/repos/git/create-a-readme?view=azure-devops). You can also seek inspiration from the below readme files:
- [ASP.NET Core](https://github.com/aspnet/Home)
- [Visual Studio Code](https://github.com/Microsoft/vscode)
- [Chakra Core](https://github.com/Microsoft/ChakraCore)
