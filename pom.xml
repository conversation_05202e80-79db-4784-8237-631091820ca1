<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.6.RELEASE</version>
    </parent>

    <groupId>com.navigator</groupId>
    <artifactId>navigator</artifactId>
    <packaging>pom</packaging>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>navigator-gateway</module>
        <module>navigator-admin</module>
        <module>navigator-trade</module>
        <!--<module>navigator-pay</module>-->
        <!--<module>navigator-delivery</module>-->
        <module>navigator-web</module>
        <module>navigator-commons</module>
        <module>navigator-activiti</module>
        <module>navigator-dagama</module>
        <module>navigator-sparrow</module>
        <module>navigator-customer</module>
        <module>navigator-goods</module>
        <module>navigator-future</module>
        <module>navigator-pigeon</module>
        <module>navigator-husky</module>
<!--        <module>navigator-generator</module>-->
        <module>navigator-delivery</module>
        <module>navigator-koala</module>
        <module>navigator-cuckoo</module>
        <module>navigator-open</module>
    </modules>

    <properties>
        <navigator.version>1.0-SNAPSHOT</navigator.version>

        <spring-cloud.version>Hoxton.SR7</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <org.mapstruct.version>1.3.0.Final</org.mapstruct.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <lombok.version>1.18.18</lombok.version>
        <hutool.version>5.5.8</hutool.version>
        <sqlserver.version>7.4.1.jre8</sqlserver.version>
        <swagger.version>1.6.2</swagger.version>
        <mybatis-plus.version>3.2.0</mybatis-plus.version>
        <fastJson.version>1.2.76</fastJson.version>
        <tlog.version>1.2.6</tlog.version>
        <signit.version>2.7.6</signit.version>
        <!--<signit.version>2.6.0</signit.version>-->
        <redisson.version>3.13.6</redisson.version>
        <maven.build.timestamp.format>yyyyMMdd-HHmm</maven.build.timestamp.format>

    </properties>

    <dependencies>
        <!-- Lombok Begin -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <!-- Lombok Begin -->

        <!-- Hutool Begin -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <!-- Hutool End -->

        <!-- TLog Begin -->
        <dependency>
            <groupId>com.yomahub</groupId>
            <artifactId>tlog-all-spring-boot-starter</artifactId>
            <version>${tlog.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
            <version>2.2.4.RELEASE</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--Spring Cloud 相关依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--Spring Cloud & Alibaba 相关依赖-->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- sqlserver Begin -->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>mssql-jdbc</artifactId>
                <version>${sqlserver.version}</version>
            </dependency>
            <!-- sqlserver End -->

            <!-- swagger Begin -->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!-- swagger End -->

            <!--MyBatis Plus Begin -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--MyBatis Plus End -->

            <!-- Json Begin -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastJson.version}</version>
            </dependency>
            <!-- Json End -->

            <!-- https://mvnrepository.com/artifact/cn.signit.sdk/signit-java-sdk -->
            <!--易企签依赖-->
            <dependency>
                <groupId>cn.signit.sdk</groupId>
                <artifactId>signit-java-sdk</artifactId>
                <version>${signit.version}</version>
            </dependency>

            <!--jacoco依赖,sonar 测试覆盖率使用-->
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.8</version>
            </dependency>

            <!-- redisson Begin -->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!-- redisson End -->

            <!-- Project Begin -->
            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>admin-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>dagama-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>pay-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>koala-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>trade-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>navigator-commons</artifactId>
                <version>${navigator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>navigator-dagama</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>navigator-customer</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>navigator-pigeon</artifactId>
                <version>${navigator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>customer-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>future-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>goods-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>activiti-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>sparrow-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>pigeon-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>cuckoo-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>husky-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>

            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>delivery-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>
            <dependency>
                <groupId>com.navigator</groupId>
                <artifactId>open-facade</artifactId>
                <version>${navigator.version}</version>
            </dependency>
            <!-- Project End -->

            <dependency>
                <groupId>net.minidev</groupId>
                <artifactId>json-smart</artifactId>
                <version>2.4.10</version>
                <scope>runtime</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!--maven私服-->
    <!--<repositories>-->
    <!--<repository>-->
    <!--<id>maven-public</id>-->
    <!--<name>maven-public</name>-->
    <!--<url>http://192.168.0.8:8081/repository/maven-public/</url>-->
    <!--<snapshots>-->
    <!--<enabled>true</enabled>-->
    <!--<updatePolicy>always</updatePolicy>-->
    <!--</snapshots>-->
    <!--</repository>-->
    <!--</repositories>-->
    <!--<distributionManagement>-->
    <!--<repository>-->
    <!--<id>nexus-releases</id>-->
    <!--<name>maven-releases</name>-->
    <!--<url>http://192.168.0.8:8081/repository/maven-releases/</url>-->
    <!--</repository>-->
    <!--<snapshotRepository>-->
    <!--<id>nexus-snapshots</id>-->
    <!--<name>maven-snapshots</name>-->
    <!--<url>http://192.168.0.8:8081/repository/maven-snapshots/</url>-->
    <!--</snapshotRepository>-->
    <!--</distributionManagement>-->
    <distributionManagement>
        <repository>
            <id>nav-maven</id>
            <url>https://pkgs.dev.azure.com/LDC-Technology-and-Operations/Navigator/_packaging/nav-maven/maven/v1</url>
        </repository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>nav-maven</id>
            <name>nav-maven</name>
            <url>https://pkgs.dev.azure.com/LDC-Technology-and-Operations/Navigator/_packaging/nav-maven/maven/v1</url>
        </repository>
        <repository>
            <id>alimaven</id>
            <name>aliyun maven</name>
            <layout>default</layout>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>alimaven</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

</project>
