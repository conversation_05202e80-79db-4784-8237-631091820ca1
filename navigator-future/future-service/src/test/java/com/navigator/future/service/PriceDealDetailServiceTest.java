package com.navigator.future.service;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.TradeDayFacade;
import com.navigator.common.dto.Result;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.FutureApplication;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.dao.PriceDealDetailDao;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.future.pojo.vo.PriceDealInfoVO;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.DomainCodeFacade;
import com.navigator.trade.pojo.entity.DomainPriceEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FutureApplication.class)
//@Transactional
//@Rollback
class PriceDealDetailServiceTest {

    @Resource
    PriceApplyDao priceApplyDao;
    @Resource
    PriceDealDetailDao priceDealDetailDao;
    @Resource
    IPriceDealService priceDealService;
    @MockBean
    TradeDayFacade tradeDayFacade;
    @MockBean
    DomainCodeFacade domainCodeFacade;
    @MockBean
    ContractFacade contractFacade;

    @BeforeEach
    void setUp() {

        //Mockito.when(tradeDayFacade.isTradeDay(Mockito.anyString())).thenReturn(true);
        //Mockito.when(tradeDayFacade.isTradeDay("2022-03-31")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-01")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-02")).thenReturn(false);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-03")).thenReturn(false);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-04")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-05")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-06")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-07")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-08")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-09")).thenReturn(false);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-10")).thenReturn(false);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-11")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-12")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-13")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-14")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-15")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-16")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-17")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-18")).thenReturn(true);
        Mockito.when(tradeDayFacade.isTradeDay("2022-04-19")).thenReturn(true);
    }

    @Test
    public void testQueryPriceDealInfo() {

        List<PriceDealInfoVO> priceDealInfoVOList = priceDealService.queryPriceDealInfo(1);

        List<PriceDealDetailVO> priceDealDetailVOList = priceDealService.queryPriceDealDetail(1930);

        System.out.println(JSON.toJSONString(priceDealInfoVOList));
        System.out.println(JSON.toJSONString(priceDealDetailVOList));
    }

    @Test
    public void testPriceDealProcess() {

        Date d = new Date();
        for (int j = 0; j < 1; j++) {
            int pid = 1970 + j;

            for (int i = 0; i < 20; i++) {

                DomainPriceEntity domainPriceEntity = new DomainPriceEntity();

                domainPriceEntity.setPrice(BigDecimal.valueOf(3000 + (RandomUtil.randomInt(1, 4) - 1) * 50));

                Result<DomainPriceEntity> rtn = Result.success(domainPriceEntity);

                Mockito.when(domainCodeFacade.getClosingPrice(Mockito.any(), Mockito.anyString(), null)).thenReturn(rtn);

                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(pid);

                d = DateTimeUtil.addDays(i);

                priceDealService.priceDealProcess(priceApplyEntity, d);
            }
        }

    }

    @Test
    public void testQueryDealInfo() {
        List<PriceDealDetailVO> ll = priceDealService.queryPriceDealDetail(1);

        System.out.println(JSON.toJSONString(ll));

    }
}
