package com.navigator.future.testdata;

import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;

import java.math.BigDecimal;

public class CustomerFuturesDataMocker {

    public static CustomerFuturesDTO genCustomerFuturesDTO() {
        CustomerEntity customerEntity = com.navigator.future.testdata.CustomerDataMocker.genCustomer();
        CustomerFuturesDTO customerFuturesDTO = new CustomerFuturesDTO();
        customerFuturesDTO.setCustomerId(customerEntity.getId())
                .setCustomerName(customerEntity.getName())
                .setMayPriceNum(BigDecimal.valueOf(1357))
                .setMayTransferNum(BigDecimal.valueOf(1357));
        return customerFuturesDTO;
    }
}
