package com.navigator.future.testdata;

import cn.hutool.core.util.RandomUtil;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.customer.pojo.enums.InvoiceTypeEnum;
import com.navigator.trade.pojo.enums.PaymentTypeEnum;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerDataMocker {

    public static CustomerEntity genCustomer(){
        CustomerEntity customerEntity=new CustomerEntity();
        customerEntity.setId(10001)
                .setLinkageCustomerCode("LKGC_"+ RandomUtil.randomNumbers(6))
                .setMdmCustomerCode("LDCC_"+RandomUtil.randomNumbers(8))
                .setParentId(0)
                .setUseYqq(1)
                .setFrameExpired(0)
                .setTemplateExpired(0)
                .setOriginalPaper(1)
                .setPaperNeed("要求邮寄")
                .setType(1)
                .setGrade("VIP")
                .setName("上海甜甜圈商贸有限公司")
                .setShortName("上海甜甜圈")
                .setContact("唐大明")
                .setLargestAdvance(BigDecimal.valueOf(1000000) )
                .setCreditDays(10)
                .setPaymentType(PaymentTypeEnum.CREDIT.getType())
                .setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue())
                .setPhone("15800001111")
                .setAddress("上海市浦东新区世纪大道1008号")
                .setMoving(1000)
                .setCnfSelling(1000)
                .setEnterprise(0)
                .setLng("116.397128")
                .setLat("39.916527")
                .setAxCode("AX"+RandomUtil.randomNumbers(4))
                .setTradeType("active")
                .setStatus(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setIsCustomer(1)
                .setIsSupplier(0)
                .setSignPlace("上海市浦东新区")
                .setCode("NC"+RandomUtil.randomNumbers(6));
        return customerEntity;
    }

    public static CustomerEntity genSupplier(){
        CustomerEntity customerEntity=new CustomerEntity();
        customerEntity.setId(20001)
                .setLinkageCustomerCode("LKGS_"+ RandomUtil.randomNumbers(6))
                .setMdmCustomerCode("LDCS_"+RandomUtil.randomNumbers(6))
                .setParentId(0)
                .setUseYqq(1)
                .setFrameExpired(0)
                .setTemplateExpired(0)
                .setOriginalPaper(1)
                .setPaperNeed("要求邮寄")
                .setType(1)
                .setGrade("VIP")
                .setName("苏州达孚贸易有限公司")
                .setShortName("苏州达孚")
                .setContact("苏大强")
                .setLargestAdvance(BigDecimal.valueOf(1000000))
                .setCreditDays(10)
                .setPaymentType(PaymentTypeEnum.CREDIT.getType())
                .setInvoiceType(InvoiceTypeEnum.SPECIAL.getValue())
                .setPhone("13112332123")
                .setAddress("苏州市金鸡湖大道1024号")
                .setMoving(1000)
                .setCnfSelling(1000)
                .setEnterprise(0)
                .setLng("258.397128")
                .setLat("369.916527")
                .setAxCode("AX"+RandomUtil.randomNumbers(4))
                .setTradeType("active")
                .setStatus(1)
                .setIsDeleted(0)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setIsCustomer(0)
                .setIsSupplier(1)
                .setSignPlace("苏州市金鸡湖大道")
                .setCode("NS"+RandomUtil.randomNumbers(6));
        return customerEntity;
    }

}
