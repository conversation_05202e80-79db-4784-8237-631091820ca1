package com.navigator.future.service;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.FutureApplication;
import com.navigator.future.enums.PendingTypeEnum;
import com.navigator.future.enums.PriceApplyOperationTypeEnum;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.testdata.ContractDataMocker;
import com.navigator.future.testdata.CustomerFuturesDataMocker;
import com.navigator.future.testdata.EmployDataMocker;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = FutureApplication.class)
//@Transactional
//@Rollback
class PriceApplyServiceTest {

    @Autowired
    IPriceApplyService priceApplyService;
    @MockBean
    OperationLogFacade operationLogFacade;
    @MockBean
    EmployFacade employFacade;
    @MockBean
    IFuturesDomainService futuresDomainService;

    @BeforeEach
    void setUp() {

        EmployEntity employEntity = EmployDataMocker.genEmploy();
        Mockito.when(employFacade.getEmployById(Mockito.anyInt())).thenReturn(employEntity);

        Mockito.when(futuresDomainService.mayPriceNum(Mockito.any())).thenReturn(CustomerFuturesDataMocker.genCustomerFuturesDTO());

    }

    @Test
    public void testBatchPriceApply() {
        for (int i = 0; i < 10; i++) {
            testPriceApply();
        }
    }

    @Test
    public void testPriceApply() {

        ContractEntity contractEntity = ContractDataMocker.genContract4Structure();
        ContractStructureEntity contractStructureEntity = ContractDataMocker.genContractStructure(contractEntity);

        //豆粕\豆粕销售结构化定价合同
        if ((contractEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_MEAL.getValue())
                || contractEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue()))
                && contractEntity.getSalesType() == ContractSalesTypeEnum.SALES.getValue()
                && contractEntity.getContractType() == ContractTypeEnum.STRUCTURE.getValue()) {

            PriceApplyEntity priceApplyEntity = new PriceApplyDTO()
                    .setCustomerId(contractEntity.getCustomerId())
                    .setCustomerName(contractEntity.getCustomerName())
                    .setType(PriceTypeEnum.STRUCTURE_PRICING.getValue())
                    .setDominantCode(contractEntity.getDomainCode())
                    .setTranferDominantCode(contractEntity.getDomainCode())
                    .setApplyDiffPrice(BigDecimal.ZERO)
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setPendingType(PendingTypeEnum.FOLLOW_LARGE_CAP.getValue())
                    .setStructureType(contractStructureEntity.getStructureType())
                    .setApplyNum(contractEntity.getContractNum())
                    .setApplyPrice(BigDecimal.ZERO)
                    .setTransactionPrice(BigDecimal.ZERO)
                    .setTransactionDiffPrice(BigDecimal.ZERO)
                    .setDealHandNum(0)
                    .setContractId(3266)
                    //.setMaxPrice(contractStructureEntity.getMaxPrice())
                    //.setMinPrice(contractStructureEntity.getMinPrice())
                    .setUnitNum(contractStructureEntity.getUnitNum())
                    .setStartTime(new Date())
                    .setEndTime(DateTimeUtil.parseDateString("2022-04-11"))  //两个日期之间的工作日=10天即可
                    .setTotalDay(contractStructureEntity.getTotalDay());
//                    .setTriggerSys(SystemEnum.MAGELLAN.getDesc());

            PriceApplyDTO priceApplyDTO = (PriceApplyDTO) priceApplyEntity;
            priceApplyDTO.setModifyType(PriceApplyOperationTypeEnum.NORMAL_APPLY.getValue());
            priceApplyDTO.setTriggerSys(SystemEnum.MAGELLAN.getDescription());


            priceApplyService.priceApply(priceApplyDTO);

            //Assert.assertEquals(1, 1);
        }
    }

}