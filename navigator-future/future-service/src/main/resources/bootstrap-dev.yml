spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        server-addr: http://localhost:8848
        namespace: dev-hxs
      config:
        server-addr: http://localhost:8848
        file-extension: yaml
        namespace: dev-hxs
        group: SERVICE_GROUP
        shared-configs:
          - data-id: navigator-commons.yaml
            group: DEFAULT_GROUP
            refresh: true
        extension-configs:
          - data-id: kingstar-sync-rules.json
            group: BUSINESS_SYNC_GROUP
            refresh: true