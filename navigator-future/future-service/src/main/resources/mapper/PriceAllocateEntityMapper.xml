<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.future.mapper.PriceAllocateMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.future.pojo.entity.PriceAllocateEntity">
                    <id column="id" property="id"/>
                    <result column="price_apply_id" property="priceApplyId"/>
                    <result column="price_apply_code" property="priceApplyCode"/>
                    <result column="status" property="status"/>
                    <result column="price_apply_type" property="priceApplyType"/>
                    <result column="type" property="type"/>
                    <result column="contract_id" property="contractId"/>
                    <result column="contract_code" property="contractCode"/>
                    <result column="subcontract_id" property="subcontractId"/>
                    <result column="subcontract_code" property="subcontractCode"/>
                    <result column="allocate_num" property="allocateNum"/>
                    <result column="audit_reason" property="auditReason"/>
                    <result column="customer_id" property="customerId"/>
                    <result column="customer_name" property="customerName"/>
                    <result column="category_id" property="categoryId"/>
                    <result column="category_name" property="categoryName"/>
                    <result column="reviewer_id" property="reviewerId"/>
                    <result column="reviewer_name" property="reviewerName"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, price_apply_id, price_apply_code, status, price_apply_type, type, contract_id, contract_code, subcontract_id, subcontract_code, allocate_num, audit_reason, customer_id, customer_name, category_id, category_name, reviewer_id, reviewer_name, created_by, updated_by, created_at, updated_at
        </sql>
</mapper>
