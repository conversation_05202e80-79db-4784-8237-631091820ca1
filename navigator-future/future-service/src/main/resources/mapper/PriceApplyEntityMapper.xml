<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.future.mapper.PriceApplyMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.future.pojo.entity.PriceApplyEntity">
                    <id column="id" property="id"/>
                    <result column="customer_id" property="customerId"/>
                    <result column="customer_name" property="customerName"/>
                    <result column="category_id" property="categoryId"/>
                    <result column="category_name" property="categoryName"/>
                    <result column="orientation" property="orientation"/>
                    <result column="type" property="type"/>
                    <result column="code" property="code"/>
                    <result column="dominant_code" property="dominantCode"/>
                    <result column="tranfer_dominant_code" property="tranferDominantCode"/>
                    <result column="contract_id" property="contractId"/>
                    <result column="status" property="status"/>
                    <result column="audit_status" property="auditStatus"/>
                    <result column="pending_type" property="pendingType"/>
                    <result column="apply_hand_num" property="applyHandNum"/>
                    <result column="apply_num" property="applyNum"/>
                    <result column="apply_price" property="applyPrice"/>
                    <result column="apply_diff_price" property="applyDiffPrice"/>
                    <result column="deal_hand_num" property="dealHandNum"/>
                    <result column="deal_num" property="dealNum"/>
                    <result column="transaction_price" property="transactionPrice"/>
                    <result column="transaction_diff_price" property="transactionDiffPrice"/>
                    <result column="not_deal_num" property="notDealNum"/>
                    <result column="allocate_num" property="allocateNum"/>
                    <result column="allocate_status" property="allocateStatus"/>
                    <result column="latest_message" property="latestMessage"/>
                    <result column="cancel_reason" property="cancelReason"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, customer_id, customer_name, category_id, category_name, orientation, type, code, dominant_code, tranfer_dominant_code, contract_id, status, audit_status, pending_type, apply_hand_num, apply_num, apply_price, apply_diff_price, deal_hand_num, deal_num, transaction_price, transaction_diff_price, notDealNum, allocate_num, allocate_status, latest_message, cancel_reason, created_by, updated_by, created_at, updated_at
        </sql>
</mapper>
