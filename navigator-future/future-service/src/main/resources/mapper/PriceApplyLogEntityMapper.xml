<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.navigator.future.mapper.PriceApplyLogMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.navigator.future.pojo.entity.PriceApplyLogEntity">
                    <id column="id" property="id"/>
                    <result column="price_apply_id" property="priceApplyId"/>
                    <result column="price_apply_code" property="priceApplyCode"/>
                    <result column="type" property="type"/>
                    <result column="apply_hand_num" property="applyHandNum"/>
                    <result column="apply_num" property="applyNum"/>
                    <result column="apply_price" property="applyPrice"/>
                    <result column="apply_diff_price" property="applyDiffPrice"/>
                    <result column="change_price" property="changePrice"/>
                    <result column="change_num" property="changeNum"/>
                    <result column="change_hand_num" property="changeHandNum"/>
                    <result column="change_diff_price" property="changeDiffPrice"/>
                    <result column="audit_status" property="auditStatus"/>
                    <result column="memo" property="memo"/>
                    <result column="created_by" property="createdBy"/>
                    <result column="updated_by" property="updatedBy"/>
                    <result column="created_at" property="createdAt"/>
                    <result column="updated_at" property="updatedAt"/>
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
            id, price_apply_id, price_apply_code, type, apply_hand_num, apply_num, apply_price, applyDiffPrice, change_price, change_num, change_hand_num, change_diff_price, audit_status, memo, created_by, updated_by, created_at, updated_at
        </sql>
</mapper>
