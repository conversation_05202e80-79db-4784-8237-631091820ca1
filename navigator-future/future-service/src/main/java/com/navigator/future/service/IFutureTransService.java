package com.navigator.future.service;

import com.navigator.future.pojo.dto.CustomerDomainTransDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;

public interface IFutureTransService {
    /**
     * 获取客户合约交易信息
     * @param contractFuturesDTO
     * @return
     */
    CustomerDomainTransDTO getCustomerDomainTransInfo(ContractFuturesDTO contractFuturesDTO);

    /**
     * 新增期货交易单
     */
    void saveFutureOrder();

}
