package com.navigator.future.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.mapper.PriceApplyMapper;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:53
 */
@Dao
public class PriceApplyDao extends BaseDaoImpl<PriceApplyMapper, PriceApplyEntity> {

    /**
     * 查询以点价转月数量
     *
     * @param priceApplyOperateNum
     * @return
     */
    public List<PriceApplyEntity> queryPriceApplyOperateNum(PriceApplyOperateNumDTO priceApplyOperateNum) {
        return this.baseMapper.selectList(Wrappers.<PriceApplyEntity>lambdaQuery()
                .eq(priceApplyOperateNum.getCustomerId() != null, PriceApplyEntity::getCustomerId, priceApplyOperateNum.getCustomerId())
                .eq(priceApplyOperateNum.getCategoryId() != null, PriceApplyEntity::getCategoryId, priceApplyOperateNum.getCategoryId())
                .in(PriceApplyEntity::getType, priceApplyOperateNum.getType())
                .eq(StrUtil.isNotBlank(priceApplyOperateNum.getDominantCode()), PriceApplyEntity::getDominantCode, priceApplyOperateNum.getDominantCode())
                .eq(PriceApplyEntity::getCompanyId, priceApplyOperateNum.getCompanyId())
                .eq(StringUtil.isNotEmpty(priceApplyOperateNum.getBuCode()), PriceApplyEntity::getBuCode, priceApplyOperateNum.getBuCode())
                .eq(StringUtil.isNotEmpty(priceApplyOperateNum.getFutureCode()), PriceApplyEntity::getFutureCode, priceApplyOperateNum.getFutureCode())
                .eq(priceApplyOperateNum.getCategory2() != null, PriceApplyEntity::getCategory2, priceApplyOperateNum.getCategory2())
                //.gt(PriceApplyEntity::getNotAllocateNum, 0)
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .notIn(PriceApplyEntity::getStatus, Arrays.asList(PriceStatusEnum.NOT_TRANSACTION.getValue(), PriceStatusEnum.CONTRARY.getValue()))
        );
    }

    /**
     * 分页查询申请单
     *
     * @param applyBOQueryDTO
     * @return
     */
    public IPage<PriceApplyEntity> queryStructurePricingApply(QueryDTO<PriceApplyBO> applyBOQueryDTO) {
        PriceApplyBO priceApplyBO = applyBOQueryDTO.getCondition();
        return this.baseMapper.selectPage(
                new Page<>(applyBOQueryDTO.getPageNo(), applyBOQueryDTO.getPageSize()),
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(null != priceApplyBO.getSalesType(), PriceApplyEntity::getSalesType, priceApplyBO.getSalesType())
                        .eq(null != priceApplyBO.getType(), PriceApplyEntity::getType, priceApplyBO.getType())
                        .eq(null != priceApplyBO.getStatus(), PriceApplyEntity::getStatus, priceApplyBO.getStatus())
                        .le(priceApplyBO.isStructurePricing(), PriceApplyEntity::getStartTime, new Date())
                        .ge(priceApplyBO.isStructurePricing(), PriceApplyEntity::getEndTime, new Date())
                        .lt(priceApplyBO.isStructurePricing(), PriceApplyEntity::getLatestDealDay, DateTimeUtil.formatDateValue())
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByDesc(PriceApplyEntity::getCreatedAt)
        );
    }

    /**
     * 分页查询申请单
     *
     * @param applyBOQueryDTO
     * @return
     */
    public IPage<PriceApplyEntity> queryPriceApply(QueryDTO<PriceApplyBO> applyBOQueryDTO, SystemEnum systemEnum) {
        PriceApplyBO priceApplyBO = applyBOQueryDTO.getCondition();
        List<Integer> status = null;
        if (StrUtil.isNotBlank(priceApplyBO.getStatus())) {
            status = Arrays.stream(priceApplyBO.getStatus().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());

            // MGL点价中的数据对应CLB显示在“成交状态”
            if (SystemEnum.COLUMBUS.equals(systemEnum) && status.contains(PriceStatusEnum.WAIT_TRANSACTION.getValue())) {
                status.add(PriceStatusEnum.PRICING.getValue());
            }
        }

        String createdBy = StrUtil.isNotBlank(priceApplyBO.getCreatedBy()) ? priceApplyBO.getCreatedBy().trim() : null;

        LambdaQueryWrapper lambdaQueryWrapper = Wrappers.<PriceApplyEntity>lambdaQuery()
                .eq(null != priceApplyBO.getSalesType(), PriceApplyEntity::getSalesType, priceApplyBO.getSalesType())
                .in(null != priceApplyBO.getCompanyIds() && !priceApplyBO.getCompanyIds().isEmpty(), PriceApplyEntity::getCompanyId, priceApplyBO.getCompanyIds())
                .eq(null != priceApplyBO.getCustomerId(), PriceApplyEntity::getCustomerId, priceApplyBO.getCustomerId())
                .like(StrUtil.isNotBlank(priceApplyBO.getCustomerName()), PriceApplyEntity::getCustomerName, priceApplyBO.getCustomerName())
                .eq(StringUtil.isNotEmpty(priceApplyBO.getCategory2()), PriceApplyEntity::getCategory2, priceApplyBO.getCategory2())
                .eq(null != priceApplyBO.getType(), PriceApplyEntity::getType, priceApplyBO.getType())
                .like(StrUtil.isNotBlank(priceApplyBO.getCode()), PriceApplyEntity::getCode, priceApplyBO.getCode().trim())
                .eq(StringUtil.isNotEmpty(priceApplyBO.getFutureCode()), PriceApplyEntity::getFutureCode, priceApplyBO.getFutureCode())
                .eq(StrUtil.isNotBlank(priceApplyBO.getDominantCode()), PriceApplyEntity::getDominantCode, priceApplyBO.getDominantCode())
                .in(!status.isEmpty(), PriceApplyEntity::getStatus, status)
                //.ne(!status.isEmpty() && status.get(0) == PriceStatusEnum.WAIT_PENDING.getValue(), PriceApplyEntity::getType, PriceTypeEnum.STRUCTURE_PRICING.getValue())
                //.ne(null != priceApplyBO.getApplyType() && priceApplyBO.getApplyType().equals(PriceApplyTypeEnum.MODIFY_APPLY.getValue()), PriceApplyEntity::getAuditStatus, 0)
                .eq(null != priceApplyBO.getApplyPrice(), PriceApplyEntity::getApplyPrice, priceApplyBO.getApplyPrice())
                .eq(null != priceApplyBO.getApplyType(), PriceApplyEntity::getAllocateNum, BigDecimal.ZERO)
                .like(StrUtil.isNotBlank(createdBy), PriceApplyEntity::getCreatedBy, createdBy)
                .and(null != priceApplyBO.getAllocateNumType(),
                        wrapper -> wrapper.eq(null != priceApplyBO.getAllocateNumType() && priceApplyBO.getAllocateNumType() == 1, PriceApplyEntity::getAllocateNum, 0)
                                .gt(null != priceApplyBO.getAllocateNumType() && priceApplyBO.getAllocateNumType() == 2, PriceApplyEntity::getAllocateNum, 0)
                )
                .between(null != priceApplyBO.getApplyStartTime() && null != priceApplyBO.getApplyEndTime(), PriceApplyEntity::getCreatedAt, DateTimeUtil.formatDateTimeString00(priceApplyBO.getApplyStartTime()), DateTimeUtil.formatDateTimeString24(priceApplyBO.getApplyEndTime()))
                .in(null != priceApplyBO.getTypeList(), PriceApplyEntity::getType, priceApplyBO.getTypeList())
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(PriceApplyEntity::getUpdatedAt);

        return this.baseMapper.selectPage(new Page<>(applyBOQueryDTO.getPageNo(), applyBOQueryDTO.getPageSize()), lambdaQueryWrapper);
    }

    /**
     * 申请单各个数量
     *
     * @param status
     * @param customerId
     * @return
     */
    public Integer getPriceApplyStat(Integer status, Integer customerId, Integer categoryId) {
        return this.baseMapper.selectCount(Wrappers.<PriceApplyEntity>lambdaQuery()
                .eq(PriceApplyEntity::getStatus, status)
                .eq(PriceApplyEntity::getCategoryId, categoryId)
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .eq(customerId != null, PriceApplyEntity::getCustomerId, customerId));
    }


    public PriceApplyEntity getPriceApplyEntityById(String applyId) {
        return this.getById(applyId);
    }

    /**
     * 查询转月的合同
     *
     * @param contractId
     * @return
     */
    public PriceApplyEntity getStructuringByContractId(Integer contractId) {
        List<PriceApplyEntity> priceApplyEntities = this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getContractId, contractId)
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

        return priceApplyEntities.isEmpty() ? null : priceApplyEntities.get(0);
    }

    /**
     * 查询可改单撤单的申请单
     *
     * @param id
     * @return
     */
    public PriceApplyEntity getPriceApplyByReversePricing(Integer id) {
        return this.baseMapper.selectOne(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getId, id)
                        .in(PriceApplyEntity::getStatus, Arrays.asList(PriceStatusEnum.WAIT_PENDING.getValue(), PriceStatusEnum.WAIT_TRANSACTION.getValue()))
        );
    }

    /**
     * 根据状态查询申请单  ne  status
     *
     * @param status
     * @return
     */
    public List<PriceApplyEntity> queryPriceApplyByStatusAndContractId(Integer status, Integer contractId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getContractId, contractId)
                        .ne(PriceApplyEntity::getStatus, status)
        );
    }

    /**
     * 根据状态查询申请单  in  status
     *
     * @param statusList
     * @param contractId
     * @return
     */
    public List<PriceApplyEntity> queryPriceApplyByStatus(List<Integer> statusList, Integer contractId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(null != contractId, PriceApplyEntity::getContractId, contractId)
                        .ne(PriceApplyEntity::getType, PriceTypeEnum.STRUCTURE_PRICING.getValue())
                        .in(CollectionUtil.isNotEmpty(statusList), PriceApplyEntity::getStatus, statusList)
                        .eq(PriceApplyEntity::getSalesType, ContractSalesTypeEnum.SALES.getValue())
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }


    /**
     * 查询待挂单或待成交状态的申请单
     *
     * @param id
     * @param status
     * @param auditStatusList
     * @return
     */
    public PriceApplyEntity getPriceApplyByIdAndStatus(Integer id, List<Integer> status, List<Integer> auditStatusList) {
        return this.baseMapper.selectOne(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getId, id)
                        .in(PriceApplyEntity::getStatus, status)
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
//                        .notIn(PriceApplyEntity::getAuditStatus, auditStatusList)
        );
    }


    /**
     * 根据合约查询申请单信息
     *
     * @param domainCode
     * @return
     */
    public List<PriceApplyEntity> queryPriceApplyByStatusAndContractId(String domainCode) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getDominantCode, domainCode)
        );
    }

    /**
     * 根据合约查询成交待分配的申请单
     *
     * @param domainCode
     */
    public List<PriceApplyEntity> getPriceApplysOrdersByDominantCode(String customerId, String domainCode, Integer categoryId) {
        List<PriceApplyEntity> list = this.list(new LambdaQueryWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getType, PriceTypeEnum.TRANSFER_MONTH.getValue())
                .eq(PriceApplyEntity::getCustomerId, customerId)
                .eq(PriceApplyEntity::getDominantCode, domainCode)
                .eq(PriceApplyEntity::getCategoryId, categoryId)
                .eq(PriceApplyEntity::getStatus, PriceStatusEnum.WAIT_ALLOCATE.getValue())
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .apply("deal_num - allocate_num > 0"));
        return list;
    }

    public PriceApplyEntity getPriceApplyByCode(String code) {
        List<PriceApplyEntity> priceApplyEntities = this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getCode, code)
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

        return priceApplyEntities.isEmpty() ? null : priceApplyEntities.get(0);
    }

    public PriceApplyEntity getStructurePriceApplyByCode(String code) {
        List<PriceApplyEntity> priceApplyEntities = this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getCode, code)
                        .eq(PriceApplyEntity::getStatus, PriceStatusEnum.STRUCTURING.getValue())
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );

        return priceApplyEntities.isEmpty() ? null : priceApplyEntities.get(0);
    }

    public List<PriceApplyEntity> queryPriceApplyByPriceStatus(String priceStatus, String startTime, String endTime) {
        List<PriceApplyEntity> priceApplyEntities = this.list(new LambdaQueryWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getStatus, priceStatus)
                .gt(PriceApplyEntity::getNotAllocateNum, BigDecimal.ZERO)
                .gt(StrUtil.isNotBlank(startTime), PriceApplyEntity::getCreatedAt, startTime)
                .lt(StrUtil.isNotBlank(endTime), PriceApplyEntity::getCreatedAt, endTime)
                .in(PriceApplyEntity::getType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue()))
                .orderByDesc(PriceApplyEntity::getType));
        return priceApplyEntities;
    }

    public List<PriceApplyEntity> getNotDealByContractId(Integer contractId) {
        return this.list(new LambdaQueryWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getContractId, contractId)
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(PriceApplyEntity::getStatus, Arrays.asList(PriceStatusEnum.WAIT_PENDING.getValue(), PriceStatusEnum.WAIT_TRANSACTION.getValue()))
        );
    }

    public List<PriceApplyEntity> getByContractId(Integer contractId) {
        return this.list(new LambdaQueryWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getContractId, contractId));
    }


    public List<PriceApplyEntity> getPriceApplyHasOperation(Integer customerId, String domainCode, Integer categoryId) {
        return this.list(new LambdaQueryWrapper<PriceApplyEntity>()
                .eq(PriceApplyEntity::getCustomerId, customerId)
                .eq(PriceApplyEntity::getDominantCode, domainCode)
                .eq(PriceApplyEntity::getCategoryId, categoryId)
                .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .in(PriceApplyEntity::getType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue()))
                .in(PriceApplyEntity::getStatus, Arrays.asList(PriceStatusEnum.WAIT_PENDING.getValue(), PriceStatusEnum.WAIT_TRANSACTION.getValue(), PriceStatusEnum.WAIT_ALLOCATE.getValue()))
        );
    }


    public List<PriceApplyEntity> getContractByCondition(PriceApplyBO priceApplyBO) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(null != priceApplyBO.getCategoryId(), PriceApplyEntity::getCategory2, priceApplyBO.getCategoryId())
                        .eq(null != priceApplyBO.getType(), PriceApplyEntity::getType, priceApplyBO.getType())
                        .ne(PriceApplyEntity::getStatus, PriceStatusEnum.NOT_TRANSACTION.getValue())
                        .between(PriceApplyEntity::getCreatedAt, DateTimeUtil.formatDateTimeString00(new Date()), DateTimeUtil.formatDateTimeString24(new Date()))
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByAsc(PriceApplyEntity::getCustomerId, PriceApplyEntity::getDominantCode));
    }

    public List<PriceApplyEntity> priceApplyMigration() {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .ne(PriceApplyEntity::getType, PriceTypeEnum.REVERSE_PRICING.getValue())
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .eq(PriceApplyEntity::getStatus, PriceStatusEnum.WAIT_ALLOCATE.getValue()));
    }

    public List<PriceApplyEntity> queryPriceApplyByContractId(Integer contractId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyEntity>lambdaQuery()
                        .eq(PriceApplyEntity::getStatus, PriceStatusEnum.STRUCTURING.getValue())
                        .eq(PriceApplyEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .eq(PriceApplyEntity::getContractId, contractId));
    }

}
