package com.navigator.future.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.navigator.admin.facade.TradeDayFacade;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.CRoleEntity;
import com.navigator.admin.pojo.entity.TradeDayEntity;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.future.constant.ConfigurationConstants;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.dao.PriceDealDetailDao;
import com.navigator.future.dao.PriceDealInfoDao;
import com.navigator.future.enums.PendingTypeEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.enums.SendNotDealInmailTimeEnum;
import com.navigator.future.mapper.PriceDealDetailMapper;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.entity.PriceDealInfoEntity;
import com.navigator.future.pojo.vo.PriceApplyVO;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.future.pojo.vo.PriceDealInfoVO;
import com.navigator.future.service.IFuturesDomainService;
import com.navigator.future.service.IPriceAllocateService;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.future.service.IPriceDealService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.facade.DomainCodeFacade;
import com.navigator.trade.facade.TtTranferFacade;
import com.navigator.trade.pojo.dto.contract.ContractStructureDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.ContractSignStatusEnum;
import com.navigator.trade.pojo.enums.ContractStructurePricingStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
@Slf4j
public class PriceDealServiceImpl extends ServiceImpl<PriceDealDetailMapper, PriceDealDetailEntity> implements IPriceDealService {

    @Resource
    private PriceDealDetailDao priceDealDetailDao;
    @Resource
    private PriceApplyDao priceApplyDao;
    @Resource
    private IPriceApplyService iPriceApplyService;
    @Resource
    private TradeDayFacade tradeDayFacade;
    @Resource
    private DomainCodeFacade domainCodeFacade;
    @Resource
    private ContractFacade contractFacade;
    @Resource
    private PriceDealInfoDao priceDealInfoDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private MessageFacade messageFacade;
    @Resource
    private CRoleFacade cRoleFacade;
    @Resource
    private IFuturesDomainService iFuturesDomainService;
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private TtTranferFacade ttTranferFacade;
    @Resource
    private IPriceAllocateService priceAllocateService;
    @Autowired
    private CategoryFacade categoryFacade;


    @Override
    public List<PriceDealDetailVO> queryPriceDealDetail(Integer priceApplyId) {
        return priceDealDetailDao.queryPriceDealDetail(priceApplyId).stream().map(priceDealDetailEntity ->
                BeanConvertUtils.convert(PriceDealDetailVO.class, priceDealDetailEntity)
        ).collect(Collectors.toList());
    }

    @Override
    public List<PriceDealDetailEntity> queryPriceDealDetailByApplyIdStatus(Integer priceApplyId, Integer status) {

        return priceDealDetailDao.queryPriceDealDetailByApplyIdStatus(priceApplyId, status);
    }

    @Override
    public List<PriceDealDetailEntity> queryPriceDealDetailByApplyId(Integer priceApplyId) {
        return priceDealDetailDao.queryPriceDealDetail(priceApplyId);
    }

    @Override
    public List<PriceDealDetailEntity> queryDominantPriceDealDetail(Integer priceApplyId) {
        return priceDealDetailDao.queryDominantPriceDealDetail(priceApplyId);
    }

    @Override
    @Transactional
    public String priceDealProcess(PriceApplyEntity priceApplyEntity) {
        return priceDealProcess(priceApplyEntity, new Date());
    }

    @Override
    public List<PriceDealInfoVO> queryPriceDealInfo(Integer priceApplyId) {
        List<PriceDealInfoVO> priceDealInfoVOS = priceDealInfoDao.queryPriceDealInfo(priceApplyId).stream().map(
                priceDealInfoEntity -> {
                    return BeanConvertUtils.convert(PriceDealInfoVO.class, priceDealInfoEntity);
                }).collect(Collectors.toList());


        return priceDealInfoVOS;
    }

    @Override
    public PriceDealDetailEntity priceDealDealDetailById(Integer priceDealDetailId) {
        return priceDealDetailDao.getById(priceDealDetailId);
    }

    @Override
    public List<PriceDealDetailEntity> queryPriceDealDetailOperateNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {
        return priceDealDetailDao.queryPriceDealDetailOperateNum(priceApplyOperateNumDTO);
    }

    @Override
    @Transactional
    public String priceDealProcess(Integer priceApplyId, Date d) {
        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceApplyId);
        return priceDealProcess(priceApplyEntity, d);
    }

    @Override
    @Transactional
    @Deprecated
    public String priceDealProcess(PriceApplyEntity priceApplyEntity, Date d) {
        String processResult = "";
        // 结构化合同，且在定价时间区间内
        if (priceApplyEntity.getType() != PriceTypeEnum.STRUCTURE_PRICING.getValue()
                || !DateTimeUtil.isBetween(d, priceApplyEntity.getStartTime(), priceApplyEntity.getEndTime())) {
            processResult = "不定价：不在定价时间范围(" + DateTimeUtil.formatDate(d) + ")";
            log.warn("");
            return processResult;
        }

        //不能重复处理
        PriceDealDetailEntity priceDealDetailEntity = priceDealDetailDao.getPriceDealDetail(priceApplyEntity.getId(), DateTimeUtil.formatDateString(d));
        if (null != priceDealDetailEntity) {
            //TODO NEO
            processResult = "不定价：不在重复定价(" + DateTimeUtil.formatDate(d) + ")";
            //log.warn("");
            return processResult;
        }

        //交易日才能处理
        String tradeDay = DateTimeUtil.formatDateString(d);
        if (!tradeDayFacade.isTradeDay(tradeDay)) {
            //TODO NEO
            processResult = "不定价：非交易日(" + DateTimeUtil.formatDate(d) + ")";
            //log.warn("");
            return processResult;
        }

        System.out.println("=====*****开始处理：" + DateTimeUtil.formatDate(d));

        //有收盘价
        Result rtn = domainCodeFacade.getClosingPrice(priceApplyEntity.getCategoryId(), priceApplyEntity.getDominantCode(), null);
        DomainPriceEntity domainPriceEntity = (DomainPriceEntity) rtn.getData();
        if (!rtn.isSuccess() || null == domainPriceEntity) {
            processResult = "不定价：无收盘价，待重试(" + DateTimeUtil.formatDate(d) + ")";
            return processResult;
        }

        boolean isLastDay = DateTimeUtil.formatDateString(priceApplyEntity.getEndTime()).equals(DateTimeUtil.formatDateString(d));
        if (isLastDay) {
            System.out.println("=====*****最后一天了：" + DateTimeUtil.formatDate(d));
        }

        BigDecimal closingPrice = domainPriceEntity.getPrice().stripTrailingZeros();

        int dealNum = 0;
        int notDealNum = 0;
        Integer dealPrice = 0;

        String priceRule = "";

        switch (priceApplyEntity.getStructureType()) {
            case 1:
                /**
                 * 收盘价>=maxPrice（敲出价格），不定价，释放2个单位量
                 * minPrice（增强价格）<收盘价<maxPrice（敲出价格），定价1个单位量，释放1个单位量
                 * 收盘价<=minPrice（增强价格），定价2个单位
                 */
//                if (priceApplyEntity.getMinPrice().intValue() >= closingPrice.intValue()) {
//                    //第一档，价格低于预期最低价，定2放0
//                    priceRule = "低价，定2单位量，释放0；";
//                    dealNum = priceApplyEntity.getUnitNum().intValue() * 2;
//                    notDealNum = 0;
//                } else if (closingPrice.intValue() >= priceApplyEntity.getMaxPrice().intValue()) {
//                    //第三档，价格高于预期最高价，定0放2
//                    priceRule = "高价，定0，释放2单位量；";
//                    dealNum = 0;
//                    notDealNum = priceApplyEntity.getUnitNum().intValue() * 2;
//                } else {
//                    //第二档，价格位于预期价中间，定1放1
//
//                }
                priceRule = "中间价，定1单位量，释放1单位量；";
                dealNum = priceApplyEntity.getUnitNum().intValue();
                notDealNum = priceApplyEntity.getUnitNum().intValue();
                dealPrice = priceApplyEntity.getMinPrice().intValue();
                priceApplyEntity.setNotDealNum(priceApplyEntity.getNotDealNum().add(BigDecimal.valueOf(notDealNum)));
                break;
            case 2:
                /**
                 * 收盘价>=maxPrice（敲出价格），定价1个单位量，释放1个单位量(高价）
                 * minPrice（增强价格）<收盘价<maxPrice（敲出价格），定价1个单位量，释放1个单位量（低价）
                 * 收盘价<=minPrice（增强价格），定价2个单位（低价）
                 */
//                if (priceApplyEntity.getMinPrice().intValue() >= closingPrice.intValue()) {
//                    //第一档，价格低于预期最低价，定2放0
//                    priceRule = "低价，定2单位量，释放0；";
//                    dealNum = priceApplyEntity.getUnitNum().intValue() * 2;
//                    notDealNum = 0;
//                    dealPrice = priceApplyEntity.getMinPrice().intValue();
//                } else if (closingPrice.intValue() >= priceApplyEntity.getMaxPrice().intValue()) {
//                    //第三档，价格高于预期最高价，定1放1
//                    priceRule = "高价，定1单位量，释放1单位量；";
//                    dealNum = priceApplyEntity.getUnitNum().intValue();
//                    notDealNum = priceApplyEntity.getUnitNum().intValue();
//                    dealPrice = priceApplyEntity.getMaxPrice().intValue();
//                } else {
//                    //第二档，价格位于预期价中间，定1放1
//
//                }
                priceRule = "中间价，定1单位量，释放1单位量；";
                dealNum = priceApplyEntity.getUnitNum().intValue();
                notDealNum = priceApplyEntity.getUnitNum().intValue();
                dealPrice = priceApplyEntity.getMinPrice().intValue();
                priceApplyEntity.setNotDealNum(priceApplyEntity.getNotDealNum().add(BigDecimal.valueOf(notDealNum)));
                break;
            case 3:
                /**
                 * 收盘价>=maxPrice（敲出价格），不定价，释放2个单位量
                 * minPrice（增强价格）<收盘价<maxPrice（敲出价格），定价1个单位量，释放1个单位量（低价）
                 * 收盘价<=minPrice（增强价格），定价1个单位量，释放1个单位量（低价）
                 * 最后一天 且 收盘价<=minPrice（增强价格）时，额外再定价 总申请量的一半
                 */
//                if (closingPrice.intValue() >= priceApplyEntity.getMaxPrice().intValue()) {
//                    //第三档，价格高于预期最高价，定0放2
//                    priceRule = "高价，定0，释放2单位量（统一释放）；";
//                    dealNum = 0;
//                    //notDealNum = priceApplyEntity.getUnitNum() * 2;
//                } else {
//                    //第一、二档，价格低于预期最高价，定1锁1，不释放
//
//                }
                priceRule = "中低价，定1单位量，释放1单位量（统一释放）；";
                dealNum = priceApplyEntity.getUnitNum().intValue();
                //notDealNum = priceApplyEntity.getUnitNum();
                dealPrice = priceApplyEntity.getMinPrice().intValue();
                if (isLastDay) {
                    //最后一天 且 第一档，额外再定价总量的一半，其余的释放
                    if (priceApplyEntity.getMinPrice().intValue() >= closingPrice.intValue()) {
                        priceRule = "最后一天低价，额外再定一半的量；";
                        dealNum = dealNum + priceApplyEntity.getApplyNum().intValue() / 2;
                        dealPrice = priceApplyEntity.getMinPrice().intValue();
                    }

                    BigDecimal otherNum = priceApplyEntity.getApplyNum().subtract(priceApplyEntity.getDealNum()).subtract(BigDecimal.valueOf(dealNum));
                    notDealNum = otherNum.intValue();
                }
                break;
            default:
                break;
        }

        priceApplyEntity.setDealNum(priceApplyEntity.getDealNum().add(BigDecimal.valueOf(dealNum)));
        priceApplyEntity.setNotDealNum(priceApplyEntity.getNotDealNum().add(BigDecimal.valueOf(notDealNum)));

        if (isLastDay) {
            priceApplyEntity.setStatus(PriceStatusEnum.WAIT_ALLOCATE.getValue());
        } else {
            priceApplyEntity.setStatus(PriceStatusEnum.WAIT_TRANSACTION.getValue());
        }

        priceApplyEntity.setLatestDealDay(DateTimeUtil.formatDateValue(d));
        priceApplyEntity.setUpdatedAt(new Date());
        priceApplyEntity.setUpdatedBy("SYSTEM");

        priceDealDetailEntity = new PriceDealDetailEntity();
        priceDealDetailEntity.setPriceApplyId(priceApplyEntity.getId())
                .setApplyNum(priceApplyEntity.getApplyNum())
                .setDealNum(BigDecimal.valueOf(dealNum))
                .setNotDealNum(BigDecimal.valueOf(notDealNum))
                .setUnitNum(priceApplyEntity.getUnitNum())
                .setClosingPrice(closingPrice)
                .setDealPrice(BigDecimal.valueOf(dealPrice))
                .setDealDate(DateTimeUtil.formatDateString(d))
                .setMemo(priceRule)
                .setCreatedAt(new Date())
                .setUpdatedAt(new Date())
                .setCreatedBy("SYSTEM")
                .setUpdatedBy("SYSTEM");

        priceApplyDao.updateById(priceApplyEntity);
        priceDealDetailDao.save(priceDealDetailEntity);

        if (dealNum > 0 && dealPrice > 0) {
            createPriceDealInfo(priceApplyEntity, priceDealDetailEntity);
        }

        //更新定价状态
        ContractStructureDTO contractStructureDTO = new ContractStructureDTO();
        contractStructureDTO.setContractId(priceApplyEntity.getContractId())
                .setPriceStatus(ContractStructurePricingStatusEnum.PRICING.getValue());
        if (isLastDay) {
            contractStructureDTO.setPriceStatus(ContractStructurePricingStatusEnum.COMPLATE.getValue());
        }

        contractFacade.updateStructureContractPricingStatus(contractStructureDTO);

        processResult = "定价成功。";
        return processResult;
        //TODO NEO 增加操作日志

    }


    private void createPriceDealInfo(PriceApplyEntity priceApplyEntity, PriceDealDetailEntity priceDealDetailEntity) {
        PriceDealInfoEntity priceDealInfoEntity = priceDealInfoDao.getPriceDealDetail(priceApplyEntity.getId(), priceDealDetailEntity.getDealPrice());

        if (null == priceDealInfoEntity) {
            priceDealInfoEntity = new PriceDealInfoEntity()
                    .setPriceApplyId(priceApplyEntity.getId())
                    .setContractId(priceDealDetailEntity.getStructureContractId())
                    .setPriceType(priceApplyEntity.getType())
                    .setApplyNum(priceApplyEntity.getApplyNum())
                    .setDealNum(BigDecimal.ZERO)
                    .setAssignedNum(BigDecimal.ZERO)
                    .setDealPrice(BigDecimal.ZERO)
                    .setCreatedBy("SYSTEM")
                    .setUpdatedBy("SYSTEM");
            priceDealInfoDao.save(priceDealInfoEntity);
        }
        priceDealInfoEntity.setDealNum(priceDealInfoEntity.getDealNum().add(priceDealDetailEntity.getDealNum()))
                .setDealPrice(priceDealDetailEntity.getDealPrice());
        priceDealInfoDao.updateById(priceDealInfoEntity);
    }

    @Override
    public boolean savePriceDeal(PriceDealDetailDTO priceDealDetailDTO) {
        PriceDealDetailEntity priceDealDetailEntity = BeanConvertUtils.convert(PriceDealDetailEntity.class, priceDealDetailDTO);
        if (PriceStatusEnum.WAIT_ALLOCATE.getValue() == priceDealDetailDTO.getStatus()) {
            sendDealInmail(priceDealDetailEntity);
        }
        return priceDealDetailDao.save(priceDealDetailEntity);
    }

    @Override
    public Result queryPriceDealDetailIPage(QueryDTO<PriceApplyBO> applyBOQueryDTO, SystemEnum systemEnum) {


        IPage<PriceDealDetailEntity> priceDealDetailIPage = priceDealDetailDao.queryPriceDealDetailIPage(applyBOQueryDTO);

        return Result.page(priceDealDetailIPage, priceDealDetailIPage.getRecords().stream().map(priceDealDetailEntity -> {

            //查询申请单
            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceDealDetailEntity.getPriceApplyId());

            PriceApplyVO priceApplyVO = new PriceApplyVO();
            BeanConvertUtils.copy(priceApplyVO, priceDealDetailEntity);
            priceApplyVO
                    .setPriceApplyId(priceDealDetailEntity.getPriceApplyId())
                    .setCode(priceApplyEntity.getCode())
                    .setPendingType(priceApplyEntity.getPendingType())
                    .setContractId(priceDealDetailEntity.getStructureContractId())
                    .setApplyHandNum(priceApplyEntity.getApplyHandNum().intValue())
                    .setApplyHand(priceApplyEntity.getApplyHandNum().intValue() + "手，" + priceDealDetailEntity.getApplyNum().setScale(3, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString() + "吨")
                    .setTransactionDiffPrice(priceDealDetailEntity.getTransactionDiffPrice())
                    .setTransactionPrice(priceDealDetailEntity.getDealPrice().toString())
                    .setAllocateNum(priceDealDetailEntity.getAllocateNum().stripTrailingZeros().setScale(3, BigDecimal.ROUND_UP))
                    .setNotAllocateNum(priceDealDetailEntity.getDealNum().stripTrailingZeros().subtract(priceDealDetailEntity.getAllocateNum()))
                    .setDealNum(priceDealDetailEntity.getDealHandNum() + "手，" + priceDealDetailEntity.getDealNum().setScale(3, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString() + "吨")
                    .setNotDealNum(priceDealDetailEntity.getNotDealNum().stripTrailingZeros().setScale(3, BigDecimal.ROUND_UP))
                    .setCompanyShortName(priceDealDetailEntity.getCompanyName())
                    .setDealContraryNum(priceApplyEntity.getDealContraryNum())
                    .setCreatedAt(priceApplyEntity.getCreatedAt());
            if (SystemEnum.COLUMBUS.equals(systemEnum)) {
                priceApplyVO.setApplyHandNum(null)
                        .setApplyHand(priceDealDetailEntity.getApplyNum().setScale(3, BigDecimal.ROUND_UP).stripTrailingZeros().toPlainString())
                        .setDealNum(priceDealDetailEntity.getDealNum().stripTrailingZeros().toPlainString());
            }
            return priceApplyVO.setTypeName(PriceTypeEnum.getByValue(priceDealDetailEntity.getType()).getDesc())
                    .setPendingTypeName(PendingTypeEnum.getByValue(priceApplyEntity.getPendingType()).getDescription());
        }).collect(Collectors.toList()));
    }

    @Override
    public List<PriceDealDetailEntity> queryPriceDealListByPriceStatus(Integer status) {
        return priceDealDetailDao.queryPriceDealListByPriceStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean structuringPriceDeal(PriceDealDetailDTO priceDealDetailDTOS) {

        List<PriceDealDetailEntity> priceDealDetailEntities = priceDealDetailDTOS.getPriceDealDetailEntities();

        //判断申请单
        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceDealDetailDTOS.getPriceApplyId());

        if (null == priceApplyEntity) {
            throw new BusinessException(ResultCodeEnum.PRICE_APPLY_NOT_EXIST);
        }

        if (PriceTypeEnum.STRUCTURE_PRICING.getValue() != priceApplyEntity.getType()) {
            throw new BusinessException("该申请单非结构化定价申请单");
        }


        //校验成交数量
        BigDecimal dealNum = priceApplyEntity.getDealNum();

        BigDecimal cumulativeNotDealNum = BigDecimal.ZERO;
        BigDecimal cumulativeDealNum = BigDecimal.ZERO;
        BigDecimal cumulativeCashReturn = BigDecimal.ZERO;

        for (PriceDealDetailEntity priceDealDetailDTO : priceDealDetailEntities) {

            BigDecimal notDealNum = BigDecimal.ZERO;
            if (null != priceDealDetailDTO.getNotDealNum()) {
                notDealNum = priceDealDetailDTO.getNotDealNum();
                cumulativeNotDealNum = cumulativeNotDealNum.add(notDealNum);
            }
            BigDecimal dealDetailDealNum = BigDecimal.ZERO;
            if (null != priceDealDetailDTO.getDealNum()) {
                dealDetailDealNum = priceDealDetailDTO.getDealNum();
                cumulativeDealNum = cumulativeDealNum.add(dealDetailDealNum);
            }
            if (null != priceDealDetailDTO.getDayTotalCashReturn()) {
                cumulativeCashReturn = cumulativeCashReturn.add(priceDealDetailDTO.getDayTotalCashReturn());
            }
            //成交量和释放量相加
            dealNum = dealNum.add(dealDetailDealNum.add(notDealNum));
            boolean b = addStructuringPriceDeal(priceDealDetailDTO, priceApplyEntity);

            if (!b) {
                throw new BusinessException(ResultCodeEnum.PRICE_DEAL_DE_DEFEATED);
            }
        }
        //成交量校验数量
        if (priceApplyEntity.getApplyNum().subtract(dealNum).compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(ResultCodeEnum.DEAL_HAND_NUM_MORE_THAN_APPLY_NUM);
        }

        ContractStructureCumulativeDTO contractStructureCumulativeDTO = new ContractStructureCumulativeDTO();
        contractStructureCumulativeDTO.setCashReturn(cumulativeCashReturn)
                .setDealNum(cumulativeDealNum)
                .setNotDealNum(cumulativeNotDealNum);

        //累加成交量
        disposeContractStructureCumulative(priceApplyEntity.getContractId(), contractStructureCumulativeDTO);

        //当申请单数量为0时修改申请单状态
        if (BigDecimalUtil.isEqual(priceApplyEntity.getApplyNum().subtract(dealNum), BigDecimal.ZERO)) {
            priceApplyEntity.setStatus(PriceStatusEnum.WAIT_ALLOCATE.getValue());
        }

        priceApplyEntity.setDealNum(dealNum);
        priceApplyDao.updateById(priceApplyEntity);
        // TODO 这边事务发生异常，合同处理合同数据，这边获取合同数据 ADD by zengshl
        //更改结构化定价合同数量
        ContractEntity contractEntity = contractFacade.getBasicContractById(priceApplyEntity.getContractId());

        if (null == contractEntity) {
            throw new BusinessException(ResultCodeEnum.TEMPLATE_IS_NOT_EXIST);
        }

        contractFacade.updateContract(contractEntity.setContractNum(dealNum));

        return true;
    }

    private boolean disposeContractStructureCumulative(Integer contractId, ContractStructureCumulativeDTO contractStructureCumulativeDTO) {
        //结构化定价合同
        ContractStructureEntity contractStructureEntity = contractFacade.getContractStructureById(contractId);
        if (null == contractStructureEntity) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
        }


        /*BigDecimal notDealNum = null == priceDealDetailDTO.getNotDealNum() ? BigDecimal.ZERO : priceDealDetailDTO.getNotDealNum();
        BigDecimal dealNum = null == priceDealDetailDTO.getDealNum() ? BigDecimal.ZERO : priceDealDetailDTO.getDealNum();
        BigDecimal dayTotalCashReturn = null == priceDealDetailDTO.getDayTotalCashReturn() ? BigDecimal.ZERO : priceDealDetailDTO.getDayTotalCashReturn();*/

        BigDecimal notDealNum = contractStructureCumulativeDTO.getNotDealNum();
        BigDecimal dealNum = contractStructureCumulativeDTO.getDealNum();
        BigDecimal dayTotalCashReturn = contractStructureCumulativeDTO.getCashReturn();
        BigDecimal totalNum = contractStructureEntity.getTotalNum();

        /*BigDecimal cumulativePrice = BigDecimal.ZERO;

        if (!BigDecimalUtil.isEqual(priceDealDetailDTO.getDealPrice())) {
            cumulativePrice = dealNum.multiply(priceDealDetailDTO.getDealPrice());
        }*/


        totalNum = totalNum.subtract(dealNum).subtract(notDealNum);
        notDealNum = notDealNum.add(contractStructureEntity.getCumulativeNotDealNum());
        dealNum = dealNum.add(contractStructureEntity.getCumulativeDealNum());
        dayTotalCashReturn = dayTotalCashReturn.add(contractStructureEntity.getCumulativeCashReturn());

        contractStructureEntity
                .setTotalNum(totalNum)
                .setCumulativeNotDealNum(notDealNum)
                .setCumulativeDealNum(dealNum)
                .setCumulativeCashReturn(dayTotalCashReturn);

        return contractFacade.updateStructureContract(contractStructureEntity);

    }

    private boolean addStructuringPriceDeal(PriceDealDetailEntity priceDealDetailDTO, PriceApplyEntity priceApplyEntity) {

        //判断该笔结构化定价成交是否有同一申请单,同一价格的成交单
        List<PriceDealDetailEntity> priceDealDetailEntities = priceDealDetailDao.getDealDetailDealPriceApplyIdEq(priceApplyEntity.getId(), priceDealDetailDTO.getDealPrice());
        priceDealDetailDTO
                .setStructureContractId(priceApplyEntity.getContractId())
                .setPriceApplyId(priceApplyEntity.getId());
        PriceDealDetailEntity priceDealDetailEntity = new PriceDealDetailEntity();
        if (!priceDealDetailEntities.isEmpty() && !BigDecimalUtil.isEqual(priceDealDetailDTO.getDealNum(), BigDecimal.ZERO)) {
            priceDealDetailEntity = priceDealDetailEntities.get(0);
            //成交数量相加
            BigDecimal dealNum = priceDealDetailEntity.getDealNum().add(priceDealDetailDTO.getDealNum());
            BigDecimal notAllocateNum = priceDealDetailEntity.getNotAllocateNum().add(priceDealDetailDTO.getDealNum());

            Integer dealHandNum = BigDecimalUtil.div(RoundingMode.HALF_UP, priceDealDetailDTO.getDealNum(), BigDecimalUtil.multiply(CalcTypeEnum.COUNT, ConfigurationConstants.HAND_BASICS, ConfigurationConstants.HAND_RATE)).intValue();

            priceDealDetailEntity
                    .setDealHandNum(priceDealDetailEntity.getDealHandNum() + dealHandNum)
                    .setDealNum(dealNum)
                    .setNotAllocateNum(notAllocateNum);

            priceDealDetailDao.updateById(priceDealDetailEntity);
            //结构化定价成交记录
            return saveDealInfoDao(priceDealDetailDTO, priceDealDetailEntity.getId());
        }

        priceDealDetailEntity
                .setPriceApplyId(priceApplyEntity.getId())
                .setApplyCode(priceApplyEntity.getCode())
                .setType(priceApplyEntity.getType())
                .setCustomerId(priceApplyEntity.getCustomerId())
                .setCustomerName(priceApplyEntity.getCustomerName())
                .setCategoryId(priceApplyEntity.getCategoryId())
                .setApplyNum(priceApplyEntity.getApplyNum())
                .setCreatedBy(priceApplyEntity.getCreatedBy())
                .setDominantCode(priceApplyEntity.getDominantCode())
                .setCompanyId(priceApplyEntity.getCompanyId())
                .setCompanyName(priceApplyEntity.getCompanyName())
                .setStructureContractId(priceApplyEntity.getContractId())
                .setCategory1(priceApplyEntity.getCategory1())
                .setCategory2(priceApplyEntity.getCategory2())
                .setCategory3(priceApplyEntity.getCategory3())
                .setFutureCode(priceApplyEntity.getFutureCode())
        ;

        if (null != priceDealDetailDTO.getDealNum() && !BigDecimalUtil.isEqual(priceDealDetailDTO.getDealNum(), BigDecimal.ZERO)) {
            //set参数成交的单
            priceDealDetailEntity
                    .setTranferDominantCode(priceApplyEntity.getTranferDominantCode())
                    .setDealHandNum(BigDecimalUtil.div(RoundingMode.HALF_UP, priceDealDetailDTO.getDealNum(), BigDecimalUtil.multiply(CalcTypeEnum.COUNT, ConfigurationConstants.HAND_BASICS, ConfigurationConstants.HAND_RATE)).intValue())
                    .setTransactionDiffPrice(null != priceDealDetailDTO.getTransactionDiffPrice() ? priceDealDetailDTO.getTransactionDiffPrice() : BigDecimal.ZERO)
                    .setDealPrice(null != priceDealDetailDTO.getDealPrice() ? priceDealDetailDTO.getDealPrice() : BigDecimal.ZERO)
                    .setDealNum(priceDealDetailDTO.getDealNum())
                    .setDayTotalCashReturn(priceDealDetailDTO.getDayTotalCashReturn())
                    .setNotAllocateNum(priceDealDetailDTO.getDealNum())
                    .setNotDealNum(BigDecimal.ZERO)
                    .setDealDate(StrUtil.isNotBlank(priceDealDetailDTO.getDealDate()) ? priceDealDetailDTO.getDealDate() : DateTimeUtil.formatDateString())
                    .setStatus(PriceStatusEnum.WAIT_ALLOCATE.getValue());

            //结构化定价成交量生成成交单
            priceDealDetailDao.save(priceDealDetailEntity);
        }

        BigDecimal notDealNum = BigDecimal.ZERO;
        if (null != priceDealDetailDTO.getNotDealNum()) {
            notDealNum = priceDealDetailDTO.getNotDealNum();
        }

        //结构化定价释放量生成未成交单
        if (!BigDecimalUtil.isEqual(notDealNum, BigDecimal.ZERO)) {
            priceDealDetailEntity
                    .setDealNum(BigDecimal.ZERO)
                    .setNotDealNum(priceDealDetailDTO.getNotDealNum())
                    .setStatus(PriceStatusEnum.NOT_TRANSACTION.getValue());
            priceDealDetailDao.save(priceDealDetailEntity);

            //结构化合同添加释放量
            contractFacade.addStructureRelease(priceApplyEntity.getContractId(), priceDealDetailDTO.getNotDealNum());

        }
        saveDealInfoDao(priceDealDetailDTO, priceDealDetailEntity.getId());
        return true;
    }

    /**
     * 结构化定价成交记录表
     *
     * @param priceDealDetailDTO
     * @param dealDetailId
     * @return
     */
    private boolean saveDealInfoDao(PriceDealDetailEntity priceDealDetailDTO, Integer dealDetailId) {

        PriceDealInfoEntity priceDealInfoEntity = new PriceDealInfoEntity();
        priceDealInfoEntity.setDealNum(priceDealDetailDTO.getDealNum())
                .setDealPrice(priceDealDetailDTO.getDealPrice())
                .setPriceApplyId(priceDealDetailDTO.getPriceApplyId())
                .setNotDealNum(priceDealDetailDTO.getNotDealNum())
                .setContractId(priceDealDetailDTO.getStructureContractId())
                .setDayTotalCashReturn(priceDealDetailDTO.getDayTotalCashReturn())
                .setDealDetailId(dealDetailId)
                .setDealDate(StrUtil.isNotBlank(priceDealDetailDTO.getDealDate()) ? priceDealDetailDTO.getDealDate() : DateTimeUtil.formatDateString())
                .setCreatedBy(employFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId())).getName())
                .setCreatedAt(new Date());

        return priceDealInfoDao.save(priceDealInfoEntity);
    }


    @Override
    public void priceApplyMigration() {
        List<PriceApplyEntity> priceApplyEntities = priceApplyDao.priceApplyMigration();

        for (PriceApplyEntity priceApplyEntity : priceApplyEntities) {
            PriceDealDetailEntity priceDealDetailEntity = new PriceDealDetailEntity();

            //set参数成交的单
            priceDealDetailEntity.setPriceApplyId(priceApplyEntity.getId())
                    .setApplyCode(priceApplyEntity.getCode())
                    .setType(priceApplyEntity.getType())
                    .setCustomerId(priceApplyEntity.getCustomerId())
                    .setCustomerName(priceApplyEntity.getCustomerName())
                    .setCategoryId(priceApplyEntity.getCategoryId())
                    .setApplyNum(priceApplyEntity.getApplyNum())
                    .setDominantCode(priceApplyEntity.getDominantCode())
                    .setTranferDominantCode(priceApplyEntity.getTranferDominantCode())
                    .setDealHandNum(priceApplyEntity.getDealHandNum())
                    .setTransactionDiffPrice(priceApplyEntity.getTransactionDiffPrice())
                    .setDealPrice(priceApplyEntity.getTransactionPrice())
                    .setDealNum(priceApplyEntity.getDealNum())
                    .setNotAllocateNum(priceApplyEntity.getNotAllocateNum())
                    .setNotDealNum(BigDecimal.ZERO)
                    .setStatus(priceApplyEntity.getStatus())
                    .setStructureContractId(priceApplyEntity.getContractId())
                    .setCreatedBy(priceApplyEntity.getCreatedBy());

            //结构化定价成交量生成成交单
            priceDealDetailDao.save(priceDealDetailEntity);
        }
    }


    @Override
    public boolean getPriceDealOrdersByDominantCode(String customerId, String domainCode, Integer categoryId) {
        List<PriceDealDetailEntity> priceApplyEntities = priceDealDetailDao.getPriceDealOrdersByDominantCode(customerId, domainCode, categoryId);
        return !priceApplyEntities.isEmpty();
    }

    @Override
    public boolean priceDealDetailUpdateById(PriceDealDetailEntity priceDealDetailEntity) {
        return priceDealDetailDao.updateById(priceDealDetailEntity);
    }

    /**
     * 成交发送站内信
     */
    public void sendDealInmail(PriceDealDetailEntity priceDealDetailEntity) {

        MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
        messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_DEAL_ALLOCATE.getDesc());
        messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_DEAL_ALLOCATE.name());
        messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        messageInfoDTO.setCategoryId(priceDealDetailEntity.getCategoryId());
        messageInfoDTO.setMenuCode(String.valueOf(MenuCodeEnum.C_PRICE.getValue()));
        messageInfoDTO.setCustomerId(priceDealDetailEntity.getCustomerId());
        Map<String, Object> dataMap = new HashMap<>();

        List<CRoleEntity> cRoleEntities = cRoleFacade.queryRoleListByDefInfosSalesType(Arrays.asList(204, 207, 205), priceDealDetailEntity.getCategoryId(), ContractSalesTypeEnum.PURCHASE.getValue());
        if (cRoleEntities.isEmpty()) {
            return;
        }
        CRoleEntity roleEntity = cRoleEntities.get(0);
        List<String> receiver = new ArrayList<>();
        receiver.add(String.valueOf(roleEntity.getId()));
        receiver.add(String.valueOf(202));
        messageInfoDTO.setReceivers(receiver);

        dataMap.put("priceType", PriceTypeEnum.getByValue(priceDealDetailEntity.getType()).getDesc());
        String lkgFutureSymbol = priceDealDetailEntity.getFutureCode();
        dataMap.put("dominantCode", lkgFutureSymbol + priceDealDetailEntity.getDominantCode());
        dataMap.put("applyNum", priceDealDetailEntity.getApplyNum());
        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceDealDetailEntity.getPriceApplyId());
        BigDecimal price = PriceTypeEnum.TRANSFER_MONTH.getValue() == priceApplyEntity.getType() ? priceApplyEntity.getApplyDiffPrice() : priceApplyEntity.getApplyPrice();
        dataMap.put("applyPrice", price);
        dataMap.put("customerName", priceApplyEntity.getCustomerName());

        //根据主体类型给出主体名称
        dataMap.put("LDCName", priceApplyEntity.getCompanyName());
        dataMap.put("categoryName", priceDealDetailEntity.getFutureCode());
        dataMap.put("transactionPrice", PriceTypeEnum.TRANSFER_MONTH.getValue() == priceApplyEntity.getType() ? priceDealDetailEntity.getTransactionDiffPrice() : priceDealDetailEntity.getDealPrice());
        dataMap.put("priceCode", priceApplyEntity.getCode());
        messageInfoDTO.setDataMap(dataMap);
        messageFacade.sendMessage(messageInfoDTO);
    }

    /**
     * 未成交发送站内信
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendNotDealInmail() {
        //交易日才能处理
        String tradeDay = DateTimeUtil.formatDateString(new Date());

        log.error("sendNotDealInmail.未成交发送站内信开始:{}", new Date());
        if (!tradeDayFacade.isTradeDayValue(tradeDay)) {
            return;
        }

        String hhmm = DateTimeUtil.formatDateHHMMString(new Date());
        if (!SendNotDealInmailTimeEnum.isSendTime(hhmm)) {
            return;
        }


        SendNotDealInmailTimeDTO sendNotDealInmailTimeDTO = sendNotDealInmailTime();
        if (null == sendNotDealInmailTimeDTO) {
            return;
        }

        //根据客户id分组查询未成交单
        List<PriceDealDetailEntity> priceDealDetailGroup = priceDealDetailDao.getDealDetailGroup(null, null, sendNotDealInmailTimeDTO.getStartTime(), sendNotDealInmailTimeDTO.getEndTime());
        for (PriceDealDetailEntity priceDealDetailEntity : priceDealDetailGroup) {
            MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
            messageInfoDTO.setBusinessSceneCode(BusinessSceneEnum.COLUMBUS_NOT_DEAL.getDesc());
            messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.COLUMBUS_NOT_DEAL.name());
            messageInfoDTO.setSystem(SystemEnum.COLUMBUS.getValue());
            messageInfoDTO.setCategoryId(priceDealDetailEntity.getCategoryId());
            messageInfoDTO.setCustomerId(priceDealDetailEntity.getCustomerId());
            List<CRoleEntity> cRoleEntities = cRoleFacade.queryRoleListByDefInfosSalesType(Arrays.asList(204, 207, 205), priceDealDetailEntity.getCategoryId(), ContractSalesTypeEnum.PURCHASE.getValue());
            if (cRoleEntities.isEmpty()) {
                return;
            }

            CRoleEntity roleEntity = cRoleEntities.get(0);
            List<String> receiver = new ArrayList<>();
            receiver.add(String.valueOf(roleEntity.getId()));
            receiver.add(String.valueOf(202));
            messageInfoDTO.setReceivers(receiver);


            List<Map<String, Object>> dataMaps = new ArrayList<>();

            //根据分组查询出的客户id去遍历数据
            List<PriceDealDetailEntity> priceDealDetailEntities = priceDealDetailDao.getDealDetailGroup(priceDealDetailEntity.getCustomerId(), priceDealDetailEntity.getCategoryId(), sendNotDealInmailTimeDTO.getStartTime(), sendNotDealInmailTimeDTO.getEndTime());
            for (PriceDealDetailEntity priceDealDetail : priceDealDetailEntities) {

                CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(Integer.valueOf(priceDealDetail.getCategory2()));

                Map<String, Object> dataMap = new HashMap<>();
                dataMap.put("priceType", PriceTypeEnum.getByValue(priceDealDetail.getType()).getDesc());
                String lkgFutureSymbol = priceDealDetailEntity.getFutureCode();
                dataMap.put("dominantCode", lkgFutureSymbol + priceDealDetail.getDominantCode());
                dataMap.put("applyNum", priceDealDetail.getApplyNum());
                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceDealDetail.getPriceApplyId());
                BigDecimal price = PriceTypeEnum.TRANSFER_MONTH.getValue() == priceApplyEntity.getType() ? priceApplyEntity.getApplyDiffPrice() : priceApplyEntity.getApplyPrice();
                dataMap.put("applyPrice", price);
                dataMap.put("customerName", priceApplyEntity.getCustomerName());
                dataMap.put("LDCName", priceApplyEntity.getCompanyName());
                dataMap.put("categoryName", categoryEntity.getName());
                dataMap.put("transactionPrice", PriceTypeEnum.TRANSFER_MONTH.getValue() == priceApplyEntity.getType() ? priceDealDetail.getTransactionDiffPrice() : priceDealDetail.getDealPrice());
                dataMap.put("priceCode", priceApplyEntity.getCode());

                dataMaps.add(dataMap);
            }
            messageInfoDTO.setDataMaps(dataMaps);
            messageFacade.sendMessage(messageInfoDTO);
        }

        log.error("sendNotDealInmail.未成交发送站内信结束:{}", new Date());
    }


    public SendNotDealInmailTimeDTO sendNotDealInmailTime() {
        log.error("sendNotDealInmail.未成交发送站内信发送消息:{}", new Date());

        String hhmm = DateTimeUtil.formatDateHHMMString(new Date());
        SendNotDealInmailTimeEnum sendNotDealInmailTimeEnum = SendNotDealInmailTimeEnum.getSendTime(hhmm);
        if (null == sendNotDealInmailTimeEnum) {
            return null;
        }

        String startTime = "";
        String endTime = "";

        String time = DateTimeUtil.formatDateString();
        if (sendNotDealInmailTimeEnum == SendNotDealInmailTimeEnum.TIME1) {
            TradeDayEntity tradeDayByDayAgo = tradeDayFacade.getTradeDayByDayAgo(DateTimeUtil.formatDateString());
            startTime = tradeDayByDayAgo.getDayValue() + sendNotDealInmailTimeEnum.getStartTime();
        } else {
            startTime = time + sendNotDealInmailTimeEnum.getStartTime();
        }
        endTime = time + sendNotDealInmailTimeEnum.getEndTime();
        SendNotDealInmailTimeDTO sendNotDealInmailTimeDTO = new SendNotDealInmailTimeDTO();
        sendNotDealInmailTimeDTO
                .setEndTime(endTime)
                .setStartTime(startTime);
        return sendNotDealInmailTimeDTO;
    }


    /**
     * 根据id撤回成交单
     *
     * @param applyContraryDTO
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean priceDealContrary(ApplyContraryDTO applyContraryDTO) {
        //根据成交单id查询出成交单
        List<PriceAllocateEntity> priceAllocateEntities = priceAllocateService.getAllocateByPriceApplyIdStatus(applyContraryDTO.getPriceApplyId());
        if (!priceAllocateEntities.isEmpty()) {
            return priceAllocateService.priceAllocateContrary(applyContraryDTO);
        } else {
            return priceDealCancel(applyContraryDTO);
        }
    }

    @Override
    public boolean priceDealCancel(ApplyContraryDTO applyContraryDTO) {
        List<PriceDealDetailEntity> priceDealDetailEntities = priceDealDetailDao.queryPriceDealDetailByPriceApplyId(applyContraryDTO.getPriceApplyId());
        if (priceDealDetailEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.PRICE_DEAL_NOT_EXIST);
        }

        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(applyContraryDTO.getPriceApplyId());
        if (priceApplyEntity.getDealContraryNum() > 2) {
            throw new BusinessException(ResultCodeEnum.APPLY_DEAL_CONTRARY_NUM);
        }

        BigDecimal priceNum = BigDecimal.ZERO;

        for (PriceDealDetailEntity priceDealDetail : priceDealDetailEntities) {
            //反点价是一口价合同进行操作,不用可操作数量校验
            if (PriceStatusEnum.NOT_TRANSACTION.getValue() == priceDealDetail.getStatus()) {
                if (PriceTypeEnum.PRICING.getValue() == priceDealDetail.getType()) {
                    //查询点价量
                    ContractFuturesDTO contractFuturesDTO = setContractFuturesDTO(priceDealDetail);

                    BigDecimal mayPriceNum = iFuturesDomainService.mayPriceNum(contractFuturesDTO).getMayPriceNum();

                    priceNum = mayPriceNum.subtract(priceDealDetail.getNotDealNum());
                } else if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceDealDetail.getType()) {
                    //查询转月量
                    ContractFuturesDTO contractFuturesDTO = setContractFuturesDTO(priceDealDetail);

                    BigDecimal mayTransferNum = iFuturesDomainService.mayTransferNum(contractFuturesDTO).getMayTransferNum();

                    priceNum = mayTransferNum.subtract(priceDealDetail.getNotDealNum());
                } else if (PriceTypeEnum.REVERSE_PRICING.getValue() == priceDealDetail.getType()) {
                    //反点价数量校验,合同可操作量减未成交量
                    //BUGFIX case-1002581 定价单含税单价错误bug修复衍生出的反点价成交校验合同状态 Author:Wan 2824-05-10 Start
                    ContractEntity contractEntity = contractFacade.getBasicContractById(priceApplyEntity.getContractId());
                    //BUGFIX case-1002581 定价单含税单价错误bug修复衍生出的反点价成交校验合同状态 Author:Wan 2824-05-10 End
                    BigDecimal contractNum = contractEntity.getContractNum();
                    priceNum = contractNum.subtract(priceDealDetail.getNotDealNum());
                }

                if (priceNum.compareTo(BigDecimal.ZERO) < 0) {
                    throw new BusinessException(ResultCodeEnum.NUMBER_CANNOT_CONTRARY);
                }

            }
            priceDealDetail.setStatus(PriceStatusEnum.CONTRARY.getValue());
            priceDealDetailDao.updateById(priceDealDetail);
        }
        //根据申请单id撤回申请单
        iPriceApplyService.priceApplyWithdraw(applyContraryDTO);
        return true;
    }

    //校验申请单是否能撤回
    private Integer contractSignStatus(PriceAllocateEntity priceAllocateEntity) {
        //查询合同协议状态
        Integer TTid = 0;
        if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()
                || PriceTypeEnum.REVERSE_PRICING.getValue() == priceAllocateEntity.getPriceApplyType()) {
            //转月,反点价协议状态
            if (ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntity.getSalesType()) {
                List<TTTranferEntity> ttTranferEntity = ttTranferFacade.selectTTTranferByPriceAllocateId(priceAllocateEntity.getId());
                if (!ttTranferEntity.isEmpty()) {
                    TTid = ttTranferEntity.get(0).getTtId();
                }
            } else {
                List<TTTranferEntity> ttTranferEntity = ttTranferFacade.getTTTranferByPriceApplyId(priceAllocateEntity.getPriceApplyId());
                if (!ttTranferEntity.isEmpty()) {
                    TTid = ttTranferEntity.get(0).getTtId();
                }
            }
        } else {
            //定价协议状态
            if (ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntity.getSalesType()) {
                TTPriceEntity ttPriceEntity = contractFacade.getTTPriceByAllocateId(priceAllocateEntity.getId());
                TTid = null == ttPriceEntity ? 0 : ttPriceEntity.getTtId();
            } else {
                List<TTPriceEntity> ttPriceEntity = contractFacade.getTTPriceByApplyId(priceAllocateEntity.getPriceApplyId());
                if (!ttPriceEntity.isEmpty()) {
                    TTid = ttPriceEntity.get(0).getTtId();
                }
            }
        }
        ContractSignEntity contractSignEntity = contractSignFacade.getContractSignDetailByTtId(TTid);

        return null == contractSignEntity ? ContractSignStatusEnum.PROCESSING.getValue() : contractSignEntity.getStatus();
    }

    private ContractFuturesDTO setContractFuturesDTO(PriceDealDetailEntity priceDealDetail) {
        return new ContractFuturesDTO()
                .setCustomerId(priceDealDetail.getCustomerId().toString())
                .setApplyId(priceDealDetail.getPriceApplyId().toString())
                .setDomainCode(priceDealDetail.getDominantCode())
                .setCompanyId(priceDealDetail.getCompanyId())
                .setGoodsCategoryId(priceDealDetail.getCategoryId());
    }
}
