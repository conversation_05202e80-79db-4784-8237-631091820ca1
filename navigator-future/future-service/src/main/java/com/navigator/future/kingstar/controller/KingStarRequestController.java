package com.navigator.future.kingstar.controller;

import com.navigator.future.kingstar.dao.KingStarSyncRequestDao;
import com.navigator.future.kingstar.service.IKingStarSyncService;
import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import com.navigator.future.pojo.entity.kingstar.KingStarSyncRequestEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/kingstar")
public class KingStarRequestController {
    @Resource
    private IKingStarSyncService kingStarSyncService;
    @Resource
    private KingStarSyncRequestDao kingStarSyncRequestDao;
    @Resource
    private RestTemplate insecureRestTemplate;

    /**
     * KingStar 获取token
     */
    @GetMapping("/getToken")
    public KingStarResponseDTO getToken(@RequestParam("userCode") String userCode,
                                        @RequestParam("forceRefresh") boolean forceRefresh) {
        String token = kingStarSyncService.getTokenByUserCode(userCode, forceRefresh);
        return KingStarResponseDTO.success(token);
    }


    /**
     * 根据requestId重新同步KingStar数据
     *
     * @param requestId 请求ID
     * @return KingStarResponseDTO
     */
    @GetMapping("/reSyncByRequestId")
    public KingStarResponseDTO reSyncByRequestId(@RequestParam Integer requestId) {
        try {
            kingStarSyncService.reSyncByRequestId(requestId);
            return KingStarResponseDTO.success("同步成功");
        } catch (Exception e) {
            log.error("重新同步失败", e);
            return KingStarResponseDTO.failure("重新同步失败: " + e.getMessage());
        }
    }

    @GetMapping("/testEsb")
    public String test(@RequestParam Integer requestId) {
        KingStarSyncRequestEntity syncRequest = kingStarSyncRequestDao.getById(requestId);
        if (syncRequest == null) {
            log.warn("请求记录不存在，requestId: {}", requestId);
            return "请求记录不存在";
        }

        String token = kingStarSyncService.getTokenByUserCode("admin", false);
        log.info("【KingStar】成功获取 Token: {}", token);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("token", token);
        headers.set("x-Gateway-APIKey", "a0ebe09d-64e5-4ba0-aec2-1d38bea186c1");

        HttpEntity<String> request = new HttpEntity<>(syncRequest.getRequestInfo(), headers);

        log.info("请求地址: {}", syncRequest.getRequestUrl());
        log.info("请求体: {}", syncRequest.getRequestInfo());
        log.info("请求头: {}", headers);

        try {
            ResponseEntity<String> response = insecureRestTemplate.postForEntity(
                    syncRequest.getRequestUrl(),
                    request,
                    String.class
            );
            log.info("响应状态: {}", response.getStatusCode());
            log.info("响应体: {}", response.getBody());
            return response.getBody();
        } catch (Exception e) {
            log.error("请求异常，requestId: {}, error: {}", requestId, e.getMessage(), e);
            return "请求异常: " + e.getMessage();
        }
    }
}
