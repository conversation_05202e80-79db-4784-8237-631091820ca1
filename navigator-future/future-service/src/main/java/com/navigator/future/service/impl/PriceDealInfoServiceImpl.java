package com.navigator.future.service.impl;

import com.navigator.future.dao.PriceDealInfoDao;
import com.navigator.future.pojo.entity.PriceDealInfoEntity;
import com.navigator.future.service.IPriceDealInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 17:54
 */
@Service
@Slf4j
public class PriceDealInfoServiceImpl implements IPriceDealInfoService {

    @Resource
    private PriceDealInfoDao priceDealInfoDao;

    @Override
    public List<PriceDealInfoEntity> priceDealInfoByPriceApplyId(Integer priceApplyId) {
        return priceDealInfoDao.queryPriceDealInfo(priceApplyId);
    }
}
