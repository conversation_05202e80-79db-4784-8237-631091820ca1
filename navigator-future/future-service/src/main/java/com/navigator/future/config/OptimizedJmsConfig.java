//package com.navigator.future.config;
//
//import com.azure.spring.autoconfigure.jms.AzureServiceBusJMSProperties;
//import com.azure.spring.autoconfigure.jms.ConnectionStringResolver;
//import com.azure.spring.autoconfigure.jms.ServiceBusKey;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.qpid.jms.JmsConnectionFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.jms.annotation.EnableJms;
//import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
//import org.springframework.jms.config.JmsListenerContainerFactory;
//import org.springframework.jms.connection.CachingConnectionFactory;
//
//import javax.jms.ConnectionFactory;
//import javax.jms.Session;
//
///**
// * 优化的JMS配置 - 解决Future服务重启问题
// *
// * 主要优化：
// * 1. 统一JMS配置，避免Bean冲突
// * 2. 简化连接参数，减少资源消耗
// * 3. 优化错误处理，防止容器重启
// * 4. 保守的并发设置，避免资源竞争
// *
// * <AUTHOR>
// * @date 2025-01-24
// */
//@Slf4j
//@Configuration
//@EnableJms
//@ConditionalOnProperty(name = "future.jms.optimized.enabled", havingValue = "true", matchIfMissing = true)
//public class OptimizedJmsConfig {
//
//    @Value("${future.servicebus.message.processing-timeout:30000}")
//    private long messageProcessingTimeout;
//
//    @Value("${future.servicebus.recovery-interval:15000}")
//    private long recoveryInterval;
//
//    /**
//     * 创建优化的JMS连接工厂
//     * 简化配置，提高稳定性
//     */
//    @Bean
//    @Primary
//    public ConnectionFactory optimizedJmsConnectionFactory(AzureServiceBusJMSProperties busJMSProperties) {
//        final String connectionString = busJMSProperties.getConnectionString();
//        final String clientId = busJMSProperties.getTopicClientId();
//        final int idleTimeout = busJMSProperties.getIdleTimeout();
//
//        final ServiceBusKey serviceBusKey = ConnectionStringResolver.getServiceBusKey(connectionString);
//
//        // 简化连接URI参数，只保留必要配置
//        final String remoteUri = String.format(
//            "amqps://%s?amqp.idleTimeout=%d&amqp.traceFrames=false&amqp.maxFrameSize=1048576",
//            serviceBusKey.getHost(),
//            idleTimeout
//        );
//
//        try {
//            // 创建QPID JMS连接工厂
//            JmsConnectionFactory jmsConnectionFactory = new JmsConnectionFactory(
//                serviceBusKey.getSharedAccessKeyName(),
//                serviceBusKey.getSharedAccessKey(),
//                remoteUri
//            );
//            jmsConnectionFactory.setClientID(clientId + "-optimized");
//
//            // 使用缓存连接工厂，但配置保守参数
//            CachingConnectionFactory cachingConnectionFactory = new CachingConnectionFactory(jmsConnectionFactory);
//
//            // 保守的缓存配置，确保稳定性
//            cachingConnectionFactory.setCacheProducers(false);
//            cachingConnectionFactory.setCacheConsumers(false);
//            cachingConnectionFactory.setSessionCacheSize(1);
//            cachingConnectionFactory.setReconnectOnException(true);
//
//            log.info("优化JMS连接工厂初始化完成，Host: {}, 空闲超时: {}ms",
//                    serviceBusKey.getHost(), idleTimeout);
//            return cachingConnectionFactory;
//
//        } catch (Exception e) {
//            log.error("创建JMS连接工厂失败", e);
//            throw new RuntimeException("JMS连接工厂初始化失败", e);
//        }
//    }
//
//    /**
//     * 创建优化的JMS监听器容器工厂
//     * 防止重启和资源泄漏
//     */
//    @Bean
//    @Primary
//    public JmsListenerContainerFactory<?> optimizedJmsListenerContainerFactory(ConnectionFactory connectionFactory) {
//        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
//        factory.setConnectionFactory(connectionFactory);
//
//        // 消息确认模式 - 使用客户端确认
//        factory.setSessionAcknowledgeMode(Session.AUTO_ACKNOWLEDGE);
//
//        // 禁用事务以避免死锁问题
//        factory.setSessionTransacted(false);
//
//        // 保守的并发设置，避免资源竞争
//        factory.setConcurrency("1-1"); // 单线程处理，确保稳定性
//
//        // 设置恢复间隔 - 连接断开后的重连间隔
//        factory.setRecoveryInterval(recoveryInterval);
//
//        // 设置接收超时
//        factory.setReceiveTimeout(messageProcessingTimeout);
//
//        // 优化的错误处理器 - 关键：不抛出异常
//        factory.setErrorHandler(throwable -> {
//            log.error("JMS消息处理异常，但不会导致容器重启: {}", throwable.getMessage());
//
//            // 记录详细的异常信息用于问题排查
//            if (throwable.getCause() != null) {
//                log.error("异常根因: {}", throwable.getCause().getMessage());
//            }
//
//            // 关键：不抛出异常，避免导致容器重启
//            // 让Spring JMS的内置重试机制处理
//        });
//
//        // 设置任务执行器 - 使用默认的SimpleAsyncTaskExecutor
//        factory.setTaskExecutor(null);
//
//        log.info("优化JMS监听器容器工厂初始化完成，并发数: 1-1, 恢复间隔: {}ms", recoveryInterval);
//
//        return factory;
//    }
//
//    /**
//     * 提供连接字符串Bean，供延迟消息发送器使用
//     */
//    @Bean("serviceBusConnectionString")
//    public String serviceBusConnectionString(AzureServiceBusJMSProperties busJMSProperties) {
//        return busJMSProperties.getConnectionString();
//    }
//}
