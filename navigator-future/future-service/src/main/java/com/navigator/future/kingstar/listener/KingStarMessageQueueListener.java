package com.navigator.future.kingstar.listener;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.enums.KingStarBusinessTypeEnum;
import com.navigator.future.enums.KingStarInterfaceStatusEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.kingstar.dao.KingStarSyncRequestDao;
import com.navigator.future.kingstar.service.KingStarInstructionService;
import com.navigator.future.pojo.dto.kingstar.KingStarMessageRequestDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.kingstar.KingStarSyncRequestEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * KingStar 消息队列监听
 * </p>
 *
 * <AUTHOR>
 * @date 2025-7-14
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class KingStarMessageQueueListener {
    @Value("${messageQueue.kingstar.syncQueueName}")
    private String syncQueueName;
    @Value("${messageQueue.kingstar.syncDeadLetterName}")
    private String syncDeadLetterName;

    private final KingStarInstructionService instructionService;
    private final KingStarSyncRequestDao kingStarSyncRequestDao;
    private final PriceApplyDao priceApplyDao;
    private final Gson gson = new Gson();

    /**
     * 监听KingStar同步队列，接收并处理同步消息
     *
     * @param message ASCII编码的消息字符串
     */
    @JmsListener(destination = "${messageQueue.kingstar.syncQueueName}", containerFactory = "futureJmsListenerContainerFactory")
    public void receiveSyncMessage(String message) {
        try {
            // 解析消息为JSON对象
            JSONObject kingStarMessage = parseKingStarMessage(message);
            log.info("队列名：{}，解析后内容：{}", syncQueueName, kingStarMessage);

            // 转换为DTO对象
            KingStarMessageRequestDTO requestDTO = gson.fromJson(String.valueOf(kingStarMessage), KingStarMessageRequestDTO.class);

            if (requestDTO == null) {
                log.error("指令数据解析失败，消息内容: {}", kingStarMessage);
                return;
            }

            // 处理同步消息
            processSyncMessage(requestDTO);

        } catch (Exception e) {
            log.error("处理KingStar同步消息失败: message={}, error={}", message, e.getMessage(), e);
        }
    }


    /**
     * 处理同步消息，校验数据并分发业务处理
     *
     * @param requestDTO 消息请求DTO
     */
    private void processSyncMessage(KingStarMessageRequestDTO requestDTO) {
        log.info("开始处理监听消息: requestId={}, instructId={}, requestDTO={}",
                requestDTO.getRequestId(), requestDTO.getInstructId(), requestDTO.getRequestDTO());

        // 查询同步请求记录，添加重试机制防止事务未提交问题
        KingStarSyncRequestEntity syncRequest = findSyncRequestWithRetry(requestDTO.getRequestId());
        if (syncRequest == null) {
            log.error("重试后仍未找到同步请求记录，requestId: {}", requestDTO.getRequestId());
            return;
        }

        // 查询申请单
        PriceApplyEntity priceApply = priceApplyDao.getPriceApplyByCode(requestDTO.getInstructId());
        if (priceApply == null) {
            log.error("申请单不存在，申请单号: {}", requestDTO.getInstructId());
            return;
        }

        // 获取业务类型枚举
        KingStarBusinessTypeEnum typeEnum = KingStarBusinessTypeEnum.getByValue(requestDTO.getRequestDTO().getType());
        if (typeEnum == null) {
            log.error("未知的业务类型: {}", requestDTO.getRequestDTO().getType());
            return;
        }

        // 分发业务处理
        handleBusinessAction(typeEnum, requestDTO, syncRequest, priceApply);

        // 最终更新数据库
        priceApplyDao.updateById(priceApply);
        kingStarSyncRequestDao.updateById(syncRequest);
    }

    /**
     * 根据业务类型分发处理并更新同步请求和申请单状态
     *
     * @param typeEnum    业务类型枚举
     * @param requestDTO  消息请求DTO
     * @param syncRequest 同步请求实体
     * @param priceApply  申请单实体
     */
    private void handleBusinessAction(KingStarBusinessTypeEnum typeEnum,
                                      KingStarMessageRequestDTO requestDTO,
                                      KingStarSyncRequestEntity syncRequest,
                                      PriceApplyEntity priceApply) {

        String actionDesc = typeEnum.getDescription();
        String instructId = requestDTO.getInstructId();
        log.info("调用KingStar接口: action={}, instructId={}", actionDesc, instructId);

        KingStarResponseDTO responseDTO = null;
        String responseStr = "";
        String resultMessage = "";

        try {
            // 分发具体业务请求
            responseDTO = dispatchRequest(typeEnum, requestDTO, syncRequest);
            resultMessage = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
            responseStr = JSONUtil.toJsonStr(responseDTO);

            log.info("KingStar接口调用成功: action={}, instructId={}", actionDesc, instructId);
        } catch (Exception e) {
            resultMessage = "接口调用失败";
            responseStr = "KingStar接口调用失败: " + e.getMessage();
            log.error("KingStar接口调用失败: instructId={}, error={}", instructId, e.getMessage(), e);
        }

        // 更新申请单状态
        updatePriceApply(typeEnum, priceApply, resultMessage);

        // 更新同步请求状态和响应信息
        syncRequest
                .setSyncStatus(resultMessage)
                .setSyncTime(DateUtil.format(new Date(), "yyyyMMddHHmmss"))
                .setResponseInfo(responseStr);
    }

    /**
     * 根据业务类型分发到不同的KingStar接口
     *
     * @param typeEnum    业务类型枚举
     * @param requestDTO  消息请求DTO
     * @param syncRequest 同步请求实体
     * @return KingStarResponseDTO 响应结果
     */
    private KingStarResponseDTO dispatchRequest(KingStarBusinessTypeEnum typeEnum,
                                                KingStarMessageRequestDTO requestDTO,
                                                KingStarSyncRequestEntity syncRequest) {
        switch (typeEnum) {
            case PRICING:
            case PRICING_TRANSFER:
                // 调用定价相关接口
                return instructionService.applyPricing(
                        requestDTO.getUserCode(),
                        requestDTO.getRequestDTO(),
                        syncRequest);
            case REVERSE_PRICING:
            case REVERSE_PRICING_TRANSFER:
                // 调用反向定价接口
                return instructionService.applyReversePricing(
                        requestDTO.getUserCode(),
                        requestDTO.getRequestDTO(),
                        syncRequest);
            case MODIFY:
                // 调用修改接口
                return instructionService.applyModify(
                        requestDTO.getUserCode(),
                        requestDTO.getRequestDTO(),
                        syncRequest);
            case CANCEL:
                // 调用撤销接口
                return instructionService.applyCancel(
                        requestDTO.getUserCode(),
                        requestDTO.getRequestDTO(),
                        syncRequest);
            default:
                throw new IllegalArgumentException("不支持的业务类型: " + typeEnum);
        }
    }

    /**
     * 根据业务类型和结果信息更新申请单状态
     *
     * @param typeEnum      业务类型枚举
     * @param priceApply    申请单实体
     * @param resultMessage 结果信息
     */
    private void updatePriceApply(KingStarBusinessTypeEnum typeEnum,
                                  PriceApplyEntity priceApply,
                                  String resultMessage) {
        Date now = new Date();

        switch (typeEnum) {
            case PRICING:
            case PRICING_TRANSFER:
            case REVERSE_PRICING:
            case REVERSE_PRICING_TRANSFER:
                // 定价相关业务，更新待处理和接口状态
                priceApply
                        .setStatus("成功".equals(resultMessage) ? PriceStatusEnum.WAIT_PENDING.getValue() : PriceStatusEnum.PRICING.getValue())
                        .setInterfaceStatus("成功".equals(resultMessage) ? KingStarInterfaceStatusEnum.PENDING.getValue() : KingStarInterfaceStatusEnum.NOT_CALL_INTERFACE.getValue())
                        .setPendingResult(resultMessage)
                        .setUpdatedAt(now);
                break;
            case MODIFY:
                // 修改业务，更新接口状态和变更结果
                priceApply
                        .setInterfaceStatus(KingStarInterfaceStatusEnum.MODIFYING.getValue())
                        .setChangeResult(resultMessage)
                        .setUpdatedAt(now);
                break;
            case CANCEL:
                // 撤销业务，更新接口状态和变更结果
                priceApply
                        .setInterfaceStatus(KingStarInterfaceStatusEnum.CANCELING.getValue())
                        .setChangeResult(resultMessage)
                        .setUpdatedAt(now);
                break;
            default:
                // 其他类型不做处理
        }
    }

    /**
     * 监听Kingstar死信队列，记录死信消息
     *
     * @param message 死信队列消息
     */
    @JmsListener(destination = "${messageQueue.kingstar.syncDeadLetterName}", containerFactory = "futureJmsListenerContainerFactory")
    public void receiveDLQueueMessage(String message) {
        log.info("===============[{}] DLQ Message received : {}===============", syncDeadLetterName, parseKingStarMessage(message));
    }

    /**
     * 带重试机制查找同步请求记录，防止事务未提交问题
     *
     * @param requestId 请求ID
     * @return 同步请求实体，如果重试后仍未找到则返回null
     */
    private KingStarSyncRequestEntity findSyncRequestWithRetry(Integer requestId) {
        int maxRetries = 3;
        int retryDelayMs = 500; // 500毫秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            KingStarSyncRequestEntity syncRequest = kingStarSyncRequestDao.getById(requestId);
            if (syncRequest != null) {
                if (attempt > 1) {
                    log.info("第{}次重试成功找到同步请求记录，requestId: {}", attempt, requestId);
                }
                return syncRequest;
            }

            if (attempt < maxRetries) {
                log.info("第{}次查询同步请求记录失败，{}ms后重试，requestId: {}", attempt, retryDelayMs, requestId);
                try {
                    Thread.sleep(retryDelayMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("重试等待被中断", e);
                    break;
                }
            }
        }

        return null;
    }

    /**
     * 将 ASCII 编码字符串解析为 JSON 对象
     * 示例输入："123,34,97,99,116,105,111,110,34,58,34,116,101,115,116,34,..."
     *
     * @param asciiString ASCII 数字字符串（逗号分隔）
     * @return JSONObject 解析结果
     * @throws IllegalArgumentException 如果输入格式非法或无法解析
     */
    public static JSONObject parseKingStarMessage(String asciiString) {
        if (StrUtil.isBlank(asciiString)) {
            throw new IllegalArgumentException("ASCII 字符串不能为空");
        }

        try {
            List<String> parts = StrUtil.split(asciiString, ',');
            byte[] bytes = new byte[parts.size()];
            for (int i = 0; i < parts.size(); i++) {
                bytes[i] = (byte) Integer.parseInt(parts.get(i).trim());
            }

            String jsonStr = new String(bytes, StandardCharsets.UTF_8);
            return JSONUtil.parseObj(jsonStr);

        } catch (Exception e) {
            throw new IllegalArgumentException("解析 ASCII 字符串失败", e);
        }
    }
}
