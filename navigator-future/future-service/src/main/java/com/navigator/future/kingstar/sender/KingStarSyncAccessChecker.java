package com.navigator.future.kingstar.sender;

import com.navigator.future.kingstar.config.KingStarSyncProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 同步访问检查器 - 优化版本
 * 支持高效规则匹配和跨天时间计算
 *
 * <AUTHOR>
 * @since 2025-7-15
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class KingStarSyncAccessChecker {

    private final KingStarSyncProperties syncProperties;

    // 使用高效数据结构缓存规则，避免重复解析
    private final Map<String, List<KingStarSyncProperties.SyncControlRule>> ruleCache = new ConcurrentHashMap<>();
    private volatile long lastCacheTime = 0;
    private static final long CACHE_REFRESH_INTERVAL = 60000; // 1分钟刷新缓存

    /**
     * 检查是否需要延迟发送，如果需要则返回延迟到的时间
     *
     * @param exchange     交易所
     * @param businessType 业务类型
     * @param now          当前时间
     * @return 延迟到的时间，如果不需要延迟则返回空
     */
    public Optional<LocalDateTime> getDelayUntil(String exchange, String businessType, LocalDateTime now) {
        log.debug("检查延迟规则: exchange={}, businessType={}, now={}", exchange, businessType, now);

        List<KingStarSyncProperties.SyncControlRule> rules = getExchangeRules(exchange);
        if (CollectionUtils.isEmpty(rules)) {
            log.debug("未找到交易所 {} 的延迟规则", exchange);
            return Optional.empty();
        }

        LocalTime currentTime = now.toLocalTime();

        for (KingStarSyncProperties.SyncControlRule rule : rules) {
            Optional<LocalDateTime> delayTime = checkRuleMatch(rule, businessType, now, currentTime);
            if (delayTime.isPresent()) {
                log.debug("匹配到延迟规则: exchange={}, businessType={}, delayUntil={}",
                        exchange, businessType, delayTime.get());
                return delayTime;
            }
        }

        log.debug("未匹配到延迟规则，立即发送: exchange={}, businessType={}", exchange, businessType);
        return Optional.empty();
    }

    /**
     * 获取指定交易所的规则列表（带缓存）
     */
    private List<KingStarSyncProperties.SyncControlRule> getExchangeRules(String exchange) {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否需要刷新
        if (currentTime - lastCacheTime > CACHE_REFRESH_INTERVAL) {
            refreshRuleCache();
            lastCacheTime = currentTime;
        }

        return ruleCache.getOrDefault(exchange.toLowerCase(), Collections.emptyList());
    }

    /**
     * 刷新规则缓存
     */
    private void refreshRuleCache() {
        ruleCache.clear();

        if (syncProperties.getSyncControlRules() == null) {
            log.warn("同步控制规则配置为空");
            return;
        }

        for (KingStarSyncProperties.SyncControlRule rule : syncProperties.getSyncControlRules()) {
            if (rule.getExchange() != null) {
                String exchangeKey = rule.getExchange().toLowerCase();
                ruleCache.computeIfAbsent(exchangeKey, k -> new ArrayList<>()).add(rule);
            }
        }

        log.debug("规则缓存已刷新，共加载 {} 个交易所的规则", ruleCache.size());
    }

    /**
     * 检查单个规则是否匹配
     */
    private Optional<LocalDateTime> checkRuleMatch(
            KingStarSyncProperties.SyncControlRule rule,
            String businessType,
            LocalDateTime now,
            LocalTime currentTime) {

        try {
            LocalTime startTime = LocalTime.parse(rule.getStartTime());
            LocalTime endTime = LocalTime.parse(rule.getEndTime());

            // 检查当前时间是否在规则时间范围内
            if (!isTimeInRange(currentTime, startTime, endTime)) {
                return Optional.empty();
            }

            // 检查业务类型匹配
            if (CollectionUtils.isEmpty(rule.getBusinessTypes())) {
                return Optional.empty();
            }

            for (KingStarSyncProperties.BusinessTypeRule bizRule : rule.getBusinessTypes()) {
                if (isBusinessTypeMatch(bizRule, businessType)) {
                    return calculateDelayTime(bizRule, now);
                }
            }

        } catch (Exception e) {
            log.error("解析延迟规则失败: rule={}, error={}", rule, e.getMessage(), e);
        }

        return Optional.empty();
    }

    /**
     * 检查时间是否在范围内（支持跨天）
     */
    private boolean isTimeInRange(LocalTime current, LocalTime start, LocalTime end) {
        if (start.equals(end)) {
            return false; // 开始时间等于结束时间，无效范围
        }

        if (start.isBefore(end)) {
            // 同一天内的时间范围
            return !current.isBefore(start) && !current.isAfter(end);
        } else {
            // 跨天的时间范围（如 22:00 - 06:00）
            return !current.isBefore(start) || !current.isAfter(end);
        }
    }

    /**
     * 检查业务类型是否匹配
     */
    private boolean isBusinessTypeMatch(KingStarSyncProperties.BusinessTypeRule bizRule, String businessType) {
        if (bizRule.getType() == null) {
            return false;
        }

        // 支持 "全部业务" 或精确匹配
        return "全部业务".equals(bizRule.getType()) ||
                bizRule.getType().equalsIgnoreCase(businessType);
    }

    /**
     * 计算延迟时间
     */
    private Optional<LocalDateTime> calculateDelayTime(
            KingStarSyncProperties.BusinessTypeRule bizRule,
            LocalDateTime now) {

        if (bizRule.isSyncEnable()) {
            return Optional.empty(); // 启用同步，不需要延迟
        }

        if (bizRule.getSyncReleaseTime() == null) {
            log.warn("业务类型 {} 配置了禁用同步但未设置释放时间", bizRule.getType());
            return Optional.empty();
        }

        try {
            LocalTime releaseTime = LocalTime.parse(bizRule.getSyncReleaseTime());
            LocalDateTime delayUntil = now.toLocalDate().atTime(releaseTime);

            // 如果释放时间已过，延迟到明天的释放时间
            if (delayUntil.isBefore(now) || delayUntil.isEqual(now)) {
                delayUntil = delayUntil.plusDays(1);
            }

            return Optional.of(delayUntil);

        } catch (Exception e) {
            log.error("解析释放时间失败: syncReleaseTime={}, error={}",
                    bizRule.getSyncReleaseTime(), e.getMessage());
            return Optional.empty();
        }
    }
}
