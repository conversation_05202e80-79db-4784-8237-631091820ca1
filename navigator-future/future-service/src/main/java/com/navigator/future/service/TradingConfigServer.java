package com.navigator.future.service;

import com.navigator.future.pojo.entity.TradingConfigEntity;
import com.navigator.future.pojo.vo.TradingConfigVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
public interface TradingConfigServer {
    /**
     * 查询所有
     *
     * @return
     */
    List<TradingConfigVO> queryTradingConfigList();

    /**
     * 根据期货代码查询
     *
     * @param getFutureCode
     * @return
     */
    TradingConfigVO getDomainTypeByCategoryCode(String getFutureCode);

    List<TradingConfigEntity> getDomainTypeEntityByCategoryCode(String getFutureCode);

    List<TradingConfigVO> getDomainTypeByCategory2(String category2);
}
