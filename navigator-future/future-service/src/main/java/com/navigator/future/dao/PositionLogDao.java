package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.mapper.PositionLogMapper;
import com.navigator.future.pojo.entity.PositionLogEntity;

import java.util.List;

@Dao
public class PositionLogDao extends BaseDaoImpl<PositionLogMapper, PositionLogEntity> {

    public List<PositionLogEntity> queryLog(Integer positionId) {
        return list(Wrappers.<PositionLogEntity>lambdaQuery()
                .eq(PositionLogEntity::getPositionId, positionId)
        );
    }
}
