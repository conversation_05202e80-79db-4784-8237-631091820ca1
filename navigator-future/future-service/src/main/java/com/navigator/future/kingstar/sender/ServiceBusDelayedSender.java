package com.navigator.future.kingstar.sender;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.gson.Gson;
import com.microsoft.azure.servicebus.Message;
import com.microsoft.azure.servicebus.QueueClient;
import com.microsoft.azure.servicebus.ReceiveMode;
import com.microsoft.azure.servicebus.primitives.ConnectionStringBuilder;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

/**
 * Azure Service Bus 延迟消息发送器
 *
 * <AUTHOR>
 * @date 2025-7-14
 */
@Slf4j
@Service
public class ServiceBusDelayedSender {

    @Value("${spring.jms.servicebus.connection-string}")
    private String connectionString;
    @Value("${messageQueue.kingstar.syncQueueName}")
    private String queueName;

    @Resource
    private RedisUtil redisUtil;

    private final Gson gson = new Gson();

    private static final String MESSAGE_KEY_PREFIX = "kingstar:message:";

    // 使用 Caffeine 自动清理客户端缓存，防止内存泄漏
    private final Cache<String, QueueClient> queueClientCache = Caffeine.newBuilder()
            .maximumSize(100)
            .expireAfterAccess(1, TimeUnit.HOURS)
            .removalListener((String queue, QueueClient client, com.github.benmanes.caffeine.cache.RemovalCause cause) -> {
                if (client != null) {
                    try {
                        client.close();
                        log.info("自动释放 QueueClient: {}, cause={}", queue, cause);
                    } catch (Exception e) {
                        log.warn("释放 QueueClient 异常: queue={}, error={}", queue, e.getMessage());
                    }
                }
            })
            .build();

    /**
     * 发送延迟消息（按秒）
     */
    public void sendDelayedMessage(Object messageData, String delayedMessageId, int delaySeconds) {
        LocalDateTime scheduledTime = LocalDateTime.now().plusSeconds(delaySeconds);
        sendDelayedMessage(messageData, delayedMessageId, scheduledTime);
    }

    /**
     * 发送延迟消息（指定时间）
     */
    public void sendDelayedMessage(Object messageData, String delayedMessageId, LocalDateTime scheduledTime) {
        try {
            QueueClient queueClient = getOrCreateClient(queueName);

            String messageBody = (messageData instanceof String)
                    ? (String) messageData
                    : gson.toJson(messageData);

            Message message = new Message(messageBody);
            Instant instant = scheduledTime.atZone(ZoneId.systemDefault()).toInstant();
            message.setScheduledEnqueueTimeUtc(instant);

            // 发送延迟消息并获取序列号
            long sequenceNumber = queueClient.scheduleMessage(message, instant);

            // messageUniqueId 不为空 缓存sequenceNumber 24小时
            if (StringUtil.isNotBlank(delayedMessageId)) {
                redisUtil.set(MESSAGE_KEY_PREFIX + delayedMessageId, sequenceNumber, 86400L);
                log.info("延迟消息发送成功: queue={}, messageUniqueId={}, sequenceNumber={}, scheduledTime={}",
                        queueName, delayedMessageId, sequenceNumber, scheduledTime);
            } else {
                log.info("延迟消息发送成功: queue={}, sequenceNumber={}, scheduledTime={}",
                        queueName, sequenceNumber, scheduledTime);
            }

        } catch (Exception e) {
            log.error("发送延迟消息失败: queue={}, error={}", queueName, e.getMessage(), e);
            throw new RuntimeException("发送延迟消息失败", e);
        }
    }

    /**
     * 获取或创建 QueueClient（线程安全 + 有缓存控制）
     */
    private QueueClient getOrCreateClient(String queueName) throws Exception {
        QueueClient existing = queueClientCache.getIfPresent(queueName);
        if (existing != null) {
            return existing;
        }

        synchronized (this) {
            // 双重检查
            existing = queueClientCache.getIfPresent(queueName);
            if (existing != null) {
                return existing;
            }

            try {
                ConnectionStringBuilder builder = new ConnectionStringBuilder(connectionString, queueName);
                builder.setOperationTimeout(Duration.ofSeconds(30));

                QueueClient client = new QueueClient(builder, ReceiveMode.PEEKLOCK);
                queueClientCache.put(queueName, client);
                log.info("创建新的 QueueClient: {}", queueName);
                return client;
            } catch (Exception e) {
                log.error("创建 QueueClient 失败: queue={}, error={}", queueName, e.getMessage(), e);
                throw e;
            }
        }
    }

    /**
     * 应用关闭时清理所有 QueueClient 实例
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始释放所有 QueueClient 连接");
        queueClientCache.asMap().forEach((queue, client) -> {
            try {
                client.close();
                log.info("成功关闭 QueueClient: {}", queue);
            } catch (Exception e) {
                log.warn("关闭 QueueClient 失败: queue={}, error={}", queue, e.getMessage());
            }
        });
    }

    /**
     * 取消延迟消息
     */
    public boolean cancelDelayedMessage(String instructId) {
        try {
            Object obj = redisUtil.get(MESSAGE_KEY_PREFIX + instructId);
            Long sequenceNumber = obj != null ? ((Number) obj).longValue() : null;

            if (sequenceNumber == null) {
                log.error("未找到消息序列号，无法取消: instructId={}", instructId);
                return false;
            }

            QueueClient queueClient = getOrCreateClient(queueName);
            queueClient.cancelScheduledMessage(sequenceNumber);

            // 从Redis中移除
            redisUtil.del(MESSAGE_KEY_PREFIX + instructId);

            log.info("成功取消延迟消息: instructId={}, sequenceNumber={}", instructId, sequenceNumber);
            return true;

        } catch (Exception e) {
            log.error("取消延迟消息失败: instructId={}, error={}", instructId, e.getMessage(), e);
            return false;
        }
    }
}
