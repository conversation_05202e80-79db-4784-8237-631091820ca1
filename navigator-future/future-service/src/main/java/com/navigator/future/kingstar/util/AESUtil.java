package com.navigator.future.kingstar.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.logging.log4j.util.Strings;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;


/**
 * AES加解密
 */
@Slf4j
public class AESUtil {

    /**
     * 算法名称/加密模式/数据填充方式
     */
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";

    /**
     * AES加解密默认密钥(16位，也可以用其它长度，那就用 New 方法 )
     */
    private static final String KEY = "KingStar";

    /**
     * AES
     */
    private static final String KeyGeneratorName = "AES";

    /**
     * 授权码有效时间
     */
    private static final Long VALID_TIME_MILLIS = 30 * 60 * 1000L;

    //获取授权码(30分钟内授权码登录有效)
    public static String getLicenseCode(String userCode) {
        //通过用户code获取登录授权码
        JSONObject json = new JSONObject();
        //用户登录code
        json.put("userCode", userCode);
        //授权码创建时间
        json.put("timestamp", "" + System.currentTimeMillis());
        //获取加密结果
        String licenseCode = encrypt(json.toJSONString());
        //如果为空,加密失败直接返回
        if (Strings.isEmpty(licenseCode)) return licenseCode;
        //处理http请求中+替换空格的问题
        licenseCode = licenseCode.replaceAll("\\+", "%2B");
        //返回处理之后的结果
        return licenseCode;
    }

    //解密授权码
    public static String getUserCode(String licenseCode) {
        //获取当前授权码
        JSONObject loginUser = decryptJson(licenseCode);
        //如果为空解密失败
        if (loginUser == null) return null;
        //获取当前有效时间
        String timestamp = loginUser.getString("timestamp");
        //不存在创建时间,直接返回
        if (Strings.isEmpty(timestamp)) return null;
        //授权码已经超过有效期
        if (VALID_TIME_MILLIS > 0) {
            //获取当前授权最大有效期
            BigDecimal maxValidTime = new BigDecimal(timestamp).add(new BigDecimal(VALID_TIME_MILLIS));
            //系统当前时间
            BigDecimal curSysTime = new BigDecimal(System.currentTimeMillis());
            //授权码已经超过有效期(两个机器的时间不允许间隔太久)
            if (maxValidTime.compareTo(curSysTime) < 0) {
                //无效直接返回
                log.error("当前授权码已经超过有效期,loginUser=[{}],最大有效期限=[{}],系统当前时间=[{}]", loginUser, maxValidTime, curSysTime);
                //返回结果
                return null;
            }
        }
        //返回当前有效用户
        return loginUser.getString("userCode");
    }

    /**
     * AES解密方法
     */
    private static String decrypt(String decryptStr) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(KeyGeneratorName);
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(KEY.getBytes());
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secretKey.getEncoded(), KeyGeneratorName));
            //采用base64算法进行转码，避免出现中文乱码
            byte[] encryptBytes = Base64.decodeBase64(decryptStr);
            byte[] decryptBytes = cipher.doFinal(encryptBytes);
            return new String(decryptBytes);
        } catch (Exception e) {
            log.error("decrypt({} , {})解密异常", decryptStr, KEY, e);
        }
        return null;
    }

    /**
     * AES解密方法
     */
    private static JSONObject decryptJson(String decryptStr) {
        try {
            //处理http请求中+替换空格的问题
            decryptStr = decryptStr.replaceAll("%2B", "\\+");
            //解密json
            String decryptValue = decrypt(decryptStr);
            if (Strings.isEmpty(decryptValue)) return null;
            return JSONObject.parseObject(decryptValue);
        } catch (Exception e) {
            log.error("decryptJson({} , {})解密异常", decryptStr, KEY, e);
        }
        return null;
    }

    /**
     * AES加密方法
     */
    private static String encrypt(String encryptStr) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(KeyGeneratorName);
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(KEY.getBytes());
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secretKey.getEncoded(), KeyGeneratorName));
            byte[] b = cipher.doFinal(encryptStr.getBytes(StandardCharsets.UTF_8));
            //采用base64算法进行转码，避免出现中文乱码
            return Base64.encodeBase64String(b);
        } catch (Exception e) {
            log.error("encrypt({} , {})加密异常", encryptStr, KEY, e);
        }
        return null;
    }

    //hPE0/R92aBTI1sCMRqlKB4Qr7u7IEUsCCLd%2BgC7xkGLM273TmGWoMv5HPRUN8Ag%2B4MFfIXiXo4R4gBi0sjVr7w==
    public static void main(String[] args) throws Exception {
        System.out.println("加密后：" + getLicenseCode("admin"));
        System.out.println("解密后：" + getUserCode(getLicenseCode("admin")));
    }
}

