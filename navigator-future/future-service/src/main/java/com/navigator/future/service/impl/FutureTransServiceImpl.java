package com.navigator.future.service.impl;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.pojo.dto.CustomerDomainTransDTO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.service.*;
import com.navigator.trade.facade.ContractEquityFacade;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Service
@Slf4j
public class FutureTransServiceImpl implements IFutureTransService {

    @Resource
    private ContractFacade contractFacade;
    @Resource
    private IPriceAllocateService priceAllocateService;
    @Resource
    private IPriceApplyService priceApplyService;
    @Resource
    private IPriceApplyLogService priceApplyLogService;
    @Resource
    private IPriceDealService iPriceDealService;
    @Resource
    private ContractEquityFacade contractEquityFacade;


    @Override
    public CustomerDomainTransDTO getCustomerDomainTransInfo(ContractFuturesDTO contractFuturesDTO) {

        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start


        //获取合同可点价量,可转月量
        CustomerDomainTransDTO customerDomainTransDTO = this.getCustomerDomainContractInfo(contractFuturesDTO, PriceTypeEnum.PRICING.getValue());
        if (ContractSalesTypeEnum.SALES.getValue() == contractFuturesDTO.getSalesType()) {
            //获取头寸数量
            getCustomerDomainApplyInfo(contractFuturesDTO, customerDomainTransDTO);
        }


        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
        return customerDomainTransDTO;
    }

    @Override
    public void saveFutureOrder() {
        //TODO NEO
    }

    /**
     * 合同中的量
     *
     * @param contractFuturesDTO
     * @return
     */
    private CustomerDomainTransDTO getCustomerDomainContractInfo(ContractFuturesDTO contractFuturesDTO, Integer type) {
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        //合合同可点价量
        BigDecimal pricedCount = BigDecimal.ZERO;
        //合同可转月量
        BigDecimal transferredCount = BigDecimal.ZERO;

        //查询可操作合同数量
        contractFuturesDTO.setContractType(Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()))
                .setPriceType(type);

        //根据客户 品种 合约 采销类型查询出客户的合同上的可操作量
        List<ContractEntity> contractEntities = contractFacade.queryContractsFuturesNum(contractFuturesDTO);

        CustomerDomainTransDTO customerDomainTransDTO = new CustomerDomainTransDTO();
        for (ContractEntity contractEntity : contractEntities) {
            //获取合同可操作量合同数量-以定价量
            BigDecimal validCount = contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum());
            //可操作量>0进行计算
            if (validCount.compareTo(BigDecimal.ZERO) > 0) {
                //合同定价量基差合同可操作量+基差暂定价可操作量
                pricedCount = pricedCount.add(validCount);
                //合同转月总量--合同类型为基差合同,合同转月次数>0
                //case-1003232 可结构化定价数量逻辑与PRD不符 Author: wan 2025-05-26 Start
                if ((ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType() && contractEntity.getAbleTransferTimes() > 0) ||
                        (ContractTypeEnum.JI_CHA.getValue() == contractEntity.getContractType() && PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus())) {
                    transferredCount = transferredCount.add(validCount);
                }
                //case-1003232 可结构化定价数量逻辑与PRD不符 Author: wan 2025-05-26 end
            }
        }
        customerDomainTransDTO
                .setTransferredCount(transferredCount)
                .setPricedCount(pricedCount)
                .setCanPriceCount(pricedCount)
                .setCanTransferCount(transferredCount)
        ;
        return customerDomainTransDTO;
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
    }

    /**
     * 申请单 分配单中的量
     *
     * @param contractFuturesDTO
     * @return
     */
    private CustomerDomainTransDTO getCustomerDomainApplyInfo(ContractFuturesDTO contractFuturesDTO, CustomerDomainTransDTO customerDomainTransDTO) {
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        //申请单总量
        BigDecimal applyCount = BigDecimal.ZERO;
        //成交单总量
        BigDecimal dealCount = BigDecimal.ZERO;
        //分配单总量
        BigDecimal allocateCount = BigDecimal.ZERO;
        //可定价量
        BigDecimal canPriceCount = customerDomainTransDTO.getCanPriceCount();
        //可转月量
        BigDecimal canTransferCount = customerDomainTransDTO.getCanTransferCount();

        PriceApplyOperateNumDTO priceApplyOperateNumDTO = new PriceApplyOperateNumDTO()
                .setType(Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue(), PriceTypeEnum.STRUCTURE_PRICING.getValue()))
                .setCategory2(contractFuturesDTO.getCategory2())
                .setCustomerId(Integer.parseInt(contractFuturesDTO.getCustomerId()))
                .setCompanyId(contractFuturesDTO.getCompanyId())
                .setDominantCode(contractFuturesDTO.getDomainCode())
                .setFutureCode(contractFuturesDTO.getFutureCode());
        //查询申请单中的未操作总量
        //申请单中的量
        applyCount = this.getApplyInfoNum(priceApplyOperateNumDTO);
        log.info("=======================================================申请单总量:{}", applyCount);
        customerDomainTransDTO.setApplyCount(applyCount);
        //查询成交单中的未操作总量
        dealCount = priceDealTemporaryNum(priceApplyOperateNumDTO);
        log.info("=======================================================交单中的未操作总量:{}", dealCount);
        customerDomainTransDTO.setDealCount(dealCount);
        //查询分配单中的未操作总量
        List<PriceAllocateEntity> priceAllocateEntity = priceAllocateService.getPriceAllocateNum(priceApplyOperateNumDTO);
        //点价量累加
        BigDecimal allocatePriceIngNum = priceAllocateTemporaryNum(priceAllocateEntity, PriceTypeEnum.PRICING.getValue());
        log.info("=======================================================点价量累加:{}", allocatePriceIngNum);
        //转月量特殊处理
        BigDecimal allocateTransferMonthNum = priceAllocateTemporaryNum(priceAllocateEntity, PriceTypeEnum.TRANSFER_MONTH.getValue());
        log.info("=======================================================转月量特殊处理总量:{}", allocateTransferMonthNum);
        customerDomainTransDTO.setAllocateCount(allocateCount);

        //头寸点价总量
        BigDecimal priceNum = applyCount.add(dealCount).add(allocatePriceIngNum);
        //头寸转月总量
        BigDecimal transferNum = applyCount.add(dealCount).add(allocateTransferMonthNum);
        log.info("=======================================================头寸点价总量:{}", priceNum);
        log.info("=======================================================头寸转月总量:{}", transferNum);
        log.info("=======================================================可定价量-canPriceCount:{}", canPriceCount);
        log.info("=======================================================可转月量-canTransferCount:{}", canTransferCount);
        canPriceCount = canPriceCount.subtract(priceNum);
        canTransferCount = canTransferCount.subtract(transferNum);


        log.info("=======================================================可定价量-canPriceCount:{}", canPriceCount);
        log.info("=======================================================可转月量-canTransferCount:{}", canTransferCount);
        return customerDomainTransDTO
                .setCanPriceCount(canPriceCount)
                .setCanTransferCount(canTransferCount);
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
    }


    /**
     * 查询申请单中的量
     *
     * @param priceApplyOperateNumDTO
     * @return
     */
    private BigDecimal getApplyInfoNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {

        BigDecimal operateNum = BigDecimal.ZERO;

        //根据条件查询出申请表信息
        List<PriceApplyEntity> priceApplyEntities = priceApplyService.queryPriceApplyOperateNum(priceApplyOperateNumDTO);
        //判断是否为空
        if (priceApplyEntities == null) {
            return BigDecimal.ZERO;
        }
        for (PriceApplyEntity priceApply : priceApplyEntities) {
            //申请单的量
            if (priceApply.getStatus() == PriceStatusEnum.WAIT_PENDING.getValue()
                    || priceApply.getStatus() == PriceStatusEnum.WAIT_TRANSACTION.getValue()
                    || priceApply.getStatus() == PriceStatusEnum.PRICING.getValue()
                    || priceApply.getStatus() == PriceStatusEnum.STRUCTURING.getValue()) {
                //查询最新改单数据


                if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == priceApply.getType()) {
                    //结构化定价单申请单总量减去成交量
                    BigDecimal applyNum = priceApply.getApplyNum().subtract(priceApply.getDealNum());
                    operateNum = operateNum.add(applyNum);

                } else {
                    BigDecimal applyNum = priceApply.getApplyNum();
                    operateNum = operateNum.add(applyNum);
                }
            }
            //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
            //成交单中的量
/*            if (priceApply.getStatus() == PriceStatusEnum.WAIT_ALLOCATE.getValue()
                    || (priceApply.getStatus() == PriceStatusEnum.WAIT_TRANSACTION.getValue()
                    && PriceTypeEnum.STRUCTURE_PRICING.getValue() == priceApply.getType())) {

                List<PriceDealDetailEntity> priceDealDetailEntities = iPriceDealService.queryDominantPriceDealDetail(priceApply.getId());
                for (PriceDealDetailEntity priceDealDetailEntity : priceDealDetailEntities) {
                    //未分配量
                    operateNum = operateNum.add(priceDealDetailEntity.getDealNum().subtract(priceDealDetailEntity.getAllocateNum()));
                }

            }*/
            //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
        }

        return operateNum;
    }

    /**
     * 查询分配单中的量
     *
     * @param contractFuturesDTO
     * @return
     */
    private BigDecimal priceAllocateNum(ContractFuturesDTO contractFuturesDTO, Integer priceType) {
        PriceApplyOperateNumDTO priceApplyOperateNumDTO = new PriceApplyOperateNumDTO()
                .setType(Arrays.asList(priceType))
                .setCategory2(contractFuturesDTO.getCategory2())
                .setCustomerId(Integer.parseInt(contractFuturesDTO.getCustomerId()))
                .setCompanyId(contractFuturesDTO.getCompanyId())
                .setFutureCode(contractFuturesDTO.getFutureCode())
                .setDominantCode(contractFuturesDTO.getDomainCode());

        BigDecimal allocateNum = BigDecimal.ZERO;

        //查询客户合约下的申请单id
        List<PriceDealDetailEntity> priceDealDetailEntities = iPriceDealService.queryPriceDealDetailOperateNum(priceApplyOperateNumDTO);

        for (PriceDealDetailEntity priceDealDetailEntity : priceDealDetailEntities) {
            //根据申请单id 查询出分配单信息
            List<PriceAllocateEntity> priceAllocateEntity = priceAllocateService.getAllocateByDominantCode(priceDealDetailEntity.getPriceApplyId());
            if (!priceAllocateEntity.isEmpty()) {
                allocateNum = allocateNum.add(this.priceAllocateTemporaryNum(priceAllocateEntity, priceType));
            }
        }


        return allocateNum;
    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start

    /**
     * 查询成交单中重量
     */
    public BigDecimal priceDealTemporaryNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {
        //查询成交单中的总量
        BigDecimal dealNum = BigDecimal.ZERO;

        //查询待分配下的成交未分配的量
        List<PriceDealDetailEntity> priceDealDetailEntities = iPriceDealService.queryPriceDealDetailOperateNum(priceApplyOperateNumDTO);
        for (PriceDealDetailEntity priceDealDetailEntity : priceDealDetailEntities) {
            //成交量-未分配量
            dealNum = dealNum.add(priceDealDetailEntity.getNotAllocateNum());

        }
        return dealNum;
    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end

    /**
     * 分配单中的量相加
     *
     * @param priceAllocateEntity
     * @param priceType
     * @return
     */
    public BigDecimal priceAllocateTemporaryNum(List<PriceAllocateEntity> priceAllocateEntity, Integer priceType) {
        //申请单点价量
        BigDecimal allocateNum = BigDecimal.ZERO;
        //申请单中基差量
        BigDecimal diffPriceNum = BigDecimal.ZERO;
        //基差暂定价
        BigDecimal tentativeDiffPriceNum = BigDecimal.ZERO;
        //审核通过的量相加
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
        for (PriceAllocateEntity priceAllocate : priceAllocateEntity) {
            if (priceType != PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                allocateNum = allocateNum.add(priceAllocate.getAllocateNum());
            } else {
                ContractEntity contractEntity = contractFacade.getBasicContractById(priceAllocate.getContractId());
                if (null != contractEntity && contractEntity.getContractType() != ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
                    diffPriceNum = diffPriceNum.add(priceAllocate.getAllocateNum());
                } else {
                    tentativeDiffPriceNum = tentativeDiffPriceNum.add(priceAllocate.getAllocateNum());
                }
            }
        }
        //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end

        //转月数量查询限制
        if (diffPriceNum.add(tentativeDiffPriceNum).compareTo(BigDecimal.ZERO) > 0) {
            if (diffPriceNum.compareTo(tentativeDiffPriceNum) >= 0) {
                allocateNum = allocateNum.add(diffPriceNum);
            } else {
                allocateNum = allocateNum.add(tentativeDiffPriceNum);
            }

        }

        return allocateNum;
    }
}
