package com.navigator.future.service.impl;

import cn.hutool.core.lang.tree.Tree;
import com.navigator.future.dao.TradingConfigDao;
import com.navigator.future.pojo.entity.TradingConfigEntity;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.future.service.TradingConfigServer;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@Service
public class TradingConfigServerImpl implements TradingConfigServer {

    @Resource
    private TradingConfigDao tradingConfigDao;
    @Resource
    private CategoryFacade categoryFacade;


    @Override
    public List<TradingConfigVO> queryTradingConfigList() {
        List<TradingConfigVO> tradingConfigList = new ArrayList<>();
        List<TradingConfigEntity> tradingConfigEntities = tradingConfigDao.queryTradingConfigList();

        for (TradingConfigEntity tradingConfigEntity : tradingConfigEntities) {
            TradingConfigVO tradingConfigVO = assembleDominantCode(tradingConfigEntity);
            tradingConfigList.add(tradingConfigVO);
        }

        return tradingConfigList;
    }


    @Override
    public TradingConfigVO getDomainTypeByCategoryCode(String getFutureCode) {
        List<TradingConfigEntity> tradingConfigEntities = tradingConfigDao.queryTradingConfigByFutureCode(getFutureCode);

        if (tradingConfigEntities.isEmpty()) {
            return null;
        }
        TradingConfigEntity tradingConfigEntity = tradingConfigEntities.get(0);
        return assembleDominantCode(tradingConfigEntity);
    }

    @Override
    public List<TradingConfigEntity> getDomainTypeEntityByCategoryCode(String getFutureCode) {
        return tradingConfigDao.queryTradingConfigByFutureCode(getFutureCode);
    }

    //根据二级品类查询数据
    @Override
    public List<TradingConfigVO> getDomainTypeByCategory2(String category2) {
        List<TradingConfigVO> tradingConfigList = new ArrayList<>();
        List<CategoryEntity> categoryEntities = categoryFacade.queryCategoryList(new CategoryQO().setParentSerialNo(Integer.parseInt(category2)));

        //List<String> facadeCodeList = categoryFacade.queryFutureCodeList(Integer.parseInt(category2));
        for (CategoryEntity categoryEntity : categoryEntities) {
            //取出品种编码
            //String futureCode = String.valueOf(tree.get("domainCode"));
            TradingConfigVO tradingConfigVO = getDomainTypeByCategoryCode(categoryEntity.getFutureCode());
            if (null != tradingConfigVO) {
                tradingConfigVO.setCategoryName(categoryEntity.getName());
                tradingConfigList.add(tradingConfigVO);
            }
        }

        return tradingConfigList;
    }

    /**
     * 根据期货代码获取期货月份
     *
     * @param tradingConfigEntity
     * @return
     */
    private TradingConfigVO assembleDominantCode(TradingConfigEntity tradingConfigEntity) {

        List<String> dominantCodes = new ArrayList<>();

        LocalDate today = LocalDate.now();
        List<String> futureMonths = Arrays.asList(tradingConfigEntity.getDomainMonth().split("\\$"));
        //case 1003224 增加M2605合约 Author: Wan 2025-05-20 Start
        for (int i = 0; i < 13; i++) {
        //case 1003224 增加M2605合约 Author: Wan 2025-05-20 end
            // 计算当前月份加i个月后的日期
            LocalDate futureMonth = today.plusMonths(i).plusMonths(0);

            // 提取年份和月份的最后两位数字，并拼接成字符串
            int year = futureMonth.getYear();
            int month = futureMonth.getMonthValue();

            // 假设我们只关心年份和月份的最后两位（这在实际应用中可能不是最佳实践，因为它可能导致年份混淆）
            // 但根据你的要求，我们这样做
            for (String c : futureMonths) {
                if (c.equals(String.valueOf(futureMonth.getMonthValue()))) {
                    String formattedMonth = String.format("%02d%02d", year % 100, month);

                    dominantCodes.add(formattedMonth);
                }

            }
        }
        return new TradingConfigVO()
                .setFutureCode(tradingConfigEntity.getFutureCode())
                .setDomainCodes(dominantCodes)
                .setExchange(tradingConfigEntity.getExchange())
                ;
    }

}



