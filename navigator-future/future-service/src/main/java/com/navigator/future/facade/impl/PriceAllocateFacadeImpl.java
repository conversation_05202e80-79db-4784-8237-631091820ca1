package com.navigator.future.facade.impl;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.util.BigDecimalUtil;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.future.enums.AllocateSourceEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.service.IPriceAllocateService;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.future.service.IPriceDealService;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: No Description
 * Created by YuYong on 2022/1/6 14:47
 */
@RestController
@Slf4j
@Component
public class PriceAllocateFacadeImpl implements PriceAllocateFacade {

    @Autowired
    private IPriceAllocateService iPriceAllocateService;
    @Autowired
    private IPriceApplyService iPriceApplyService;
    @Autowired
    private ContractFacade contractFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private IPriceDealService iPriceDealService;


    @Override
    public Result genDistributionOrder(DistributionDTO distributionDTO) {
        return iPriceAllocateService.genDistributionOrder(distributionDTO);
    }

    @Override
    public Result queryPriceAllocates(QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        return iPriceAllocateService.queryPriceAllocates(queryDTO);
    }

    @Override
    public Result getPriceAllocateDetail(String applyCode) {
        return iPriceAllocateService.getPriceAllocateDetail(applyCode);
    }

    @Override
    public boolean judgeExistsAllocatesAndOrder(String customerId, String dominantCode, Integer categoryId) {
        return iPriceAllocateService.judgeExistsAllocatesAndOrder(customerId, dominantCode, categoryId);
    }

    /**
     * 审核点价和转月的分配单
     *
     * @param auditPriceAllocateDTO
     * @return
     */
    @Override
    public Result auditPriceAllocate(List<AuditPriceAllocateDTO> auditPriceAllocateDTO) {
        Result result = new Result();
        for (AuditPriceAllocateDTO auditPriceAllocate : auditPriceAllocateDTO) {
            result = iPriceAllocateService.auditPriceAllocateNew(auditPriceAllocate);
        }

        return result;
    }

    @Override
    public BigDecimal getSumPriceAllocateOfContract(String contractId) {
        return iPriceAllocateService.getSumPriceAllocateOfContract(contractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result automaticGenDistributionOrder() {
        log.info("=====================================================================");
        log.info("自动分配开始");
        operationLogFacade.recordOperationLog(new OperationDetailDTO().setBizCode(LogBizCodeEnum.PRICE_APPLY_TRANSACTION.getBizCode())
                .setBizModule(ModuleTypeEnum.PRICE.getModule())
                .setLogLevel(OperationSourceEnum.SYSTEM.getValue())
                .setSource(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorType(OperationSourceEnum.SYSTEM.getValue())
                .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                .setOperationName("Job定时任务automaticGenDistributionOrder")
        );
        // 获取到成交待分配的申请单（点价单和转月单，转月单在前） 【时间要求暂时为空（未约束】
        //List<PriceApplyEntity> applyEntityList = iPriceApplyService.queryPriceApplyByPriceStatus(String.valueOf(PriceStatusEnum.WAIT_ALLOCATE.getValue()), null, null);

        List<PriceDealDetailEntity> priceDealDetailEntities = iPriceDealService.queryPriceDealListByPriceStatus(PriceStatusEnum.WAIT_ALLOCATE.getValue());

        // 成功数量
        int successNum = 0;

        for (PriceDealDetailEntity priceDealDetailEntity : priceDealDetailEntities) {

            if (priceDealDetailEntity.getNotAllocateNum().compareTo(BigDecimal.ZERO) < 0 || priceDealDetailEntity.getNotAllocateNum().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }

            List<DistributionDTO.Distribution> distributions = new ArrayList<>();
            //查询合约下的合同
            List<ContractEntity> contractEntities = contractFacade.queryContractsByDomainCodeList(new QueryContractDTO()
                    .setCategory2(Integer.parseInt(priceDealDetailEntity.getCategory2()))
                    .setFutureCode(priceDealDetailEntity.getFutureCode())
                    .setType(priceDealDetailEntity.getType().toString())
                    .setDomainCode(priceDealDetailEntity.getDominantCode())
                    .setCompanyId(priceDealDetailEntity.getCompanyId().toString())
                    .setCustomerId(String.valueOf(priceDealDetailEntity.getCustomerId())));

            // 成交数量 分配量
            BigDecimal dealNum = priceDealDetailEntity.getDealNum();
            BigDecimal allocateNum = priceDealDetailEntity.getAllocateNum();
            BigDecimal canAllocateNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, dealNum, allocateNum);
            for (ContractEntity contractEntity : contractEntities) {
                // 合同数据处理（优先分配原则）
                DistributionDTO.Distribution distribution = new DistributionDTO.Distribution();
                distribution.setContractId(String.valueOf(contractEntity.getId()));
                //查询分配单中的审核待分配(转月和点价)
                BigDecimal sumPriceAllocateOfContract = this.getSumPriceAllocateOfContract(String.valueOf(contractEntity.getId()));
                // 该合同可以分配的量
                BigDecimal canAllocateOfContract = BigDecimal.ZERO;

                // canAllocateOfContract = 合同总量 - 已点量 - 转月 - 变更 - 待审核(转月和点价)
                BigDecimal num = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), contractEntity.getTotalPriceNum());
                canAllocateOfContract = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, num, sumPriceAllocateOfContract);

                if (canAllocateNum.compareTo(BigDecimal.ZERO) == 0 || canAllocateNum.compareTo(BigDecimal.ZERO) < 0) {
                    continue;
                }

                if (BigDecimalUtil.isGreater(canAllocateNum, canAllocateOfContract)) {
                    if (canAllocateOfContract.compareTo(BigDecimal.ZERO) > 0) {
                        log.info("========================================" + canAllocateNum);
                        log.info("========================================" + canAllocateOfContract);
                        //canAllocateNum = BigDecimalUtil.div(CalcTypeEnum.COUNT, canAllocateNum, canAllocateOfContract);
                        distribution.setAllocateNum(canAllocateOfContract);
                        distributions.add(distribution);
                        canAllocateNum = canAllocateNum.subtract(canAllocateOfContract);
                    }
                } else {
                    distribution.setAllocateNum(canAllocateNum);
                    distributions.add(distribution);
                    canAllocateNum = canAllocateNum.subtract(canAllocateNum);
                }
            }
            //转月申请单未能分配完跳过分配
            if (BigDecimalUtil.isGreaterThanZero(canAllocateNum) && priceDealDetailEntity.getType() == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                log.info("===========================================================================");
                continue;
            }

            DistributionDTO distributionDTO = new DistributionDTO()
                    .setVoluntarily("自动分配")
                    .setApplyId(String.valueOf(priceDealDetailEntity.getId()))
                    .setOperationType(String.valueOf(priceDealDetailEntity.getType()))
                    .setDomainCode(priceDealDetailEntity.getDominantCode())
                    .setCustomerId(String.valueOf(priceDealDetailEntity.getCustomerId()))
                    .setDistributions(distributions);

            iPriceAllocateService.genDistributionOrder(distributionDTO);

            successNum++;

        }
        log.info("自动分配结束");
        log.info("=====================================================================");

        return priceDealDetailEntities.size() == successNum ? Result.success() : Result.failure();

    }

    @Override
    public PriceAllocateEntity getPriceAllocateById(String allocateId) {
        return iPriceAllocateService.getPriceAllocateById(allocateId);
    }

    @Override
    public Result purchaseContractPriceTransfer(List<PriceTransferDTO> priceTransferDTOs) {
        return iPriceAllocateService.purchaseContractPriceTransferNew(priceTransferDTOs);
    }

    @Override
    public Result queryPriceAllocateRecord(QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        return Result.success(iPriceAllocateService.queryPriceAllocateRecord(queryDTO));
    }

    @Override
    public Result purchaseContractReverse(ReverseDTO reverseDTO) {
        return iPriceAllocateService.purchaseContractReverseNew(reverseDTO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result modifyAllocates(DistributionDTO distributionDTO) {

        PriceAllocateEntity priceAllocateEntity = iPriceAllocateService.getPriceAllocateById(String.valueOf(distributionDTO.getPriceAllId()));

        //将申请单作废
        iPriceAllocateService.cancelPriceAllocateOfPriceAllId(priceAllocateEntity, distributionDTO);

        /*// 将合同下面待审核的分配单作废
        List<DistributionDTO.Distribution> distributions = distributionDTO.getDistributions();
        for (DistributionDTO.Distribution distribution : distributions) {
            String contractId = distribution.getContractId();
            iPriceAllocateService.cancelPriceAllocateOfContract(contractId, distributionDTO.getOperationType());
        }*/

        // 重新生成分配审核单
        distributionDTO.setUpdatePass("修改并通过")
                .setApplyId(priceAllocateEntity.getPriceDealId().toString());
        Result result = iPriceAllocateService.genDistributionOrder(distributionDTO);

        if (AllocateSourceEnum.VOLUNTARILY.getValue() != priceAllocateEntity.getSource() && AllocateSourceEnum.UPDATE_PASS.getValue() != priceAllocateEntity.getSource()) {

            try {
                sendMessage(priceAllocateEntity, distributionDTO);
            } catch (Exception e) {
                log.debug("modifyAllocates.sendMessage:{}", e.getMessage());
            }

        }

        return result;

    }

    @Override
    public BigDecimal hasOperation(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {
        return iPriceAllocateService.hasOperation(priceApplyOperateNumDTO);
    }

    @Override
    public Result structuringPriceDeal(PriceDealDetailDTO priceDealDetailDTOS) {
        return Result.success(iPriceDealService.structuringPriceDeal(priceDealDetailDTOS));
    }

    @Override
    public void priceApplyMigration() {
        iPriceDealService.priceApplyMigration();
    }

    @Override
    public boolean getPriceDealOrdersByDominantCode(String customerId, String domainCode, Integer categoryId) {
        return iPriceDealService.getPriceDealOrdersByDominantCode(customerId, domainCode, categoryId);
    }

    @Override
    public List<PriceAllocateEntity> getAllocateByContractStructureId(Integer contractStructureId) {
        return iPriceAllocateService.getAllocateByContractStructureId(contractStructureId);
    }

    @Override
    public List<PriceAllocateEntity> getByContractId(Integer contractId) {
        return iPriceAllocateService.getByContractId(contractId);
    }

    @Override
    public boolean getNotAllocateByContractId(Integer contractId) {
        return !CollectionUtils.isEmpty(iPriceAllocateService.getNotDealByContractId(contractId));
    }

    @Override
    public Result priceAllocateContrary(ApplyContraryDTO applyContraryDTO) {
        return Result.success(iPriceAllocateService.priceAllocateContrary(applyContraryDTO));
    }

    private void sendMessage(PriceAllocateEntity priceAllocateEntity, DistributionDTO distributionDTO) {
        //申请单
        PriceApplyEntity priceApplyEntity = iPriceApplyService.getPriceApplyEntityById(String.valueOf(priceAllocateEntity.getPriceApplyId()));

        //成交单
        PriceDealDetailEntity priceDealDetailEntity = iPriceDealService.priceDealDealDetailById(priceAllocateEntity.getPriceDealId());
        for (DistributionDTO.Distribution distributions : distributionDTO.getDistributions()) {
            MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
            //messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.CUSTOMER_PRICE_ALLOCATE_NOTICE.name());
            messageInfoDTO.setBusinessCode("UpdateAllocate_Sms");
            messageInfoDTO.setSystem(priceApplyEntity.getSystem());
            messageInfoDTO.setReferBizId(priceDealDetailEntity.getId().toString());

            List<String> receivers = new ArrayList<>();

            if (AllocateSourceEnum.BUSINESS.getValue() == priceAllocateEntity.getSource()) {
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.UPDATE_ALLOCATE_SMS.name());
                receivers.add(priceAllocateEntity.getCreatedBy().toString());
            } else {
                messageInfoDTO.setBusinessCode(MessageBusinessCodeEnum.UPDATE_ALLOCATE_SMS_CUSTOMER.name());
                receivers.add(priceAllocateEntity.getCreatedBy().toString());
            }
            messageInfoDTO.setReceivers(receivers);
            //发送模板code
            Map<String, Object> map = new HashMap<>();
            map.put("customerName", priceAllocateEntity.getCustomerName());
            map.put("priceApplyCode", priceAllocateEntity.getPriceApplyCode());
            map.put("dealNum", priceDealDetailEntity.getDealNum());
            map.put("priceType", PriceTypeEnum.getByValue(priceDealDetailEntity.getType()).getDesc());
            map.put("transactionPrice", PriceTypeEnum.TRANSFER_MONTH.getValue() != priceDealDetailEntity.getType() ? priceDealDetailEntity.getDealPrice() : priceDealDetailEntity.getTransactionDiffPrice());
            //原合同信息
            ContractEntity contractEntity = contractFacade.getBasicContractById(priceAllocateEntity.getContractId());
            map.put("contractCode", contractEntity.getContractCode());
            map.put("contractNum", contractEntity.getContractNum());
            map.put("allocateNum", priceAllocateEntity.getAllocateNum());
            map.put("extraPrice", contractEntity.getExtraPrice());
            map.put("deliveryStartTime", DateTimeUtil.formatDateString(contractEntity.getDeliveryStartTime()));
            map.put("deliveryEndTime", DateTimeUtil.formatDateString(contractEntity.getDeliveryEndTime()));
            //新合同信息
            ContractEntity contractEntity1 = contractFacade.getBasicContractById(Integer.parseInt(distributions.getContractId()));
            map.put("newContractCode", contractEntity1.getContractCode());
            map.put("newContractNum", contractEntity1.getContractNum());
            map.put("newExtraPrice", contractEntity1.getExtraPrice());
            map.put("newAllocateNum", distributions.getAllocateNum());
            map.put("newDeliveryStartTime", DateTimeUtil.formatDateString(contractEntity1.getDeliveryStartTime()));
            map.put("newDeliveryEndTime", DateTimeUtil.formatDateString(contractEntity1.getDeliveryEndTime()));

            messageInfoDTO.setDataMap(map);
            messageFacade.sendMessage(messageInfoDTO);

        }

    }

}
