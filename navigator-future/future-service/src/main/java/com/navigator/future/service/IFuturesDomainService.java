package com.navigator.future.service;

import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.future.pojo.vo.ContractsFuturesVO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 18:38
 */
public interface IFuturesDomainService {

    /**
     * 查询期货信息
     *
     * @param queryContractFuturesDTO
     * @return
     */
    List<ContractsFuturesVO> queryContractsFutures(QueryContractFuturesDTO queryContractFuturesDTO);

    /**
     * 查询可转月量
     *
     * @param contractFuturesDTO
     * @return
     */
    CustomerFuturesDTO mayTransferNum(ContractFuturesDTO contractFuturesDTO);

    /**
     * 查询可点价量
     *
     * @param contractFuturesDTO
     * @return
     */
    CustomerFuturesDTO mayPriceNum(ContractFuturesDTO contractFuturesDTO);

}
