package com.navigator.future.dao;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.future.enums.AllocateStatusEnum;
import com.navigator.future.mapper.PriceAllocateMapper;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.dto.QueryPriceAllocateDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:52
 */
@Dao
public class PriceAllocateDao extends BaseDaoImpl<PriceAllocateMapper, PriceAllocateEntity> {

    public IPage<PriceAllocateEntity> queryPriceAllocates(QueryDTO<QueryPriceAllocateDTO> queryDTO) {

        Integer pageSize = queryDTO.getPageSize();
        Integer pageNo = queryDTO.getPageNo();


        QueryPriceAllocateDTO queryPriceAllocateDTO = queryDTO.getCondition();
        // 客户名称 品种Id 操作类型 申请编号 分配状态 合同编号 期货合约
        String customerName = queryPriceAllocateDTO.getCustomerName();

        Integer type = queryPriceAllocateDTO.getType();
        String code = StrUtil.isNotBlank(queryPriceAllocateDTO.getCode()) ? queryPriceAllocateDTO.getCode().trim() : null;
        String contractCode = StrUtil.isNotBlank(queryPriceAllocateDTO.getContractCode()) ? queryPriceAllocateDTO.getContractCode().trim() : null;
        String customerId = queryPriceAllocateDTO.getCustomerId();
        String dominantCode = queryPriceAllocateDTO.getDominantCode();

        List<Integer> status = new ArrayList<>();
        if (StrUtil.isNotBlank(queryPriceAllocateDTO.getStatus())) {

            status = Arrays.stream(queryPriceAllocateDTO.getStatus().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());

        }
        List<Integer> finalStatus = status;
        return this.page(new Page<>(pageNo, pageSize), new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(null != queryPriceAllocateDTO.getSalesType(), PriceAllocateEntity::getSalesType, queryPriceAllocateDTO.getSalesType())
                .in(null != queryPriceAllocateDTO.getCompanyIds() && !queryPriceAllocateDTO.getCompanyIds().isEmpty(), PriceAllocateEntity::getCompanyId, queryPriceAllocateDTO.getCompanyIds())
                .in(!status.isEmpty(), PriceAllocateEntity::getStatus, status)
                .eq(StringUtil.isNotEmpty(queryPriceAllocateDTO.getSiteCode()), PriceAllocateEntity::getSiteCode, queryPriceAllocateDTO.getSiteCode())
                //.in(null != queryPriceAllocateDTO.getColumbusStatus(), PriceAllocateEntity::getStatus, Arrays.asList(AllocateStatusEnum.WAIT_AUDIT.getValue(), AllocateStatusEnum.AUDIT_PASS.getValue()))
                .like(StrUtil.isNotBlank(customerName), PriceAllocateEntity::getCustomerName, customerName)
                .eq(StringUtil.isNotEmpty(queryPriceAllocateDTO.getCategory2()), PriceAllocateEntity::getCategory2, queryPriceAllocateDTO.getCategory2())
                .eq(type != null, PriceAllocateEntity::getPriceApplyType, type)
                .eq(StrUtil.isNotEmpty(queryPriceAllocateDTO.getFutureCode()), PriceAllocateEntity::getRawFutureCode, queryPriceAllocateDTO.getFutureCode())
                .eq(StringUtil.isNotEmpty(queryPriceAllocateDTO.getDeliveryFactoryCode()), PriceAllocateEntity::getDeliveryFactoryCode, queryPriceAllocateDTO.getDeliveryFactoryCode())
                .and(StrUtil.isNotBlank(contractCode),
                        wrapper -> wrapper.like(StrUtil.isNotBlank(contractCode), PriceAllocateEntity::getContractCode, contractCode)
                                .or().like(StrUtil.isNotBlank(contractCode) && AllocateStatusEnum.getBooleanByValue(finalStatus), PriceAllocateEntity::getSubcontractCode, contractCode)
                )
                .eq(null != queryPriceAllocateDTO.getAuditStatus(), PriceAllocateEntity::getAuditStatus, queryPriceAllocateDTO.getAuditStatus())
                .like(StrUtil.isNotBlank(code), PriceAllocateEntity::getPriceApplyCode, code)
                .eq(StrUtil.isNotBlank(customerId), PriceAllocateEntity::getCustomerId, customerId)
                .eq(StrUtil.isNotBlank(dominantCode), PriceAllocateEntity::getRawDominantCode, dominantCode)
                .orderByDesc(PriceAllocateEntity::getCreatedAt)
        );
    }

    /**
     * 分配单各个状态数量
     *
     * @param status
     * @param customerId
     * @return
     */
    public Integer getPriceAllocateStat(Integer status, Integer customerId, Integer categoryId) {
        return this.baseMapper.selectCount(Wrappers.<PriceAllocateEntity>lambdaQuery()
                .eq(PriceAllocateEntity::getCategoryId, categoryId)
                .eq(PriceAllocateEntity::getStatus, status)
                .eq(customerId != null, PriceAllocateEntity::getCustomerId, customerId));
    }

    /**
     * 查找指定合约下的分配待审核的分配单
     *
     * @param dominantCode
     * @return
     */
    public List<PriceAllocateEntity> getAllocateOrdersByDominantCode(String customerId, String dominantCode, Integer categoryId) {
        List<PriceAllocateEntity> list = this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getPriceApplyType, PriceTypeEnum.TRANSFER_MONTH.getValue())
                .eq(PriceAllocateEntity::getCustomerId, customerId)
                .eq(PriceAllocateEntity::getDominantCode, dominantCode)
                .eq(PriceAllocateEntity::getCategoryId, categoryId)
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.WAIT_AUDIT.getValue()));
        return list;
    }

    public BigDecimal getSumPriceAllocateOfContract(String contractId) {
        List<PriceAllocateEntity> list = this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .in(PriceAllocateEntity::getStatus, Arrays.asList(AllocateStatusEnum.WAIT_AUDIT.getValue()))
                .eq(PriceAllocateEntity::getContractId, contractId)
                .in(PriceAllocateEntity::getPriceApplyType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue(), PriceTypeEnum.STRUCTURE_PRICING.getValue())));

        return CollectionUtil.isEmpty(list) ? BigDecimal.ZERO : list.stream().map(PriceAllocateEntity::getAllocateNum).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 根据申请单id查询出分配单信息
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceAllocateEntity> getAllocateByPriceApplyId(Integer priceApplyId) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getPriceApplyId, priceApplyId)
                .orderByDesc(PriceAllocateEntity::getId)
        );
    }

    public List<PriceAllocateEntity> getAllocateByPriceApplyIdStatus(Integer priceApplyId) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getPriceApplyId, priceApplyId)
                .lt(PriceAllocateEntity::getStatus, AllocateStatusEnum.AUDIT_REJECT.getValue())
                .orderByDesc(PriceAllocateEntity::getType)
        );
    }

    public List<PriceAllocateEntity> getAllocateTypeByPriceApplyId(Integer contractId, Integer priceApplyId, Integer type) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                //.eq(PriceAllocateEntity::getPriceApplyType, priceApplyType)
                .eq(PriceAllocateEntity::getContractId, contractId)
                .ne(PriceAllocateEntity::getPriceApplyId, priceApplyId)
                .eq(PriceAllocateEntity::getType, type)
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.AUDIT_PASS.getValue())
        );
    }


    /**
     * 结构定价查询分记录
     *
     * @param queryDTO
     * @return
     */
    public IPage<PriceAllocateEntity> queryAllocateRecord(QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        QueryPriceAllocateDTO queryPriceAllocateDTO = queryDTO.getCondition();

        Integer pageNo = queryDTO.getPageNo();
        Integer pageSize = queryDTO.getPageSize();

        return this.page(new Page<>(pageNo, pageSize), new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getContractId, queryPriceAllocateDTO.getCategoryId()));
    }


    /**
     * 根据合约查询出分配单
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceAllocateEntity> getAllocateByDominantCode(Integer priceApplyId) {
        return this.baseMapper.selectList(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getPriceApplyId, priceApplyId)
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.WAIT_AUDIT.getValue())
                .in(PriceAllocateEntity::getPriceApplyType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue())));
    }

    public PriceAllocateEntity getPriceAllocateById(String allocateId) {
        return this.getById(allocateId);
    }


    public List<PriceAllocateEntity> cancelPriceAllocateOfContract(String contractId, String priceType) {
        List<PriceAllocateEntity> list = this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .in(PriceAllocateEntity::getStatus, Arrays.asList(AllocateStatusEnum.WAIT_AUDIT.getValue()))
                .eq(PriceAllocateEntity::getContractId, contractId)
                .eq(PriceAllocateEntity::getPriceApplyType, priceType));
        return list;
    }


    public List<PriceAllocateEntity> getPriceAllocateHasOperation(Integer customerId, String domainCode, Integer categoryId) {

        return baseMapper.selectList(Wrappers.<PriceAllocateEntity>lambdaQuery()
                .eq(PriceAllocateEntity::getCustomerId, customerId)
                .eq(PriceAllocateEntity::getDominantCode, domainCode)
                .eq(PriceAllocateEntity::getCategoryId, categoryId)
                .in(PriceAllocateEntity::getPriceApplyType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue()))
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.WAIT_AUDIT.getValue()));

    }

    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
    public List<PriceAllocateEntity> getPriceAllocateNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {

        return baseMapper.selectList(Wrappers.<PriceAllocateEntity>lambdaQuery()
                .eq(PriceAllocateEntity::getCustomerId, priceApplyOperateNumDTO.getCustomerId())
                .eq(PriceAllocateEntity::getCompanyId, priceApplyOperateNumDTO.getCompanyId())
                .eq(PriceAllocateEntity::getRawDominantCode, priceApplyOperateNumDTO.getDominantCode())
                .eq(PriceAllocateEntity::getRawFutureCode, priceApplyOperateNumDTO.getFutureCode())
                .eq(PriceAllocateEntity::getCategory2, priceApplyOperateNumDTO.getCategory2())
                .in(PriceAllocateEntity::getPriceApplyType, priceApplyOperateNumDTO.getType())
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.WAIT_AUDIT.getValue()));

    }
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end

    public List<PriceAllocateEntity> getNotDealByContractId(Integer contractId) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getContractId, contractId)
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.WAIT_AUDIT.getValue()));
    }

    public PriceAllocateEntity getPriceAllocateByIdStatus(Integer priceAllocateId, Integer allocateStatus) {
        List<PriceAllocateEntity> priceAllocateEntities = this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getId, priceAllocateId)
                .eq(PriceAllocateEntity::getStatus, allocateStatus));

        return priceAllocateEntities.isEmpty() ? null : priceAllocateEntities.get(0);
    }

    public List<PriceAllocateEntity> getByContractId(Integer contractId) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getContractId, contractId));
    }

    public List<PriceAllocateEntity> getAllocateByContractStructureId(Integer contractStructureId) {
        return this.list(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getStructureContractId, contractStructureId)
                .eq(PriceAllocateEntity::getStatus, AllocateStatusEnum.AUDIT_PASS.getValue()));
    }
}
