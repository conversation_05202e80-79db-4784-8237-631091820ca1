package com.navigator.future.kingstar.controller;

import com.navigator.future.kingstar.service.IKingStarSyncService;
import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequiredArgsConstructor
public class KingStarCallBackController {
    @Resource
    private IKingStarSyncService kingStarSyncService;

    /**
     * 状态回调
     *
     * @param kingStarCallbackDTO 回执信息
     * @return 处理结果
     */
    @PostMapping("/kingstar/callback")
    public KingStarResponseDTO handleInstructCallback(@RequestBody String kingStarCallbackDTO) {
        return kingStarSyncService.handleInstructCallback(kingStarCallbackDTO);
    }

}
