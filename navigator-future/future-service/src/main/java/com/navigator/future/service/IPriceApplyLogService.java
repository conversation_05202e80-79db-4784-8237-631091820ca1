package com.navigator.future.service;


import com.navigator.future.pojo.dto.QueryPriceApplyLogDTO;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;
import com.navigator.future.pojo.vo.PriceApplyLogVo;

import java.util.List;

/**
 * <p>
 * 改撤单记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface IPriceApplyLogService {

    /**
     * 根据申请单id 查询改单待审核的数量
     *
     * @param priceApplyId
     * @return
     */
    PriceApplyLogEntity getPriceApplyLog(Integer priceApplyId);

    /**
     * 根据申请单id查询操作记录
     *
     * @param queryPriceApplyLogDTO
     * @return
     */
    List<PriceApplyLogVo> getPriceApplyLogPriceApplyId(QueryPriceApplyLogDTO queryPriceApplyLogDTO);

    /**
     * 查询申请单的改撤单记录
     *
     * @param priceApplyId
     * @return
     */
    List<PriceApplyLogVo> queryPriceApplyLog(Integer priceApplyId);
}
