package com.navigator.future.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.TradingConfigFacade;
import com.navigator.future.pojo.entity.TradingConfigEntity;
import com.navigator.future.pojo.vo.TradingConfigVO;
import com.navigator.future.service.TradingConfigServer;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@RestController
@Component
public class TradingConfigFacadeImpl implements TradingConfigFacade {

    @Resource
    private TradingConfigServer tradingConfigServer;

    @Override
    public Result queryTradingConfigList() {
        return Result.success(tradingConfigServer.queryTradingConfigList());
    }

    @Override
    public TradingConfigVO getDomainTypeByCategoryCode(String getFutureCode) {
        return tradingConfigServer.getDomainTypeByCategoryCode(getFutureCode);
    }

    @Override
    public List<TradingConfigEntity> getDomainTypeEntityByCategoryCode(String getFutureCode) {
        return tradingConfigServer.getDomainTypeEntityByCategoryCode(getFutureCode);
    }

    @Override
    public Result getDomainTypeByCategory2(String category2) {
        return Result.success(tradingConfigServer.getDomainTypeByCategory2(category2));
    }
}
