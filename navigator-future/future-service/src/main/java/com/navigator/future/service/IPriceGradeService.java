package com.navigator.future.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.future.pojo.entity.PriceGradeEntity;

import java.util.List;

public interface IPriceGradeService {

    Boolean updatePriceGrade(PriceGradeEntity priceGradeEntity);

    List<PriceGradeEntity> queryPriceGradeList(QueryDTO<PriceGradeEntity> queryDTO);

    PriceGradeEntity queryPriceGradeByCode(String businessCode);
}
