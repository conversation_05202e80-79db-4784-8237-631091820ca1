package com.navigator.future.kingstar.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.enums.KingStarBusinessTypeEnum;
import com.navigator.future.enums.KingStarInterfaceStatusEnum;
import com.navigator.future.enums.PendingTypeEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.kingstar.dao.KingStarSyncRequestDao;
import com.navigator.future.kingstar.sender.KingStarDelayedMessageService;
import com.navigator.future.pojo.dto.kingstar.KingStarMessageRequestDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarRequestDTO;
import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.kingstar.KingStarSyncRequestEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.Optional;

/**
 * <p>
 * URL 转换的实现类
 * </p>
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class KingStarInstructionService {

    @Value("${kingstar.endpoint.url}")
    private String endpointUrl;
    @Value("${kingstar.notice.email}")
    private String kingStarNoticeEmail;
    @Value("${kingstar.navigatorToEsb.authentication.api-key}")
    private String apiKey;
    @Value("${kingstar.navigatorToEsb.authentication.api-value}")
    private String apiValue;

    @Resource
    private PriceApplyDao priceApplyDao;
    @Resource
    private KingStarSyncRequestDao kingStarSyncRequestDao;
    @Resource
    private IKingStarSyncService kingStarSyncService;
    @Resource
    private KingStarDelayedMessageService delayedMessageService;
    @Resource
    private MessageFacade messageFacade;
    @Resource
    private RestTemplate insecureRestTemplate;

    /**
     * 获取 KingStar Token
     *
     * @param licenseCode 用户编码
     * @return KingStar Token
     */
    public String fetchTokenByLicense(String licenseCode) {
        String url = endpointUrl + "/hedgingcustomer/user/getTokenByLicense";
        log.info("[KST] 获取Token，licenseCode: {}", licenseCode);
        try {
            return fetchTokenByLicense(url, licenseCode);
        } catch (Exception e) {
            log.error("[KST] 获取Token异常: {}", licenseCode, e);
            throw new RuntimeException("获取Token失败", e);
        }
    }

    public KingStarResponseDTO applyPricing(String userCode, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        return sendInstruction("/hedgingtrader/instruct/pricing", userCode, dto, "点价", syncRequest);
    }

    public KingStarResponseDTO applyReversePricing(String userCode, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        return sendInstruction("/hedgingtrader/instruct/reversePricing", userCode, dto, "反点价", syncRequest);
    }

    public KingStarResponseDTO applyModify(String userCode, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        return sendInstruction("/hedgingtrader/instruct/update", userCode, dto, "改单", syncRequest);
    }

    public KingStarResponseDTO applyCancel(String userCode, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        return sendInstruction("/hedgingtrader/instruct/revoke", userCode, dto, "撤单", syncRequest);
    }

    /**
     * 支持延迟发送的点价申请 - 优化版本
     *
     * @param priceApplyEntity 申请实体
     * @param dto              请求DTO
     * @param syncRequest      同步请求实体
     */
    public void applyPricingWithDelay(PriceApplyEntity priceApplyEntity, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        log.info("开始点价申请: instructId={}", syncRequest.getInstructId());

        String userCode = syncRequest.getUserCode();

        // 构造指令数据
        KingStarMessageRequestDTO requestDTO = KingStarMessageRequestDTO.builder()
                .userCode(userCode)
                .instructId(syncRequest.getInstructId())
                .delayedMessageId(syncRequest.getInstructId())
                .requestId(syncRequest.getId())
                .requestDTO(dto)
                .build();

        // 业务类型
        String businessType = priceApplyEntity.getType() == PriceTypeEnum.TRANSFER_MONTH.getValue() ? "转月" : "点价";

        delayedMessageService.sendMessage(syncRequest.getExchange(), businessType, requestDTO,
                () -> {
                    String pendingResult = "";
                    String response = "";
                    try {
                        KingStarResponseDTO responseDTO = applyPricing(requestDTO.getUserCode(), requestDTO.getRequestDTO(), syncRequest);
                        pendingResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
                        response = JSONUtil.toJsonStr(responseDTO);
                    } catch (Exception e) {
                        pendingResult = "接口调用失败";
                        response = "同步挂单失败" + e.getMessage();
                        log.error("直连点价申请失败: {}", e.getMessage(), e);
                    }

                    // 更新申请单状态
                    priceApplyEntity
                            .setStatus(pendingResult.equals("成功") ? PriceStatusEnum.WAIT_PENDING.getValue() : PriceStatusEnum.PRICING.getValue())
                            .setInterfaceStatus(pendingResult.equals("成功") ? KingStarInterfaceStatusEnum.PENDING.getValue() : KingStarInterfaceStatusEnum.NOT_CALL_INTERFACE.getValue())
                            .setPendingResult(pendingResult)
                            .setUpdatedAt(new Date());
                    priceApplyDao.updateById(priceApplyEntity);

                    // 更新request记录
                    syncRequest.setSyncStatus(pendingResult)
                            .setResponseInfo(response);
                    kingStarSyncRequestDao.updateById(syncRequest);
                });
    }

    /**
     * 支持延迟发送的反点价申请 - 优化版本
     */
    public void applyReversePricingWithDelay(PriceApplyEntity priceApplyEntity, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        log.info("开始延迟反点价申请:  instructId={}", syncRequest.getInstructId());

        String userCode = syncRequest.getUserCode();
        KingStarMessageRequestDTO requestDTO = KingStarMessageRequestDTO.builder()
                .userCode(userCode)
                .instructId(syncRequest.getInstructId())
                .delayedMessageId(syncRequest.getInstructId())
                .requestId(syncRequest.getId())
                .requestDTO(dto)
                .build();

        delayedMessageService.sendMessage(syncRequest.getExchange(), "反点价", requestDTO,
                () -> {
                    String pendingResult = "";
                    String response = "";
                    try {
                        KingStarResponseDTO responseDTO = applyReversePricing(userCode, dto, syncRequest);
                        pendingResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
                        response = JSONUtil.toJsonStr(responseDTO);
                    } catch (Exception e) {
                        pendingResult = "接口调用失败";
                        response = "同步反点价失败" + e.getMessage();
                        log.error("直连反点价申请失败: {}", e.getMessage(), e);
                    }

                    // 更新申请单状态
                    priceApplyEntity
                            .setStatus(pendingResult.equals("成功") ? PriceStatusEnum.WAIT_PENDING.getValue() : PriceStatusEnum.PRICING.getValue())
                            .setInterfaceStatus(pendingResult.equals("成功") ? KingStarInterfaceStatusEnum.PENDING.getValue() : KingStarInterfaceStatusEnum.NOT_CALL_INTERFACE.getValue())
                            .setPendingResult(pendingResult)
                            .setUpdatedAt(new Date());
                    priceApplyDao.updateById(priceApplyEntity);

                    // 更新request记录
                    syncRequest.setSyncStatus(pendingResult)
                            .setResponseInfo(response);
                    kingStarSyncRequestDao.updateById(syncRequest);
                });
    }

    /**
     * 支持延迟发送的改单申请 - 优化版本
     */
    public void applyModifyWithDelay(PriceApplyEntity priceApplyEntity, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        log.info("开始延迟改单申请: instructId={}", syncRequest.getInstructId());
        String userCode = syncRequest.getUserCode();

        KingStarMessageRequestDTO requestDTO = KingStarMessageRequestDTO.builder()
                .userCode(userCode)
                .instructId(syncRequest.getInstructId())
                .requestId(syncRequest.getId())
                .requestDTO(dto)
                .build();

        delayedMessageService.sendMessage(syncRequest.getExchange(), "改单", requestDTO,
                () -> {
                    String changeResult = "";
                    String response = "";
                    try {
                        KingStarResponseDTO responseDTO = applyModify(userCode, dto, syncRequest);
                        changeResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
                        response = JSONUtil.toJsonStr(responseDTO);
                    } catch (Exception e) {
                        changeResult = "接口调用失败";
                        response = "同步改单失败" + e.getMessage();
                        log.error("直连改单申请失败: {}", e.getMessage(), e);
                    }

                    // 更新申请单状态
                    priceApplyEntity
                            .setInterfaceStatus(KingStarInterfaceStatusEnum.MODIFYING.getValue())
                            .setChangeResult(changeResult)
                            .setUpdatedAt(new Date());
                    priceApplyDao.updateById(priceApplyEntity);

                    // 更新request记录
                    syncRequest.setSyncStatus(changeResult)
                            .setResponseInfo(response);
                    kingStarSyncRequestDao.updateById(syncRequest);
                });
    }

    /**
     * 支持延迟发送的撤单申请 - 优化版本
     */
    public void applyCancelWithDelay(PriceApplyEntity priceApplyEntity, KingStarRequestDTO dto, KingStarSyncRequestEntity syncRequest) {
        log.info("开始延迟撤单申请: instructId={}", syncRequest.getInstructId());
        String userCode = syncRequest.getUserCode();

        KingStarMessageRequestDTO requestDTO = KingStarMessageRequestDTO.builder()
                .userCode(userCode)
                .instructId(syncRequest.getInstructId())
                .requestId(syncRequest.getId())
                .requestDTO(dto)
                .build();

        delayedMessageService.sendMessage(syncRequest.getExchange(), "撤单", requestDTO,
                () -> {
                    String changeResult = "";
                    String response = "";
                    try {
                        KingStarResponseDTO responseDTO = applyCancel(userCode, dto, syncRequest);
                        changeResult = (responseDTO == null) ? "接口调用失败" : responseDTO.getMessage();
                        response = JSONUtil.toJsonStr(responseDTO);
                    } catch (Exception e) {
                        changeResult = "接口调用失败";
                        response = "同步撤单失败" + e.getMessage();
                        log.error("直连撤单申请失败: {}", e.getMessage(), e);
                    }

                    // 更新申请单状态
                    priceApplyEntity
                            .setInterfaceStatus(KingStarInterfaceStatusEnum.CANCELING.getValue())
                            .setChangeResult(changeResult)
                            .setUpdatedAt(new Date());
                    priceApplyDao.updateById(priceApplyEntity);

                    // 更新request记录
                    syncRequest.setSyncStatus(changeResult)
                            .setResponseInfo(response);
                    kingStarSyncRequestDao.updateById(syncRequest);
                });
    }

    /**
     * 通用挂单请求处理逻辑
     */
    private KingStarResponseDTO sendInstruction(
            String path, String userCode, KingStarRequestDTO dto, String actionDesc, KingStarSyncRequestEntity syncRequest) {

        String url = endpointUrl + path;
        log.info("[KST] 开始{}请求，userCode: {}, instructId: {}, 参数: {}", actionDesc, userCode, syncRequest.getInstructId(), dto);

        int maxAttempts = 3;
        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                String token = kingStarSyncService.getTokenByUserCode(userCode, false);
                syncRequest.setRequestUrl(url).setToken(token);

                KingStarResponseDTO response = sendKingStarPostRequest(url, dto, token);
                if (response != null && response.getCode() == 401) {
                    log.warn("[KST] {}请求Token失效，刷新Token重试", actionDesc);
                    token = kingStarSyncService.getTokenByUserCode(userCode, true);
                    syncRequest.setToken(token).setTryTimes(syncRequest.getTryTimes() + 1);
                    response = sendKingStarPostRequest(url, dto, token);
                }

                log.info("[KST] 第{}次{}成功，instructId: {}, 响应: {}", attempt, actionDesc, syncRequest.getInstructId(), response);
                return response;
            } catch (Exception e) {
                log.warn("[KST] 第{}次{}异常，instructId: {}, userCode: {}, 异常: {}", attempt, actionDesc, syncRequest.getInstructId(), userCode, e.getMessage(), e);
                syncRequest.setTryTimes(syncRequest.getTryTimes() + 1);

                if (attempt == 3) {
                    log.error("[KST] {}失败，已达最大重试次数，instructId: {}", actionDesc, syncRequest.getInstructId());
                    sendKingStarFailureEmail(dto, syncRequest.getInstructId(), actionDesc);
                    throw new RuntimeException("金仕达挂单失败", e);
                }
            }
        }
        return null;
    }

    public KingStarResponseDTO sendKingStarPostRequest(String url, KingStarRequestDTO dto, String token) {
        HttpHeaders headers = buildHeaders(token);
        HttpEntity<String> entity = new HttpEntity<>(JSONUtil.toJsonStr(dto), headers);

        try {
            ResponseEntity<String> response = insecureRestTemplate.postForEntity(url, entity, String.class);
            log.info("[KST] POST请求响应: status={}, body={}", response.getStatusCode(), response.getBody());
            if (response.getStatusCode().is2xxSuccessful()) {
                return JSONUtil.toBean(response.getBody(), KingStarResponseDTO.class, true);
            }
        } catch (Exception e) {
            log.error("[KST] POST请求异常, url: {}, error: {}", url, e.getMessage(), e);
            throw e;
        }
        return null;
    }

    private String fetchTokenByLicense(String url, String licenseCode) {
        HttpHeaders headers = buildHeaders(null);
        try {
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            String requestUrl = UriComponentsBuilder.fromHttpUrl(url)
                    .queryParam("licenseCode", licenseCode)
                    .build()
                    .toUriString();
            ResponseEntity<String> response = insecureRestTemplate.exchange(requestUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
            log.info("[KST] GET请求响应: status={}, body={}", response.getStatusCode(), response.getBody());
            if (response.getStatusCode().is2xxSuccessful()) {
                KingStarResponseDTO responseDTO = JSONUtil.toBean(response.getBody(), KingStarResponseDTO.class, true);
                return String.valueOf(responseDTO.getData());
            }
            return "";
        } catch (Exception e) {
            log.error("[KST] GET请求异常, url: {}, error: {}", url, e.getMessage(), e);
            throw e;
        }
    }

    private HttpHeaders buildHeaders(String token) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(apiKey, apiValue);
        if (StringUtils.isNotBlank(token)) {
            headers.set("token", token);
        }
        return headers;
    }

    /**
     * 发送 KingStar 失败的邮件通知
     *
     * @param requestDTO KingStar 请求数据传输对象
     * @param instructId 指令ID
     * @param action     操作类型
     */
    private void sendKingStarFailureEmail(KingStarRequestDTO requestDTO, String instructId, String action) {
        log.info("【KingStar】发送失败邮件通知开始，指令ID: {}, 操作: {}", instructId, action);

        try {
            String title = String.format("【%s】NAV-KST交易接口异常！", SpringContextUtil.getEnv());

            PriceApplyEntity priceApplyEntity = priceApplyDao.getPriceApplyByCode(instructId);
            if (priceApplyEntity == null) {
                log.warn("【KingStar】指令ID对应的报价申请不存在，指令ID: {}", instructId);
                return;
            }

            // 类型描述
            String priceType = "申请" + KingStarBusinessTypeEnum.getByValue(requestDTO.getType()).getDescription();

            // 买卖方向
            String orientation = PriceTypeEnum.REVERSE_PRICING.getValue() == priceApplyEntity.getType() ? "卖" : "买";

            String dealDirection = "自动开平";

            // 主力合约
            String futureCode = priceApplyEntity.getFutureCode();
            String dominantCode = futureCode + priceApplyEntity.getDominantCode() +
                    (PriceTypeEnum.PRICING.getValue() != priceApplyEntity.getType()
                            ? "->" + futureCode + priceApplyEntity.getTranferDominantCode()
                            : "");
            // 数量
            String applyHandNum = String.valueOf(CollectionUtil.isNotEmpty(requestDTO.getInstructStkList()) ? requestDTO.getInstructStkList().get(0).getAmt() : 0);

            // 点价 or 转月价差
            boolean isPending = PendingTypeEnum.PENDING.getValue() == priceApplyEntity.getPendingType();
            String applyPrice = "随盘";
            if (isPending) {
                applyPrice = String.valueOf(CollectionUtil.isNotEmpty(requestDTO.getInstructStkList()) ? requestDTO.getInstructStkList().get(0).getPrice() : 0);
            }

            String companyName = Optional.ofNullable(priceApplyEntity.getCompanyName()).orElse("");

            // 邮件正文
            String content = String.format(
                    "【NAV】航海家金仕达挂单接口出现错误，请转人工挂单\n%s %s %s %s %s手 %s 编号:%s %s",
                    priceType, orientation, dealDirection, dominantCode, applyHandNum, applyPrice, instructId, companyName
            );
            log.info("【KingStar】邮件内容: {}", content);

            // 发送邮件
            messageFacade.sendEmailInfo(kingStarNoticeEmail, title, content);

            log.info("【KingStar】发送失败邮件通知成功，指令ID: {}, 操作: {}", instructId, action);
        } catch (Exception e) {
            log.error("【KingStar】发送失败邮件通知异常，指令ID: {}, 操作: {}", instructId, action, e);
        }
    }
}
