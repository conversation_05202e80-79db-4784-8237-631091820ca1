package com.navigator.future.service.impl;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.dao.PriceGradeDao;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import com.navigator.future.service.IPriceGradeService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.dto.CategoryDTO;
import com.navigator.goods.pojo.vo.CategoryQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PriceGradeServiceImpl implements IPriceGradeService {

    @Resource
    PriceGradeDao priceGradeDao;

    @Resource
    private EmployFacade employFacade;

    @Autowired
    OperationLogFacade operationLogFacade;
    @Autowired
    private CategoryFacade categoryFacade;

    @Override
    public Boolean updatePriceGrade(PriceGradeEntity priceGradeEntity) {
        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        priceGradeEntity.setUpdatedAt(DateTimeUtil.now());
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(priceGradeEntity))
                    .setOperationActionEnum(OperationActionEnum.PRICE_GRADE_SETTING_UPDATE);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null == priceGradeEntity.getId()) {
            priceGradeEntity.setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue())
                    .setCreatedAt(DateTime.now())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    .setCreatedByName(name);
            return priceGradeDao.save(priceGradeEntity);
        } else {
            PriceGradeEntity gradeEntity = priceGradeDao.getById(priceGradeEntity.getId());
            gradeEntity.setBusinessCode(priceGradeEntity.getBusinessCode())
                    .setGradeStart(priceGradeEntity.getGradeStart())
                    .setGradeEnd(priceGradeEntity.getGradeEnd())
                    .setUpdatedAt(DateTimeUtil.now())
                    .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    .setUpdatedByName(name);
            return priceGradeDao.updateById(gradeEntity);
        }
    }

    @Override
    public List<PriceGradeEntity> queryPriceGradeList(QueryDTO<PriceGradeEntity> queryDTO) {
        //1、获取所有有效的二级品类集合
        List<CategoryDTO> categoryDTOList = categoryFacade.queryCategoryDTOList(new CategoryQO().setLevel(2).setStatus(DisableStatusEnum.ENABLE.getValue()));
        if (CollectionUtils.isEmpty(categoryDTOList)) {
            return new ArrayList<>();
        }
        List<PriceGradeEntity> priceGradeEntityList = priceGradeDao.queryPriceGradeList(queryDTO);
        Map<Integer, List<PriceGradeEntity>> priceGradeMapByCategory2 = priceGradeEntityList.stream().collect(Collectors.groupingBy(PriceGradeEntity::getCategoryId));
        List<PriceGradeEntity> priceGradeList = categoryDTOList.stream().map(categoryDTO -> {
                    Integer category2SerialNo = categoryDTO.getCategory2();
                    PriceGradeEntity priceGradeVO = new PriceGradeEntity()
                            .setCategoryId(category2SerialNo)
                            .setCategoryName(categoryDTO.getCategoryName2())
                            .setBusinessName(categoryDTO.getCategoryName2() + "客户申请夜盘")
                            .setBusinessCode(categoryDTO.getCode() + "_APPLY_NIGHT")
                            .setIsDeleted(IsDeletedEnum.NOT_DELETED.getValue());
                    if (!CollectionUtils.isEmpty(priceGradeMapByCategory2)) {
                        List<PriceGradeEntity> gradeEntityList = priceGradeMapByCategory2.get(category2SerialNo);
                        if (!CollectionUtils.isEmpty(gradeEntityList)) {
                            priceGradeVO.setId(gradeEntityList.get(0).getId())
                                    .setGradeStart(gradeEntityList.get(0).getGradeStart())
                                    .setGradeEnd(gradeEntityList.get(0).getGradeEnd())
                                    .setCreatedAt(gradeEntityList.get(0).getCreatedAt())
                                    .setCreatedBy(gradeEntityList.get(0).getCreatedBy())
                                    .setCreatedByName(gradeEntityList.get(0).getCreatedByName())
                                    .setUpdatedAt(gradeEntityList.get(0).getUpdatedAt())
                                    .setUpdatedBy(gradeEntityList.get(0).getUpdatedBy())
                                    .setUpdatedByName(gradeEntityList.get(0).getUpdatedByName())
                                    .setIsDeleted(gradeEntityList.get(0).getIsDeleted())
                            ;
                        }
                    }
                    return priceGradeVO;
                })
                .collect(Collectors.toList());
        return priceGradeList;
    }

    @Override
    public PriceGradeEntity queryPriceGradeByCode(String businessCode) {
        return priceGradeDao.queryPriceGradeByCode(businessCode);
    }
}
