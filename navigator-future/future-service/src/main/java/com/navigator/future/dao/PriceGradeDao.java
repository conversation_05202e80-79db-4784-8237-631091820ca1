package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.mapper.PriceGradeMapper;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Dao
public class PriceGradeDao extends BaseDaoImpl<PriceGradeMapper, PriceGradeEntity> {

    public List<PriceGradeEntity> queryPriceGradeList(QueryDTO<PriceGradeEntity> queryDTO) {
        return this.list(
                Wrappers.<PriceGradeEntity>lambdaQuery()
                        .eq(PriceGradeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .orderByDesc(PriceGradeEntity::getId)
        );
    }

    public PriceGradeEntity queryPriceGradeByCode(String businessCode) {
        List<PriceGradeEntity> gradeEntityList = this.list(
                Wrappers.<PriceGradeEntity>lambdaQuery()
                        .eq(PriceGradeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                        .eq(PriceGradeEntity::getBusinessCode, businessCode)
                        .orderByDesc(PriceGradeEntity::getId)
        );
        return CollectionUtils.isEmpty(gradeEntityList) ? null : gradeEntityList.get(0);
    }
}
