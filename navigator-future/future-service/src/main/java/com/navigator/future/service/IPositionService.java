package com.navigator.future.service;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.PositionDTO;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import com.navigator.future.pojo.vo.PositionVO;
import com.navigator.future.pojo.vo.RefreshQueryVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <p>
 * 持仓记录表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
public interface IPositionService{

    void save(PositionDTO positionDTO);

    Result queryPositionList(QueryDTO<PositionQueryDTO> positionQueryDTO);

    PositionVO queryPositionDetail(Integer id);

    void updatePositionStatus(PositionDTO positionDTO);

    void dealBatch(PositionDTO positionDTO);

    List<PositionVO> export(Integer categoryId);

    Result check(MultipartFile file);

    Result importFile(MultipartFile file);

    List<RefreshQueryVO> queryPositionRefreshLog(Integer positionId);

    void modify(PositionDTO positionDTO);

    void cancel(PositionDTO positionDTO);

    Result queryModifyLog(Integer positionId);
}
