package com.navigator.future.kingstar.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "")
public class KingStarSyncProperties {
    private List<SyncControlRule> syncControlRules;

    @Data
    public static class SyncControlRule {
        private String exchange;
        private String startTime;
        private String endTime;
        private List<BusinessTypeRule> businessTypes;
    }

    @Data
    public static class BusinessTypeRule {
        private String type;
        private boolean syncEnable;
        private String syncReleaseTime;
    }
}
