package com.navigator.future.service;


import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.ApplyContraryDTO;
import com.navigator.future.pojo.dto.NotDealDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.vo.PriceApplyExportVO;
import com.navigator.future.pojo.vo.PriceApplyVO;
import com.navigator.future.pojo.vo.RefreshQueryVO;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 点价申请表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface IPriceApplyService {

    /**
     * 查询申请单申请明细
     *
     * @param id
     * @return
     */
    PriceApplyVO getpriceApplyDetail(Integer id);

    /**
     * 申请点价/转月/反点价
     *
     * @param priceApplyDTO
     */
    int priceApply(PriceApplyDTO priceApplyDTO);

    /**
     * 申请改单/撤单  点价/转月/反点价
     *
     * @param priceApplyDTO
     */
    void modifyPriceApply(PriceApplyDTO priceApplyDTO);

    /**
     * 分页查询申请单信息
     *
     * @param applyBOQueryDTO
     * @param systemEnum      {@link SystemEnum}
     * @return
     */
    Result queryPriceApply(QueryDTO<PriceApplyBO> applyBOQueryDTO, SystemEnum systemEnum);

    /**
     * 分页查询申请单信息
     *
     * @param applyBOQueryDTO
     * @return
     */
    List<PriceApplyEntity> queryStructurePricingApply(QueryDTO<PriceApplyBO> applyBOQueryDTO);

    /**
     * 查询已点价转月数量
     *
     * @param priceApplyOperateNum
     * @return
     */
    List<PriceApplyEntity> queryPriceApplyOperateNum(PriceApplyOperateNumDTO priceApplyOperateNum);

    /**
     * 根据申请Id获取申请单
     *
     * @param applyId
     * @return
     */
    PriceApplyEntity getPriceApplyEntityById(String applyId);


    /**
     * 点价员申请单批量挂单
     *
     * @param ids
     * @return
     */
    Result batchPending(List<Integer> ids);

    /**
     * 确认成交 - 废弃
     *
     * @param priceApplyDTO
     */
    void priceApplyDeal(PriceApplyDTO priceApplyDTO);

    /**
     * 确认成交 - 新版
     */
    void priceApplyDealNew(PriceApplyDTO priceApplyDTO);

    /**
     * 批量不成交
     *
     * @param notDealDTOS
     * @return
     */
    Result batchNotDeal(List<NotDealDTO> notDealDTOS);

    /**
     * 查询申请单的合同分配明细
     *
     * @param applyId
     * @return
     */
    Result getAllocationDetails(String applyId);

    /**
     * 根据申请单id查询申请单信息
     *
     * @param id
     * @return
     */
    PriceApplyEntity queryPriceApplyById(Integer id);

    /**
     * magellan 查询点转反状态数量
     *
     * @param customerId
     * @param goodsCategoryId
     * @return
     */
    Result magellanApplyAllocateStatus(Integer customerId, Integer salesType, Integer goodsCategoryId);

    /**
     * columbus 查询点转反状态数量
     *
     * @param employId
     * @param goodsCategoryId
     * @return
     */
    Result columbusApplyAllocateStatus(Integer employId, Integer salesType, Integer goodsCategoryId);

    /**
     * 下载申请单
     *
     * @return
     */
    List<PriceApplyVO> exportPriceApply();

    /**
     * 校验上传数据
     *
     * @param uploadFile
     * @return
     */
    Result checkPriceApply(MultipartFile uploadFile);


    Result checkStructurePriceApply(MultipartFile file);

    /**
     * 上传点价成交数据
     *
     * @param uploadFile
     * @return
     */
    Result importPriceApply(MultipartFile uploadFile);


    Result importStructurePriceApply(MultipartFile file);

    /**
     * 可反点价量
     *
     * @param priceApplyDTO
     * @return
     */
    BigDecimal mayReversePricing(PriceApplyDTO priceApplyDTO);

    /**
     * 根据点价状态查询申请单(
     * orderByDesc(PriceApplyEntity::getType) -> 转月单在前)
     *
     * @param priceStatus 状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return
     */
    List<PriceApplyEntity> queryPriceApplyByPriceStatus(String priceStatus, String startTime, String endTime);

    /**
     * 根据合同id获取未成交记录
     *
     * @param contractId
     * @return
     */
    Boolean getNotDealByContractId(Integer contractId);

    /**
     * 根据合同id获取记录
     *
     * @param contractId
     * @return
     */
    Boolean judgeExistApply(Integer contractId);

    Boolean closePriceApplyByContractId(Integer contractId);

    List<PriceApplyEntity> getPriceApplyHasOperation(Integer customerId, String domainCode, Integer categoryId);


    List<PriceApplyExportVO> getPriceApplyExport(PriceApplyBO priceApplyBO);


    List<PriceApplyExportVO> getPriceApplyTheReportList(PriceApplyBO priceApplyBO);


    List<PriceApplyEntity> queryPriceApplyByContractId(Integer contractId);


    List<Integer> getValidStructureContractIds();

    List<RefreshQueryVO> queryPriceApplyRefreshLog(Integer priceApplyId);

    boolean priceApplyWithdraw(ApplyContraryDTO applyContraryDTO);
}
