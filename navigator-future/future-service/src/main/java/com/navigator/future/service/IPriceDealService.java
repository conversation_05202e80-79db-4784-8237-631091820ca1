package com.navigator.future.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.ApplyContraryDTO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.dto.PriceDealDetailDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.vo.PriceDealDetailVO;
import com.navigator.future.pojo.vo.PriceDealInfoVO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface IPriceDealService extends IService<PriceDealDetailEntity> {

    List<PriceDealDetailVO> queryPriceDealDetail(Integer priceApplyId);

    List<PriceDealDetailEntity> queryPriceDealDetailByApplyIdStatus(Integer priceApplyId, Integer status);

    List<PriceDealDetailEntity> queryPriceDealDetailByApplyId(Integer priceApplyId);

    /**
     * (合约量)根据申请单id查询成交记录
     *
     * @param priceApplyId
     * @return
     */
    List<PriceDealDetailEntity> queryDominantPriceDealDetail(Integer priceApplyId);

    String priceDealProcess(Integer priceApplyId, Date d);

    String priceDealProcess(PriceApplyEntity priceApplyEntity, Date d);

    String priceDealProcess(PriceApplyEntity priceApplyEntity);

    List<PriceDealInfoVO> queryPriceDealInfo(Integer priceApplyId);

    PriceDealDetailEntity priceDealDealDetailById(Integer priceDealDetailId);

    List<PriceDealDetailEntity> queryPriceDealDetailOperateNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO);

    /**
     * 生成成交单
     *
     * @param priceDealDetailDTO
     * @return
     */
    boolean savePriceDeal(PriceDealDetailDTO priceDealDetailDTO);

    Result queryPriceDealDetailIPage(QueryDTO<PriceApplyBO> applyBOQueryDTO, SystemEnum systemEnum);

    List<PriceDealDetailEntity> queryPriceDealListByPriceStatus(Integer status);

    /**
     * 结构化定价成交
     *
     * @param priceDealDetailDTOS
     * @return
     */
    boolean structuringPriceDeal(PriceDealDetailDTO priceDealDetailDTOS);

    void priceApplyMigration();

    boolean getPriceDealOrdersByDominantCode(String customerId, String domainCode, Integer categoryId);

    boolean priceDealDetailUpdateById(PriceDealDetailEntity priceDealDetailEntity);

    void sendNotDealInmail();

    /**
     * 根据id撤回成交单
     *
     * @param applyContraryDTO
     * @return
     */
    Boolean priceDealContrary(ApplyContraryDTO applyContraryDTO);

    boolean priceDealCancel(ApplyContraryDTO applyContraryDTO);

}
