package com.navigator.future.service;


import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PriceAllocateEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 点价分配表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface IPriceAllocateService {


    /**
     * 点、转 、反分配生成分配单
     *
     * @param distributionDTO
     * @return
     */
    Result genDistributionOrder(DistributionDTO distributionDTO);


    /**
     * 根据条件查询待审核的分配单（分页）
     *
     * @param queryDTO
     * @return
     */
    Result queryPriceAllocates(QueryDTO<QueryPriceAllocateDTO> queryDTO);

    /**
     * 审核点价和转月的分配单
     *
     * @return
     */
    Result auditPriceAllocate(AuditPriceAllocateDTO auditPriceAllocateDTO);

    Result auditPriceAllocateNew(AuditPriceAllocateDTO auditPriceAllocateDTO);

    /**
     * 分配明细查询
     *
     * @param applyCode
     * @return
     */
    Result getPriceAllocateDetail(String applyCode);

    /**
     * 判断该合约下是否有成交待分配的申请单和分配待审核的分配单
     *
     * @param dominantCode
     * @return
     */
    boolean judgeExistsAllocatesAndOrder(String customerId, String dominantCode, Integer categoryId);

    BigDecimal getSumPriceAllocateOfContract(String contractId);

    /**
     * 根据申请单id查询出分配单信息
     *
     * @param priceApplyId
     * @return
     */
    List<PriceAllocateEntity> getAllocateByPriceApplyId(Integer priceApplyId);

    List<PriceAllocateEntity> getAllocateByPriceApplyIdStatus(Integer priceApplyId);

    List<PriceAllocateEntity> getAllocateByDominantCode(Integer priceApplyId);

    PriceAllocateEntity getPriceAllocateById(String allocateId);


    /**
     * 审核通过 - 废弃
     *
     * @param priceTransferDTOs
     * @return
     */
    Result purchaseContractPriceTransfer(List<PriceTransferDTO> priceTransferDTOs);

    /**
     * 采购合同审核通过 - 新版
     *
     * @param priceTransferDTOs
     * @return
     */
    Result purchaseContractPriceTransferNew(List<PriceTransferDTO> priceTransferDTOs);

    /**
     * 采购反点价 - 废弃
     *
     * @param reverseDTO
     * @return
     */
    Result purchaseContractReverse(ReverseDTO reverseDTO);

    /**
     * 采购反点价 - 新版
     *
     * @param reverseDTO
     * @return
     */
    Result purchaseContractReverseNew(ReverseDTO reverseDTO);


    Result queryPriceAllocateRecord(QueryDTO<QueryPriceAllocateDTO> queryDTO);


    /**
     * 将合同下面待审核的分配单作废
     *
     * @param contractId
     * @param priceType
     * @return
     */
    boolean cancelPriceAllocateOfContract(String contractId, String priceType);


    boolean cancelPriceAllocateOfPriceAllId(PriceAllocateEntity priceAllocateEntity, DistributionDTO distributionDTO);


    /**
     * 已操作量
     *
     * @param priceApplyOperateNumDTO
     * @return
     */
    BigDecimal hasOperation(PriceApplyOperateNumDTO priceApplyOperateNumDTO);


    //查询申请单中的已操作量
    List<PriceAllocateEntity> getPriceAllocateNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO);

    List<PriceAllocateEntity> getPriceAllocateHasOperation(Integer customerId, String domainCode, Integer categoryId);

    /**
     * 查询未成交的分配单
     *
     * @param contractId
     * @return
     */
    List<PriceAllocateEntity> getNotDealByContractId(Integer contractId);

    /**
     * 根据合同查询分配单
     *
     * @param contractId
     * @return
     */
    List<PriceAllocateEntity> getByContractId(Integer contractId);

    /**
     * 根据结构化定价Id查询分配单
     *
     * @param contractStructureId
     * @return
     */
    List<PriceAllocateEntity> getAllocateByContractStructureId(Integer contractStructureId);

    /**
     * 分配单撤回
     *
     * @param applyContraryDTO
     * @return
     */
    Boolean priceAllocateContrary(ApplyContraryDTO applyContraryDTO);
}
