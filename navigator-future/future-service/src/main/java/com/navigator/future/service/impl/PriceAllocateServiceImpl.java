package com.navigator.future.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.*;
import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.dao.PriceAllocateDao;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.dao.PriceDealDetailDao;
import com.navigator.future.enums.AuditStatusEnum;
import com.navigator.future.enums.*;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.vo.PriceAllocateVO;
import com.navigator.future.service.IFuturesDomainService;
import com.navigator.future.service.IPriceAllocateService;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.future.service.IPriceDealService;
import com.navigator.trade.facade.*;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.contract.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.contract.ContractBackUpDTO;
import com.navigator.trade.pojo.dto.contract.ContractTransferDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.dto.future.ContraryPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTPriceDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesContractTTTransferDTO;
import com.navigator.trade.pojo.dto.tradeticket.TTDTO;
import com.navigator.trade.pojo.entity.*;
import com.navigator.trade.pojo.enums.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 点价分配表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
@Slf4j
public class PriceAllocateServiceImpl implements IPriceAllocateService {

    @Autowired
    private PriceApplyDao priceApplyDao;
    @Autowired
    private IPriceApplyService iPriceApplyService;
    @Autowired
    private ContractFacade contractFacade;
    @Autowired
    private PriceAllocateDao priceAllocateDao;
    @Autowired
    private ContractPriceFacade contractPriceFacade;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TtTranferFacade ttTranferFacade;
    @Autowired
    private TradeTicketFacade tradeTicketFacade;
    @Autowired
    private DomainCodeFacade domainCodeFacade;
    @Autowired
    private DeliveryTypeFacade deliveryTypeFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Resource
    private PriceDealDetailDao priceDealDetailDao;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private IPriceDealService iPriceDealService;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private IFuturesDomainService futuresDomainService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result genDistributionOrder(DistributionDTO distributionDTO) {
        // 获取道申请单
        String applyId = distributionDTO.getApplyId();


        PriceDealDetailEntity priceDealDetailEntity = null;

        //判断是否是反点价
        if (null != distributionDTO.getType() && PriceTypeEnum.REVERSE_PRICING.getValue() == distributionDTO.getType()) {
            List<PriceDealDetailEntity> priceDealDetailEntities = priceDealDetailDao.queryPriceDealDetail(Integer.parseInt(applyId));

            if (!priceDealDetailEntities.isEmpty()) {
                priceDealDetailEntity = priceDealDetailEntities.get(0);
            }
        } else {
            priceDealDetailEntity = priceDealDetailDao.getById(applyId);
        }

        if (null == priceDealDetailEntity) {
            throw new BusinessException(ResultCodeEnum.PRICE_DEAL_NOT_EXIST);
        }
        //1003031-Case-点价单重复-校验成交单状态 Date:20250312 By:wan Start
        //校验成交单状态
        if (PriceStatusEnum.WAIT_ALLOCATE.getValue() != priceDealDetailEntity.getStatus()) {
            throw new BusinessException(ResultCodeEnum.PRICE_DEAL_NOT_EXIST);
        }
        //1003031-Case-点价单重复-校验成交单状态 Date:20250312 By:wan end

        // 成交数量
        BigDecimal dealNum = priceDealDetailEntity.getDealNum();
        // 已经分配的数量
        BigDecimal allocateNum = priceDealDetailEntity.getAllocateNum();
        // 操作类型（1.点价 2.转月 3.反点价）
        String operationType = distributionDTO.getOperationType();
        // 剩余可以分配的数量
        BigDecimal remainAllocateNum = dealNum.subtract(allocateNum);

        List<DistributionDTO.Distribution> distributions = distributionDTO.getDistributions();
        // 本次总分配量
        BigDecimal sumAllocateNum = distributions.stream().map(DistributionDTO.Distribution::getAllocateNum).reduce(BigDecimal.ZERO, BigDecimal::add);

        if (BigDecimalUtil.isGreater(sumAllocateNum, remainAllocateNum)) {
            return Result.failure(ResultCodeEnum.TOTAL_AMOUNT_ALLOCATED_GREATER_THAN_REMAINING_DISTRIBUTABLE_AMOUNT.getMsg());
        }

        Integer userId = Integer.parseInt(JwtUtils.getCurrentUserId());
        // 统计新增分配单的数量
        int saveNum = 0;

        for (DistributionDTO.Distribution distribution : distributions) {
            log.info("==================>" + distribution.getContractId());
            ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(distribution.getContractId()));
            if (null == contractEntity) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
            }

            // 尾量合同不允许分配
            if (BigDecimalUtil.isGreater(contractEntity.getCloseTailNum(), BigDecimal.ZERO)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
            }

            if (distribution.getAllocateNum().compareTo(contractEntity.getContractNum()) > 0 && priceDealDetailEntity.getType() != PriceTypeEnum.REVERSE_PRICING.getValue()) {
                ContractEntity basicContractEntity = contractFacade.getBasicContractById(distributionDTO.getSubcontractId());

                contractFacade.updateContract(basicContractEntity
                        .setIsDeleted(IsDeletedEnum.DELETED.getValue())
                        .setRepeatContractCode(basicContractEntity.getRepeatContractCode() + "-fix"));
                throw new BusinessException("合同数量不够!无法成交");
            }

            // 如果多个合同，其中仅一个合同剩余可点和剩余可转月量  < 分配量 ， 那么是否全部回滚

            Integer auditStatus = AuditStatusEnum.WAIT_AUDIT.getValue();

            /*if (StrUtil.isNotBlank(distributionDTO.getUpdatePass())) {
                auditStatus = AuditStatusEnum.UPDATE_AUDIT.getValue();
            } else if (Integer.valueOf(operationType) != PriceTypeEnum.REVERSE_PRICING.getValue()) {
                auditStatus = AuditStatusEnum.AUDIT_PASS.getValue();
            }*/

            //分配单未通过数量
            BigDecimal allocateContractNum = priceAllocateDao.getSumPriceAllocateOfContract(distribution.getContractId());
            //合同量减去分配单未通过量 (合同已分配量)
            BigDecimal alreadyAllocateNum = contractEntity.getContractNum().subtract(allocateContractNum).add(contractEntity.getTotalPriceNum());
            //todo 塞入三级品类等信息
            PriceAllocateEntity priceAllocateEntity = new PriceAllocateEntity();

            priceAllocateEntity
                    .setCategory1(priceDealDetailEntity.getCategory1())
                    .setCategory2(priceDealDetailEntity.getCategory2())
                    .setCategory3(priceDealDetailEntity.getCategory3())
                    .setFutureCode(priceDealDetailEntity.getTranferFutureCode())
                    .setRawFutureCode(priceDealDetailEntity.getFutureCode())
                    .setBuCode(priceDealDetailEntity.getBuCode())
                    .setPriceDealId(priceDealDetailEntity.getId())
                    .setPriceApplyId(priceDealDetailEntity.getPriceApplyId())
                    .setPriceApplyCode(priceDealDetailEntity.getApplyCode())
                    .setStatus(Integer.valueOf(operationType) == PriceTypeEnum.REVERSE_PRICING.getValue() ? AllocateStatusEnum.AUDIT_PASS.getValue() : AllocateStatusEnum.WAIT_AUDIT.getValue())
                    .setCategoryId(priceDealDetailEntity.getCategoryId())
                    //.setCategoryName(priceDealDetailEntity.getCategoryName())
                    .setPriceApplyType(priceDealDetailEntity.getType())
                    .setType(BigDecimalUtil.isEqual(distribution.getAllocateNum(), alreadyAllocateNum) ? AllocateTypeEnum.ALL.getValue() : AllocateTypeEnum.PART.getValue()).setContractId(Integer.valueOf(distribution.getContractId()))
                    .setContractCode(contractEntity.getContractCode())
                    .setSiteCode(contractEntity.getSiteCode())
                    .setAllocateNum(distribution.getAllocateNum())
                    .setCustomerId(priceDealDetailEntity.getCustomerId())
                    .setCustomerName(priceDealDetailEntity.getCustomerName())
                    .setDominantCode(priceDealDetailEntity.getTranferDominantCode())
                    .setRawDominantCode(priceDealDetailEntity.getDominantCode())
                    .setAuditStatus(auditStatus)
                    .setSystem(distributionDTO.getSystem())
                    .setCompanyId(priceDealDetailEntity.getCompanyId())
                    .setCompanyName(priceDealDetailEntity.getCompanyName())
                    .setCreatedBy(userId)
                    .setUpdatedBy(userId);

            allocateNum = allocateNum.add(distribution.getAllocateNum());

            if (PriceTypeEnum.REVERSE_PRICING.getValue() == Integer.valueOf(operationType)) {
                priceAllocateEntity
                        .setAuditStatus(AuditStatusEnum.AUDIT_PASS.getValue())
                        .setSubcontractId(distributionDTO.getSubcontractId())
                        .setSubcontractCode(distributionDTO.getSubcontractCode());

            }

            if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == Integer.valueOf(operationType)) {
                priceAllocateEntity.setStructureContractId(priceDealDetailEntity.getStructureContractId());

                if (StrUtil.isEmpty(distributionDTO.getUpdatePass())) {
                    //结构化定价合同
                    ContractStructureEntity contractStructureEntity = contractFacade.getContractStructureById(priceDealDetailEntity.getStructureContractId());
                    if (null == contractStructureEntity) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_IS_NOT_EXIST);
                    }
                    //ContractStructureDTO contractStructureDTO = BeanConvertUtils.convert(ContractStructureDTO.class, contractStructureEntity);
                    contractStructureEntity.setCumulativeAllocateNum(contractStructureEntity.getCumulativeAllocateNum().add(distribution.getAllocateNum()));

                    contractFacade.updateStructureContract(contractStructureEntity);
                }

            }

            priceAllocateEntity.setBelongCustomerId(contractEntity.getBelongCustomerId())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode());

            if (null != distributionDTO.getSystem()) {
                priceAllocateEntity.setSource(SystemEnum.MAGELLAN.getValue() == distributionDTO.getSystem() ? AllocateSourceEnum.BUSINESS.getValue() : AllocateSourceEnum.CUSTOMER.getValue());
            }

            //自动分配
            if (StrUtil.isNotBlank(distributionDTO.getVoluntarily())) {
                priceAllocateEntity.setSource(AllocateSourceEnum.VOLUNTARILY.getValue());
            }
            boolean save = priceAllocateDao.save(priceAllocateEntity);
            if (save) {
                saveNum += 1;
            }

            try {
                recordOperationLog(
                        LogBizCodeEnum.PRICE_APPLY_ALLOCATE,
                        priceAllocateEntity.getPriceApplyId(),
                        priceAllocateEntity.getPriceApplyCode(),
                        JSON.toJSONString(priceAllocateEntity),
                        JSON.toJSONString(distributionDTO),
                        distributionDTO.getSystem());
            } catch (Exception e) {
                log.debug("记录日志错误,{}", e.getMessage());
            }

            if (null != distributionDTO.getUpdatePass()) {

                //通过分配单
                AuditPriceAllocateDTO auditPriceAllocateDTO = new AuditPriceAllocateDTO()
                        .setUpdatePass(distributionDTO.getUpdatePass())
                        .setAuditResult(String.valueOf(AllocateStatusEnum.AUDIT_PASS.getValue()))
                        .setPriceAllocateId(priceAllocateEntity.getId().toString())
                        .setContractId(distribution.getContractId());
                this.auditPriceAllocateNew(auditPriceAllocateDTO);

            }
            //priceAllocateDao.updateById(priceAllocateEntity);


            // 反点价操作生成定价单
            /*if (Integer.valueOf(operationType) == PriceTypeEnum.REVERSE_PRICING.getValue()) {
                TTPriceEntity ttPriceEntity = new TTPriceEntity();
                BeanUtil.copyProperties(priceAllocateEntity, ttPriceEntity);
                BeanUtil.copyProperties(contractEntity, ttPriceEntity);
                ttPriceEntity.setNum(priceAllocateEntity.getAllocateNum())
                        .setRemainPriceNum(contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()).subtract(priceAllocateEntity.getAllocateNum()))
                        .setOriginalPriceNum(priceAllocateEntity.getAllocateNum())
                        .setContractId(contractEntity.getId())
                        .setType(priceAllocateEntity.getPriceApplyType());
                contractPriceFacade.saveTtPrice(ttPriceEntity);
            }*/
        }

        priceDealDetailEntity.setAllocateNum(allocateNum)
                .setNotAllocateNum(priceDealDetailEntity.getDealNum().subtract(allocateNum));

        boolean update = priceDealDetailDao.updateById(priceDealDetailEntity);
        //boolean update = priceApplyDao.updateById(priceApplyEntity);

        return saveNum == distributions.size() && update ? Result.success() : Result.failure();
    }

    @Override
    public Result queryPriceAllocates(QueryDTO<QueryPriceAllocateDTO> queryDTO) {

        if (null != queryDTO.getCondition().getSystem() && SystemEnum.COLUMBUS.getValue() == queryDTO.getCondition().getSystem()) {
            CustomerEntity customerEntity = customerFacade.queryCustomerById(Integer.parseInt(queryDTO.getCondition().getCustomerId()));
            if (DisableStatusEnum.DISABLE.getValue() == customerEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
            }

            String currentUserId = JwtUtils.getCurrentUserId();
            CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(currentUserId));
            if (DisableStatusEnum.DISABLE.getValue() == cEmployEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
            }
        }

        IPage<PriceAllocateEntity> page = priceAllocateDao.queryPriceAllocates(queryDTO);

        List<PriceAllocateVO> priceAllocateVOS = page.getRecords().stream().map(priceAllocateEntity -> {

            //查询申请单
            PriceDealDetailEntity priceDealDetailEntity = priceDealDetailDao.getById(priceAllocateEntity.getPriceDealId());

            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceAllocateEntity.getPriceApplyId());
            if (null == priceDealDetailEntity) {
                //当成交单为空时查询申请单
                //申请单
                priceDealDetailEntity = BeanConvertUtils.convert(PriceDealDetailEntity.class, priceApplyEntity);

                priceDealDetailEntity.setDealPrice(priceApplyEntity.getTransactionPrice())
                        .setTranferDominantCode(priceApplyEntity.getTranferDominantCode())
                        .setTransactionDiffPrice(priceApplyEntity.getTransactionDiffPrice())
                        .setPriceApplyId(priceApplyEntity.getId());

            }

            // 合同
            ContractEntity contractEntity = contractFacade.getBasicContractById(priceAllocateEntity.getContractId());

            PriceAllocateVO priceAllocateVO = BeanConvertUtils.convert(PriceAllocateVO.class, priceAllocateEntity);
            if (null != contractEntity) {
                BeanUtil.copyProperties(contractEntity, priceAllocateVO);
                if (ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocateEntity.getSalesType()) {
                    priceAllocateVO.setCustomerId(priceAllocateEntity.getCustomerId().toString());
                    priceAllocateVO.setCustomerName(priceAllocateEntity.getCustomerName());
                }
                // 获取合同所属商务
                EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
                priceAllocateVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);
                priceAllocateVO.setBuCode(contractEntity.getBuCode());
            }
            if (ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocateEntity.getSalesType()) {
                priceAllocateVO.setPlatePrice(priceApplyEntity.getApplyPrice());
                if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()) {
                    priceAllocateVO.setTranferDominantCode(priceApplyEntity.getTranferDominantCode());
                    priceAllocateVO.setTranferFutureCode(priceApplyEntity.getTranferFutureCode());
                }
            } else {
                if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.PRICING.getValue() || priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.REVERSE_PRICING.getValue() || priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.STRUCTURE_PRICING.getValue()) {
                    priceAllocateVO.setPlatePrice(priceDealDetailEntity.getDealPrice());
                } else if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                    priceAllocateVO.setTranferDominantCode(priceDealDetailEntity.getTranferDominantCode());
                    priceAllocateVO.setTranferFutureCode(priceApplyEntity.getTranferFutureCode());
                    priceAllocateVO.setPlatePrice(priceDealDetailEntity.getTransactionDiffPrice());
                }
            }

            //case-1003161 根据合同id查询合同数据接口修改,校验子合同id是否为空 Author:Wan 2025-04-29 Start
            if (null != priceAllocateEntity.getSubcontractId()) {
                ContractEntity contractEntity1 = contractFacade.getBasicContractById(priceAllocateEntity.getSubcontractId());
                if (null != contractEntity1) {
                    priceAllocateVO.setSubcontractType(contractEntity1.getContractType());
                }
            }
            //case-1003161 根据合同id查询合同数据接口修改,校验子合同id是否为空 Author:Wan 2025-04-29 end

            if (AllocateStatusEnum.AUDIT_PASS.getValue() == priceAllocateEntity.getStatus()) {
                //获取协议状态
                Integer contractSignStatus = contractSignStatus(priceAllocateEntity);
                priceAllocateVO.setContractSignStatus(contractSignStatus);
            }

            //校验定价单是否可撤回
            Integer allowCancel = AllowCancelEnum.CANCEL.getValue();
            if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == priceAllocateEntity.getPriceApplyType()) {
                allowCancel = AllowCancelEnum.NOT_CANCEL.getValue();
            } else if (PriceTypeEnum.PRICING.getValue() == priceAllocateEntity.getPriceApplyType()) {
                allowCancel = allowCancel(priceAllocateEntity.getPriceApplyId());
            }
            priceAllocateVO.setAllowCancel(allowCancel);
            priceAllocateVO.setAllocateNum(priceAllocateEntity.getAllocateNum());
            priceAllocateVO.setAllocateId(String.valueOf(priceAllocateEntity.getId()));
            priceAllocateVO.setDealId(String.valueOf(priceDealDetailEntity.getId()));
            priceAllocateVO.setPriceApplyId(String.valueOf(priceAllocateEntity.getPriceApplyId()));
            priceAllocateVO.setStatus(priceAllocateEntity.getStatus());
            priceAllocateVO.setCreatedAt(priceApplyEntity.getCreatedAt());
            priceAllocateVO.setDealContraryNum(priceApplyEntity.getDealContraryNum());
            priceAllocateVO.setCompanyShortName(priceAllocateEntity.getCompanyName());
            priceAllocateVO.setAllocatesCreatedAt(priceAllocateEntity.getCreatedAt());
            priceAllocateVO.setPriceAppBy(priceDealDetailEntity.getCreatedBy());
            priceAllocateVO.setUpdatedAt(priceAllocateEntity.getUpdatedAt());
            String operationName = "";

            //查询分配人名称
            if (SystemEnum.MAGELLAN.getValue() == priceAllocateEntity.getSystem()) {
                operationName = employFacade.getEmployCache(priceAllocateEntity.getCreatedBy());
            } else {
                operationName = cEmployFacade.getEmployById(priceAllocateEntity.getCreatedBy()).getName();
            }
            priceAllocateVO.setOperationName(operationName);
            if (StringUtil.isEmpty(priceAllocateVO.getPriceApplyCode())) {
                priceAllocateVO.setPriceApplyCode(priceApplyEntity.getCode());
            }

            return priceAllocateVO;

        }).collect(Collectors.toList());

        return Result.page(page, priceAllocateVOS);
    }

    private Integer contractSignStatus(PriceAllocateEntity priceAllocateEntity) {
        //查询合同协议状态
        Integer TTid = 0;
        if (PriceTypeEnum.TRANSFER_MONTH.getValue() == priceAllocateEntity.getPriceApplyType()
                || PriceTypeEnum.REVERSE_PRICING.getValue() == priceAllocateEntity.getPriceApplyType()) {
            //转月,反点价协议状态
            if (ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntity.getSalesType()) {
                List<TTTranferEntity> ttTranferEntity = ttTranferFacade.selectTTTranferByPriceAllocateId(priceAllocateEntity.getId());
                if (!ttTranferEntity.isEmpty()) {
                    TTid = ttTranferEntity.get(0).getTtId();
                }
            } else {
                List<TTTranferEntity> ttTranferEntity = ttTranferFacade.getTTTranferByPriceApplyId(priceAllocateEntity.getPriceApplyId());
                if (!ttTranferEntity.isEmpty()) {
                    TTid = ttTranferEntity.get(0).getTtId();
                }
            }
        } else {
            //定价协议状态
            if (ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntity.getSalesType()) {
                TTPriceEntity ttPriceEntity = contractFacade.getTTPriceByAllocateId(priceAllocateEntity.getId());
                TTid = null == ttPriceEntity ? 0 : ttPriceEntity.getTtId();
            } else {
                List<TTPriceEntity> ttPriceEntity = contractFacade.getTTPriceByApplyId(priceAllocateEntity.getPriceApplyId());
                if (!ttPriceEntity.isEmpty()) {
                    TTid = ttPriceEntity.get(0).getTtId();
                }
            }
        }
        ContractSignEntity contractSignEntity = contractSignFacade.getContractSignDetailByTtId(TTid);

        return contractSignEntity == null || contractSignEntity.getId() == null ? ContractSignStatusEnum.PROCESSING.getValue() : contractSignEntity.getStatus();
    }

    public Integer allowCancel(Integer priceApplyId) {
        //协议状态待回签后不允许撤回
        List<PriceAllocateEntity> priceAllocateEntityList = priceAllocateDao.getAllocateByPriceApplyIdStatus(priceApplyId);
        for (PriceAllocateEntity priceAllocateEntity : priceAllocateEntityList) {
            ContractEntity contractEntity = contractFacade.getBasicContractById(priceAllocateEntity.getContractId());
            if (ContractTypeEnum.JI_CHA.getValue() != contractEntity.getContractType()
                    && ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue() != contractEntity.getContractType()
            ) {
                return AllowCancelEnum.NOT_CANCEL.getValue();
            }
            if (ContractStatusEnum.EFFECTIVE.getValue() != contractEntity.getStatus()) {
                return AllowCancelEnum.NOT_CANCEL.getValue();
            }
            TTPriceEntity ttPriceEntity = contractFacade.getTTPriceByAllocateId(priceAllocateEntity.getId());
            if (null != ttPriceEntity && ttPriceEntity.getNum().compareTo(BigDecimal.ZERO) == 0) {
                return AllowCancelEnum.NOT_CANCEL.getValue();
            }
        }
        return AllowCancelEnum.CANCEL.getValue();
    }

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Result auditPriceAllocate(AuditPriceAllocateDTO auditPriceAllocateDTO) {
        String auditId = JwtUtils.getCurrentUserId();
        EmployEntity employEntity = employFacade.getEmployById(Integer.valueOf(auditId));
        // 合同Id
        String contractId = auditPriceAllocateDTO.getContractId();

        // 分配单
        PriceAllocateEntity priceAllocateEntity = priceAllocateDao.getPriceAllocateByIdStatus(Integer.valueOf(auditPriceAllocateDTO.getPriceAllocateId()), AllocateStatusEnum.WAIT_AUDIT.getValue());

        if (null == priceAllocateEntity) {
            throw new BusinessException(ResultCodeEnum.PRICE_ALLOCATE_NOT_EXIST);
        }

        if (AuditStatusEnum.IN_OPERATION.getValue() == priceAllocateEntity.getAuditStatus()) {
            return null;
        } else {
            //修改分配单状态(审核操作中)
            priceAllocateEntity.setAuditStatus(AuditStatusEnum.IN_OPERATION.getValue());
            priceAllocateDao.updateById(priceAllocateEntity);
        }
        CustomerDTO customerDTO = customerFacade.getCustomerById(priceAllocateEntity.getCustomerId());

        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }


        //PriceApplyEntity priceApplyEntity = iPriceApplyService.getPriceApplyEntityById(String.valueOf(priceAllocateEntity.getPriceApplyId()));
        //根据成交单id查询成交单
        PriceDealDetailEntity priceDealDetailEntity = priceDealDetailDao.getById(priceAllocateEntity.getPriceDealId());

        // 审核结果
        String auditResult = auditPriceAllocateDTO.getAuditResult();
        // if 通过  else if 驳回
        boolean priceAllocateUpdate = false;
        if (Integer.valueOf(auditResult) == AllocateStatusEnum.AUDIT_PASS.getValue()) {
            priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());

            ContractEntity contractEntity = contractFacade.getContractById(Integer.valueOf(contractId));

            if (StrUtil.isNotBlank(contractEntity.getLkgContractException())) {
                throw new BusinessException(ResultCodeEnum.GET_LKG_CONTRACT_EXCEPTION);
            }

            PriceDetailBO priceDetailBO = new PriceDetailBO();
            ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(Integer.valueOf(contractId));
            BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
            TTDTO ttdto = new TTDTO();

            if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.PRICING.getValue() || priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.STRUCTURE_PRICING.getValue()) {

                // 初始点价量
                BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum();
                // 新的点价量
                BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalPriceNum, priceAllocateEntity.getAllocateNum());
                if (contractEntity.getContractNum().compareTo(newTotalPriceNum) < 0) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_INSUFFICIENT);
                }

                contractEntity.setTotalPriceNum(newTotalPriceNum);
                // 修改合同的已点量 【（未校验 oldTotalPriceNum 是否变化）】
                contractFacade.updateContract(contractEntity);

                //点价
                SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
                BeanUtil.copyProperties(priceAllocateEntity, salesContractTTPriceDTO);

                //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                salesContractTTPriceDTO
                        .setCategory1(contractEntity.getCategory1())
                        .setCategory2(contractEntity.getCategory2())
                        .setCategory3(contractEntity.getCategory3())
                        //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end
                        .setType(priceAllocateEntity.getPriceApplyType())
                        .setRemainPriceNum(contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()))
                        .setOriginalPriceNum(priceAllocateEntity.getAllocateNum())
                        .setThisContractNum(contractEntity.getContractNum())
                        .setUnitPrice(contractEntity.getUnitPrice())
                        .setTotalPriceNum(contractEntity.getTotalPriceNum())
                        .setPriceEndType(contractEntity.getPriceEndType())
                        .setPriceEndTime(contractEntity.getPriceEndTime())
                        .setOwnerId(contractEntity.getOwnerId())
                        .setGoodsPackageId(priceAllocateEntity.getCategoryId())
                        .setContractId(Integer.valueOf(contractId))
                        .setTempPrice(contractEntity.getTemporaryPrice())
                        .setDiffPrice(contractEntity.getExtraPrice())
                        .setNum(priceAllocateEntity.getAllocateNum())
                        .setPrice(priceDealDetailEntity.getDealPrice())
                        .setTransactionPrice(priceDealDetailEntity.getDealPrice())
                        .setUserId(JwtUtils.getCurrentUserId())
                        .setAllocateId(priceAllocateEntity.getId())
                        .setContractType(contractEntity.getContractType())
                        .setGoodsId(contractEntity.getGoodsId())
                        .setGoodsSpecId(contractEntity.getGoodsSpecId())
                        .setGoodsPackageId(contractEntity.getGoodsPackageId())
                        .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                        .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                        .setCustomerId(contractEntity.getCustomerId())
                        .setSupplierId(contractEntity.getSupplierId())
                        .setCustomerName(contractEntity.getCustomerName())
                        .setGoodsCategoryId(contractEntity.getGoodsCategoryId())
                        .setCustomerCode(contractEntity.getCustomerCode())
                        .setSupplierName(contractEntity.getSupplierName())
                        .setSalesType(contractEntity.getSalesType())
                        .setSourceContractId(contractEntity.getId())
                        .setDomainCode(contractEntity.getDomainCode())
                ;
                salesContractTTPriceDTO.setBelongCustomerId(contractEntity.getBelongCustomerId());

                ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

                //String processorType = this.processorType(priceAllocateEntity.getCategoryId(), contractEntity.getSalesType(), PriceTypeEnum.PRICING.getValue());
                String processorType = TTHandlerUtil.getTTProcessor(contractEntity.getSalesType(), TTTypeEnum.PRICE.getType(), priceAllocateEntity.getCategoryId());

                ttdto.setProcessorType(processorType);
                ttdto.setPriceDetailBO(priceDetailBO);

                log.info("======purchaseContractPriceTransfer.ttdto:{}", JSON.toJSONString(ttdto));


                try {
                    Result result1 = tradeTicketFacade.saveTT(ttdto);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        //log.error("result1:{}", JSON.toJSONString(result1));
                        //新增TT失败合同定价量回滚
                        contractEntity.setTotalPriceNum(oldTotalPriceNum);
                        contractFacade.updateContract(contractEntity);
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }
                } catch (Exception e) {

                    //新增TT失败合同定价量回滚
                    contractEntity.setTotalPriceNum(oldTotalPriceNum);
                    contractFacade.updateContract(contractEntity);

                    log.error("AuditPriceAllocateDTO:新增TT:{}", e.getMessage());
                    throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                }
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), newTotalPriceNum)) {
                    // 全部定价后：【期货价格】需更新为：合同所有点价单单价（不加基差价）的加权平均价
                    contractEntity = updateSalesContractForwardPrice(contractEntity, contractEntity.getContractNum(), newTotalPriceNum);
                    contractFacade.updateAndBackUpContract(new ContractBackUpDTO()
                            .setContractEntity(contractEntity)
                            .setBackTradeType(String.valueOf(ContractTradeTypeEnum.PRICE.getValue())));
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                }


            } /*else if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.STRUCTURE_PRICING.getValue()) {
                SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
                salesContractTTPriceDTO.setType(PriceTypeEnum.STRUCTURE_PRICING.getValue());
                BeanUtil.copyProperties(priceAllocateEntity, salesContractTTPriceDTO);
                salesContractTTPriceDTO
                        .setOwnerId(contractEntity.getOwnerId())
                        .setPrice(priceDealDetailEntity.getTransactionDiffPrice())
                        .setNum(priceAllocateEntity.getAllocateNum())
                        .setTransactionPrice(priceDealDetailEntity.getTransactionDiffPrice())
                        .setTempPrice(contractEntity.getTemporaryPrice())
                        .setUserId(JwtUtils.getCurrentUserId())
                        .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                        .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                        .setAllocateId(priceAllocateEntity.getId())
                        .setContractId(Integer.valueOf(contractId))
                        .setGoodsPackageId(priceAllocateEntity.getCategoryId())
                        .setDiffPrice(contractEntity.getExtraPrice())
                        .setSalesType(contractEntity.getSalesType())
                        .setSourceContractId(contractEntity.getId())
                ;
                ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

                //String processorType = this.processorType(priceAllocateEntity.getCategoryId(), contractEntity.getSalesType(), PriceTypeEnum.STRUCTURE_PRICING.getValue());
                String processorType = TTHandlerUtil.getTTProcessor(contractEntity.getSalesType(), TTTypeEnum.STRUCTURE_PRICE.getType(), priceAllocateEntity.getCategoryId());
                ttdto.setProcessorType(processorType);
                ttdto.setPriceDetailBO(priceDetailBO);

                Result result1 = tradeTicketFacade.saveTT(ttdto);
                log.info("======purchaseContractPriceTransfer.ttdto:{}", JSON.toJSONString(ttdto));
                if (result1 != null && result1.getCode() != ResultCodeEnum.OK.getCode()) {
                    log.error("result1:{}", JSON.toJSONString(result1));
                    throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                }

                // 初始点价量
                BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum();
                // 新的点价量
                BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalPriceNum, priceAllocateEntity.getAllocateNum());
                contractEntity.setTotalPriceNum(newTotalPriceNum);

                // 修改合同的已点量 【（未校验 oldTotalPriceNum 是否变化）】
                contractFacade.updateContract(contractEntity);

            }*/ else if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.TRANSFER_MONTH.getValue()) {


                // 全部转月
                // 1、生成转月单tt
                // 2、转月补充协议
                // 3、更新原合同
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), priceAllocateEntity.getAllocateNum())) {
                    SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
                    BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);
                    ContractEntity newContractEntity = new ContractEntity();
                    BeanUtils.copyProperties(contractEntity, newContractEntity);

                    // 全部转月原合同：取修改日期前（不含签订日期当天）23:00最近一次此品种、合约的收盘价上传记录【流程启动的时候更新】
                    Result result = domainCodeFacade.getLastestClosingPrice(newContractEntity.getGoodsCategoryId(), priceAllocateEntity.getDominantCode(), new Date(), newContractEntity.getFutureCode());
                    if (result.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(result.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            priceDetailBO.setForwardPrice(domainPriceEntity.getPrice());
                        }
                    }
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                    //重新计算价格
                    recalculatePrice(contractEntity.getContractNum(), priceDealDetailEntity, newContractEntity, priceDetailBO, contractPriceEntity, salesContractTTTransferDTO);
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                    salesContractTTTransferDTO
                            .setCategory1(contractEntity.getCategory1())
                            .setCategory2(contractEntity.getCategory2())
                            .setCategory3(contractEntity.getCategory3())
                            //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end
                            .setContractType(contractEntity.getContractType())
                            .setPriceApplyId(priceAllocateEntity.getPriceApplyId())
                            .setPriceAllocateId(priceAllocateEntity.getId())
                            .setNum(priceAllocateEntity.getAllocateNum())
                            .setCategoryId(priceAllocateEntity.getCategoryId())
                            .setGoodsPackageId(contractEntity.getGoodsPackageId())
                            //1002884-Case-货物名称显示错误 Date:20241227 By:wan Start
                            .setGoodsSpecId(contractEntity.getGoodsSpecId())
                            //1002884-Case-货物名称显示错误 Date:20241227 By:wan end
                            .setOriginalDomainCode(contractEntity.getDomainCode())
                            .setDiffPrice(contractEntity.getExtraPrice())
                            .setPrice(priceDealDetailEntity.getTransactionDiffPrice())
                            .setTempPrice(contractEntity.getTemporaryPrice())
                            .setContractId(Integer.valueOf(contractId))
                            .setSonContractId(Integer.valueOf(contractId))
                            .setTotalAmount(newContractEntity.getTotalAmount())
                            .setCifUnitPrice(newContractEntity.getCifUnitPrice())
                            .setFobUnitPrice(newContractEntity.getFobUnitPrice())
                            .setUnitPrice(newContractEntity.getUnitPrice())
                            .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                            .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                            .setType(TTTranferTypeEnum.TRANSFER_MONTH.getValue())
                            .setSalesType(contractEntity.getSalesType())
                            .setContractSource(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue())
                            .setDomainCode(priceAllocateEntity.getDominantCode())
                            .setTradeType(ContractTradeTypeEnum.TRANSFER_ALL.getValue())
                            .setSourceContractId(Integer.valueOf(contractId))
                    ;
                    // 点价截止日期
                    setPriceEndTime(newContractEntity, newContractEntity.getDeliveryStartTime(), priceAllocateEntity.getDominantCode());
                    salesContractTTTransferDTO.setPriceEndType(newContractEntity.getPriceEndType());
                    salesContractTTTransferDTO.setPriceEndTime(newContractEntity.getPriceEndTime());
                    salesContractTTTransferDTO.setBelongCustomerId(newContractEntity.getBelongCustomerId());
                    ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
                    ttdto.setPriceDetailBO(priceDetailBO);

                    //String processorType = this.processorType(priceAllocateEntity.getCategoryId(), contractEntity.getSalesType(), PriceTypeEnum.TRANSFER_MONTH.getValue());
                    String processorType = TTHandlerUtil.getTTProcessor(contractEntity.getSalesType(), TTTypeEnum.TRANSFER.getType(), priceAllocateEntity.getCategoryId());
                    ttdto.setProcessorType(processorType);

                    log.info("======purchaseContractPriceTransfer.ttdto:{}", JSON.toJSONString(ttdto));

                    Result result1 = tradeTicketFacade.saveTT(ttdto);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        log.error("result1:{}", JSON.toJSONString(result1));
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }

                    // 扣除父合同的转月次数
                    if (newContractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                        CustomerDetailBO customerDetailBO = new CustomerDetailBO();
                        customerDetailBO.setCustomerId(contractEntity.getCustomerId());
                        customerDetailBO.setCategory2(String.valueOf(contractEntity.getCategory2()));
                        customerDetailBO.setCategory3(String.valueOf(contractEntity.getCategory3()));
                        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

                        if (!customerDetailEntityList.isEmpty()) {
                            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
                            Integer totalTransferTimes = newContractEntity.getTotalTransferTimes();
                            if (customerDetailEntity != null) {
                                // 普通用户考虑超远期合同
                                if (customerDetailEntity.getIsWhiteList() == 0) {
                                    // 判断是否是超远期合同
                                    if (contractEntity.getIsOverForward() == 1) {
                                        totalTransferTimes = Math.max(totalTransferTimes, 2);
                                    } else {
                                        totalTransferTimes = Math.max(totalTransferTimes, 1);
                                    }
                                }
                            }

                            newContractEntity
                                    .setTransferredTimes(newContractEntity.getTransferredTimes() + 1)
                                    .setAbleTransferTimes(Math.max(totalTransferTimes - newContractEntity.getTransferredTimes(), 0))
                                    .setTotalTransferTimes(totalTransferTimes);
                        }

                    }

                    // 更新合同信息
                    newContractEntity
                            .setTotalAmount(salesContractTTTransferDTO.getTotalAmount())
                            .setCifUnitPrice(salesContractTTTransferDTO.getCifUnitPrice())
                            .setFobUnitPrice(salesContractTTTransferDTO.getFobUnitPrice())
                            .setUnitPrice(salesContractTTTransferDTO.getUnitPrice())
                            .setDomainCode(priceAllocateEntity.getDominantCode())
                            .setExtraPrice(BigDecimalUtil.add(CalcTypeEnum.PRICE, contractEntity.getExtraPrice(), priceDealDetailEntity.getTransactionDiffPrice()))
                            .setContractNum(priceAllocateEntity.getAllocateNum())
                            .setTotalTransferNum(BigDecimal.ZERO)
                            .setStatus(ContractStatusEnum.MODIFYING.getValue())
                            .setTradeType(ContractTradeTypeEnum.TRANSFER_ALL.getValue())
                            .setUpdatedAt(DateTimeUtil.now());
                    contractFacade.updateContract(newContractEntity);

                    priceAllocateEntity
                            .setSubcontractId(newContractEntity.getId())
                            .setSubcontractCode(newContractEntity.getContractCode())
                            .setBelongCustomerId(newContractEntity.getBelongCustomerId())
                            .setDeliveryFactoryCode(newContractEntity.getDeliveryFactoryCode());
                    //1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息,不需要更新原合同价格) Date:20241023 By:NanaHou
//                    contractPriceEntity.setExtraPrice(priceDetailBO.getExtraPrice());
//                    contractPriceEntity.setFee(priceDetailBO.getFee());
//                    contractPriceEntity.setForwardPrice(priceDetailBO.getForwardPrice());
//                    contractPriceEntity.setTransportPrice(priceDetailBO.getTransportPrice());
//                    contractPriceFacade.updatePriceByContractId(contractPriceEntity);
                } else {

                    if (contractEntity.getContractNum().compareTo(priceAllocateEntity.getAllocateNum()) < 0) {
                        throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_INSUFFICIENT);
                    }
                    //部分转月
                    SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
                    BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);

                    ContractEntity newContractEntity = new ContractEntity();
                    BeanUtils.copyProperties(contractEntity, newContractEntity);
                    newContractEntity.setParentId(contractEntity.getId());
                    newContractEntity.setRootId(contractEntity.getParentId() == 0 ? contractEntity.getId() : contractEntity.getParentId());
                    newContractEntity.setContractNum(priceAllocateEntity.getAllocateNum());
                    priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                    // 新合同的签订日期
                    Date signDate = new Date();

                    // 部分转月更新期货价格
                    Result domainResult = domainCodeFacade.getLastestClosingPrice(newContractEntity.getGoodsCategoryId(), priceAllocateEntity.getDominantCode(), signDate, newContractEntity.getFutureCode());
                    if (domainResult.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(domainResult.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            priceDetailBO.setForwardPrice(domainPriceEntity.getPrice());
                        }
                    }

                    //重新计算价格
                    recalculatePrice(priceAllocateEntity.getAllocateNum(), priceDealDetailEntity, newContractEntity, priceDetailBO, contractPriceEntity, salesContractTTTransferDTO);

                    // 扣除子合同的转月次数
                    if (newContractEntity.getSalesType().equals(ContractSalesTypeEnum.SALES.getValue())) {
                        newContractEntity.setAbleTransferTimes(Math.max(newContractEntity.getAbleTransferTimes() - 1, 0))
                                .setTransferredTimes(newContractEntity.getTransferredTimes() + 1);
                    }

                    // 点价截止日期
                    setPriceEndTime(newContractEntity, newContractEntity.getDeliveryStartTime(), priceAllocateEntity.getDominantCode());

                    //签订日期
                    newContractEntity.setSignDate(signDate);
                    // 基差价格
                    newContractEntity.setExtraPrice(priceDetailBO.getExtraPrice());

                    ContractTransferDTO contractTransferDTO = new ContractTransferDTO();
                    contractTransferDTO
                            .setTtTranferType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                            .setContractEntity(newContractEntity)
                            .setTransferNum(priceAllocateEntity.getAllocateNum())
                            .setDomainCode(priceAllocateEntity.getDominantCode())
                    ;
                    Result result = contractFacade.createSonContract(contractTransferDTO);
                    ObjectMapper mapper = new ObjectMapper();
                    ContractEntity sonContractProcessorEntity = mapper.convertValue(result.getData(), ContractEntity.class);
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                    salesContractTTTransferDTO
                            .setCategory1(contractEntity.getCategory1())
                            .setCategory2(contractEntity.getCategory2())
                            .setCategory3(contractEntity.getCategory3())
                            //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end
                            .setContractType(contractEntity.getContractType())
                            .setPriceApplyId(priceAllocateEntity.getPriceApplyId())
                            .setPriceAllocateId(priceAllocateEntity.getId())
                            .setFobUnitPrice(newContractEntity.getFobUnitPrice())
                            .setCifUnitPrice(newContractEntity.getCifUnitPrice())
                            .setUnitPrice(newContractEntity.getUnitPrice())
                            .setTotalAmount(newContractEntity.getTotalAmount())
                            .setType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                            .setNum(priceAllocateEntity.getAllocateNum())
                            .setSonContractId(sonContractProcessorEntity.getId())
                            //1002884-Case-货物名称显示错误 Date:20241227 By:wan Start
                            .setGoodsPackageId(contractEntity.getGoodsPackageId())
                            .setGoodsSpecId(contractEntity.getGoodsSpecId())
                            //1002884-Case-货物名称显示错误 Date:20241227 By:wan end
                            .setContractCode(sonContractProcessorEntity.getContractCode())
                            .setOriginalDomainCode(contractEntity.getDomainCode())
                            .setTempPrice(contractEntity.getTemporaryPrice())
                            .setDiffPrice(contractEntity.getExtraPrice())
                            .setContractId(Integer.valueOf(contractId))
                            .setPrice(priceDealDetailEntity.getTransactionDiffPrice())
                            .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                            .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                            .setSignDate(signDate)
                            .setPriceEndTime(newContractEntity.getPriceEndTime())
                            .setPriceEndType(newContractEntity.getPriceEndType())
                            .setSalesType(contractEntity.getSalesType())
                            .setDomainCode(priceAllocateEntity.getDominantCode())
                            .setContractSource(ContractActionEnum.TRANSFER_CONFIRM.getActionValue())
                            .setTradeType(ContractTradeTypeEnum.TRANSFER_PART.getValue())
                            .setSourceContractId(Integer.parseInt(contractId))
                    ;
                    salesContractTTTransferDTO.setBelongCustomerId(newContractEntity.getBelongCustomerId());

                    ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);

                    //String processorType = this.processorType(priceAllocateEntity.getCategoryId(), contractEntity.getSalesType(), PriceTypeEnum.TRANSFER_MONTH.getValue());
                    String processorType = TTHandlerUtil.getTTProcessor(contractEntity.getSalesType(), TTTypeEnum.TRANSFER.getType(), priceAllocateEntity.getCategoryId());
                    ttdto.setProcessorType(processorType);
                    ttdto.setPriceDetailBO(priceDetailBO);
                    log.info("======purchaseContractPriceTransfer.ttdto:{}", JSON.toJSONString(ttdto));
                    Result result1 = tradeTicketFacade.saveTT(ttdto);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        log.error("result1:{}", JSON.toJSONString(result1));
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }

                    //更改原合同
                    // 初始转月量
                    BigDecimal oldTotalTransferNum = contractEntity.getTotalTransferNum();
                    // 新的点价量
                    BigDecimal newTotalTransferNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalTransferNum, priceAllocateEntity.getAllocateNum());
                    BigDecimal newContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), priceAllocateEntity.getAllocateNum());
                    // 转月导致的原合同全部定价
                    contractEntity = updateSalesContractForwardPrice(contractEntity, newContractNum, contractEntity.getTotalPriceNum());

                    contractEntity
                            .setTotalAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getUnitPrice(), newContractNum))
                            .setDepositAmount(BigDecimalUtil.multiply(CalcTypeEnum.PRICE, contractEntity.getTotalAmount(), BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)))
                            .setTotalTransferNum(newTotalTransferNum)
                            .setContractNum(newContractNum);

                    priceAllocateEntity
                            .setSubcontractId(sonContractProcessorEntity.getId())
                            .setSubcontractCode(sonContractProcessorEntity.getContractCode())
                            .setBelongCustomerId(sonContractProcessorEntity.getBelongCustomerId())
                            .setDeliveryFactoryCode(sonContractProcessorEntity.getDeliveryFactoryCode());

                    // 原合同处于修改中
                    contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());

                    contractFacade.updateContract(contractEntity);
                }
            }
            // 更新分配单
            priceAllocateEntity.setStatus(Integer.valueOf(auditResult));
            priceAllocateEntity.setReviewerId(Integer.valueOf(auditId));
            priceAllocateEntity.setAuditStatus(StrUtil.isNotBlank(auditPriceAllocateDTO.getUpdatePass()) ? AuditStatusEnum.UPDATE_AUDIT.getValue() : AuditStatusEnum.AUDIT_PASS.getValue());
            priceAllocateEntity.setReviewerName(employEntity != null ? employEntity.getRealName() : null);
            priceAllocateEntity.setUpdatedAt(new Date())
                    .setAuditTime(new Date());
            priceAllocateUpdate = priceAllocateDao.updateById(priceAllocateEntity);

            try {
                recordOperationLog(LogBizCodeEnum.PRICE_APPLY_ALLOCATE_PASS,
                        priceAllocateEntity.getPriceApplyId(),
                        priceAllocateEntity.getPriceApplyCode(),
                        JSON.toJSONString(priceAllocateEntity),
                        JSON.toJSONString(auditPriceAllocateDTO),
                        SystemEnum.MAGELLAN.getValue());
            } catch (Exception e) {
                log.debug("记录日志错误,{}", e.getMessage());
            }

        } else if (Integer.valueOf(auditResult) == AllocateStatusEnum.AUDIT_REJECT.getValue()) {
            // 将分配单上的分配量还到申请单上
            BigDecimal allocateNum = priceAllocateEntity.getAllocateNum();
            Integer priceApplyId = priceAllocateEntity.getPriceApplyId();
//            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceApplyId);
            priceDealDetailEntity.setAllocateNum(BigDecimalUtil.subtract(CalcTypeEnum.COUNT, priceDealDetailEntity.getAllocateNum(), allocateNum));

            priceDealDetailDao.updateById(priceDealDetailEntity);
            //priceApplyDao.updateById(priceApplyEntity);
            // 更新分配单
            priceAllocateEntity.setStatus(Integer.valueOf(auditResult));
            priceAllocateEntity.setAuditReason(auditPriceAllocateDTO.getAuditResult());
            priceAllocateEntity.setReviewerId(Integer.valueOf(auditId));
            priceAllocateEntity.setReviewerName(employEntity != null ? employEntity.getRealName() : null);
            priceAllocateEntity.setUpdatedAt(new Date());
            priceAllocateEntity.setAuditStatus(AuditStatusEnum.AUDIT_PASS.getValue());
            priceAllocateUpdate = priceAllocateDao.updateById(priceAllocateEntity);
        }
        return priceAllocateUpdate ? Result.success() : Result.failure();
    }

    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Result auditPriceAllocateNew(AuditPriceAllocateDTO auditPriceAllocateDTO) {
        String auditId = JwtUtils.getCurrentUserId();
        EmployEntity employEntity = employFacade.getEmployById(Integer.valueOf(auditId));
        // 合同Id
        String contractId = auditPriceAllocateDTO.getContractId();
        ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(contractId));

        // 分配单
        PriceAllocateEntity priceAllocateEntity = priceAllocateDao.getPriceAllocateByIdStatus(Integer.valueOf(auditPriceAllocateDTO.getPriceAllocateId()), AllocateStatusEnum.WAIT_AUDIT.getValue());

        if (null == priceAllocateEntity) {
            throw new BusinessException(ResultCodeEnum.PRICE_ALLOCATE_NOT_EXIST);
        }

        if (AuditStatusEnum.IN_OPERATION.getValue() == priceAllocateEntity.getAuditStatus()) {
            return null;
        } else {
            //修改分配单状态(审核操作中)
            priceAllocateEntity.setAuditStatus(AuditStatusEnum.IN_OPERATION.getValue());
            priceAllocateDao.updateById(priceAllocateEntity);
        }
        CustomerDTO customerDTO = customerFacade.getCustomerById(priceAllocateEntity.getCustomerId());

        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        //根据成交单id查询成交单
        PriceDealDetailEntity priceDealDetailEntity = priceDealDetailDao.getById(priceAllocateEntity.getPriceDealId());

        BigDecimal transactionDiffPrice = priceDealDetailEntity.getTransactionDiffPrice();

        // 金仕达逆转月价差特殊处理
        try {
            PriceApplyEntity priceApply = priceApplyDao.getById(priceAllocateEntity.getPriceApplyId());
            boolean isReverseMonth = Integer.parseInt(priceApply.getTranferDominantCode()) < Integer.parseInt(priceApply.getDominantCode())
                    && ObjectUtil.notEqual(priceApply.getInterfaceStatus(), KingStarInterfaceStatusEnum.NOT_CALL_INTERFACE.getValue());

            if (isReverseMonth) {
                transactionDiffPrice = priceDealDetailEntity.getTransactionDiffPrice().negate();
            }
        } catch (Exception e) {
            log.error("处理KingStar逆转月价差出错", e);
        }

        // 审核结果
        String auditResult = auditPriceAllocateDTO.getAuditResult();
        // if 通过  else if 驳回
        boolean priceAllocateUpdate = false;
        if (Integer.parseInt(auditResult) == AllocateStatusEnum.AUDIT_PASS.getValue()) {
            priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());

            if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.PRICING.getValue() || priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.STRUCTURE_PRICING.getValue()) {
                SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
                BeanUtil.copyProperties(priceAllocateEntity, salesContractTTPriceDTO);

                // 校验使用
                salesContractTTPriceDTO.setAllocateNum(priceAllocateEntity.getAllocateNum());
                ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(Integer.valueOf(contractId));
                // 生成TT
                salesContractTTPriceDTO
                        .setContractId(Integer.valueOf(contractId))
                        .setPrice(priceDealDetailEntity.getDealPrice())
                        .setTransactionPrice(priceDealDetailEntity.getDealPrice())
                        .setContractPriceDetail(JSON.toJSONString(contractPriceEntity))
                        .setAllocateId(priceAllocateEntity.getId());

                try {
                    Result result1 = contractFacade.priceContract(salesContractTTPriceDTO);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }
                } catch (Exception e) {
                    log.error("AuditPriceAllocateDTO:新增TT:{}", e.getMessage());
                    throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                }

                // 全部定价处理
                BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, contractEntity.getTotalPriceNum(), salesContractTTPriceDTO.getAllocateNum());

                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), newTotalPriceNum)) {
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                }
            } else if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                // 全部转月
                ContractTransferDTO contractTransferDTO = new ContractTransferDTO();
                contractTransferDTO.setContractId(contractEntity.getId());

                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), priceAllocateEntity.getAllocateNum())) {
                    contractTransferDTO.setTtTranferType(TTTranferTypeEnum.TRANSFER_MONTH.getValue());

                    // 全部转月原合同：取修改日期前（不含签订日期当天）23:00最近一次此品种、合约的收盘价上传记录【流程启动的时候更新】
                    Result result = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), priceAllocateEntity.getDominantCode(), new Date(), contractEntity.getFutureCode());
                    if (result.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(result.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            contractTransferDTO.setLatestForwardPrice(domainPriceEntity.getPrice());
                        }
                    }

                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());

                    contractTransferDTO.setPriceApplyId(priceAllocateEntity.getPriceApplyId());
                    contractTransferDTO.setPriceAllocateId(priceAllocateEntity.getId());
                    contractTransferDTO.setAllocateNum(priceAllocateEntity.getAllocateNum());
                    contractTransferDTO.setTransactionDiffPrice(transactionDiffPrice);
                    contractTransferDTO.setDomainCode(priceAllocateEntity.getDominantCode());
                    contractTransferDTO.setTransferDominantCode(priceAllocateEntity.getDominantCode());

                    // 合同处理
                    Result transferredResult = contractFacade.transferMonthContract(contractTransferDTO);
                    if (!transferredResult.isSuccess()) {
                        throw new BusinessException(ResultCodeEnum.getByMsg(transferredResult.getMessage()));
                    }

                    priceAllocateEntity
                            .setSubcontractId(contractEntity.getId())
                            .setSubcontractCode(contractEntity.getContractCode())
                            .setBelongCustomerId(contractEntity.getBelongCustomerId())
                            .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode());
                } else {
                    // 部分转月
                    priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                    contractTransferDTO
                            .setTtTranferType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                            .setTransferNum(priceAllocateEntity.getAllocateNum())
                            .setAllocateNum(priceAllocateEntity.getAllocateNum())
                            .setDomainCode(priceAllocateEntity.getDominantCode())
                            .setTransferDominantCode(priceAllocateEntity.getDominantCode())
                            .setPriceAllocateId(priceAllocateEntity.getId())
                            .setPriceApplyId(priceAllocateEntity.getPriceApplyId())
                            .setTransactionDiffPrice(transactionDiffPrice);
                    // 部分转月更新期货价格
                    Result domainResult = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), priceAllocateEntity.getDominantCode(), new Date(), contractEntity.getFutureCode());
                    if (domainResult.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(domainResult.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            contractTransferDTO.setLatestForwardPrice(domainPriceEntity.getPrice());
                        }
                    }

                    // 合同处理
                    Result result = contractFacade.transferMonthContract(contractTransferDTO);
                    if (result.isSuccess()) {
                        ContractEntity childContractEntity = JSON.parseObject(JSON.toJSONString(result.getData()), ContractEntity.class);

                        priceAllocateEntity
                                .setSubcontractId(childContractEntity.getId())
                                .setSubcontractCode(childContractEntity.getContractCode())
                                .setBelongCustomerId(childContractEntity.getBelongCustomerId())
                                .setDeliveryFactoryCode(childContractEntity.getDeliveryFactoryCode());
                    } else {
                        throw new BusinessException(ResultCodeEnum.getByMsg(result.getMessage()));
                    }
                }
            }
            // 更新分配单
            priceAllocateEntity.setSiteCode(contractEntity.getSiteCode());
            priceAllocateEntity.setSiteName(contractEntity.getSiteName());
            priceAllocateEntity.setStatus(Integer.valueOf(auditResult));
            priceAllocateEntity.setReviewerId(Integer.valueOf(auditId));
            priceAllocateEntity.setAuditStatus(StrUtil.isNotBlank(auditPriceAllocateDTO.getUpdatePass()) ? AuditStatusEnum.UPDATE_AUDIT.getValue() : AuditStatusEnum.AUDIT_PASS.getValue());
            priceAllocateEntity.setReviewerName(employEntity != null ? employEntity.getRealName() : null);
            priceAllocateEntity.setUpdatedAt(new Date())
                    .setAuditTime(new Date());
            priceAllocateUpdate = priceAllocateDao.updateById(priceAllocateEntity);

            try {
                recordOperationLog(LogBizCodeEnum.PRICE_APPLY_ALLOCATE_PASS,
                        priceAllocateEntity.getPriceApplyId(),
                        priceAllocateEntity.getPriceApplyCode(),
                        JSON.toJSONString(priceAllocateEntity),
                        JSON.toJSONString(auditPriceAllocateDTO),
                        SystemEnum.MAGELLAN.getValue());
            } catch (Exception e) {
                log.debug("记录日志错误,{}", e.getMessage());
            }

        } else if (Integer.valueOf(auditResult) == AllocateStatusEnum.AUDIT_REJECT.getValue()) {
            // 将分配单上的分配量还到申请单上
            BigDecimal allocateNum = priceAllocateEntity.getAllocateNum();
            Integer priceApplyId = priceAllocateEntity.getPriceApplyId();
//            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceApplyId);
            priceDealDetailEntity.setAllocateNum(BigDecimalUtil.subtract(CalcTypeEnum.COUNT, priceDealDetailEntity.getAllocateNum(), allocateNum));

            priceDealDetailDao.updateById(priceDealDetailEntity);
            //priceApplyDao.updateById(priceApplyEntity);
            // 更新分配单
            priceAllocateEntity.setStatus(Integer.valueOf(auditResult));
            priceAllocateEntity.setAuditReason(auditPriceAllocateDTO.getAuditResult());
            priceAllocateEntity.setReviewerId(Integer.valueOf(auditId));
            priceAllocateEntity.setReviewerName(employEntity != null ? employEntity.getRealName() : null);
            priceAllocateEntity.setUpdatedAt(new Date());
            priceAllocateEntity.setAuditStatus(AuditStatusEnum.AUDIT_PASS.getValue());
            priceAllocateUpdate = priceAllocateDao.updateById(priceAllocateEntity);
        }
        return priceAllocateUpdate ? Result.success() : Result.failure();
    }

    /**
     * 判断 dominantCode 是否小于 transferDominantCode
     */
    private boolean compareCode(String dominantCode, String transferDominantCode) {
        try {
            return Integer.parseInt(dominantCode) < Integer.parseInt(transferDominantCode);
        } catch (NumberFormatException e) {
            // 处理非法数字格式
            return false;
        }
    }

    /**
     * 点价截止日期
     *
     * @param contractEntity
     * @return
     */
    private ContractEntity setPriceEndTime(ContractEntity contractEntity, Date deliveryStartTime, String domainCode) {
        Integer priceEndType = null;
        String priceEndTime = null;
        // 开始交货日>期货合约
        DeliveryTypeEntity deliveryTypeEntity = deliveryTypeFacade.getDeliveryTypeById(contractEntity.getDeliveryType());
        String deliveryTime = DateTimeUtil.formatDateValue(deliveryStartTime).substring(2, 6);
        if (deliveryTime.compareTo(domainCode) < 0) {
            if (null != deliveryTypeEntity) {
                if (deliveryTypeEntity.getType().equals(DeliveryModeEnum.TAKE.getValue())) {
                    priceEndType = 2;
                    priceEndTime = "提货";
                } else if (deliveryTypeEntity.getType().equals(DeliveryModeEnum.SEND.getValue())) {
                    priceEndType = 2;
                    priceEndTime = "发货";
                }
            }
        } else {
            // 点价截止日期”显示为“期货合约”前一个月的20号
            priceEndType = 1;
            priceEndTime = DateTimeUtil.calculatePriceEndTime(domainCode);
        }

        return contractEntity.setPriceEndType(priceEndType)
                .setPriceEndTime(priceEndTime);
    }

    private String processorType(Integer categoryId, Integer salesType, Integer priceType) {
        String processorType = null;
        if (salesType == ContractSalesTypeEnum.SALES.getValue()) {
            //销售处理

            if (priceType == PriceTypeEnum.PRICING.getValue()) {
                //操作类型  点价
                switch (GoodsCategoryEnum.getByValue(categoryId)) {
                    case OSM_MEAL:
                        processorType = ProcessorTypeEnum.SBM_S_PRICE.getTtValue();
                        break;
                    case OSM_OIL:
                        processorType = ProcessorTypeEnum.SBO_S_PRICE.getTtValue();
                        break;
                    default:
                        break;
                }

            } else if (priceType == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                //操作类型  转月

                switch (GoodsCategoryEnum.getByValue(categoryId)) {
                    case OSM_MEAL:
                        processorType = ProcessorTypeEnum.SBM_S_TRANSFER.getTtValue();
                        break;
                    case OSM_OIL:
                        processorType = ProcessorTypeEnum.SBO_S_TRANSFER.getTtValue();
                        break;
                    default:
                        break;
                }
            } else if (priceType == PriceTypeEnum.STRUCTURE_PRICING.getValue()) {
                //操作类型 结构化定价
                switch (GoodsCategoryEnum.getByValue(categoryId)) {
                    case OSM_MEAL:
                        processorType = ProcessorTypeEnum.SBM_S_STRUCTURE_PRICE.getTtValue();
                        break;
                    case OSM_OIL:
                        processorType = ProcessorTypeEnum.SBO_S_STRUCTURE_PRICE.getTtValue();
                        break;
                    default:
                        break;
                }
            }

        } else if (salesType == ContractSalesTypeEnum.PURCHASE.getValue()) {
            //采购合同

            if (priceType == PriceTypeEnum.PRICING.getValue()) {
                //操作类型  点价
                switch (GoodsCategoryEnum.getByValue(categoryId)) {
                    case OSM_MEAL:
                        processorType = ProcessorTypeEnum.SBM_P_PRICE.getTtValue();
                        break;
                    case OSM_OIL:
                        processorType = ProcessorTypeEnum.SBO_P_PRICE.getTtValue();
                        break;
                    default:
                        break;
                }

            } else if (priceType == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                //操作类型  转月

                switch (GoodsCategoryEnum.getByValue(categoryId)) {
                    case OSM_MEAL:
                        processorType = ProcessorTypeEnum.SBM_P_TRANSFER.getTtValue();
                        break;
                    case OSM_OIL:
                        processorType = ProcessorTypeEnum.SBO_P_TRANSFER.getTtValue();
                        break;
                    default:
                        break;
                }
            }

        }

        return processorType;

    }

    private void recalculatePrice(BigDecimal contractNum, PriceDealDetailEntity priceDealDetailEntity, ContractEntity contractEntity, PriceDetailBO priceDetailBO, ContractPriceEntity contractPriceEntity, SalesContractTTTransferDTO salesContractTTTransferDTO) {
        //1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息,不需要更新原合同价格) Date:20241023 By:NanaHou
        //基差价(新的基差价=原基差价+转月价差)
        BigDecimal newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, contractPriceEntity.getExtraPrice(), priceDealDetailEntity.getTransactionDiffPrice());
        priceDetailBO.setExtraPrice(newExtraPrice);
        //手续费
        if (contractEntity.getTotalTransferTimes() == null || contractEntity.getAbleTransferTimes() == null) {
            log.error("transfer times error,contractId:{}", contractEntity.getId());
            throw new BusinessException(ResultCodeEnum.TRANSFER_MONTH_TIMES_ERROR);
        }
//        Integer totalTransferNum = contractEntity.getTotalTransferTimes();
//        Integer ableTransferNum = contractEntity.getAbleTransferTimes();
//        int currentTransferNum = totalTransferNum - ableTransferNum + 1;
        int currentTransferNum = contractEntity.getTransferredTimes() + 1;
        BigDecimal fee;
        //普通用户转月费用规则：豆粕为1元/吨/次；豆油为2元/吨/次。白名单用户转月费用规则：
        //豆粕：前两次转月费用为1元/吨/次，后两次转月时费用为2元/吨/次；
        //豆油：前两次转月费用为2元/吨/次，后两次转月时费用为4元/吨/次；
        BigDecimal originalFee = null != priceDetailBO.getFee() ? priceDetailBO.getFee() : BigDecimal.ZERO;
        if (currentTransferNum > 2) {
            fee = GoodsCategoryEnum.OSM_MEAL.getValue().equals(priceDealDetailEntity.getCategoryId()) ? new BigDecimal("2") : new BigDecimal("4");
        } else {
            fee = GoodsCategoryEnum.OSM_MEAL.getValue().equals(priceDealDetailEntity.getCategoryId()) ? new BigDecimal("1") : new BigDecimal("2");
        }
        BigDecimal newFee = originalFee.add(fee);
        priceDetailBO.setFee(newFee);
        //总价
        BigDecimal deliveryPrice = contractPriceEntity.getTransportPrice()
                .add(contractPriceEntity.getLiftingPrice())
                .add(contractPriceEntity.getDelayPrice())
                .add(contractPriceEntity.getTemperaturePrice())
                .add(contractPriceEntity.getOtherDeliveryPrice());
        BigDecimal taxRate = contractEntity.getTaxRate();
        BigDecimal unitPrice = contractPriceFacade.calculatePriceBo(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        BigDecimal totalAmount = unitPrice.multiply(contractNum);
        salesContractTTTransferDTO.setThisTimeFee(fee);
        contractEntity.setTotalAmount(totalAmount);
        contractEntity.setCifUnitPrice(cifUnitPrice);
        contractEntity.setFobUnitPrice(fobUnitPrice);
        contractEntity.setUnitPrice(unitPrice);
        contractEntity.setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
    }

    @Override
    public Result getPriceAllocateDetail(String applyCode) {
        PriceAllocateEntity priceAllocateEntity = priceAllocateDao.getOne(new LambdaQueryWrapper<PriceAllocateEntity>()
                .eq(PriceAllocateEntity::getPriceApplyCode, applyCode));
        return Result.success(priceAllocateEntity);
    }

    @Override
    public boolean judgeExistsAllocatesAndOrder(String customerId, String dominantCode, Integer categoryId) {

        List<PriceApplyEntity> priceApplyEntities = priceApplyDao.getPriceApplysOrdersByDominantCode(customerId, dominantCode, categoryId);
        //List<PriceAllocateEntity> priceAllocateEntities = priceAllocateDao.getAllocateOrdersByDominantCode(customerId, dominantCode, categoryId);
        //return priceAllocateEntities.size() + priceApplyEntities.size() > 0;
        return priceApplyEntities.size() > 0;
    }

    @Override
    public BigDecimal getSumPriceAllocateOfContract(String contractId) {
        return priceAllocateDao.getSumPriceAllocateOfContract(contractId);
    }

    @Override
    public List<PriceAllocateEntity> getAllocateByPriceApplyId(Integer priceApplyId) {
        return priceAllocateDao.getAllocateByPriceApplyId(priceApplyId);
    }

    @Override
    public List<PriceAllocateEntity> getAllocateByPriceApplyIdStatus(Integer priceApplyId) {
        return priceAllocateDao.getAllocateByPriceApplyIdStatus(priceApplyId);
    }

    @Override
    public List<PriceAllocateEntity> getAllocateByDominantCode(Integer priceApplyId) {
        return priceAllocateDao.getAllocateByDominantCode(priceApplyId);
    }

    @Override
    public PriceAllocateEntity getPriceAllocateById(String allocateId) {
        return priceAllocateDao.getPriceAllocateById(allocateId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result purchaseContractPriceTransfer(@RequestBody List<PriceTransferDTO> priceTransferDTOs) {

        log.info("======purchaseContractPriceTransfer.priceTransferDTOs:{}", JSON.toJSONString(priceTransferDTOs));

        // 处理成功数量
        int successNum = 0;
        //case-1002795 并发操作的事务控制问题（采购,点价转月数量校验） Author: wan 2024-10-15 Start
        //校验可操作量是否满足需求量
        this.hasOperation(priceTransferDTOs);
        //case-1002795 并发操作的事务控制问题（采购,点价转月数量校验） Author: wan 2024-10-15 end


        for (PriceTransferDTO priceTransferDTO : priceTransferDTOs) {

            ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(priceTransferDTO.getContractId()));

            CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getSupplierId());

            if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
                throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
            }

            //判断合同数量是否充足
            BigDecimal contractNum = contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum());
            if (BigDecimalUtil.isGreater1(priceTransferDTO.getPriceTransferNum(), contractNum)) {
                throw new BusinessException(ResultCodeEnum.CONTRACT_NUM_INSUFFICIENT);
            }

            //创建申请单
            PriceApplyDTO priceApplyDTO = new PriceApplyDTO();

            priceApplyDTO
                    .setSystem(SystemEnum.MAGELLAN.getValue())
                    .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setCategory1(contractEntity.getCategory1().toString())
                    .setCategory2(contractEntity.getCategory2().toString())
                    .setCategory3(contractEntity.getCategory3().toString())
                    .setCustomerId(contractEntity.getSupplierId())
                    .setType(priceTransferDTO.getType())
                    .setApplyPrice(priceTransferDTO.getTransferPrice())
                    .setContractId(Integer.parseInt(priceTransferDTO.getContractId()))
            ;

            PriceAllocateEntity priceAllocateEntity = new PriceAllocateEntity();

            CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
            String auditId = JwtUtils.getCurrentUserId();
            priceAllocateEntity
                    .setContractCode(contractEntity.getContractCode())
                    .setContractId(contractEntity.getId())
                    .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setPriceApplyType(priceTransferDTO.getType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setSiteCode(contractEntity.getSiteCode())
                    .setSiteName(contractEntity.getSiteName())
                    .setDominantCode(contractEntity.getDomainCode())
                    .setAllocateNum(priceTransferDTO.getPriceTransferNum())
                    .setStatus(AllocateStatusEnum.AUDIT_PASS.getValue())
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setCustomerId(contractEntity.getSupplierId())
                    .setCustomerName(contractEntity.getSupplierName())
                    .setReviewerId(Integer.parseInt(auditId))
                    .setReviewerName(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())))
                    .setCreatedBy(Integer.parseInt(auditId))
                    .setRawDominantCode(contractEntity.getDomainCode())
                    .setAuditTime(new Date())
                    .setCompanyName(companyEntity.getName())
            ;

            if (priceTransferDTO.getType() == PriceTypeEnum.PRICING.getValue()) {
                // 初始点价量
                BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum();
                // 新的点价量
                BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalPriceNum, priceTransferDTO.getPriceTransferNum());
                contractEntity.setTotalPriceNum(newTotalPriceNum);
                BigDecimal unitPrice = contractEntity.getUnitPrice();

                priceApplyDTO
                        .setContractId(contractEntity.getId())
                        .setDominantCode(contractEntity.getDomainCode())
                        .setApplyNum(priceTransferDTO.getPriceTransferNum())
                        .setApplyPrice(priceTransferDTO.getTransferPrice())
                        .setPendingType(PendingTypeEnum.PENDING.getValue())
                ;
                priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                //创建申请单
                Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);

                ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractEntity.getId());

                // 全部定价后：【期货价格】需更新为：合同所有点价单单价（不加基差价）的加权平均价
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), newTotalPriceNum)) {
                    Result rtn = contractFacade.getConfirmPricedList(contractEntity.getId());
                    if (rtn.isSuccess()) {
                        List<ConfirmPriceDTO> confirmPriceDTOList = JSON.parseArray(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
                        BigDecimal totalPrice = priceTransferDTO.getPriceTransferNum().multiply(priceTransferDTO.getTransferPrice());
                        BigDecimal totalNum = priceTransferDTO.getPriceTransferNum();
                        for (TTPriceEntity ttPriceEntity : confirmPriceDTOList) {
                            totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                            totalNum = totalNum.add(ttPriceEntity.getNum());
                        }
                        if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                            // 加权平均价
                            BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                            log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                            // 更新期货价格
                            Result result = contractPriceFacade.updateContractForwardPrice(contractEntity, averagePrice);
                            if (result.isSuccess()) {
                                contractEntity = JSON.parseObject(JSON.toJSONString(result.getData()), ContractEntity.class);
                            }
                        }
                    }
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                }
                // 修改合同的已点量 【（未校验 oldTotalPriceNum 是否变化）】
                contractFacade.updateAndBackUpContract(new ContractBackUpDTO()
                        .setContractEntity(contractEntity)
                        .setBackTradeType(String.valueOf(ContractTradeTypeEnum.PRICE.getValue())));

                TTDTO ttdto = new TTDTO();
                SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
                //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                salesContractTTPriceDTO
                        .setCategory1(contractEntity.getCategory1())
                        .setCategory2(contractEntity.getCategory2())
                        .setCategory3(contractEntity.getCategory3())
                        .setType(PriceTypeEnum.PRICING.getValue());
                //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end
                BeanUtil.copyProperties(contractEntity, salesContractTTPriceDTO);
                salesContractTTPriceDTO.setUnitPrice(unitPrice);
                salesContractTTPriceDTO.setContractId(contractEntity.getId());
                salesContractTTPriceDTO.setTempPrice(contractEntity.getTemporaryPrice());
                salesContractTTPriceDTO.setDiffPrice(contractEntity.getExtraPrice());
                salesContractTTPriceDTO.setContractPriceDetail(JSON.toJSONString(contractPriceEntity));
                salesContractTTPriceDTO.setNum(priceTransferDTO.getPriceTransferNum());
                salesContractTTPriceDTO.setPrice(priceTransferDTO.getTransferPrice());
                salesContractTTPriceDTO.setTransactionPrice(priceTransferDTO.getTransferPrice());
                salesContractTTPriceDTO.setUserId(JwtUtils.getCurrentUserId());
                salesContractTTPriceDTO.setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode());
                salesContractTTPriceDTO.setDeliveryFactoryName(contractEntity.getDeliveryFactoryName());
                salesContractTTPriceDTO.setOwnerId(contractEntity.getOwnerId());
                salesContractTTPriceDTO.setSourceContractId(contractEntity.getId());
                salesContractTTPriceDTO.setRemainPriceNum(contractEntity.getContractNum().subtract(contractEntity.getTotalPriceNum()));
                salesContractTTPriceDTO.setOriginalPriceNum(priceTransferDTO.getPriceTransferNum());
                salesContractTTPriceDTO.setThisContractNum(contractEntity.getContractNum());
                salesContractTTPriceDTO.setDomainCode(contractEntity.getDomainCode());
                salesContractTTPriceDTO.setPriceApplyId(priceId);

                ttdto.setSalesContractTTPriceDTO(salesContractTTPriceDTO);

                // 获取处理TT接口
                String ttProcessorType = getTTProcessor(
                        contractEntity.getSalesType(),
                        TTTypeEnum.PRICE.getType(),
                        contractEntity.getGoodsCategoryId());
                ttdto.setProcessorType(ttProcessorType);
                log.info("=====purchaseContractPriceTransfer.ttdto{}", JSON.toJSONString(ttdto));

                try {
                    Result result1 = tradeTicketFacade.saveTT(ttdto);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        //新增TT失败合同定价量回滚
                        contractEntity.setTotalPriceNum(oldTotalPriceNum);
                        contractFacade.updateContract(contractEntity);
                        log.error("result1:{}", ResultCodeEnum.SAVE_TT_FAIL);
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }
                } catch (Exception e) {

                    //新增TT失败合同定价量回滚
                    contractEntity.setTotalPriceNum(oldTotalPriceNum);
                    contractFacade.updateContract(contractEntity);
                    log.error("purchaseContractPriceTransfer:新增TT:{}", e.getMessage());

                }

                // 处理回购的点价单(销售合同A，回购为新采购合同B，当新采购合同B定价后，需同步点价单至原销售合同A)
                if (contractEntity.getContractSource().equals(ContractActionEnum.BUYBACK.getActionValue())
                        && contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {

                    List<ConfirmPriceDTO> confirmPriceDTOList = new ArrayList<>();
                    ConfirmPriceDTO confirmPriceDTO = null;

                    // 获取点价单同步至原销售合同
                    Result rtn = contractFacade.getConfirmPricedList(contractEntity.getId());
                    if (rtn.isSuccess()) {
                        confirmPriceDTOList = JSON.parseArray(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
                    }
                    if (CollectionUtils.isNotEmpty(confirmPriceDTOList)) {
                        confirmPriceDTO = confirmPriceDTOList.get(confirmPriceDTOList.size() - 1);

                        TTPriceEntity ttPriceEntity = BeanConvertUtils.map(TTPriceEntity.class, confirmPriceDTO);

                        ContractEntity parentContract = contractFacade.getBasicContractById(contractEntity.getParentId());

                        // 同步定价单
                        ttPriceEntity.setId(null)
                                .setContractId(parentContract.getId())
                                .setContractCode(parentContract.getContractCode())
                                .setCreatedAt(DateTimeUtil.now())
                                .setTtId(null)
                        ;
                        contractFacade.saveConfirmPrice(ttPriceEntity);

                        // 更新父合同的定价量
                        parentContract
                                .setTotalPriceNum(parentContract.getTotalPriceNum().add(ttPriceEntity.getNum()))
                                .setStatus(ContractStatusEnum.MODIFYING.getValue());
                        contractFacade.updateContract(parentContract);
                    }
                }
                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceId);
                priceAllocateEntity
                        .setPriceApplyCode(priceApplyEntity.getCode())
                        .setSubcontractId(contractEntity.getId())
                        .setPriceApplyId(priceId);
            } else {
                // 初始转月量
                BigDecimal oldTotalTransferNum = contractEntity.getTotalTransferNum();
                // 新的转月量
                BigDecimal newTotalTransferNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalTransferNum, priceTransferDTO.getPriceTransferNum());
                BigDecimal newContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), priceTransferDTO.getPriceTransferNum());
                contractEntity.setTotalTransferNum(newTotalTransferNum);
                contractEntity.setContractNum(newContractNum);

                // 更新contractPrice
                ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractEntity.getId());
                PriceDetailBO priceDetailBO = new PriceDetailBO();
                ContractPriceEntity preContractPriceEntity = new ContractPriceEntity();
                BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
                BeanUtils.copyProperties(contractPriceEntity, preContractPriceEntity);

                priceApplyDTO
                        .setContractId(contractEntity.getId())
                        .setTranferDominantCode(priceTransferDTO.getTransferMonth())
                        .setTranferFutureCode(priceTransferDTO.getTransferFutureCode())
                        .setDominantCode(contractEntity.getDomainCode())
                        .setApplyNum(priceTransferDTO.getPriceTransferNum())
                        .setApplyDiffPrice(priceTransferDTO.getTransferPrice())
                        .setPendingType(PendingTypeEnum.PENDING.getValue())
                ;
                //创建申请单
                Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);

                // 全部转月
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), BigDecimal.ZERO)) {

                    // 全部转月原合同：取修改日期前（不含签订日期当天）23:00最近一次此品种、合约的收盘价上传记录【流程启动的时候更新】
                    Result result = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), priceTransferDTO.getTransferMonth(), new Date(), contractEntity.getFutureCode());
                    if (result.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(result.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            priceDetailBO.setForwardPrice(domainPriceEntity.getPrice());
                        }
                    }
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                    TTDTO ttdto = new TTDTO();
                    SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();

                    // 重新计算价格
                    recalculatePrice(contractEntity, priceDetailBO, priceTransferDTO, priceTransferDTO.getPriceTransferNum(), salesContractTTTransferDTO);

                    // 点价截止日期
                    setPriceEndTime(contractEntity, contractEntity.getDeliveryStartTime(), priceTransferDTO.getTransferMonth());
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                    salesContractTTTransferDTO
                            .setCategory1(contractEntity.getCategory1())
                            .setCategory2(contractEntity.getCategory2())
                            .setCategory3(contractEntity.getCategory3())
                            .setType(TTTranferTypeEnum.TRANSFER_MONTH.getValue());
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 end
                    BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);
                    salesContractTTTransferDTO.setNum(priceTransferDTO.getPriceTransferNum());
                    salesContractTTTransferDTO.setOriginalDomainCode(contractEntity.getDomainCode());
                    salesContractTTTransferDTO.setDomainCode(priceTransferDTO.getTransferMonth());
                    salesContractTTTransferDTO.setFutureCode(priceTransferDTO.getTransferFutureCode());
                    salesContractTTTransferDTO.setTempPrice(contractEntity.getTemporaryPrice());
                    salesContractTTTransferDTO.setDiffPrice(contractEntity.getExtraPrice());
                    salesContractTTTransferDTO.setContractId(contractEntity.getId());
                    salesContractTTTransferDTO.setSourceContractId(contractEntity.getId());
                    salesContractTTTransferDTO.setContractSource(ContractActionEnum.TRANSFER_ALL_CONFIRM.getActionValue());
                    salesContractTTTransferDTO.setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode());
                    salesContractTTTransferDTO.setDeliveryFactoryName(contractEntity.getDeliveryFactoryName());
                    salesContractTTTransferDTO.setPriceEndTime(contractEntity.getPriceEndTime());
                    salesContractTTTransferDTO.setPriceEndType(contractEntity.getPriceEndType());
                    salesContractTTTransferDTO.setPrice(priceTransferDTO.getTransferPrice());
                    salesContractTTTransferDTO.setPriceApplyId(priceId);
                    ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);

                    ttdto.setPriceDetailBO(priceDetailBO);

                    // 获取处理TT接口
                    String ttProcessorType = getTTProcessor(
                            contractEntity.getSalesType(),
                            TTTypeEnum.TRANSFER.getType(),
                            contractEntity.getGoodsCategoryId());
                    ttdto.setProcessorType(ttProcessorType);
                    log.info("=====purchaseContractPriceTransfer.ttdto{}", JSON.toJSONString(ttdto));
                    tradeTicketFacade.saveTT(ttdto);
                    // BEGIN: 1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息,不需要更新原合同价格) Date:20241023 By:NanaHou
                    // 更新合同
                    contractEntity
                            .setDomainCode(priceTransferDTO.getTransferMonth())
                            .setExtraPrice(priceDetailBO.getExtraPrice())
                            .setFutureCode(priceTransferDTO.getTransferFutureCode())
                            .setContractNum(priceTransferDTO.getPriceTransferNum())
                            .setStatus(ContractStatusEnum.MODIFYING.getValue())
                            .setTradeType(ContractTradeTypeEnum.TRANSFER_ALL.getValue())
                            .setUpdatedAt(DateTimeUtil.now());

                    // 更新contract Price
//                    contractPriceEntity.setExtraPrice(priceDetailBO.getExtraPrice())
//                            .setFee(priceDetailBO.getFee())
//                            .setForwardPrice(priceDetailBO.getForwardPrice())
//                            .setTransportPrice(priceDetailBO.getTransportPrice());
//                    contractPriceFacade.updatePriceByContractId(contractPriceEntity);
                    // END: 1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息,不需要更新原合同价格) Date:20241023 By:NanaHou
                    priceAllocateEntity
                            .setSubcontractCode(contractEntity.getContractCode())
                            .setSubcontractId(contractEntity.getId());

                } else {
                    // 部分转月
                    TTDTO ttdto = new TTDTO();
                    SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
                    priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                    // 生成基差子合同
                    ContractEntity sonContractEntity = BeanConvertUtils.map(ContractEntity.class, contractEntity);
                    sonContractEntity.setParentId(contractEntity.getId());
                    sonContractEntity.setRootId(contractEntity.getParentId() == 0 ? contractEntity.getId() : contractEntity.getParentId());

                    // 重新计算价格
                    recalculatePrice(sonContractEntity, priceDetailBO, priceTransferDTO, priceTransferDTO.getPriceTransferNum(), salesContractTTTransferDTO);

                    // 点价截止日期
                    setPriceEndTime(sonContractEntity, sonContractEntity.getDeliveryStartTime(), priceTransferDTO.getTransferMonth());

                    //签订日期
                    Date signDate = new Date();
                    sonContractEntity.setSignDate(signDate);
                    ContractTransferDTO contractTransferDTO = new ContractTransferDTO();
                    contractTransferDTO.setTtTranferType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                            .setContractEntity(sonContractEntity)
                            .setExtraPrice(priceDetailBO.getExtraPrice())
                            .setFee(priceDetailBO.getFee())
                            .setTransferNum(priceTransferDTO.getPriceTransferNum())
                            .setDomainCode(priceTransferDTO.getTransferMonth());
                    Result result = contractFacade.createSonContract(contractTransferDTO);
                    ObjectMapper mapper = new ObjectMapper();
                    ContractEntity sonContractProcessorEntity = mapper.convertValue(result.getData(), ContractEntity.class);
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 start
                    salesContractTTTransferDTO
                            .setCategory1(contractEntity.getCategory1())
                            .setCategory2(contractEntity.getCategory2())
                            .setCategory3(contractEntity.getCategory3())
                            .setType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue());
                    //多品类V1 头寸生成TT增加多品类字段塞值 Author:Wan 2024-07-01 End
                    salesContractTTTransferDTO.setNum(priceTransferDTO.getPriceTransferNum());
                    BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);
                    salesContractTTTransferDTO.setSonContractId(sonContractProcessorEntity.getId());
                    salesContractTTTransferDTO.setContractCode(sonContractProcessorEntity.getContractCode());
                    salesContractTTTransferDTO.setOriginalDomainCode(contractEntity.getDomainCode());
                    salesContractTTTransferDTO.setDomainCode(priceTransferDTO.getTransferMonth());
                    salesContractTTTransferDTO.setTempPrice(contractEntity.getTemporaryPrice());
                    salesContractTTTransferDTO.setDiffPrice(contractEntity.getExtraPrice());
                    salesContractTTTransferDTO.setContractId(contractEntity.getId());
                    salesContractTTTransferDTO.setSourceContractId(contractEntity.getId());
                    salesContractTTTransferDTO.setPrice(priceTransferDTO.getTransferPrice());
                    salesContractTTTransferDTO.setContractSource(ContractActionEnum.TRANSFER_CONFIRM.getActionValue());
                    salesContractTTTransferDTO.setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode());
                    salesContractTTTransferDTO.setDeliveryFactoryName(contractEntity.getDeliveryFactoryName());
                    salesContractTTTransferDTO.setSignDate(signDate);
                    salesContractTTTransferDTO.setPriceEndTime(sonContractEntity.getPriceEndTime());
                    salesContractTTTransferDTO.setPriceEndType(sonContractEntity.getPriceEndType());
                    salesContractTTTransferDTO.setPriceApplyId(priceId);
                    ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);

                    // 获取处理TT接口
                    String ttProcessorType = getTTProcessor(
                            contractEntity.getSalesType(),
                            TTTypeEnum.TRANSFER.getType(),
                            contractEntity.getGoodsCategoryId());
                    ttdto.setProcessorType(ttProcessorType);

                    ttdto.setPriceDetailBO(priceDetailBO);
                    log.info("=====purchaseContractPriceTransfer.ttdto{}", JSON.toJSONString(ttdto.getSalesContractTTTransferDTO()));
                    tradeTicketFacade.saveTT(ttdto);

                    priceAllocateEntity
                            .setSubcontractCode(sonContractProcessorEntity.getContractCode())
                            .setSubcontractId(sonContractProcessorEntity.getId());
                }

                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceId);
                priceAllocateEntity
                        .setPriceApplyCode(priceApplyEntity.getCode())
                        .setPriceApplyId(priceApplyEntity.getId());
                contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
                contractFacade.updateContract(contractEntity);
            }
            //创建分配单
            addPurchasePriceAllocate(priceAllocateEntity);

        }

        return priceTransferDTOs.size() == successNum ? Result.failure() : Result.success();

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result purchaseContractPriceTransferNew(@RequestBody List<PriceTransferDTO> priceTransferDTOs) {

        log.info("======purchaseContractPriceTransfer.priceTransferDTOs:{}", JSON.toJSONString(priceTransferDTOs));

        // 处理成功数量
        int successNum = 0;

        for (PriceTransferDTO priceTransferDTO : priceTransferDTOs) {
            ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(priceTransferDTO.getContractId()));

            //创建申请单
            PriceApplyDTO priceApplyDTO = new PriceApplyDTO();

            priceApplyDTO
                    .setFutureCode(contractEntity.getFutureCode())
                    .setTranferFutureCode(contractEntity.getFutureCode())
                    .setSystem(SystemEnum.MAGELLAN.getValue())
                    .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setCategory1(contractEntity.getCategory1().toString())
                    .setCategory2(contractEntity.getCategory2().toString())
                    .setCategory3(contractEntity.getCategory3().toString())
                    .setCustomerId(contractEntity.getSupplierId())
                    .setType(priceTransferDTO.getType())
                    .setApplyPrice(priceTransferDTO.getTransferPrice())
                    .setContractId(Integer.parseInt(priceTransferDTO.getContractId()))
            ;

            PriceAllocateEntity priceAllocateEntity = new PriceAllocateEntity();

            CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
            String auditId = JwtUtils.getCurrentUserId();
            priceAllocateEntity
                    .setFutureCode(contractEntity.getFutureCode())
                    .setRawFutureCode(contractEntity.getFutureCode())
                    .setSiteCode(contractEntity.getSiteCode())
                    .setSiteName(contractEntity.getSiteName())
                    .setContractCode(contractEntity.getContractCode())
                    .setContractId(contractEntity.getId())
                    .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                    .setCompanyId(contractEntity.getCompanyId())
                    .setPriceApplyType(priceTransferDTO.getType())
                    .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                    .setDominantCode(contractEntity.getDomainCode())
                    .setAllocateNum(priceTransferDTO.getPriceTransferNum())
                    .setStatus(AllocateStatusEnum.AUDIT_PASS.getValue())
                    .setCategoryId(contractEntity.getGoodsCategoryId())
                    .setCustomerId(contractEntity.getSupplierId())
                    .setCustomerName(contractEntity.getSupplierName())
                    .setReviewerId(Integer.parseInt(auditId))
                    .setReviewerName(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())))
                    .setCreatedBy(Integer.parseInt(auditId))
                    .setRawDominantCode(contractEntity.getDomainCode())
                    .setCategory1(contractEntity.getCategory1().toString())
                    .setCategory2(contractEntity.getCategory2().toString())
                    .setCategory3(contractEntity.getCategory3().toString())
                    .setAuditTime(new Date())
                    .setCompanyName(companyEntity.getName())
            ;

            if (priceTransferDTO.getType() == PriceTypeEnum.PRICING.getValue()) {
                // 初始点价量
                BigDecimal oldTotalPriceNum = contractEntity.getTotalPriceNum();
                // 新的点价量
                BigDecimal newTotalPriceNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalPriceNum, priceTransferDTO.getPriceTransferNum());
                contractEntity.setTotalPriceNum(newTotalPriceNum);

                priceApplyDTO
                        .setContractId(contractEntity.getId())
                        .setDominantCode(contractEntity.getDomainCode())
                        .setApplyNum(priceTransferDTO.getPriceTransferNum())
                        .setApplyPrice(priceTransferDTO.getTransferPrice())
                        .setPendingType(PendingTypeEnum.PENDING.getValue())
                ;
                priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                //创建申请单
                Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);

                // 全部定价后：【期货价格】需更新为：合同所有点价单单价（不加基差价）的加权平均价
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), newTotalPriceNum)) {
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
                }

                SalesContractTTPriceDTO salesContractTTPriceDTO = new SalesContractTTPriceDTO();
                salesContractTTPriceDTO
                        .setPriceApplyId(priceId)
                        .setContractId(contractEntity.getId())
                        .setAllocateNum(priceTransferDTO.getPriceTransferNum())
                        .setPrice(priceTransferDTO.getTransferPrice())
                        .setTransactionPrice(priceTransferDTO.getTransferPrice());

                try {
                    Result result1 = contractFacade.priceContract(salesContractTTPriceDTO);
                    if (result1 == null || result1.getCode() != ResultCodeEnum.OK.getCode()) {
                        throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                    }
                } catch (Exception e) {
                    log.error("AuditPriceAllocateDTO:新增TT:{}", e.getMessage());
                    throw new BusinessException(ResultCodeEnum.SAVE_TT_FAIL);
                }

                // TODO 待优化
                // 处理回购的点价单(销售合同A，回购为新采购合同B，当新采购合同B定价后，需同步点价单至原销售合同A)
                if (contractEntity.getContractSource().equals(ContractActionEnum.BUYBACK.getActionValue())
                        && contractEntity.getSalesType().equals(ContractSalesTypeEnum.PURCHASE.getValue())) {

                    List<ConfirmPriceDTO> confirmPriceDTOList = new ArrayList<>();
                    ConfirmPriceDTO confirmPriceDTO = null;

                    // 获取点价单同步至原销售合同
                    Result rtn = contractFacade.getConfirmPricedList(contractEntity.getId());
                    if (rtn.isSuccess()) {
                        confirmPriceDTOList = JSON.parseArray(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
                    }
                    if (CollectionUtils.isNotEmpty(confirmPriceDTOList)) {
                        confirmPriceDTO = confirmPriceDTOList.get(confirmPriceDTOList.size() - 1);

                        TTPriceEntity ttPriceEntity = BeanConvertUtils.map(TTPriceEntity.class, confirmPriceDTO);

                        ContractEntity parentContract = contractFacade.getBasicContractById(contractEntity.getParentId());

                        // 同步定价单
                        ttPriceEntity.setId(null)
                                .setContractId(parentContract.getId())
                                .setContractCode(parentContract.getContractCode())
                                .setCreatedAt(DateTimeUtil.now())
                                .setTtId(null)
                        ;
                        contractFacade.saveConfirmPrice(ttPriceEntity);

                        // 更新父合同的定价量
                        parentContract
                                .setTotalPriceNum(parentContract.getTotalPriceNum().add(ttPriceEntity.getNum()))
                                .setStatus(ContractStatusEnum.MODIFYING.getValue());
                        contractFacade.updateContract(parentContract);
                    }
                }
                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceId);
                priceAllocateEntity
                        .setPriceApplyCode(priceApplyEntity.getCode())
                        .setSubcontractId(contractEntity.getId())
                        .setPriceApplyId(priceId);
            } else {
                // 初始转月量
                BigDecimal oldTotalTransferNum = contractEntity.getTotalTransferNum();
                // 新的转月量
                BigDecimal newTotalTransferNum = BigDecimalUtil.add(CalcTypeEnum.COUNT, oldTotalTransferNum, priceTransferDTO.getPriceTransferNum());
                BigDecimal newContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), priceTransferDTO.getPriceTransferNum());
                contractEntity.setTotalTransferNum(newTotalTransferNum);
                contractEntity.setContractNum(newContractNum);

                // 更新contractPrice
                ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractEntity.getId());
                PriceDetailBO priceDetailBO = new PriceDetailBO();
                ContractPriceEntity preContractPriceEntity = new ContractPriceEntity();
                BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
                BeanUtils.copyProperties(contractPriceEntity, preContractPriceEntity);

                priceApplyDTO
                        .setContractId(contractEntity.getId())
                        .setTranferDominantCode(priceTransferDTO.getTransferMonth())
                        .setTranferFutureCode(contractEntity.getFutureCode())
                        .setDominantCode(contractEntity.getDomainCode())
                        .setApplyNum(priceTransferDTO.getPriceTransferNum())
                        .setApplyDiffPrice(priceTransferDTO.getTransferPrice())
                        .setPendingType(PendingTypeEnum.PENDING.getValue())
                ;
                //创建申请单
                Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);

                ContractTransferDTO contractTransferDTO = new ContractTransferDTO();

                // 全部转月
                if (BigDecimalUtil.isEqual(contractEntity.getContractNum(), BigDecimal.ZERO)) {

                    // 全部转月原合同：取修改日期前（不含签订日期当天）23:00最近一次此品种、合约的收盘价上传记录【流程启动的时候更新】
                    Result result = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), priceTransferDTO.getTransferMonth(), new Date(), contractEntity.getFutureCode());
                    if (result.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(result.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            contractTransferDTO.setLatestForwardPrice(domainPriceEntity.getPrice());
                        }
                    }
                    contractTransferDTO.setTtTranferType(TTTranferTypeEnum.TRANSFER_MONTH.getValue());
                    priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());

                    //contractTransferDTO.setPriceAllocateId(priceAllocateEntity.getId());
                    contractTransferDTO
                            .setContractId(contractEntity.getId())
                            .setPriceApplyId(priceId)
                            .setAllocateNum(priceTransferDTO.getPriceTransferNum())
                            .setFee(priceTransferDTO.getTransferServiceCharge())
                            .setTransactionDiffPrice(priceTransferDTO.getTransferPrice())
                            .setDomainCode(priceTransferDTO.getTransferMonth())
                            .setTransferDominantCode(priceTransferDTO.getTransferMonth())
                            .setTransferFutureCode(priceTransferDTO.getTransferFutureCode());

                    // 合同处理
                    Result transferredResult = contractFacade.transferMonthContract(contractTransferDTO);
                    if (!transferredResult.isSuccess()) {
                        throw new BusinessException(ResultCodeEnum.getByMsg(transferredResult.getMessage()));
                    }

                    priceAllocateEntity
                            .setSubcontractCode(contractEntity.getContractCode())
                            .setSubcontractId(contractEntity.getId());

                } else {
                    // 部分转月
                    priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
                    contractTransferDTO
                            .setPriceApplyId(priceId)
                            .setContractId(contractEntity.getId())
                            .setTtTranferType(TTTranferTypeEnum.PART_TRANSFER_MONTH.getValue())
                            .setTransferNum(priceTransferDTO.getPriceTransferNum())
                            .setAllocateNum(priceTransferDTO.getPriceTransferNum())
                            .setDomainCode(priceTransferDTO.getTransferMonth())
                            .setTransferDominantCode(priceTransferDTO.getTransferMonth())
                            .setTransferFutureCode(priceTransferDTO.getTransferFutureCode())
                            .setFee(priceTransferDTO.getTransferServiceCharge())
                            .setTransactionDiffPrice(priceTransferDTO.getTransferPrice());
                    // 部分转月更新期货价格
                    Result domainResult = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), priceTransferDTO.getTransferMonth(), new Date(), contractEntity.getFutureCode());
                    if (domainResult.isSuccess()) {
                        DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(domainResult.getData()), DomainPriceEntity.class);
                        if (null != domainPriceEntity) {
                            contractTransferDTO.setLatestForwardPrice(domainPriceEntity.getPrice());
                        }
                    }

                    // 合同处理
                    Result result = contractFacade.transferMonthContract(contractTransferDTO);
                    if (result.isSuccess()) {
                        ContractEntity childContractEntity = JSON.parseObject(JSON.toJSONString(result.getData()), ContractEntity.class);

                        priceAllocateEntity
                                .setSubcontractId(childContractEntity.getId())
                                .setSubcontractCode(childContractEntity.getContractCode())
                                .setBelongCustomerId(childContractEntity.getBelongCustomerId())
                                .setDeliveryFactoryCode(childContractEntity.getDeliveryFactoryCode());
                    } else {
                        throw new BusinessException(ResultCodeEnum.getByMsg(result.getMessage()));
                    }
                }

                PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceId);
                priceAllocateEntity
                        .setPriceApplyCode(priceApplyEntity.getCode())
                        .setPriceApplyId(priceApplyEntity.getId());
            }
            //创建分配单
            addPurchasePriceAllocate(priceAllocateEntity);
        }

        return priceTransferDTOs.size() == successNum ? Result.failure() : Result.success();
    }

    /**
     * 创建采购分配单
     *
     * @param priceAllocateEntity
     */
    public void addPurchasePriceAllocate(PriceAllocateEntity priceAllocateEntity) {
        priceAllocateDao.save(priceAllocateEntity);
    }

    /**
     * 采购头寸新增定价单
     *
     * @param contractEntity
     * @param priceTransferDTO
     */
    private void savePriceApply(ContractEntity contractEntity, PriceTransferDTO priceTransferDTO) {

        PriceApplyEntity priceApplyEntity = new PriceApplyEntity();

        priceApplyEntity
                .setType(priceTransferDTO.getType())
                .setContractId(Integer.parseInt(priceTransferDTO.getContractId()))
                .setDominantCode(contractEntity.getDomainCode())
                .setDealNum(priceTransferDTO.getPriceTransferNum())
                .setApplyDiffPrice(PriceTypeEnum.PRICING.getValue() == priceTransferDTO.getType() ? null : priceTransferDTO.getTransferPrice())
                .setApplyPrice(PriceTypeEnum.TRANSFER_MONTH.getValue() == priceTransferDTO.getType() ? priceTransferDTO.getTransferPrice() : null);
        priceApplyDao.save(priceApplyEntity);
    }

    /**
     * 获取tt的processor
     *
     * @param salesType          销售类型
     * @param ttType             tt类型
     * @param subGoodsCategoryId 子商品类型
     * @return
     */
    private String getTTProcessor(Integer salesType, Integer ttType, Integer subGoodsCategoryId) {
        return GoodsCategoryEnum.getByValue(subGoodsCategoryId).getCode()
                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_TT_" + TTTypeEnum.getCodeByValue(ttType);
    }

    /**
     * 采购合同的价格重新计算
     *
     * @param contractEntity
     * @param priceDetailBO
     * @param priceTransferDTO
     * @param contractNum
     */
    private void recalculatePrice(ContractEntity contractEntity, PriceDetailBO priceDetailBO, PriceTransferDTO priceTransferDTO, BigDecimal contractNum, SalesContractTTTransferDTO salesContractTTTransferDTO) {
        //1002701-Case-TT详情内容随合同变化而变化(全部转月未生成价格信息,不需要更新原合同价格) Date:20241023 By:NanaHou
        // 基差价(新的基差价=原基差价+转月价差)
        BigDecimal newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, priceDetailBO.getExtraPrice(), priceTransferDTO.getTransferPrice());
        priceDetailBO.setExtraPrice(newExtraPrice);

        // 手续费
        BigDecimal originalFee = null != priceDetailBO.getFee() ? priceDetailBO.getFee() : BigDecimal.ZERO;
        BigDecimal newFee = originalFee.add(priceTransferDTO.getTransferServiceCharge());
        priceDetailBO.setFee(newFee);

        // 总价
        BigDecimal deliveryPrice = priceDetailBO.getTransportPrice()
                .add(priceDetailBO.getLiftingPrice())
                .add(priceDetailBO.getDelayPrice())
                .add(priceDetailBO.getTemperaturePrice())
                .add(priceDetailBO.getOtherDeliveryPrice());
        BigDecimal taxRate = contractEntity.getTaxRate();
        BigDecimal unitPrice = contractPriceFacade.calculatePriceBo(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        BigDecimal totalAmount = unitPrice.multiply(contractNum);
        salesContractTTTransferDTO.setThisTimeFee(priceTransferDTO.getTransferServiceCharge());
        contractEntity
                .setExtraPrice(newExtraPrice)
                .setTotalAmount(totalAmount)
                .setCifUnitPrice(cifUnitPrice)
                .setFobUnitPrice(fobUnitPrice)
                .setUnitPrice(unitPrice)
                .setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
    }

    @Override
    public Result queryPriceAllocateRecord(QueryDTO<QueryPriceAllocateDTO> queryDTO) {

        IPage<PriceAllocateEntity> priceAllocateEntities = priceAllocateDao.queryAllocateRecord(queryDTO);

        List<PriceAllocateVO> priceAllocateVOS = priceAllocateEntities.getRecords().stream().map(priceAllocateEntity -> {

            // 申请单
            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceAllocateEntity.getPriceApplyId());
            // 合同
            ContractEntity contractEntity = contractFacade.getBasicContractById(priceAllocateEntity.getContractId());

            PriceAllocateVO priceAllocateVO = new PriceAllocateVO();
            BeanUtil.copyProperties(priceAllocateEntity, priceAllocateVO);
            if (null != contractEntity) {
                BeanUtil.copyProperties(contractEntity, priceAllocateVO);
                // 获取合同所属商务
                EmployEntity businessPerson = employFacade.getEmployById(contractEntity.getOwnerId());
                priceAllocateVO.setBusinessPersonName(businessPerson != null ? businessPerson.getRealName() : null);
            }
            if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.PRICING.getValue()) {
                priceAllocateVO.setPlatePrice(priceApplyEntity.getTransactionPrice());
            } else if (priceAllocateEntity.getPriceApplyType() == PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                priceAllocateVO.setTranferDominantCode(priceApplyEntity.getTranferDominantCode());
                priceAllocateVO.setPlatePrice(priceApplyEntity.getTransactionDiffPrice());
            }
            priceAllocateVO.setAuditStatus(priceAllocateEntity.getAuditStatus());
            priceAllocateVO.setAllocateId(String.valueOf(priceAllocateEntity.getId()));
            priceAllocateVO.setPriceApplyId(String.valueOf(priceApplyEntity.getId()));
            priceAllocateVO.setStatus(priceAllocateEntity.getStatus());

            return priceAllocateVO;
        }).collect(Collectors.toList());


        return Result.page(priceAllocateEntities, priceAllocateVOS);
    }


    @Override
    public Result purchaseContractReverse(ReverseDTO reverseDTO) {
        String contractId = reverseDTO.getContractId();
        ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(contractId));

        // 可提数量校验
        if (null != contractEntity && null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (null != contractEntity && BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

        CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getSupplierId());

        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        this.checkPriceApplyNum(contractEntity.getContractNum(), reverseDTO.getReverseNum());

        //创建申请单
        PriceApplyDTO priceApplyDTO = new PriceApplyDTO();

        priceApplyDTO
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setApplyNum(reverseDTO.getReverseNum())
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setCompanyId(contractEntity.getCompanyId())
                .setCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerId(contractEntity.getSupplierId())
                .setType(PriceTypeEnum.REVERSE_PRICING.getValue())
                .setApplyPrice(reverseDTO.getReversePrice())
                .setDominantCode(contractEntity.getDomainCode())
                .setTranferDominantCode(StringUtil.isNotEmpty(reverseDTO.getReverseMonth()) ? reverseDTO.getReverseMonth() : contractEntity.getDomainCode())
                .setContractId(Integer.parseInt(reverseDTO.getContractId()))
        ;
        //创建申请单
        Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);
        PriceAllocateEntity priceAllocateEntity = new PriceAllocateEntity();

        CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
        String auditId = JwtUtils.getCurrentUserId();
        priceAllocateEntity
                .setContractCode(contractEntity.getContractCode())
                .setContractId(contractEntity.getId())
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setCompanyId(contractEntity.getCompanyId())
                .setPriceApplyType(PriceTypeEnum.REVERSE_PRICING.getValue())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDominantCode(contractEntity.getDomainCode())
                .setAllocateNum(reverseDTO.getReverseNum())
                .setStatus(AllocateStatusEnum.AUDIT_PASS.getValue())
                .setDominantCode(contractEntity.getDomainCode())
                .setCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerId(contractEntity.getSupplierId())
                .setCustomerName(contractEntity.getSupplierName())
                .setReviewerId(Integer.parseInt(auditId))
                .setReviewerName(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())))
                .setCreatedBy(Integer.parseInt(auditId))
                .setRawDominantCode(contractEntity.getDomainCode())
                .setAuditTime(new Date())
                .setCompanyName(companyEntity.getName())
        ;
        ContractEntity newContractEntity = new ContractEntity();
        BeanUtils.copyProperties(contractEntity, newContractEntity);
        newContractEntity.setParentId(contractEntity.getId());
        newContractEntity.setRootId(contractEntity.getParentId() == 0 ? contractEntity.getId() : contractEntity.getParentId());
        newContractEntity.setContractNum(reverseDTO.getReverseNum());
        TTDTO ttdto = new TTDTO();
        SalesContractTTTransferDTO salesContractTTTransferDTO = new SalesContractTTTransferDTO();
        BeanUtil.copyProperties(contractEntity, salesContractTTTransferDTO);
        // 获取contractPrice
        ContractPriceEntity contractPriceEntity = contractPriceFacade.getContractPriceEntityContractId(contractEntity.getId());
        PriceDetailBO priceDetailBO = new PriceDetailBO();
        ContractTransferDTO contractTransferDTO = new ContractTransferDTO();
        BeanUtils.copyProperties(contractPriceEntity, priceDetailBO);
        PriceDetailBO originalPriceDetailBo = priceDetailBO;


        Result domainResult = domainCodeFacade.getLastestClosingPrice(newContractEntity.getGoodsCategoryId(), newContractEntity.getDomainCode(), new Date(), newContractEntity.getFutureCode());
        if (domainResult.isSuccess()) {
            DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(domainResult.getData()), DomainPriceEntity.class);
            if (null != domainPriceEntity) {
                priceDetailBO.setForwardPrice(domainPriceEntity.getPrice());
            }
        }
        //重新计算价格
        recalculatePrice(reverseDTO, priceDetailBO, contractPriceEntity, newContractEntity);
        // 点价截止日期
        String domainCode = StringUtils.isNotBlank(reverseDTO.getReverseMonth()) ? reverseDTO.getReverseMonth() : contractEntity.getDomainCode();
        setPriceEndTime(contractEntity, contractEntity.getDeliveryStartTime(), domainCode);

        if (!BigDecimalUtil.isEqual(reverseDTO.getReverseNum(), contractEntity.getContractNum())) {
            //部分反点价
            contractTransferDTO.setTtTranferType(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setType(TTTranferTypeEnum.PART_REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setContractSource(ContractActionEnum.REVERSE_PRICE_ALL_CONFIRM.getActionValue());
        } else {
            //全部反点价
            contractTransferDTO.setTtTranferType(TTTranferTypeEnum.REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setType(TTTranferTypeEnum.REVERSE_PRICING.getValue());
            salesContractTTTransferDTO.setContractSource(ContractActionEnum.REVERSE_PRICE_CONFIRM.getActionValue());
        }
        //签订日期
        newContractEntity.setSignDate(contractEntity.getCreatedAt());
        newContractEntity.setPriceEndType(contractEntity.getPriceEndType())
                .setPriceEndTime(contractEntity.getPriceEndTime());
        // 生成合同
        contractTransferDTO
                .setContractEntity(newContractEntity)
                .setReversePricingNum(reverseDTO.getReverseNum())
                .setDomainCode(domainCode);
        Result result = contractFacade.createSonContract(contractTransferDTO);
        if (null != result || result.getCode() == ResultCodeEnum.OK.getCode()) {
            ContractEntity subcontractCode = JSON.parseObject(JSON.toJSONString(result.getData()), ContractEntity.class);
            priceAllocateEntity
                    .setSubcontractCode(subcontractCode.getContractCode())
                    .setSubcontractId(subcontractCode.getId());
        }

        ObjectMapper mapper = new ObjectMapper();
        ContractEntity sonContractEntity = mapper.convertValue(result.getData(), ContractEntity.class);
        // 生成TT
        salesContractTTTransferDTO
                .setNum(reverseDTO.getReverseNum())
                .setPriceApplyId(priceId)
                .setOriginalDomainCode(contractEntity.getDomainCode())
                .setContractId(contractEntity.getId())
                .setSonContractId(sonContractEntity.getId())
                .setContractCode(sonContractEntity.getContractCode())
                .setContractType(ContractTypeEnum.JI_CHA.getValue())
                .setTotalAmount(newContractEntity.getTotalAmount())
                .setCifUnitPrice(newContractEntity.getCifUnitPrice())
                .setFobUnitPrice(newContractEntity.getFobUnitPrice())
                .setUnitPrice(newContractEntity.getUnitPrice())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDeliveryFactoryName(contractEntity.getDeliveryFactoryName())
                .setSignDate(contractEntity.getCreatedAt())
                .setDomainCode(domainCode)
                .setSourceContractId(contractEntity.getId())
        ;
        salesContractTTTransferDTO.setBelongCustomerId(newContractEntity.getBelongCustomerId());
        salesContractTTTransferDTO.setAddedDepositRate2(sonContractEntity.getAddedDepositRate2());
        ttdto.setSalesContractTTTransferDTO(salesContractTTTransferDTO);
        ttdto.setPriceDetailBO(priceDetailBO);

        String processorType = TTHandlerUtil
                .getTTProcessor(sonContractEntity.getSalesType(), TTTypeEnum.REVERSE_PRICE.getType(), sonContractEntity.getGoodsCategoryId());
        ttdto.setProcessorType(processorType);
        tradeTicketFacade.saveTT(ttdto);
        //更新旧合同
        if (!BigDecimalUtil.isEqual(reverseDTO.getReverseNum(), contractEntity.getContractNum())) {
            //部分反点价
            priceAllocateEntity.setType(AllocateTypeEnum.PART.getValue());
            BigDecimal oldUnitPrice = contractEntity.getUnitPrice();
            BigDecimal oldContractNum = BigDecimalUtil.subtract(CalcTypeEnum.COUNT, contractEntity.getContractNum(), reverseDTO.getReverseNum());
            BigDecimal newTotalAmount = oldUnitPrice.multiply(oldContractNum);
            BigDecimal newDepositAmount = newTotalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01));
            contractEntity.setTotalAmount(newTotalAmount);
            contractEntity.setContractNum(oldContractNum);
            contractEntity.setDepositAmount(newDepositAmount);
        } else {
            priceAllocateEntity.setType(AllocateTypeEnum.ALL.getValue());
            //全部反点价
            contractEntity.setTotalAmount(BigDecimal.ZERO);
            contractEntity.setContractNum(BigDecimal.ZERO);
            contractEntity.setDepositAmount(BigDecimal.ZERO);
            /*contractPriceEntity.setForwardPrice(BigDecimal.ZERO);
            originalPriceDetailBo.setForwardPrice(BigDecimal.ZERO);
            BigDecimal unitPrice = contractPriceFacade.calculatePriceBo(originalPriceDetailBo);
            contractEntity.setUnitPrice(unitPrice);*/
        }

        //创建分配单
        priceAllocateEntity.setPriceApplyId(priceId);
        addPurchasePriceAllocate(priceAllocateEntity);
        //修改合同状态
        contractEntity.setStatus(ContractStatusEnum.MODIFYING.getValue());
        contractFacade.updateContract(contractEntity);
        contractPriceFacade.updatePriceByContractId(contractPriceEntity);
        return Result.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result purchaseContractReverseNew(ReverseDTO reverseDTO) {
        String contractId = reverseDTO.getContractId();
        ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.valueOf(contractId));

        // 可提数量校验
        if (null != contractEntity && null != contractEntity.getApplyDeliveryNum() && BigDecimalUtil.isGreaterThanZero(contractEntity.getApplyDeliveryNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION);
        }

        // 尾量关闭校验
        if (null != contractEntity && BigDecimalUtil.isGreaterThanZero(contractEntity.getCloseTailNum())) {
            throw new BusinessException(ResultCodeEnum.CONTRACT_TAIL_CLOSE_NUM_EXCEPTION);
        }

        CustomerDTO customerDTO = customerFacade.getCustomerById(contractEntity.getSupplierId());

        if (DisableStatusEnum.DISABLE.getValue().equals(customerDTO.getStatus())) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_STATUS_ERROR);
        }

        //创建申请单
        PriceApplyDTO priceApplyDTO = new PriceApplyDTO();

        String domainCode = StringUtil.isNotEmpty(reverseDTO.getReverseMonth()) ? reverseDTO.getReverseMonth() : contractEntity.getDomainCode();

        priceApplyDTO
                .setTransactionPrice(reverseDTO.getReversePrice())
                .setFutureCode(contractEntity.getFutureCode())
                .setTranferFutureCode(contractEntity.getFutureCode())
                .setSystem(SystemEnum.MAGELLAN.getValue())
                .setApplyNum(reverseDTO.getReverseNum())
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setCompanyId(contractEntity.getCompanyId())
                .setCustomerId(contractEntity.getSupplierId())
                .setType(PriceTypeEnum.REVERSE_PRICING.getValue())
                .setApplyPrice(reverseDTO.getReversePrice())
                .setDominantCode(contractEntity.getDomainCode())
                .setTranferDominantCode(domainCode)
                .setTranferFutureCode(reverseDTO.getTranferFutureCode())
                .setApplyHandNum(reverseDTO.getApplyHandNum())
                .setContractId(Integer.parseInt(reverseDTO.getContractId()))
                .setCategoryId(contractEntity.getGoodsCategoryId())
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;
        //创建申请单
        Integer priceId = iPriceApplyService.priceApply(priceApplyDTO);
        PriceAllocateEntity priceAllocateEntity = new PriceAllocateEntity();

        CompanyEntity companyEntity = companyFacade.queryCompanyById(contractEntity.getCompanyId());
        String auditId = JwtUtils.getCurrentUserId();
        priceAllocateEntity
                .setFutureCode(contractEntity.getFutureCode())
                .setRawFutureCode(contractEntity.getFutureCode())
                .setContractCode(contractEntity.getContractCode())
                .setContractId(contractEntity.getId())
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setCompanyId(contractEntity.getCompanyId())
                .setPriceApplyType(PriceTypeEnum.REVERSE_PRICING.getValue())
                .setDeliveryFactoryCode(contractEntity.getDeliveryFactoryCode())
                .setDominantCode(contractEntity.getDomainCode())
                .setAllocateNum(reverseDTO.getReverseNum())
                .setStatus(AllocateStatusEnum.AUDIT_PASS.getValue())
                .setDominantCode(contractEntity.getDomainCode())
                .setCategoryId(contractEntity.getGoodsCategoryId())
                .setCustomerId(contractEntity.getSupplierId())
                .setCustomerName(contractEntity.getSupplierName())
                .setReviewerId(Integer.parseInt(auditId))
                .setReviewerName(employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId())))
                .setCreatedBy(Integer.parseInt(auditId))
                .setRawDominantCode(contractEntity.getDomainCode())
                .setAuditTime(new Date())
                .setCompanyName(companyEntity.getName())
                .setCategory1(String.valueOf(contractEntity.getCategory1()))
                .setCategory2(String.valueOf(contractEntity.getCategory2()))
                .setCategory3(String.valueOf(contractEntity.getCategory3()))
        ;

        ContractTransferDTO contractTransferDTO = new ContractTransferDTO();

        // 一口价反点为基差，重新获取期货价格
        // BUGFIX：case-1003020 基差合同期货价格取用数据不同 Author: Mr 2025-03-04 Start
        // Result result = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), domainCode, contractEntity.getSignDate(), contractEntity.getFutureCode());
        Result result = domainCodeFacade.getLastestClosingPrice(contractEntity.getGoodsCategoryId(), domainCode, new Date(), contractEntity.getFutureCode());
        // BUGFIX：case-1003020 基差合同期货价格取用数据不同 Author: Mr 2025-03-04 End
        if (result.isSuccess()) {
            DomainPriceEntity domainPriceEntity = JSON.parseObject(JSON.toJSONString(result.getData()), DomainPriceEntity.class);
            if (null != domainPriceEntity) {
                contractTransferDTO.setLatestForwardPrice(domainPriceEntity.getPrice());
            }
        }

        contractTransferDTO
                .setFee(reverseDTO.getReverseServiceCharge())
                .setContractId(Integer.valueOf(contractId))
                .setReversePricingNum(reverseDTO.getReverseNum())
                .setDomainCode(contractEntity.getDomainCode())
                .setTransferDominantCode(domainCode)
                .setTransferFutureCode(reverseDTO.getTranferFutureCode())
                .setTransactionPrice(priceApplyDTO.getTransactionPrice())
                .setPriceApplyId(priceId)
                .setTtTranferType(!BigDecimalUtil.isEqual(reverseDTO.getReverseNum(), contractEntity.getContractNum())
                        ? TTTranferTypeEnum.PART_REVERSE_PRICING.getValue() : TTTranferTypeEnum.REVERSE_PRICING.getValue());


        Result result1 = contractFacade.reversePriceContract(contractTransferDTO);
        if (result1.isSuccess()) {
            ContractEntity subcontractCode = JSON.parseObject(JSON.toJSONString(result1.getData()), ContractEntity.class);
            priceAllocateEntity
                    .setSubcontractCode(subcontractCode.getContractCode())
                    .setSubcontractId(subcontractCode.getId());
        } else {
            // 抛出trade域的异常
            throw new BusinessException(ResultCodeEnum.getByMsg(result1.getMessage()));
        }

        priceAllocateEntity.setType(!BigDecimalUtil.isEqual(reverseDTO.getReverseNum(), contractEntity.getContractNum()) ?
                AllocateTypeEnum.PART.getValue() : AllocateTypeEnum.ALL.getValue());

        //创建分配单
        priceAllocateEntity.setPriceApplyId(priceId);
        addPurchasePriceAllocate(priceAllocateEntity);

        return Result.success();
    }

    @Override
    public boolean cancelPriceAllocateOfContract(String contractId, String priceType) {
        List<PriceAllocateEntity> list = priceAllocateDao.cancelPriceAllocateOfContract(contractId, priceType);

        boolean b = true;
        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        for (PriceAllocateEntity priceAllocateEntity : list) {

            // 更新分配单
            priceAllocateEntity.setStatus(AllocateStatusEnum.AUDIT_REJECT.getValue())
                    .setReviewerName(name)
                    .setCreatedAt(new Date());
            priceAllocateDao.updateById(priceAllocateEntity);


            // 将分配单上的分配量还到申请单上
            BigDecimal allocateNum = priceAllocateEntity.getAllocateNum();
            Integer priceApplyId = priceAllocateEntity.getPriceApplyId();
            PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceApplyId);
            priceApplyEntity.setAllocateNum(BigDecimalUtil.subtract(CalcTypeEnum.COUNT, priceApplyEntity.getAllocateNum(), allocateNum));
            b = priceApplyDao.updateById(priceApplyEntity);

        }
        return b;
    }


    @Override
    public boolean cancelPriceAllocateOfPriceAllId(PriceAllocateEntity priceAllocateEntity, DistributionDTO distributionDTO) {
        // 更新分配单
        priceAllocateEntity.setStatus(AllocateStatusEnum.AUDIT_REJECT.getValue())
                .setAuditStatus(AuditStatusEnum.AUDIT_REJECT.getValue());
        priceAllocateDao.updateById(priceAllocateEntity);

        // 将分配单上的分配量还到申请单上
        Integer priceDealId = priceAllocateEntity.getPriceDealId();
        PriceDealDetailEntity priceDealDetailEntity = priceDealDetailDao.getById(priceDealId);
        //成交单已分配量
        BigDecimal dealAllocateNum = priceDealDetailEntity.getAllocateNum();
        dealAllocateNum = dealAllocateNum.subtract(priceAllocateEntity.getAllocateNum());
        priceDealDetailEntity.setAllocateNum(dealAllocateNum)
                .setNotAllocateNum(priceDealDetailEntity.getNotAllocateNum().add(priceAllocateEntity.getAllocateNum()));
        return priceDealDetailDao.updateById(priceDealDetailEntity);
    }

    @Override
    public List<PriceAllocateEntity> getPriceAllocateHasOperation(Integer customerId, String domainCode, Integer categoryId) {
        return priceAllocateDao.getPriceAllocateHasOperation(customerId, domainCode, categoryId);
    }

    @Override
    public List<PriceAllocateEntity> getNotDealByContractId(Integer contractId) {
        return priceAllocateDao.getNotDealByContractId(contractId);
    }

    @Override
    public List<PriceAllocateEntity> getByContractId(Integer contractId) {
        return priceAllocateDao.getByContractId(contractId);
    }

    @Override
    public List<PriceAllocateEntity> getAllocateByContractStructureId(Integer contractStructureId) {
        return priceAllocateDao.getAllocateByContractStructureId(contractStructureId);
    }

    @Override
    public BigDecimal hasOperation(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {

        List<PriceAllocateEntity> priceAllocateEntities = priceAllocateDao.getPriceAllocateHasOperation(priceApplyOperateNumDTO.getCustomerId(), priceApplyOperateNumDTO.getDominantCode(), priceApplyOperateNumDTO.getCategoryId());

        BigDecimal num = BigDecimal.ZERO;
        //分配单中已分配量
        for (PriceAllocateEntity priceAllocateEntity : priceAllocateEntities) {

            num = num.add(priceAllocateEntity.getAllocateNum());
        }

        //查询申请单
        List<PriceApplyEntity> priceApplyEntities = priceApplyDao.getPriceApplyHasOperation(priceApplyOperateNumDTO.getCustomerId(), priceApplyOperateNumDTO.getDominantCode(), priceApplyOperateNumDTO.getCategoryId());
        //查询申请单中的量
        for (PriceApplyEntity priceApplyEntity : priceApplyEntities) {
            //未成交的量相加
            if (PriceStatusEnum.WAIT_ALLOCATE.getValue() != priceApplyEntity.getStatus()) {
                num = num.add(priceApplyEntity.getApplyNum());
            } else {
                List<PriceAllocateEntity> priceAllocateEntity = this.getAllocateByDominantCode(priceApplyEntity.getId());
                if (priceAllocateEntity == null) {
                    return BigDecimal.ZERO;
                }
                for (PriceAllocateEntity priceAllocate : priceAllocateEntity) {

                    if (AllocateStatusEnum.WAIT_AUDIT.getValue() == priceAllocate.getStatus()) {
                        num = num.add(priceAllocate.getAllocateNum());
                    }
                }
            }
        }

        return num;
    }

    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
    @Override
    public List<PriceAllocateEntity> getPriceAllocateNum(PriceApplyOperateNumDTO priceApplyOperateNumDTO) {
        return priceAllocateDao.getPriceAllocateNum(priceApplyOperateNumDTO);
    }

    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
    private void recalculatePrice(ReverseDTO reverseDTO, PriceDetailBO priceDetailBO, ContractPriceEntity contractPriceEntity, ContractEntity contractEntity) {
        //期货价格
        BigDecimal transactionPrice = reverseDTO.getReversePrice();
        //基差价(原合同的基差价+（原合同期货价格-反点价成交的期货价格）)
        BigDecimal temp = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, contractPriceEntity.getForwardPrice(), transactionPrice);
        BigDecimal newExtraPrice = BigDecimalUtil.add(CalcTypeEnum.PRICE, contractEntity.getExtraPrice(), temp);
        priceDetailBO.setExtraPrice(newExtraPrice);
        //手续费
        priceDetailBO.setFee(reverseDTO.getReverseServiceCharge());
        //总价
        BigDecimal deliveryPrice = contractPriceEntity.getTransportPrice()
                .add(contractPriceEntity.getLiftingPrice())
                .add(contractPriceEntity.getDelayPrice())
                .add(contractPriceEntity.getTemperaturePrice())
                .add(contractPriceEntity.getOtherDeliveryPrice());
        BigDecimal taxRate = contractEntity.getTaxRate();
        //priceDetailBO.setForwardPrice(BigDecimal.ZERO);
        BigDecimal unitPrice = contractPriceFacade.calculatePriceBo(priceDetailBO);
        BigDecimal fobUnitPrice = BigDecimalUtil.subtract(CalcTypeEnum.PRICE, unitPrice, deliveryPrice);
        BigDecimal cifUnitPrice = BigDecimalUtil.div(CalcTypeEnum.PRICE, unitPrice, taxRate.add(BigDecimal.ONE));
        BigDecimal totalAmount = unitPrice.multiply(reverseDTO.getReverseNum());
        contractEntity.setTotalAmount(totalAmount);
        contractEntity.setCifUnitPrice(cifUnitPrice);
        contractEntity.setFobUnitPrice(fobUnitPrice);
        contractEntity.setUnitPrice(unitPrice);
        contractEntity.setDepositAmount(totalAmount.multiply(BigDecimal.valueOf(contractEntity.getDepositRate() * 0.01)));
    }

    /**
     * 记录操作日志
     *
     * @param logBizCodeEnum
     * @param referBizId
     * @param referBizCOde
     * @param metaData
     * @param date
     */
    private void recordOperationLog(LogBizCodeEnum logBizCodeEnum, Integer referBizId, String referBizCOde, String metaData, String date, Integer system) {

        // 记录日志
        operationLogFacade.recordOperationLog(
                new OperationDetailDTO().setBizCode(logBizCodeEnum.getBizCode())
                        .setTriggerSys(SystemEnum.getByValue(system).getDescription())
                        .setBizModule(ModuleTypeEnum.PRICE.getModule())
                        .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                        .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                        .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                        .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                        .setOperationName(logBizCodeEnum.getMsg())
                        .setReferBizId(referBizId)
                        .setReferBizCode(referBizCOde)
                        .setMetaData(metaData)
                        .setData(date)
        );

    }


    /**
     * 更新销售合同的期货价格
     *
     * @param contractEntity 合同实体
     * @param contractNum    合同剩余数量
     * @param totalPriceNum  合同定价数量
     * @return
     */
    private ContractEntity updateSalesContractForwardPrice(ContractEntity contractEntity, BigDecimal contractNum, BigDecimal totalPriceNum) {
        if (BigDecimalUtil.isEqual(contractNum, totalPriceNum)) {
            Result rtn = contractFacade.getConfirmPricedList(contractEntity.getId());
            if (rtn.isSuccess()) {
                List<ConfirmPriceDTO> confirmPriceDTOList = JSON.parseArray(JSON.toJSONString(rtn.getData()), ConfirmPriceDTO.class);
                BigDecimal totalPrice = BigDecimal.ZERO;
                BigDecimal totalNum = BigDecimal.ZERO;
                for (TTPriceEntity ttPriceEntity : confirmPriceDTOList) {
                    totalPrice = totalPrice.add(ttPriceEntity.getPrice().multiply(ttPriceEntity.getNum()));
                    totalNum = totalNum.add(ttPriceEntity.getNum());
                }

                if (BigDecimalUtil.isGreaterThanZero(totalNum)) {
                    // 加权平均价
                    BigDecimal averagePrice = BigDecimalUtil.div(CalcTypeEnum.AVE_PRICE, totalPrice, totalNum);

                    log.info("updateContractForwardPrice:{},averagePrice→:{}", contractEntity.getId(), averagePrice);

                    // 更新期货价格
                    Result result = contractPriceFacade.updateContractForwardPrice(contractEntity, averagePrice);
                    if (result.isSuccess()) {
                        contractEntity = JSON.parseObject(JSON.toJSONString(result.getData()), ContractEntity.class);
                    }
                }
            }
        }
        return contractEntity;
    }

    /**
     * 分配单撤回
     *
     * @param applyContraryDTO
     * @return
     */
    @Override
    @MultiSubmit
    @Transactional(rollbackFor = Exception.class)
    public Boolean priceAllocateContrary(ApplyContraryDTO applyContraryDTO) {

        //根据申请单id查询出申请单下的分配单
        List<PriceAllocateEntity> priceAllocateEntities = priceAllocateDao.getAllocateByPriceApplyIdStatus(applyContraryDTO.getPriceApplyId());
        if (priceAllocateEntities.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.PRICE_ALLOCATE_NOT_EXIST);
        }

        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(applyContraryDTO.getPriceApplyId());
        if (priceApplyEntity.getDealContraryNum() > 2) {
            throw new BusinessException(ResultCodeEnum.APPLY_DEAL_CONTRARY_NUM);
        }
        //校验合同是否已经操作
        if (PriceTypeEnum.PRICING.getValue() == priceApplyEntity.getType()) {
            Integer allowCancel = allowCancel(applyContraryDTO.getPriceApplyId());
            if (AllowCancelEnum.CANCEL.getValue() != allowCancel) {
                throw new BusinessException(ResultCodeEnum.APPLY_CANCEL_CONTRACT_ALREADY_DISPOSE);
            }
        }

        for (PriceAllocateEntity priceAllocateEntity : priceAllocateEntities) {

            if ((ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntity.getSalesType()
                    && AllocateStatusEnum.AUDIT_PASS.getValue() == priceAllocateEntity.getStatus())
                    || ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocateEntity.getSalesType()
                    && AllocateStatusEnum.CONTRARY.getValue() != priceAllocateEntity.getStatus()) {

                Integer contractSignStatus = contractSignStatus(priceAllocateEntity);
                //已签署不予撤回
                if (ContractSignStatusEnum.WAIT_STAMP.getValue() < contractSignStatus) {
                    throw new BusinessException(ResultCodeEnum.CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY);
                }

                if (AllocateTypeEnum.PART.getValue() == priceAllocateEntity.getType()) {
                    List<PriceAllocateEntity> priceAllocates = priceAllocateDao.getAllocateTypeByPriceApplyId(priceAllocateEntity.getContractId(),
                            priceAllocateEntity.getId(),
                            AllocateTypeEnum.ALL.getValue());

                    for (PriceAllocateEntity priceAllocate : priceAllocates) {
                        Integer contractSignStatus1 = contractSignStatus(priceAllocate);
                        if (ContractSignStatusEnum.WAIT_BACK.getValue() > contractSignStatus1) {
                            throw new BusinessException(ResultCodeEnum.APPLY_CONTRARY_ALL_EXCEPTION);
                        }
                    }

                }
            }
        }


        if (ContractSalesTypeEnum.SALES.getValue() == priceAllocateEntities.get(0).getSalesType()) {
            //根据申请单id撤回成交单
            iPriceDealService.priceDealCancel(applyContraryDTO);
        } else {
            //根据申请单id撤回申请单
            iPriceApplyService.priceApplyWithdraw(applyContraryDTO);
        }

        //撤回分配单
        for (PriceAllocateEntity priceAllocate : priceAllocateEntities) {
            //调用TT定价单撤回
            if ((ContractSalesTypeEnum.SALES.getValue() == priceAllocate.getSalesType()
                    && AllocateStatusEnum.AUDIT_PASS.getValue() == priceAllocate.getStatus())
                    || ContractSalesTypeEnum.PURCHASE.getValue() == priceAllocate.getSalesType()
            ) {
                Result result = tradeTicketFacade.contraryPrice(
                        new ContraryPriceDTO()
                                .setAllocateId(priceAllocate.getId())
                                .setCategoryId(priceAllocate.getCategoryId())
                                .setPriceApplyType(priceAllocate.getPriceApplyType())
                                .setSalesType(priceAllocate.getSalesType())
                                .setContraryCause(ContraryCauseEnum.getByValue(applyContraryDTO.getContraryCause()).getDescription())
                );

                if (result == null || result.getCode() != ResultCodeEnum.OK.getCode()) {
                    throw new BusinessException(result.getCode(), result.getMessage());
                }
            }

            //撤回分配单
            priceAllocate.setStatus(AllocateStatusEnum.CONTRARY.getValue());
            priceAllocateDao.updateById(priceAllocate);
        }

        return true;
    }

    //case-1002795 并发操作的事务控制问题（采购,点价转月数量校验） Author: wan 2024-10-15 Start
    //校验采购可操作数量书否满足需求
    private void hasOperation(List<PriceTransferDTO> priceTransferDTOs) {

        BigDecimal applyNum = BigDecimal.ZERO;
        BigDecimal canPriceNum = BigDecimal.ZERO;
        Integer type = PriceTypeEnum.PRICING.getValue();
        Integer customerId = 0;
        String domainCode = "";
        Integer companyId = 1;
        Integer goodsCategoryId = 0;

        for (PriceTransferDTO priceTransferDTO : priceTransferDTOs) {
            applyNum = applyNum.add(priceTransferDTO.getPriceTransferNum());
            type = priceTransferDTO.getType();
            ContractEntity contractEntity = contractFacade.getBasicContractById(Integer.parseInt(priceTransferDTO.getContractId()));
            customerId = contractEntity.getSupplierId();
            domainCode = contractEntity.getDomainCode();
            companyId = contractEntity.getCompanyId();
            goodsCategoryId = contractEntity.getGoodsCategoryId();
        }

        PriceTypeEnum priceTypeEnum = PriceTypeEnum.getByValue(type);

        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO.setCustomerId(String.valueOf(customerId))
                .setDomainCode(domainCode)
                .setCompanyId(companyId)
                .setSalesType(ContractSalesTypeEnum.PURCHASE.getValue())
                .setGoodsCategoryId(goodsCategoryId);

        switch (priceTypeEnum) {
            case PRICING:
                CustomerFuturesDTO priceDTO = futuresDomainService.mayPriceNum(contractFuturesDTO);
                this.checkPriceApplyNum(priceDTO.getMayPriceNum(), applyNum);
                break;
            case TRANSFER_MONTH:
                CustomerFuturesDTO transferDTO = futuresDomainService.mayTransferNum(contractFuturesDTO);
                this.checkPriceApplyNum(transferDTO.getMayTransferNum(), applyNum);
                break;
            case REVERSE_PRICING:
                this.checkPriceApplyNum(canPriceNum, applyNum);
                break;
        }

    }

    /**
     * 校验申请数量 与可点数量
     *
     * @param canPriceNum
     * @param applyNum
     */
    private void checkPriceApplyNum(BigDecimal canPriceNum, BigDecimal applyNum) {
        if (BigDecimalUtil.isLess(canPriceNum, applyNum)) {
            throw new BusinessException(ResultCodeEnum.APPLY_NUM_MORE_THAN_CAN_PRICE_NUM);
        }
        if (BigDecimalUtil.isLess(canPriceNum.subtract(applyNum), BigDecimal.TEN) && !BigDecimalUtil.isEqual(canPriceNum.subtract(applyNum), BigDecimal.ZERO)) {
            throw new BusinessException(ResultCodeEnum.SURPLUS_NUM_LESS_THAN_TEN);
        }
        if (applyNum.intValue() < 10 && BigDecimalUtil.isGreater(canPriceNum.subtract(applyNum), BigDecimal.TEN)) {
            throw new BusinessException(ResultCodeEnum.APPLY_NUM_MUST_MULTIPLE_OF_TEN);
        }
    }
    //case-1002795 并发操作的事务控制问题（采购,点价转月数量校验） Author: wan 2024-10-15 end
}
