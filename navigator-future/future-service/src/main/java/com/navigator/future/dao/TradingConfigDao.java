package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.mapper.TradingConfigMapper;
import com.navigator.future.pojo.entity.TradingConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/19
 */
@Dao
public class TradingConfigDao extends BaseDaoImpl<TradingConfigMapper, TradingConfigEntity> {

    public List<TradingConfigEntity> queryTradingConfigList() {
        return this.baseMapper.selectList(Wrappers.<TradingConfigEntity>lambdaQuery()
                .eq(TradingConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public List<TradingConfigEntity> queryTradingConfigByFutureCode(String getFutureCode) {
        return this.baseMapper.selectList(Wrappers.<TradingConfigEntity>lambdaQuery()
                .eq(TradingConfigEntity::getFutureCode, getFutureCode)
                .eq(TradingConfigEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }
}
