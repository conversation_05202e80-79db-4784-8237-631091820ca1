package com.navigator.future.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.enums.PricePowerType;
import com.navigator.future.facade.PricePowerTimeFacade;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.pojo.vo.PricePowerTimeVO;
import com.navigator.future.service.IPricePowerTimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@Component
public class PricePowerTimeFacadeImpl implements PricePowerTimeFacade {

    @Autowired
    private IPricePowerTimeService pricePowerTimeService;

    @Override
    public Result<Boolean> savePricePowerTime(PricePowerTimeEntity pricePowerTime) {
        Integer result = pricePowerTimeService.savePricePowerTime(pricePowerTime);
        if (result == 1)
            return Result.success("保存成功", true);
        else if (result == 0)
            return Result.failure("保存失败");
        else if (result == 2)
            return Result.failure("与现有配置区间重合，请检查");
        else
            return Result.failure("保存失败");
    }

    @Override
    public Result<Boolean> updatePricePowerTime(PricePowerTimeEntity pricePowerTime) {
        Integer result = pricePowerTimeService.updatePricePowerTime(pricePowerTime);
        if (result == 1)
            return Result.success("更新成功", true);
        else if (result == 0)
            return Result.failure("更新失败！");
        else if (result == 2)
            return Result.failure("与现有配置区间重合，请检查");
        else
            return Result.failure("更新失败！");
    }

    @Override
    public Result<Boolean> deletePricePowerTime(PricePowerTimeEntity pricePowerTime) {
        Boolean flag = pricePowerTimeService.deletePricePowerTime(pricePowerTime.getId());
        if (flag)
            return Result.success("更新成功", flag);
        else
            return Result.failure("更新失败！");
    }

    @Override
    public Result queryPricePowerTimeList(QueryDTO<PricePowerTimeEntity> queryDTO) {
        List<PricePowerTimeEntity> pricePowerTimeEntityIPage = pricePowerTimeService.queryPricePowerTimeListPage(queryDTO);
        return Result.success(pricePowerTimeEntityIPage);
    }

    @Override
    public List<PricePowerTimeVO> exportPricePowerTimeList(String categoryIdList) {
        List<PricePowerTimeEntity> pricePowerTimeList = pricePowerTimeService.exportPricePowerTimeList(categoryIdList);
        List<PricePowerTimeVO> pricePowerTimeVOS = new ArrayList<>();
        for (PricePowerTimeEntity entity : pricePowerTimeList) {
            PricePowerTimeVO vo = new PricePowerTimeVO();
            BeanUtils.copyProperties(entity, vo);
            vo.setMagellanPricePowerTypeStr(PricePowerType.getByValue(entity.getMagellanPricePowerType()).getDescription());
            vo.setColumPricePowerTypeStr(PricePowerType.getByValue(entity.getColumPricePowerType()).getDescription());
            vo.setCreatedAt(DateTimeUtil.formatDate(entity.getCreatedAt()));
            vo.setUpdatedAt(DateTimeUtil.formatDate(entity.getUpdatedAt()));
            pricePowerTimeVOS.add(vo);
        }
        return pricePowerTimeVOS;
    }
}
