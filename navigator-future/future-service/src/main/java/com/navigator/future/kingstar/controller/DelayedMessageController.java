package com.navigator.future.kingstar.controller;

import com.navigator.future.kingstar.sender.ServiceBusDelayedSender;
import com.navigator.future.pojo.dto.kingstar.KingStarMessageRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 简单的延迟消息测试控制器
 * 使用 Azure Service Bus 原生 SDK
 *
 * <AUTHOR>
 * @date 2025-7-14
 */
@Slf4j
@RestController
@RequestMapping("/test/simple-delayed")
@RequiredArgsConstructor
public class DelayedMessageController {

    private final ServiceBusDelayedSender delayedSender;

    /**
     * 发送延迟消息 - 指定延迟秒数
     */
    @PostMapping("/send")
    public Map<String, Object> sendDelayedMessage(
            @RequestParam String message,
            @RequestParam(defaultValue = "30") int delaySeconds) {

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> messageData = new HashMap<>();
            messageData.put("action", "test");
            messageData.put("message", message);
            messageData.put("timestamp", System.currentTimeMillis());

            // 使用原生 SDK 发送延迟消息
            delayedSender.sendDelayedMessage(messageData, null, delaySeconds);

            result.put("success", true);
            result.put("message", "延迟消息发送成功");
            result.put("delaySeconds", delaySeconds);
            result.put("scheduledTime", LocalDateTime.now().plusSeconds(delaySeconds));

        } catch (Exception e) {
            log.error("发送延迟消息失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 发送延迟消息 - 接收JSON格式的KingStarMessageRequestDTO
     */
    @PostMapping("/sendJson")
    public Map<String, Object> sendDelayedJsonMessage(
            @RequestBody KingStarMessageRequestDTO messageRequest,
            @RequestParam(defaultValue = "30") int delaySeconds) {

        Map<String, Object> result = new HashMap<>();

        try {
            // 验证必要字段
            if (messageRequest == null) {
                result.put("success", false);
                result.put("error", "请求体不能为空");
                return result;
            }

            if (messageRequest.getRequestDTO() == null) {
                result.put("success", false);
                result.put("error", "requestDTO不能为空");
                return result;
            }

            // 记录接收到的消息信息
            log.info("接收到KingStar延迟消息请求: userCode={}, requestId={}, instructId={}",
                    messageRequest.getUserCode(),
                    messageRequest.getRequestId(),
                    messageRequest.getInstructId());

            // 使用原生 SDK 发送延迟消息，直接传递整个 messageRequest 对象
            delayedSender.sendDelayedMessage(messageRequest, messageRequest.getInstructId(), delaySeconds);

            result.put("success", true);
            result.put("message", "KingStar延迟消息发送成功");
            result.put("delaySeconds", delaySeconds);
            result.put("scheduledTime", LocalDateTime.now().plusSeconds(delaySeconds));
            result.put("userCode", messageRequest.getUserCode());
            result.put("requestId", messageRequest.getRequestId());
            result.put("instructId", messageRequest.getInstructId());

        } catch (Exception e) {
            log.error("发送KingStar延迟消息失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 取消延迟消息
     *
     * @param instructId 指令ID
     * @return 取消结果
     */
    @PostMapping("/cancelByInstructId")
    public Map<String, Object> cancelDelayedMessage(@RequestParam String instructId) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = delayedSender.cancelDelayedMessage(instructId);
            result.put("success", success);
            if (success) {
                result.put("message", "取消延迟消息成功");
            } else {
                result.put("message", "未找到对应的延迟消息或取消失败");
            }
        } catch (Exception e) {
            log.error("取消延迟消息失败: instructId={}, error={}", instructId, e.getMessage(), e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

}
