package com.navigator.future.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.RecordOperationDetail;
import com.navigator.admin.pojo.enums.OperationActionEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.dao.PricePowerTimeDao;
import com.navigator.future.enums.AddUpdateStateEnum;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.service.IPricePowerTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class PricePowerTimeServiceImpl implements IPricePowerTimeService {

    @Resource
    PricePowerTimeDao dao;

    @Resource
    private EmployFacade employFacade;

    @Autowired
    OperationLogFacade operationLogFacade;

    @Override
    public Integer savePricePowerTime(PricePowerTimeEntity pricePowerTimeEntity) {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String categoryIdList = pricePowerTimeEntity.getCategoryIdList();
        List<String> categoryList = new ArrayList<>();
        if (StringUtil.isNotEmpty(categoryIdList)) {
            String[] split = categoryIdList.split(",");
            if (split != null && split.length != 0) {
                for (String str : split)
                    categoryList.add(str);
            }
        }else{
            categoryList.add(categoryIdList);
        }
        List<PricePowerTimeEntity> allList = new ArrayList<>();
        for (String itemSplit : categoryList) {
            List<PricePowerTimeEntity> list = dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .ne(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, itemSplit));
            allList.addAll(list);
        }
        int startInt = DateUtil.timeToSecond(pricePowerTimeEntity.getStartTime().toString());
        int endInt = DateUtil.timeToSecond(pricePowerTimeEntity.getEndTime().toString());
        for (PricePowerTimeEntity item : allList) {
            if (startInt >= DateUtil.timeToSecond(item.getStartTime().toString()) && startInt <= DateUtil.timeToSecond(item.getEndTime().toString())) {
                return 2;
            }
            if (endInt >= DateUtil.timeToSecond(item.getStartTime().toString()) && endInt <= DateUtil.timeToSecond(item.getEndTime().toString())) {
                return 2;
            }
            if (DateUtil.timeToSecond(item.getStartTime().toString()) >= startInt && DateUtil.timeToSecond(item.getStartTime().toString()) <= endInt) {
                return 2;
            }
            if (DateUtil.timeToSecond(item.getEndTime().toString()) >= startInt && DateUtil.timeToSecond(item.getEndTime().toString()) <= endInt) {
                return 2;
            }
        }

        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        pricePowerTimeEntity.setCreatedAt(DateTimeUtil.now());
        pricePowerTimeEntity.setUpdatedAt(DateTimeUtil.now());
        pricePowerTimeEntity.setCreatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        pricePowerTimeEntity.setCreatedByName(name);
        pricePowerTimeEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        pricePowerTimeEntity.setUpdatedByName(name);
        pricePowerTimeEntity.setAddUpdateState(AddUpdateStateEnum.ADD.getValue());
        pricePowerTimeEntity.setCreatedDate(today);
        boolean save = dao.save(pricePowerTimeEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(pricePowerTimeEntity))
                    .setOperationActionEnum(OperationActionEnum.PRICE_POWER_TIME_ADD);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (save)
            return 1;
        else
            return 0;
    }

    @Override
    public Integer updatePricePowerTime(PricePowerTimeEntity pricePowerTimeEntity) {
        PricePowerTimeEntity oldPricePower = dao.getById(pricePowerTimeEntity.getId());

        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String categoryIdList = pricePowerTimeEntity.getCategoryIdList();
        List<String> categoryList = new ArrayList<>();
        if (StringUtil.isNotEmpty(categoryIdList)) {
            String[] split = categoryIdList.split(",");
            if (split != null && split.length != 0) {
                for (String str : split)
                    categoryList.add(str);
            }
        }else{
            categoryList.add(categoryIdList);
        }
        List<PricePowerTimeEntity> allList = new ArrayList<>();
        for (String itemSplit : categoryList) {
            List<PricePowerTimeEntity> list = dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .ne(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, itemSplit));
            allList.addAll(list);
        }

        //修改的时候他本身的不判断
        int startInt = DateUtil.timeToSecond(pricePowerTimeEntity.getStartTime().toString());
        int endInt = DateUtil.timeToSecond(pricePowerTimeEntity.getEndTime().toString());
        for (PricePowerTimeEntity item : allList) {
            if (pricePowerTimeEntity.getId() == item.getId())
                continue;
            if (startInt >= DateUtil.timeToSecond(item.getStartTime().toString()) && startInt <= DateUtil.timeToSecond(item.getEndTime().toString())) {
                return 2;
            }
            if (endInt >= DateUtil.timeToSecond(item.getStartTime().toString()) && endInt <= DateUtil.timeToSecond(item.getEndTime().toString())) {
                return 2;
            }
            if (DateUtil.timeToSecond(item.getStartTime().toString()) >= startInt && DateUtil.timeToSecond(item.getStartTime().toString()) <= endInt) {
                return 2;
            }
            if (DateUtil.timeToSecond(item.getEndTime().toString()) >= startInt && DateUtil.timeToSecond(item.getEndTime().toString()) <= endInt) {
                return 2;
            }
        }

        String name = employFacade.getEmployCache(Integer.parseInt(JwtUtils.getCurrentUserId()));
        PricePowerTimeEntity updateTime = new PricePowerTimeEntity();
        updateTime.setId(pricePowerTimeEntity.getId());
        updateTime.setUpdatedAt(DateTimeUtil.now());
        updateTime.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        updateTime.setUpdatedByName(name);
        updateTime.setAddUpdateState(AddUpdateStateEnum.UPDATED.getValue());
        updateTime.setUpdatedDate(today);
        dao.updateById(updateTime);

        PricePowerTimeEntity newPricePowerTimeEntity = new PricePowerTimeEntity();
        newPricePowerTimeEntity.setStartTime(pricePowerTimeEntity.getStartTime());
        newPricePowerTimeEntity.setEndTime(pricePowerTimeEntity.getEndTime());
        newPricePowerTimeEntity.setMagellanPricePowerType(pricePowerTimeEntity.getMagellanPricePowerType());
        newPricePowerTimeEntity.setColumPricePowerType(pricePowerTimeEntity.getColumPricePowerType());
        newPricePowerTimeEntity.setCategoryIdList(pricePowerTimeEntity.getCategoryIdList());
        newPricePowerTimeEntity.setCategoryNameList(pricePowerTimeEntity.getCategoryNameList());
        newPricePowerTimeEntity.setCreatedAt(oldPricePower.getCreatedAt());
        newPricePowerTimeEntity.setUpdatedAt(DateTimeUtil.now());
        newPricePowerTimeEntity.setCreatedBy(oldPricePower.getCreatedBy());
        newPricePowerTimeEntity.setCreatedByName(oldPricePower.getCreatedByName());
        newPricePowerTimeEntity.setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()));
        newPricePowerTimeEntity.setUpdatedByName(name);
        newPricePowerTimeEntity.setAddUpdateState(AddUpdateStateEnum.UPDATED_ADD.getValue());
        newPricePowerTimeEntity.setCreatedDate(today);
        boolean save = dao.save(newPricePowerTimeEntity);

        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(newPricePowerTimeEntity))
                    .setBeforeData(JSON.toJSONString(oldPricePower))
                    .setOperationActionEnum(OperationActionEnum.PRICE_POWER_TIME_UPDATE);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (save)
            return 1;
        else
            return 0;
    }

    @Override
    public Boolean deletePricePowerTime(Integer id) {
        try {
            RecordOperationDetail recordOperationDetail = new RecordOperationDetail();
            recordOperationDetail
                    .setDtoData(JSON.toJSONString(id))
                    .setOperationActionEnum(OperationActionEnum.PRICE_POWER_TIME_DELETE);
            operationLogFacade.recordMagellanOperationDetail(recordOperationDetail);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dao.removeById(id);
    }

    public List<PricePowerTimeEntity> queryPricePowerTimeListPage(QueryDTO<PricePowerTimeEntity> queryDTO) {
        return dao.queryPricePowerTimeListPage(queryDTO);
    }

    @Override
    public List<PricePowerTimeEntity> exportPricePowerTimeList(String categoryIdList) {
        return dao.exportPricePowerTimeList(categoryIdList);
    }

    @Override
    public List<PricePowerTimeEntity> queryMagellanPricePowerTimeInsertList(String categoryIdList, int type, String today, boolean isValid) {
        if (isValid)
            //包含所有新增的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getMagellanPricePowerType, type)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.ADD.getValue())
            );
        else {
            //不包含今天新增的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getMagellanPricePowerType, type)
                    .ne(PricePowerTimeEntity::getCreatedDate, today)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.ADD.getValue())
            );
        }
    }

    @Override
    public List<PricePowerTimeEntity> queryMagellanPricePowerTimeUpdateList(String categoryIdList, int type, String today, boolean isValid) {
        if (isValid)
            //所有更新后新增加的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getMagellanPricePowerType, type)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED_ADD.getValue())
            );
        else {
            //今天被更新的数据
            List<PricePowerTimeEntity> list = dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getMagellanPricePowerType, type)
                    .eq(PricePowerTimeEntity::getUpdatedDate, today)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED.getValue())
            );
            //之前更新后新增加的数据（非今天）
            list.addAll(
                    dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                            .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                            .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                            .eq(PricePowerTimeEntity::getMagellanPricePowerType, type)
                            .ne(PricePowerTimeEntity::getCreatedDate, today)
                            .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED_ADD.getValue())
                    )
            );
            return list;
        }
    }

    @Override
    public List<PricePowerTimeEntity> queryColumbusPricePowerTimeInsertList(String categoryIdList, int type, String today, boolean isValid) {
        if (isValid)
            //包含所有新增的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getColumPricePowerType, type)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.ADD.getValue())
            );
        else {
            //不包含今天新增的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getColumPricePowerType, type)
                    .ne(PricePowerTimeEntity::getCreatedDate, today)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.ADD.getValue())
            );
        }
    }

    @Override
    public List<PricePowerTimeEntity> queryColumbusPricePowerTimeUpdateList(String categoryIdList, int type, String today, boolean isValid) {
        if (isValid)
            //所有更新后新增加的数据
            return dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getColumPricePowerType, type)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED_ADD.getValue())
            );
        else {
            //今天被更新的数据
            List<PricePowerTimeEntity> list = dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                    .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                    .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                    .eq(PricePowerTimeEntity::getColumPricePowerType, type)
                    .eq(PricePowerTimeEntity::getUpdatedDate, today)
                    .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED.getValue())
            );
            //之前更新后新增加的数据（非今天）
            list.addAll(
                    dao.list(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                            .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                            .like(PricePowerTimeEntity::getCategoryIdList, categoryIdList)
                            .eq(PricePowerTimeEntity::getColumPricePowerType, type)
                            .ne(PricePowerTimeEntity::getCreatedDate, today)
                            .eq(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED_ADD.getValue())
                    )
            );
            return list;
        }
    }
}
