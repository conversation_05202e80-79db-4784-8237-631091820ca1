package com.navigator.future.facade.impl;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.service.IFutureTransService;
import com.navigator.future.service.IFuturesDomainService;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 18:41
 */
@RestController
public class FuturesDomainFacadeImpl implements FuturesDomainFacade {

    @Resource
    private IFuturesDomainService futuresDomainService;
    @Resource
    private IFutureTransService futureTransService;
    @Resource
    private IPriceApplyService priceApplyService;

    @Override
    public Result queryContractsFutures(QueryContractFuturesDTO queryContractFuturesDTO) {
        return Result.success(futuresDomainService.queryContractsFutures(queryContractFuturesDTO));
    }

    @Override
    public Result mayTransferNum(ContractFuturesDTO contractFuturesDTO) {

        return Result.success(futuresDomainService.mayTransferNum(contractFuturesDTO));
        /*CustomerDomainTransDTO customerDomainTransDTO = futureTransService.getCustomerDomainTransInfo(contractFuturesDTO);
        return Result.success(customerDomainTransDTO.getCanTransferCount());*/
    }

    @Override
    public Result mayPriceNum(ContractFuturesDTO contractFuturesDTO) {
        return Result.success(futuresDomainService.mayPriceNum(contractFuturesDTO));
       /* CustomerDomainTransDTO customerDomainTransDTO = futureTransService.getCustomerDomainTransInfo(contractFuturesDTO);
        return Result.success(customerDomainTransDTO.getCanPriceCount());*/
    }

    @Override
    public Result structurePriceNum(Integer contractId) {
        List<PriceApplyEntity> priceApplyEntities = priceApplyService.queryPriceApplyByContractId(contractId);

        PriceApplyEntity priceApplyEntity = priceApplyEntities.isEmpty() ? null : priceApplyEntities.get(0);
        ContractFuturesDTO contractFuturesDTO = new ContractFuturesDTO();
        contractFuturesDTO.setApplyId(priceApplyEntity.getId().toString())
                .setCustomerId(priceApplyEntity.getCustomerId().toString())
                .setDomainCode(priceApplyEntity.getDominantCode())
                .setCategory2(Integer.parseInt(priceApplyEntity.getCategory2()));
        return Result.success(futuresDomainService.mayPriceNum(contractFuturesDTO));
    }

    @Override
    public Result getCustomerDomainTransInfo(@RequestBody ContractFuturesDTO contractFuturesDTO) {
        return Result.success(futureTransService.getCustomerDomainTransInfo(contractFuturesDTO));
    }
}
