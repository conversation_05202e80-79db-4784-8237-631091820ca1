package com.navigator.future.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 点价权限时间 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
public interface IPricePowerTimeService {


    /**
     * 保存点价权限时间
     *
     * @param pricePowerTimeEntity
     * @return
     */
    Integer savePricePowerTime(PricePowerTimeEntity pricePowerTimeEntity);

    Integer updatePricePowerTime(PricePowerTimeEntity pricePowerTime);

    Boolean deletePricePowerTime(Integer id);

    List<PricePowerTimeEntity> queryPricePowerTimeListPage(QueryDTO<PricePowerTimeEntity> queryDTO);

    List<PricePowerTimeEntity> exportPricePowerTimeList(String categoryIdList);

    List<PricePowerTimeEntity> queryMagellanPricePowerTimeInsertList(String categoryIdList,int type,String today,boolean isInsertValid);

    List<PricePowerTimeEntity> queryMagellanPricePowerTimeUpdateList(String categoryIdList, int type,String today,boolean isValid);

    List<PricePowerTimeEntity> queryColumbusPricePowerTimeInsertList(String categoryIdList,int type,String today,boolean isInsertValid);

    List<PricePowerTimeEntity> queryColumbusPricePowerTimeUpdateList(String categoryIdList, int type,String today,boolean isValid);
}
