package com.navigator.future.facade.impl;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.facade.PositionFacade;
import com.navigator.future.pojo.dto.PositionDTO;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import com.navigator.future.pojo.vo.PositionVO;
import com.navigator.future.service.IPositionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
public class PositionFacadeImpl implements PositionFacade {
    @Autowired
    private IPositionService positionService;

    @Override
    public Result save(PositionDTO positionDTO) {
        positionService.save(positionDTO);
        return Result.success();
    }

    /**
     *
     * @param positionQueryDTO
     * @return
     */
    @Override
    public Result queryPositionList(QueryDTO<PositionQueryDTO> positionQueryDTO) {

        return positionService.queryPositionList(positionQueryDTO);
    }

    @Override
    public Result queryPositionDetail(Integer id) {
        PositionVO positionVO = positionService.queryPositionDetail(id);
        return Result.success(positionVO);
    }

    @Override
    public Result updatePositionStatus(PositionDTO positionDTO) {
        positionService.updatePositionStatus(positionDTO);
        return Result.success();
    }

    @Override
    public Result dealBatch(PositionDTO positionDTO) {
        positionService.dealBatch(positionDTO);
        return Result.success();
    }

    @Override
    public Result export(Integer categoryId) {
        List<PositionVO> positionVOList = positionService.export(categoryId);
        return Result.success(positionVOList);
    }

    @Override
    public Result check(MultipartFile file) {
        return positionService.check(file);
    }

    @Override
    public Result importFile(MultipartFile file) {
        return positionService.importFile(file);
    }

    @Override
    public Result exportSubmitList(PositionQueryDTO positionQueryDTO) {
        QueryDTO<PositionQueryDTO> queryDTO = new QueryDTO<>();
        queryDTO.setCondition(positionQueryDTO);
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(1000000);
        return positionService.queryPositionList(queryDTO);
    }

    @Override
    public Result queryPositionRefreshLog(Integer positionId) {
        return Result.success(positionService.queryPositionRefreshLog(positionId));
    }

    @Override
    public Result modify(PositionDTO positionDTO) {
        positionService.modify(positionDTO);
        return Result.success();
    }

    @Override
    public Result cancel(PositionDTO positionDTO) {
        positionService.cancel(positionDTO);
        return Result.success();
    }

    @Override
    public Result queryModifyLog(Integer positionId) {
        return positionService.queryModifyLog(positionId);
    }

}
