package com.navigator.future.facade.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.pojo.entity.StructureRuleEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.StructureCodeUtil;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.ApplyContraryDTO;
import com.navigator.future.pojo.dto.NotDealDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.dto.StructurePriceApplyDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.entity.PriceDealInfoEntity;
import com.navigator.future.pojo.vo.*;
import com.navigator.future.service.IPriceApplyLogService;
import com.navigator.future.service.IPriceApplyService;
import com.navigator.future.service.IPriceDealInfoService;
import com.navigator.future.service.IPriceDealService;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.entity.ContractStructureEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/5 18:10
 */
@RestController
@Slf4j
public class PriceApplyFacadeImpl implements PriceApplyFacade {

    @Resource
    private IPriceApplyService priceApplyService;
    @Resource
    private IPriceApplyLogService priceApplyLogService;
    @Resource
    private IPriceDealService priceDealService;
    @Autowired
    private ContractFacade contractFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Autowired
    private IPriceDealInfoService priceDealInfoService;
    @Autowired
    private StructureRuleFacade structureRuleFacade;

    @Override
    public Result getpriceApplyDetail(Integer priceApplyId) {
        return Result.success(priceApplyService.getpriceApplyDetail(priceApplyId));
    }

    @Override
    public Result<Integer> priceApply(PriceApplyDTO priceApplyDTO) {
        Result result = Result.success();
        int priceApplyId = priceApplyService.priceApply(priceApplyDTO);
        result.setData(priceApplyId);
        return result;
    }

    @Override
    public Result modifyPriceApply(PriceApplyDTO priceApplyDTO) {
        priceApplyService.modifyPriceApply(priceApplyDTO);
        return Result.success();
    }

    @Override
    public Result queryPriceApply(QueryDTO<PriceApplyBO> applyBOQueryDTO, SystemEnum systemEnum) {
        return priceApplyService.queryPriceApply(applyBOQueryDTO, systemEnum);
    }

    @Override
    public Result queryPriceApplyLog(Integer priceApplyId) {
        return Result.success(priceApplyLogService.queryPriceApplyLog(priceApplyId));
    }

    @Override
    public Result queryPriceApplyRefreshLog(Integer priceApplyId) {
        return Result.success(priceApplyService.queryPriceApplyRefreshLog(priceApplyId));
    }

    @Override
    public Result<List<PriceDealDetailVO>> queryPriceDealDetail(Integer priceApplyId) {
        Result result = Result.success();
        List<PriceDealDetailVO> priceDealDetailVOList = priceDealService.queryPriceDealDetail(priceApplyId);
        result.setData(priceDealDetailVOList);
        return result;
    }

    @Override
    public Result<List<PriceDealInfoVO>> queryPriceDealInfo(Integer priceApplyId) {
        return Result.success(priceDealService.queryPriceDealInfo(priceApplyId));
    }

    @Override
    public PriceDealDetailEntity priceDealDealDetailById(Integer priceDealDetailId) {
        return priceDealService.priceDealDealDetailById(priceDealDetailId);
    }


    @Override
    public Result batchPending(List<Integer> ids) {
        return priceApplyService.batchPending(ids);
    }

    @Override
    public Result getAllocationDetails(String applyId) {
        return priceApplyService.getAllocationDetails(applyId);
    }

    @Override
    public List<PriceApplyVO> exportPriceApply() {
        return priceApplyService.exportPriceApply();
    }

    @Override
    public List<StructurePriceDealVO> exportStructurePriceApply() {
        List<Integer> contractIds = priceApplyService.getValidStructureContractIds();

        if (contractIds.isEmpty()) {
            return null;
        }

        List<ContractStructureEntity> list = contractFacade.getValidStructureContract(contractIds);
        List<StructurePriceDealVO> dealVOList = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        list.stream().forEach(structure -> {
            StructurePriceDealVO vo = new StructurePriceDealVO();
            Result result = structureRuleFacade.queryById(structure.getStructureType());
            if (result != null && ResultCodeEnum.OK.getCode() == result.getCode()) {
                StructureRuleEntity structureRuleEntity = JSON.parseObject(JSON.toJSONString(result.getData()), StructureRuleEntity.class);
                vo.setStructureTypeStr(structureRuleEntity.getStructureName());
            }

            ContractEntity contractEntity = contractFacade.getBasicContractById(structure.getContractId());
            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(contractEntity.getCategory2());

            vo.setContractCode(structure.getContractCode());
            vo.setStructureType(structure.getStructureType());
            vo.setCategoryName(categoryEntity.getName());
            vo.setDomainCode(contractEntity.getFutureCode() + structure.getDomainCode());
            if (structure.getPriceStartDate() != null)
                vo.setPriceStartDate(sdf.format(structure.getPriceStartDate()));
            if (structure.getPriceEndDate() != null)
                vo.setPriceEndDate(sdf.format(structure.getPriceEndDate()));

            PriceApplyEntity priceApplyEntity = priceApplyService.getPriceApplyEntityById(structure.getPriceApplyId().toString());
            if (null != priceApplyEntity) {
                vo.setCompanyShortName(priceApplyEntity.getCompanyName());
                vo.setCustomerName(priceApplyEntity.getCustomerName());
            }

            log.info("PriceStartDate  is  {}", vo.getPriceStartDate());
            dealVOList.add(vo);
        });

        return dealVOList;
    }

    @Override
    public Result checkPriceApply(MultipartFile file) {
        return priceApplyService.checkPriceApply(file);
    }

    @Override
    public Result importPriceApply(MultipartFile file) {
        return priceApplyService.importPriceApply(file);
    }

    @Override
    public Result importStructurePriceApply(MultipartFile file) {
        return priceApplyService.importStructurePriceApply(file);
    }

    @Override
    public Result mayReversePricing(PriceApplyDTO priceApplyDTO) {
        return Result.success(priceApplyService.mayReversePricing(priceApplyDTO));
    }

    @Override
    public Result priceApplyDeal(PriceApplyDTO priceApplyDTO) {
        priceApplyService.priceApplyDealNew(priceApplyDTO);
        return Result.success();
    }

    @Override
    public Result structurePriceApplyDeal(StructurePriceApplyDTO structurePriceApplyDTO) {
        return null;
    }


    @Override
    public Result batchNotDeal(List<NotDealDTO> notDealDTOS) {
        return priceApplyService.batchNotDeal(notDealDTOS);
    }

    @Override
    public Result getNotDealByContractId(Integer contractId) {
        return Result.success(priceApplyService.getNotDealByContractId(contractId));
    }

    @Override
    public Result judgeExistApply(Integer contractId) {
        return Result.success(priceApplyService.judgeExistApply(contractId));
    }

    @Override
    public Result closePriceApplyByContractId(Integer contractId) {
        return Result.success(priceApplyService.closePriceApplyByContractId(contractId));
    }

    /*@Override
    public Result priceApplyUpdateAudit(Integer priceApplyId, Integer status, String memo) {
        return Result.success(priceApplyService.priceApplyUpdateAudit(priceApplyId, status, memo));
    }*/

    @Override
    public Result magellanApplyAllocateStatus(Integer customerId, Integer goodsCategoryId, Integer salesType) {
        return priceApplyService.magellanApplyAllocateStatus(customerId, goodsCategoryId, salesType);
    }

    @Override
    public Result columbusApplyAllocateStatus(Integer employId, Integer goodsCategoryId, Integer salesType) {
        return priceApplyService.columbusApplyAllocateStatus(employId, goodsCategoryId, salesType);
    }

    @Override
    public void autoStructurePriceDeal() {
        QueryDTO<PriceApplyBO> applyBOQueryDTO = new QueryDTO<>();
        PriceApplyBO priceApplyBO = new PriceApplyBO();
        priceApplyBO.setType(PriceTypeEnum.STRUCTURE_PRICING.getValue());
        priceApplyBO.setStructurePricing(true);
        List<PriceApplyEntity> listPriceApplyEntity = priceApplyService.queryStructurePricingApply(applyBOQueryDTO);
        for (PriceApplyEntity priceApplyEntity : listPriceApplyEntity) {
            priceDealService.priceDealProcess(priceApplyEntity);
        }

    }

    @Override
    public Result<String> structurePriceApplyDeal(Integer priceApplyId, Date tradeDay) {
        String rtn = priceDealService.priceDealProcess(priceApplyId, tradeDay);
        return Result.success(rtn);
    }

    @Override
    public Result getPriceApplyExport(PriceApplyBO priceApplyBO) {
        return Result.success(priceApplyService.getPriceApplyExport(priceApplyBO));
    }

    @Override
    public List<PriceApplyExportVO> getPriceApplyTheReportList(PriceApplyBO priceApplyBO) {
        return priceApplyService.getPriceApplyTheReportList(priceApplyBO);
    }

    @Override
    public List<PriceApplyEntity> queryPriceApplyByContractId(Integer contractId) {
        return priceApplyService.queryPriceApplyByContractId(contractId);
    }

    @Override
    public Result getDealListByStructureContractId(Integer contractId) {
        ContractStructureEntity contractStructureById = contractFacade.getContractStructureById(contractId);

        List<PriceDealInfoEntity> priceDealInfoEntities = priceDealInfoService.priceDealInfoByPriceApplyId(contractStructureById.getPriceApplyId());
        List<PriceDealDetailVO> priceDealDetailVOS = priceDealInfoEntities.stream().map(
                priceDealInfoEntity -> {

                    PriceDealDetailVO priceDealDetailVO = BeanConvertUtils.convert(PriceDealDetailVO.class, priceDealInfoEntity);

                    PriceDealDetailEntity priceDealDetailEntity = priceDealService.getById(priceDealInfoEntity.getDealDetailId());
                    priceDealDetailVO.setContractCode(contractStructureById.getContractCode())
                            .setTotalDay(contractStructureById.getTotalDay())
                            .setDominantCode(contractStructureById.getDomainCode())
                            .setStructureType(contractStructureById.getStructureType())
                            .setContractType(ContractTypeEnum.getDescByValue(contractStructureById.getStructureType()))
                            .setDealDate(priceDealInfoEntity.getDealDate())
                            .setCreatedBy(priceDealInfoEntity.getCreatedBy())
                            .setFutureCode(priceDealDetailEntity.getFutureCode())
                    ;
                    priceDealDetailVO.setStructureName(StructureCodeUtil.numToCode(contractStructureById.getStructureType()) + "-" + contractStructureById.getStructureName());
                    return priceDealDetailVO;
                }
        ).collect(Collectors.toList());

        /*List<PriceDealDetailVO> priceDealDetailVOS = priceDealService.queryPriceDealDetail(contractStructureById.getPriceApplyId());
        priceDealDetailVOS.stream().map(
                priceDealDetailVO -> {
                    priceDealDetailVO.setContractCode(contractStructureById.getContractCode());
                    priceDealDetailVO.setContractType(ContractTypeEnum.getDescByValue(contractStructureById.getStructureType()));
                    return priceDealDetailVO;
                }
        ).collect(Collectors.toList());*/

        return Result.success(priceDealDetailVOS);
    }

    @Override
    public Result checkStructurePriceApply(MultipartFile file) {
        return priceApplyService.checkStructurePriceApply(file);
    }

    public Result priceApplyWithdraw(ApplyContraryDTO applyContraryDTO) {
        return Result.success(priceApplyService.priceApplyWithdraw(applyContraryDTO));
    }

    @Override
    public void sendNotDealInmail() {
        priceDealService.sendNotDealInmail();
    }

    @Override
    public Result priceDealContrary(ApplyContraryDTO applyContraryDTO) {
        return Result.success(priceDealService.priceDealContrary(applyContraryDTO));
    }

}
