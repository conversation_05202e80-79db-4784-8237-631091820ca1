package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.mapper.PriceDealInfoMapper;
import com.navigator.future.pojo.entity.PriceDealInfoEntity;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022年3月2日
 */
@Dao
public class PriceDealInfoDao extends BaseDaoImpl<PriceDealInfoMapper, PriceDealInfoEntity> {

    public PriceDealInfoEntity getPriceDealDetail(Integer priceApplyId, BigDecimal dealPrice) {
        return this.baseMapper.selectOne(
                Wrappers.<PriceDealInfoEntity>lambdaQuery()
                        .eq(PriceDealInfoEntity::getPriceApplyId, priceApplyId)
                        .eq(PriceDealInfoEntity::getDealPrice, dealPrice)
        );
    }

    public List<PriceDealInfoEntity> queryPriceDealInfo(Integer priceApplyId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceDealInfoEntity>lambdaQuery()
                        .eq(PriceDealInfoEntity::getPriceApplyId, priceApplyId)
                        .orderByDesc(PriceDealInfoEntity::getId)
        );
    }


}
