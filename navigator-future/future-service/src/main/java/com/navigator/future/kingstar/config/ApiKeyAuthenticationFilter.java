package com.navigator.future.kingstar.config;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class ApiKeyAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    private final String validApiKey;
    private final String validApiValue;

    public ApiKeyAuthenticationFilter(String validApiKey, String validApiValue, String apiMatchers) {
        // 只拦截 apiMatchers 路径下的请求
        super(apiMatchers);

        this.validApiKey = validApiKey;
        this.validApiValue = validApiValue;
    }

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response)
            throws AuthenticationException {
        String apiKey = request.getHeader(validApiKey);

        if (apiKey != null && apiKey.equals(validApiValue)) {
            return new UsernamePasswordAuthenticationToken("apiKeyUser", null, null);
        }

        throw new AuthenticationException("Invalid API Key") {
        };
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                            FilterChain chain, Authentication authResult) throws IOException, ServletException {
        // 认证成功后，继续处理请求
        SecurityContextHolder.getContext().setAuthentication(authResult);
        chain.doFilter(request, response);
    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                              AuthenticationException failed) throws IOException {
        response.sendError(HttpServletResponse.SC_UNAUTHORIZED, "Authentication failed");
    }
}
