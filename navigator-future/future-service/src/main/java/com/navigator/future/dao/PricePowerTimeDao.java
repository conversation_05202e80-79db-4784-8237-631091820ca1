package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.enums.AddUpdateStateEnum;
import com.navigator.future.mapper.PricePowerTimeMapper;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;

import java.util.List;

@Dao
public class PricePowerTimeDao extends BaseDaoImpl<PricePowerTimeMapper, PricePowerTimeEntity> {

    public List<PricePowerTimeEntity> queryPricePowerTimeListPage(QueryDTO<PricePowerTimeEntity> queryDTO){
        PricePowerTimeEntity pricePowerTime = queryDTO.getCondition();
        String categoryIdList = pricePowerTime.getCategoryIdList();
        return this.baseMapper.selectList(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                .like(PricePowerTimeEntity::getCategoryIdList,categoryIdList)
                .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByAsc(PricePowerTimeEntity::getStartTime)
                .ne(PricePowerTimeEntity::getAddUpdateState, AddUpdateStateEnum.UPDATED.getValue())
        );
    }

    public List<PricePowerTimeEntity> exportPricePowerTimeList(String categoryIdList){
        return this.baseMapper.selectList(Wrappers.<PricePowerTimeEntity>lambdaQuery()
                .like(PricePowerTimeEntity::getCategoryIdList,categoryIdList)
                .eq(PricePowerTimeEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .ne(PricePowerTimeEntity::getAddUpdateState,AddUpdateStateEnum.UPDATED.getValue())
                .orderByAsc(PricePowerTimeEntity::getStartTime)
        );
    }
}
