package com.navigator.future.dao;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.common.annotation.Dao;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.StringUtil;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.mapper.PriceDealDetailMapper;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022年3月2日
 */
@Dao
public class PriceDealDetailDao extends BaseDaoImpl<PriceDealDetailMapper, PriceDealDetailEntity> {


    /**
     * 根据申请单id查询成交记录
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceDealDetailEntity> queryPriceDealDetail(Integer priceApplyId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceDealDetailEntity>lambdaQuery()
                        .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
                        .ne(PriceDealDetailEntity::getStatus, PriceStatusEnum.CONTRARY.getValue())
        );
    }


    /**
     * 根据状态查询合同
     *
     * @param priceApplyId
     * @param status
     * @return
     */
    public List<PriceDealDetailEntity> queryPriceDealDetailByApplyIdStatus(Integer priceApplyId, Integer status) {
        return this.baseMapper.selectList(
                Wrappers.<PriceDealDetailEntity>lambdaQuery()
                        .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
                        .eq(PriceDealDetailEntity::getStatus, status)
        );
    }


    /**
     * (合约量)根据申请单id查询成交记录
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceDealDetailEntity> queryDominantPriceDealDetail(Integer priceApplyId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceDealDetailEntity>lambdaQuery()
                        .eq(PriceDealDetailEntity::getStatus, PriceStatusEnum.WAIT_ALLOCATE.getValue())
                        .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
                        .gt(PriceDealDetailEntity::getNotAllocateNum, BigDecimal.ZERO)
        );
    }

    /**
     * (合约量)根据申请单id查询成交记录
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceDealDetailEntity> queryPriceDealDetailByPriceApplyId(Integer priceApplyId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceDealDetailEntity>lambdaQuery()
                        .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
        );
    }

    /**
     * 根据申请单id查询某天成交记录
     *
     * @param priceApplyId 申请单ID
     * @param d            成交日
     * @return
     */
    public PriceDealDetailEntity getPriceDealDetail(Integer priceApplyId, String d) {
        return this.baseMapper.selectOne(
                Wrappers.<PriceDealDetailEntity>lambdaQuery()
                        .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
                        .eq(PriceDealDetailEntity::getDealDate, d)
        );
    }

    public List<PriceDealDetailEntity> queryPriceDealDetailOperateNum(PriceApplyOperateNumDTO priceApplyOperateNum) {

        return this.baseMapper.selectList(Wrappers.<PriceDealDetailEntity>lambdaQuery()
                .eq(PriceDealDetailEntity::getCompanyId, priceApplyOperateNum.getCompanyId())
                .eq(priceApplyOperateNum.getCustomerId() != null, PriceDealDetailEntity::getCustomerId, priceApplyOperateNum.getCustomerId())
                .in(PriceDealDetailEntity::getType, priceApplyOperateNum.getType())
                .eq(StrUtil.isNotBlank(priceApplyOperateNum.getDominantCode()), PriceDealDetailEntity::getDominantCode, priceApplyOperateNum.getDominantCode())
                .eq(priceApplyOperateNum.getCompanyId() != null, PriceDealDetailEntity::getCompanyId, priceApplyOperateNum.getCompanyId())
                .eq(StringUtil.isNotEmpty(priceApplyOperateNum.getBuCode()), PriceDealDetailEntity::getBuCode, priceApplyOperateNum.getBuCode())
                .eq(StringUtil.isNotEmpty(priceApplyOperateNum.getFutureCode()), PriceDealDetailEntity::getFutureCode, priceApplyOperateNum.getFutureCode())
                .eq(priceApplyOperateNum.getCategory2() != null, PriceDealDetailEntity::getCategory2, priceApplyOperateNum.getCategory2())
                .gt(PriceDealDetailEntity::getNotAllocateNum, 0)
                .notIn(PriceDealDetailEntity::getStatus, Arrays.asList(PriceStatusEnum.NOT_TRANSACTION.getValue(), PriceStatusEnum.CONTRARY.getValue())));
    }


    public IPage<PriceDealDetailEntity> queryPriceDealDetailIPage(QueryDTO<PriceApplyBO> applyBOQueryDTO) {
        PriceApplyBO priceApplyBO = applyBOQueryDTO.getCondition();

        List<Integer> status = null;
        if (StrUtil.isNotBlank(priceApplyBO.getStatus())) {

            status = Arrays.stream(priceApplyBO.getStatus().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());

        }

        String createdBy = StrUtil.isNotBlank(priceApplyBO.getCreatedBy()) ? priceApplyBO.getCreatedBy().trim() : null;

        LambdaQueryWrapper lambdaQueryWrapper = Wrappers.<PriceDealDetailEntity>lambdaQuery()
                .in(null != priceApplyBO.getCompanyIds() && !priceApplyBO.getCompanyIds().isEmpty(), PriceDealDetailEntity::getCompanyId, priceApplyBO.getCompanyIds())
                .eq(null != priceApplyBO.getCustomerId(), PriceDealDetailEntity::getCustomerId, priceApplyBO.getCustomerId())
                .like(StrUtil.isNotBlank(priceApplyBO.getCustomerName()), PriceDealDetailEntity::getCustomerName, priceApplyBO.getCustomerName())
                .eq(null != priceApplyBO.getCategoryId(), PriceDealDetailEntity::getCategoryId, priceApplyBO.getCategoryId())
                .eq(null != priceApplyBO.getCategory2(), PriceDealDetailEntity::getCategory2, priceApplyBO.getCategory2())
                .eq(null != priceApplyBO.getType(), PriceDealDetailEntity::getType, priceApplyBO.getType())
                .eq(StringUtil.isNotEmpty(priceApplyBO.getFutureCode()), PriceDealDetailEntity::getFutureCode, priceApplyBO.getFutureCode())
                //Case:1002590 反点价未成交在销售头寸处理未成交界面不显示 Author:Wan 2024-05-27 start
                .ne(!String.valueOf(PriceStatusEnum.NOT_TRANSACTION.getValue()).equals(priceApplyBO.getStatus()), PriceDealDetailEntity::getType, PriceTypeEnum.REVERSE_PRICING.getValue())
                //Case:1002590 反点价未成交在销售头寸处理未成交界面不显示 Author:Wan 2024-05-27 start
                .like(StrUtil.isNotBlank(priceApplyBO.getCode()), PriceDealDetailEntity::getApplyCode, priceApplyBO.getCode().trim())
                .eq(StrUtil.isNotBlank(priceApplyBO.getDominantCode()), PriceDealDetailEntity::getDominantCode, priceApplyBO.getDominantCode())
                .in(!status.isEmpty(), PriceDealDetailEntity::getStatus, status)
                .gt(!status.isEmpty() && status.get(0) == PriceStatusEnum.WAIT_ALLOCATE.getValue(), PriceDealDetailEntity::getNotAllocateNum, 0)
                .eq(null != priceApplyBO.getApplyType(), PriceDealDetailEntity::getAllocateNum, BigDecimal.ZERO)
                .like(StrUtil.isNotBlank(createdBy), PriceDealDetailEntity::getCreatedBy, createdBy)
                .and(null != priceApplyBO.getAllocateNumType(),
                        wrapper -> wrapper.eq(null != priceApplyBO.getAllocateNumType() && priceApplyBO.getAllocateNumType() == 1, PriceDealDetailEntity::getAllocateNum, 0)
                                .gt(null != priceApplyBO.getAllocateNumType() && priceApplyBO.getAllocateNumType() == 2, PriceDealDetailEntity::getAllocateNum, 0)
                )
                .between(null != priceApplyBO.getApplyStartTime() && null != priceApplyBO.getApplyEndTime(), PriceDealDetailEntity::getCreatedAt, priceApplyBO.getApplyStartTime(), priceApplyBO.getApplyEndTime())
                .in(null != priceApplyBO.getTypeList(), PriceDealDetailEntity::getType, priceApplyBO.getTypeList())
                .orderByDesc(PriceDealDetailEntity::getCreatedAt);

        return this.baseMapper.selectPage(new Page<>(applyBOQueryDTO.getPageNo(), applyBOQueryDTO.getPageSize()), lambdaQueryWrapper);
    }


    public List<PriceDealDetailEntity> queryPriceDealListByPriceStatus(Integer status) {
        return this.baseMapper.selectList(Wrappers.<PriceDealDetailEntity>lambdaQuery()
                .gt(PriceDealDetailEntity::getNotAllocateNum, BigDecimal.ZERO)
                .eq(PriceDealDetailEntity::getStatus, status)
                .in(PriceDealDetailEntity::getType, Arrays.asList(PriceTypeEnum.PRICING.getValue(), PriceTypeEnum.TRANSFER_MONTH.getValue()))
                .orderByDesc(PriceDealDetailEntity::getType));
    }

    public List<PriceDealDetailEntity> getPriceDealOrdersByDominantCode(String customerId, String domainCode, Integer categoryId) {
        return this.list(new LambdaQueryWrapper<PriceDealDetailEntity>()
                .eq(PriceDealDetailEntity::getType, PriceTypeEnum.TRANSFER_MONTH.getValue())
                .eq(PriceDealDetailEntity::getCustomerId, customerId)
                .eq(PriceDealDetailEntity::getDominantCode, domainCode)
                .eq(PriceDealDetailEntity::getCategoryId, categoryId)
                .eq(PriceDealDetailEntity::getStatus, PriceStatusEnum.WAIT_ALLOCATE.getValue())
                .apply("deal_num - allocate_num > 0"));
    }


    public List<PriceDealDetailEntity> getDealDetailDealPriceApplyIdEq(Integer priceApplyId, BigDecimal dealPrice) {
        return this.list(new LambdaQueryWrapper<PriceDealDetailEntity>()
                .eq(PriceDealDetailEntity::getType, PriceTypeEnum.STRUCTURE_PRICING.getValue())
                .eq(PriceDealDetailEntity::getPriceApplyId, priceApplyId)
                .eq(PriceDealDetailEntity::getDealPrice, dealPrice)
                .eq(PriceDealDetailEntity::getStatus, PriceStatusEnum.WAIT_ALLOCATE.getValue()));
    }


    public List<PriceDealDetailEntity> getDealDetailGroup(Integer customerId, Integer categoryId, String startTime, String endTime) {
        LambdaQueryWrapper<PriceDealDetailEntity> wrapper = new LambdaQueryWrapper<PriceDealDetailEntity>()
                .eq(PriceDealDetailEntity::getStatus, PriceStatusEnum.NOT_TRANSACTION.getValue())
                .eq(null != customerId, PriceDealDetailEntity::getCustomerId, customerId)
                .eq(null != categoryId, PriceDealDetailEntity::getCategoryId, categoryId)
                .between(PriceDealDetailEntity::getCreatedAt, startTime, endTime);

        if (null == customerId) {
            wrapper.groupBy(PriceDealDetailEntity::getCustomerId, PriceDealDetailEntity::getCategoryId)
                    .select(PriceDealDetailEntity::getCustomerId, PriceDealDetailEntity::getCategoryId);
        }
        return this.list(wrapper);
    }


}
