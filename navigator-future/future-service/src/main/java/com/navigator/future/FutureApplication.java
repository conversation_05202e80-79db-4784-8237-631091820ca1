package com.navigator.future;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@EnableScheduling
@SpringBootApplication(scanBasePackages = "com.navigator")
@MapperScan(basePackages = {"com.navigator.future.mapper","com.navigator.future.kingstar.mapper"})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
@EnableTransactionManagement
public class FutureApplication {
    public static void main(String[] args) {
        SpringApplication.run(FutureApplication.class, args);
    }

}
