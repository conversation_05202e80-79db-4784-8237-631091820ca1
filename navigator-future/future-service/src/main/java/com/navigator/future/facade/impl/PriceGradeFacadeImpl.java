package com.navigator.future.facade.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.facade.PriceGradeFacade;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.service.IPriceGradeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class PriceGradeFacadeImpl implements PriceGradeFacade {

    @Autowired
    private IPriceGradeService service;

    @Override
    public Result<Boolean> updatePriceGrade(PriceGradeEntity priceGradeEntity) {
        Boolean flag = service.updatePriceGrade(priceGradeEntity);
        if(flag)
            return Result.success("保存成功",flag);
        else
            return Result.failure("保存失败");
    }

    @Override
    public Result queryPriceGradeList(QueryDTO<PriceGradeEntity> queryDTO) {
        return Result.success(service.queryPriceGradeList(queryDTO));
    }
}
