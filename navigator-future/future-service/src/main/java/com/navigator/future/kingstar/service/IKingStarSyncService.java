package com.navigator.future.kingstar.service;

import com.navigator.future.pojo.dto.kingstar.KingStarResponseDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;

import java.net.URISyntaxException;

/**
 * <p>
 * 同步接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/06/05
 */
public interface IKingStarSyncService {

    /**
     * 获取KingStar Token
     *
     * @param userCode     用户编码
     * @param forceRefresh 是否强制刷新Token
     * @return KingStar Token
     */
    String getTokenByUserCode(String userCode, boolean forceRefresh);

    /**
     * 同步挂单申请(点价、转月、反点价)
     *
     * @param priceApplyEntity 挂单申请实体
     */
    void syncPriceApplyInfo(PriceApplyEntity priceApplyEntity);

    /**
     * 同步撤单申请
     *
     * @param priceApplyEntity 撤单申请实体
     */
    void syncCancelPriceApplyInfo(PriceApplyEntity priceApplyEntity);

    /**
     * 同步改单申请
     *
     * @param priceApplyLogEntity 改撤单实体
     */
    void syncModifyPriceApplyInfo(PriceApplyLogEntity priceApplyLogEntity);

    /**
     * 处理KingStar回调指令
     *
     * @param kingStarCallbackDTO KingStar回调DTO
     */
    KingStarResponseDTO handleInstructCallback(String kingStarCallbackDTO);

    /**
     * 重新同步KingStar请求
     *
     * @param requestId 请求ID
     */
    void reSyncByRequestId(Integer requestId) throws URISyntaxException;
}