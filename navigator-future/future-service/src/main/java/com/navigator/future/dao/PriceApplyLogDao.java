package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.navigator.common.annotation.Dao;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.future.enums.PriceApplyOperationTypeEnum;
import com.navigator.future.mapper.PriceApplyLogMapper;
import com.navigator.future.pojo.dto.QueryPriceApplyLogDTO;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 10:52
 */
@Dao
public class PriceApplyLogDao extends BaseDaoImpl<PriceApplyLogMapper, PriceApplyLogEntity> {

    /**
     * 根据申请单id 查询改单待审核的数量
     *
     * @param priceApplyId
     * @return
     */
    public PriceApplyLogEntity getPriceApplyLog(Integer priceApplyId) {
        return this.baseMapper.selectOne(Wrappers.<PriceApplyLogEntity>lambdaQuery()
                .eq(PriceApplyLogEntity::getPriceApplyId, priceApplyId)
                .eq(PriceApplyLogEntity::getType, PriceApplyOperationTypeEnum.CHANGE_APPLY.getValue()));
//                .eq(PriceApplyLogEntity::getAuditStatus, ApplyAuditStatusEnum.AUDIT.getValue()));
    }

    /**
     * 根据申请单查询改撤单记录
     *
     * @param queryPriceApply
     * @return
     */
    public List<PriceApplyLogEntity> getPriceApplyLogPriceApplyId(QueryPriceApplyLogDTO queryPriceApply) {
        return this.baseMapper.selectList(Wrappers.<PriceApplyLogEntity>lambdaQuery()
                .eq(queryPriceApply.getPriceApplyId() != null, PriceApplyLogEntity::getPriceApplyId, queryPriceApply.getPriceApplyId())
                .eq(queryPriceApply.getType() != null, PriceApplyLogEntity::getType, queryPriceApply.getType())
                .eq(queryPriceApply.getAuditStatus() != null, PriceApplyLogEntity::getAuditStatus, queryPriceApply.getAuditStatus()));
    }

    /**
     * 根据申请单id查询改撤单记录
     *
     * @param priceApplyId
     * @return
     */
    public List<PriceApplyLogEntity> queryPriceApplyLog(Integer priceApplyId) {
        return this.baseMapper.selectList(
                Wrappers.<PriceApplyLogEntity>lambdaQuery()
                        .eq(PriceApplyLogEntity::getPriceApplyId, priceApplyId)
//                        .ne(PriceApplyLogEntity::getAuditStatus, ApplyAuditStatusEnum.AUDIT.getValue())
        );
    }

    public int queryPriceApplyLogCount(Integer priceApplyId) {
        return this.baseMapper.selectCount(
                Wrappers.<PriceApplyLogEntity>lambdaQuery()
                        .eq(PriceApplyLogEntity::getPriceApplyId, priceApplyId)
        );
    }
}
