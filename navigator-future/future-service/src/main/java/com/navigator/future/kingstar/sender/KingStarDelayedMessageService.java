package com.navigator.future.kingstar.sender;

import com.navigator.future.pojo.dto.kingstar.KingStarMessageRequestDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * KingStar 延迟消息服务
 * useMessageQueue=1时，统一发送到延迟队列，根据规则确定延迟时间（可能为0）
 *
 * <AUTHOR>
 * @since 2025-7-15
 */
@Slf4j
@Service
@RefreshScope
@RequiredArgsConstructor
public class KingStarDelayedMessageService {

    private final KingStarSyncAccessChecker syncAccessChecker;
    private final ServiceBusDelayedSender serviceBusDelayedSender;

    @Value("${kingstar.useMessageQueue:0}")
    private Integer useMessageQueue;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 根据配置决定发送方式
     * useMessageQueue=0: 直连发送
     * useMessageQueue=1: 统一发送到延迟队列，延迟时间根据规则确定（可能为0）
     *
     * @param exchange         交易所
     * @param businessType     业务类型
     * @param requestDTO       消息数据
     * @param directSendAction 直连发送的回调函数
     * @return 发送结果描述
     */
    public String sendMessage(String exchange, String businessType, KingStarMessageRequestDTO requestDTO, Runnable directSendAction) {
        log.info("KingStar消息发送决策开始: exchange={}, businessType={}, useMessageQueue={}",
                exchange, businessType, useMessageQueue);

        // 如果 useMessageQueue=0，直连发送
        if (useMessageQueue == 0) {
            log.info("配置为直连发送模式，立即执行发送");
            directSendAction.run();
            return "直连发送完成";
        }

        // useMessageQueue=1，统一发送到延迟队列
        return sendToSyncQueue(exchange, businessType, requestDTO);
    }

    /**
     * 发送到同步队列，根据延迟规则确定延迟时间
     */
    private String sendToSyncQueue(String exchange, String businessType, KingStarMessageRequestDTO requestDTO) {
        LocalDateTime now = LocalDateTime.now();

        // 检查延迟规则，确定延迟时间
        Optional<LocalDateTime> delayUntil = syncAccessChecker.getDelayUntil(exchange, businessType, now);

        // 延迟消息的标识
        String delayedMessageId = requestDTO.getDelayedMessageId();

        LocalDateTime scheduledTime;
        String delayDesc;

        if (delayUntil.isPresent()) {
            scheduledTime = delayUntil.get();
            delayDesc = String.format("延迟到 %s 发送", scheduledTime.format(FORMATTER));
            log.info("匹配到延迟规则: exchange={}, businessType={}, delayUntil={}",
                    exchange, businessType, scheduledTime.format(FORMATTER));
        } else {
            scheduledTime = now;     // 延迟时间为0，立即投递
            delayedMessageId = null; // 立即投递时不需要延迟标识
            delayDesc = "立即投递到队列";
            log.info("未匹配延迟规则，立即投递: exchange={}, businessType={}", exchange, businessType);
        }

        try {
            // 发送到延迟队列
            serviceBusDelayedSender.sendDelayedMessage(requestDTO, delayedMessageId, scheduledTime);

            return delayDesc;

        } catch (Exception e) {
            log.error("发送到同步队列失败: exchange={}, businessType={}, error={}",
                    exchange, businessType, e.getMessage(), e);
            throw new RuntimeException("发送到同步队列失败: " + e.getMessage(), e);
        }
    }
}
