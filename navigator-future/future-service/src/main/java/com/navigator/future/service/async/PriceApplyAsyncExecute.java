package com.navigator.future.service.async;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.future.dao.PositionDao;
import com.navigator.future.dao.PositionLogDao;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.dao.PriceApplyLogDao;
import com.navigator.future.enums.PositionStatusEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.pojo.dto.RefreshQueryDTO;
import com.navigator.future.pojo.entity.PositionEntity;
import com.navigator.future.pojo.entity.PositionLogEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 异步执行器
 */
@Slf4j
@Component
public class PriceApplyAsyncExecute {
    @Resource
    private PriceApplyDao priceApplyDao;
    @Resource
    private PositionDao positionDao;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private PriceApplyLogDao applyLogDao;
    @Resource
    private PositionLogDao positionLogDao;

    /**
     * @param priceApplyEntity
     * @param customerId       操作人id
     * @param applyStatus      申请状态
     * @param operateTime      操作时间
     * @param order            是否是下单员
     */
    @Async
    public void recordApplyRefreshInfo(PriceApplyEntity priceApplyEntity, Integer customerId, Integer applyStatus, Date operateTime, boolean order) {
        // “下单员（角色）”第一次刷新“待挂单”和“待成交”状态下列表的操作人、操作时间和操作内容
        if (order) {

            // 待挂单
            if (applyStatus == PriceStatusEnum.WAIT_PENDING.getValue()) {
                List<PriceApplyLogEntity> priceApplyLogList = applyLogDao.queryPriceApplyLog(priceApplyEntity.getId());

                // 获取刷新信息
                String refreshPendingInfo = getRefreshInfo(
                        priceApplyEntity.getRefreshPendingInfo(),
                        getRefreshQueryDTO(customerId, PriceStatusEnum.getByValue(applyStatus).getDescription(), operateTime),
                        CollectionUtils.isNotEmpty(priceApplyLogList)
                );

                // 更新信息
                if (StringUtils.isNotBlank(refreshPendingInfo)) {
                    priceApplyDao.updateById(priceApplyEntity.setRefreshPendingInfo(refreshPendingInfo));
                }
            }

            // 待成交
            if (applyStatus == PriceStatusEnum.WAIT_TRANSACTION.getValue()) {
                List<PriceApplyLogEntity> priceApplyLogList = applyLogDao.queryPriceApplyLog(priceApplyEntity.getId());

                // 获取刷新信息
                String refreshDealingInfo = getRefreshInfo(
                        priceApplyEntity.getRefreshDealingInfo(),
                        getRefreshQueryDTO(customerId, PriceStatusEnum.getByValue(applyStatus).getDescription(), operateTime),
                        CollectionUtils.isNotEmpty(priceApplyLogList)
                );

                // 更新信息
                if (StringUtils.isNotBlank(refreshDealingInfo)) {
                    priceApplyDao.updateById(priceApplyEntity.setRefreshDealingInfo(refreshDealingInfo));
                }
            }
        }
    }

    /**
     * @param positionEntity
     * @param status         申请状态
     * @param customerId     操作人id
     * @param operateTime    操作时间
     * @param order          是否是下单员
     */
    @Async
    public void recordPositionRefreshInfo(PositionEntity positionEntity, Integer status, Integer customerId, Date operateTime, boolean order) {
        // “下单员（角色）”第一次刷新“待挂单”和“待成交”状态下列表的操作人、操作时间和操作内容
        if (order) {
            // 待挂单
            if (status == PriceStatusEnum.WAIT_PENDING.getValue()) {
                List<PositionLogEntity> positionLogList = positionLogDao.queryLog(positionEntity.getId());

                // 获取刷新信息
                String refreshPendingInfo = getRefreshInfo(
                        positionEntity.getRefreshPendingInfo(),
                        getRefreshQueryDTO(customerId, PositionStatusEnum.getByValue(status).getDescription(), operateTime),
                        CollectionUtils.isNotEmpty(positionLogList)
                );

                // 更新信息
                if (StringUtils.isNotBlank(refreshPendingInfo)) {
                    positionDao.updateById(positionEntity.setRefreshPendingInfo(refreshPendingInfo));
                }
            }

            // 待成交
            if (status == PriceStatusEnum.WAIT_TRANSACTION.getValue()) {
                List<PositionLogEntity> positionLogList = positionLogDao.queryLog(positionEntity.getId());

                // 获取刷新信息
                String refreshDealingInfo = getRefreshInfo(
                        positionEntity.getRefreshDealingInfo(),
                        getRefreshQueryDTO(customerId, PositionStatusEnum.getByValue(status).getDescription(), operateTime),
                        CollectionUtils.isNotEmpty(positionLogList)
                );

                // 更新信息
                if (StringUtils.isNotBlank(refreshDealingInfo)) {
                    positionDao.updateById(positionEntity.setRefreshDealingInfo(refreshDealingInfo));
                }
            }
        }
    }

    /**
     * 获取刷新记录
     *
     * @param oldRefreshInfo
     * @param refreshQueryDTO
     * @param changeOrder     是否是改撤单
     * @return
     */

    private String getRefreshInfo(String oldRefreshInfo, RefreshQueryDTO refreshQueryDTO, Boolean changeOrder) {
        String newRefreshInfo = "";

        if (StringUtils.isBlank(oldRefreshInfo)) {
            JSONArray newFreshInfoArr = new JSONArray().put(refreshQueryDTO);

            if (changeOrder) {
                refreshQueryDTO.setOperateContent("改撤单：" + refreshQueryDTO.getOperateContent());
                newFreshInfoArr.put(refreshQueryDTO);
            }

            newRefreshInfo = JSON.toJSONString(newFreshInfoArr);
        } else {
            // 改撤单第二次刷新(判断是对象还是数组是因为之前存的是对象，后来需求改动变成数组了，兼容处理)
            if (changeOrder) {
                if (JSONUtil.isJsonObj(oldRefreshInfo)) {
                    newRefreshInfo = JSON.toJSONString(new JSONArray()
                            .put(JSON.parse(oldRefreshInfo))
                            .put(refreshQueryDTO));
                }

                if (JSONUtil.isJsonArray(oldRefreshInfo)) {
                    if (!oldRefreshInfo.contains("改撤单")) {
                        List<RefreshQueryDTO> refreshQueryVOS = JSON.parseArray(oldRefreshInfo, RefreshQueryDTO.class);
                        refreshQueryVOS.add(refreshQueryDTO.setOperateContent("改撤单：" + refreshQueryDTO.getOperateContent()));
                        newRefreshInfo = JSON.toJSONString(refreshQueryVOS);
                    }
                }
            }
        }

        return newRefreshInfo;
    }

    private RefreshQueryDTO getRefreshQueryDTO(Integer customerId, String applyStatus, Date operateTime) {
        RefreshQueryDTO refreshQueryDTO = new RefreshQueryDTO();
        String refreshContext = "在“" + applyStatus + "”状态下，通过页面刷新/系统自动刷新查看到此申请单";

        refreshQueryDTO
                .setOperator(employFacade.getEmployById(customerId).getName())
                .setOperateTime(operateTime)
                .setOperateContent(refreshContext);
        return refreshQueryDTO;
    }

}
