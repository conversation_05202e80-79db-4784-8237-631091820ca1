package com.navigator.future.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.entity.RoleEntity;
import com.navigator.admin.pojo.enums.LogBizCodeEnum;
import com.navigator.admin.pojo.enums.MenuCodeEnum;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.CalcTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.*;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerGradeScoreFacade;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.pogo.model.dto.MessageInfoDTO;
import com.navigator.dagama.pogo.model.dto.WebsocketInMailDTO;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import com.navigator.dagama.pogo.model.enums.MessageBusinessCodeEnum;
import com.navigator.future.constant.ConfigurationConstants;
import com.navigator.future.dao.PositionDao;
import com.navigator.future.enums.*;
import com.navigator.future.pojo.dto.PositionDTO;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import com.navigator.future.pojo.entity.PositionEntity;
import com.navigator.future.pojo.entity.PositionLogEntity;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.pojo.vo.PositionLogVO;
import com.navigator.future.pojo.vo.PositionVO;
import com.navigator.future.pojo.vo.RefreshQueryVO;
import com.navigator.future.service.IPositionLogService;
import com.navigator.future.service.IPositionService;
import com.navigator.future.service.IPriceGradeService;
import com.navigator.future.service.IPricePowerTimeService;
import com.navigator.future.service.async.PriceApplyAsyncExecute;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.entity.CategoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 持仓记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Service
@Slf4j
public class PositionServiceImpl implements IPositionService {
    @Autowired
    private PositionDao positionDao;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private PriceApplyAsyncExecute priceApplyAsyncExecute;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private IPositionLogService positionLogService;
    @Autowired
    private MessageFacade messageFacade;
    @Autowired
    private RoleFacade roleFacade;
    @Autowired
    private EmployPermissionFacade employPermissionFacade;
    @Autowired
    private IPricePowerTimeService pricePowerTimeService;
    @Autowired
    private IPriceGradeService priceGradeService;
    @Autowired
    private CustomerGradeScoreFacade customerGradeScoreFacade;
    @Autowired
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private CompanyFacade companyFacade;
    @Autowired
    private CategoryFacade categoryFacade;
    @Autowired
    private RedisUtil redisUtil;

    @Value("${price.power.insert.hour}")
    private Integer insertHour;
    @Value("${price.power.update.hour}")
    private Integer updateHour;


    @Override
    public void save(PositionDTO positionDTO) {
        //case-1002883 非挂单区间套保平仓单挂单成功 Author: wan 2024-12-30 Start
        int canResult = this.canPriceOperation(SystemEnum.MAGELLAN.getValue(), -1, positionDTO.getCategory2());
        if (canResult == 0 || canResult == 2) {
            throw new BusinessException(ResultCodeEnum.CAN_NOT_PRICE);
        }
        //case-1002883 非挂单区间套保平仓单挂单成功 Author: wan 2024-12-30 end

        Integer currentUserId = Integer.parseInt(JwtUtils.getCurrentUserId());
        PositionEntity positionEntity = new PositionEntity();
        BeanUtils.copyProperties(positionDTO, positionEntity);
        EmployEntity employEntity = employFacade.getEmployById(currentUserId);
        BigDecimal applyHandNum = positionDTO.getApplyNum() == null ? null : positionDTO.getApplyNum().divide(ConfigurationConstants.BASICS, 0, RoundingMode.HALF_UP);

        String code = "";
        if (PriceTypeEnum.STRATEGIC_ARBITRAGE.getValue() == positionEntity.getType()) {
            String TLCode = "TL:code";
            TLCode = TLCode + ":" + "TL" + DateTimeUtil.formatDateCN1(new Date());
            // 获取递增码
            long value = redisUtil.incr(TLCode, 1L);

            //生成仓单编码
            code = "TL" + DateTimeUtil.formatDateCN1(new Date()) + String.format("%04d", value);
        } else {
            code = CodeGeneratorUtil.genPositionCode();
        }

        positionEntity
                .setApplyHandNum(applyHandNum)
                .setStatus(PositionStatusEnum.PENDING.getValue())
                .setCreatedBy(currentUserId)
                .setUpdatedBy(currentUserId)
                .setCreatedByName(employEntity.getName())
                .setUpdatedByName(employEntity.getName())
                .setCode(code)
                .setCompanyId(positionDTO.getCompanyId())
        ;

        positionDao.save(positionEntity);

        try {
            log.info("sendPriceInMailBegin:{}", "发送新增站内信");
            sendPriceInmail(positionEntity, null, PriceApplyOperationTypeEnum.NORMAL_APPLY.getValue(), null);
            recordOperationLog(positionEntity, positionDTO.setStatus(PositionStatusEnum.PENDING.getValue()));
        } catch (Exception e) {
            log.debug("添加日志错误:{}", e.getMessage());
        }

    }

    @Override
    public Result queryPositionList(QueryDTO<PositionQueryDTO> positionQueryDTO) {
        Page<PositionEntity> page = new Page<>(positionQueryDTO.getPageNo(), positionQueryDTO.getPageSize());
        PositionQueryDTO positionDTO = positionQueryDTO.getCondition();

        // 判断角色是否是下单员
        List<EmployEntity> orderRoleList = employFacade.getEmploy("400", String.valueOf(positionDTO.getCategory2()), null);
        List<EmployEntity> employEntityList = orderRoleList.stream()
                .filter(employEntity -> employEntity.getId().equals(Integer.valueOf(JwtUtils.getCurrentUserId())))
                .collect(Collectors.toList());
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        Map<Integer, String> companyNameMap
                = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getName, (k1, k2) -> k1));
        IPage<PositionEntity> iPage = positionDao.queryPageByPositionQueryDTO(page, positionDTO);
        List<PositionVO> positionVOList = iPage.getRecords().stream().map(i -> {
            PositionVO positionVO = new PositionVO();
            BeanUtils.copyProperties(i, positionVO);

            CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(i.getCategory2());

            String dealHandNum = i.getDealNum() == null ? null : i.getDealNum().divide(ConfigurationConstants.BASICS, 0, RoundingMode.HALF_UP).toPlainString();
            positionVO.setDealHandNum(dealHandNum)
                    .setTransactionDiffPrice(i.getTransactionDiffPrice() == null ? null : i.getTransactionDiffPrice().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString())
                    .setTransactionPrice(i.getTransactionPrice() == null ? null : i.getTransactionPrice().setScale(3, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString())
                    .setApplyHand(i.getApplyHandNum() == null ? null : i.getApplyHandNum().setScale(0, RoundingMode.HALF_UP).toPlainString())
                    .setCloseTypeName(CloseTypeEnum.getByValue(i.getCloseType()).getDescription())
                    .setPendingTypeName(PendingTypeEnum.getByValue(i.getPendingType()).getDescription())
                    .setTypeName(PriceTypeEnum.getByValue(i.getType()).getDesc())
                    .setCategoryName(categoryEntity.getName())
                    .setSort(iPage.getRecords().indexOf(i) + 1)
                    .setCreatedAtTime(DateTimeUtil.formatDate(i.getCreatedAt()))
                    .setApplyPrice(PriceTypeEnum.TRANSFER_MONTH.getValue() == i.getType() ? i.getApplyDiffPrice() : i.getApplyPrice())
                    .setOrientationName("1".equalsIgnoreCase(i.getOrientation()) ? "买" : "卖")
                    .setUpdatedBy(i.getUpdatedBy())
                    .setUpdatedByName(i.getUpdatedByName())
                    .setLatestMessage(i.getLatestMessage())
                    .setTransactionDomainCode(i.getTransactionDomainCode())
                    .setChangeDomainCode(i.getChangeDomainCode())
                    .setChangeTransferDomainCode(i.getChangeTransferDomainCode())
                    .setCompanyName(companyNameMap.get(i.getCompanyId()))
            ;

            if (PriceTypeEnum.TRANSFER_MONTH.getValue() == i.getType()) {
                positionVO.setTransactionPrice(positionVO.getTransactionDiffPrice());
            }
            if (positionVO.getApplyPrice() != null) {
                positionVO.setApplyPrice(positionVO.getApplyPrice().stripTrailingZeros());
            }

            EmployEntity employEntity = employFacade.getEmployById(i.getCreatedBy());
            positionVO.setCreatedByName(employEntity.getName());

            // 异步记录“下单员（角色）”第一次刷新“待挂单”和“待成交”状态下列表的操作人、操作时间和操作内容
            priceApplyAsyncExecute.recordPositionRefreshInfo(i,
                    i.getStatus(),
                    Integer.valueOf(JwtUtils.getCurrentUserId()),
                    new Date(),
                    CollectionUtil.isNotEmpty(employEntityList));

            return positionVO;
        }).collect(Collectors.toList());
        return Result.page(iPage, positionVOList);
    }

    @Override
    public PositionVO queryPositionDetail(Integer id) {
        PositionEntity positionEntity = positionDao.getById(id);
        PositionVO positionVO = new PositionVO();
        BeanUtils.copyProperties(positionEntity, positionVO);
        positionVO.setApplyHand(positionEntity.getApplyHandNum() == null ? null : positionEntity.getApplyHandNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
        positionVO.setChangeHand(positionEntity.getChangeHandNum() == null ? null : positionEntity.getChangeHandNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        Map<Integer, String> companyNameMap
                = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getName, (k1, k2) -> k1));
        positionVO.setCompanyName(companyNameMap.get(positionEntity.getCompanyId()));
        return positionVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePositionStatus(PositionDTO positionDTO) {
        String currentUserId = JwtUtils.getCurrentUserId();
        String name = employFacade.getEmployCache(Integer.parseInt(currentUserId));
        PositionEntity positionEntity = positionDao.getById(positionDTO.getId());
        BigDecimal dealNum = BigDecimalUtil.isEqual(0, positionDTO.getDealHandNum(), positionEntity.getApplyHandNum()) ?
                positionEntity.getApplyNum() :
                BigDecimalUtil
                        .multiply(CalcTypeEnum.COUNT, positionDTO.getDealHandNum(), BigDecimalUtil
                                .multiply(CalcTypeEnum.COUNT, ConfigurationConstants.HAND_BASICS, ConfigurationConstants.HAND_RATE))
                        .setScale(0, RoundingMode.HALF_UP);
        BigDecimal notDealNum = BigDecimalUtil
                .subtract(CalcTypeEnum.COUNT, positionEntity.getApplyNum(), dealNum);
        positionEntity
                .setStatus(positionDTO.getStatus())
                .setUpdatedAt(new Date())
                .setUpdatedBy(Integer.parseInt(currentUserId))
                .setUpdatedByName(name)
        ;
        String transactionDomainCode = StringUtils.isNotBlank(positionDTO.getTransactionDomainCode()) ? positionDTO.getTransactionDomainCode() : positionEntity.getDomainCode();

        String transactionFutureCode = StringUtils.isNotBlank(positionDTO.getTransactionFutureCode()) ? positionDTO.getTransactionFutureCode() : positionEntity.getFutureCode();

        String transactionTransferDomainCode = StringUtils.isNotBlank(positionDTO.getTransactionTransferDomainCode()) ? positionDTO.getTransactionTransferDomainCode() : positionEntity.getTransferDomainCode();

        String transactionTransferFutureCode = StringUtils.isNotBlank(positionDTO.getTransactionTransferFutureCode()) ? positionDTO.getTransactionTransferFutureCode() : positionEntity.getTransferFutureCode();


        if (PositionStatusEnum.DONE.getValue() == positionDTO.getStatus()
                || PositionStatusEnum.UNDONE.getValue() == positionDTO.getStatus()
        ) {
            positionEntity
                    .setTransactionDiffPrice(positionDTO.getTransactionDiffPrice())
                    .setTransactionPrice(positionDTO.getTransactionPrice())
                    .setDealNum(dealNum)
                    .setNotDealNum(notDealNum)
                    .setTransactionFutureCode(transactionFutureCode)
                    .setTransactionDomainCode(transactionDomainCode)
                    .setTransactionTransferFutureCode(transactionTransferFutureCode)
                    .setTransactionTransferDomainCode(transactionTransferDomainCode)
            ;
        }
        if (PositionStatusEnum.DONE.getValue() == positionDTO.getStatus()) {
            positionEntity
                    .setFutureCode(transactionFutureCode)
                    .setDomainCode(transactionDomainCode)
                    .setTransferFutureCode(transactionTransferFutureCode)
                    .setTransferDomainCode(transactionTransferDomainCode)
            ;
        }
        positionDao.updateById(positionEntity);

        try {
            recordOperationLog(positionEntity, positionDTO);
        } catch (Exception e) {
            log.debug("添加日志错误:{}", e.getMessage());
        }

        if (PositionStatusEnum.DONE.getValue() == positionDTO.getStatus() && positionEntity.getApplyNum().compareTo(dealNum) != 0) {
            positionEntity
                    .setStatus(PositionStatusEnum.UNDONE.getValue())
                    .setId(null)
                    .setCreatedAt(new Date())
                    .setCreatedBy(Integer.parseInt(currentUserId))
                    .setCreatedByName(name)
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(Integer.parseInt(currentUserId))
                    .setUpdatedByName(name)
            ;

            positionDao.save(positionEntity);
        }
    }

    @Override
    public void dealBatch(PositionDTO positionDTO) {
        Integer currentUserId = Integer.parseInt(JwtUtils.getCurrentUserId());
        EmployEntity employEntity = employFacade.getEmployById(currentUserId);
        List<Integer> idList = positionDTO.getIdList();
        for (Integer id : idList) {
            PositionEntity positionEntity = positionDao.getById(id);
            positionEntity
                    .setNotDealNum(positionEntity.getApplyNum())
                    .setStatus(PositionStatusEnum.UNDONE.getValue())
                    .setUpdatedAt(new Date())
                    .setUpdatedBy(Integer.parseInt(JwtUtils.getCurrentUserId()))
                    .setUpdatedByName(employEntity.getName())

            ;

            positionDao.updateById(positionEntity);
        }
    }

    @Override
    public List<PositionVO> export(Integer categoryId) {
        List<PositionEntity> positionEntities = positionDao.queryListByStatus(Arrays.asList(PositionStatusEnum.DEALING.getValue(), PositionStatusEnum.PENDING.getValue()));
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        Map<Integer, String> companyNameMap
                = companyEntityList.stream().collect(Collectors.toMap(CompanyEntity::getId, CompanyEntity::getName, (k1, k2) -> k1));
        return positionEntities.stream().map(i -> {

                    StringBuilder domainCode = new StringBuilder();

                    if (PriceTypeEnum.TRANSFER_MONTH.getValue() == i.getType()) {
                        domainCode.append(i.getFutureCode());
                        domainCode.append(i.getDomainCode());
                        domainCode.append("-");
                        domainCode.append(i.getTransferFutureCode());
                        domainCode.append(i.getTransferDomainCode());
                    } else if (PriceTypeEnum.STRATEGIC_ARBITRAGE.getValue() == i.getType()) {
                        domainCode.append(i.getFutureCode());
                        domainCode.append(i.getDomainCode());
                        domainCode.append(i.getTransferFutureCode());
                        domainCode.append(i.getTransferDomainCode());
                    } else {
                        domainCode.append(i.getFutureCode());
                        domainCode.append(i.getDomainCode());
                    }

                    CategoryEntity categoryEntity = categoryFacade.getBasicCategoryBySerialNo(i.getCategory2());
                    PositionVO positionVO = BeanConvertUtils.convert(PositionVO.class, i);
                    positionVO.setSort(positionEntities.indexOf(i) + 1)
                            .setDomainCode(domainCode.toString())
                            .setTypeName(PriceTypeEnum.getByValue(i.getType()).getDesc())
                            .setPendingTypeName(PendingTypeEnum.getByValue(i.getPendingType()).getDescription())
                            .setApplyPrice(PriceTypeEnum.TRANSFER_MONTH.getValue() == i.getType() ? i.getApplyDiffPrice() : i.getApplyPrice())
                            .setApplyHand(i.getApplyHandNum().intValue() + "手，" + i.getApplyNum().intValue() + "吨")
                            .setOrientationName("1".equalsIgnoreCase(i.getOrientation()) ? "买" : "卖")
                            .setCloseTypeName(CloseTypeEnum.getByValue(i.getCloseType()).getDescription())
                            .setCategoryName(categoryEntity.getName())
                            .setCompanyName(companyNameMap.get(i.getCompanyId()))
                    ;
                    return positionVO;
                }

        ).collect(Collectors.toList());
    }

    @Override
    public Result check(MultipartFile uploadFile) {
        List<PositionVO> positionVOList = new ArrayList<>();
        try {
            positionVOList = EasyPoiUtils.importExcel(uploadFile, 1, 1, PositionVO.class);
            if (CollectionUtil.isEmpty(positionVOList)) {
                return Result.success("导入数据为空！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Result.failure("模板错误");
        }

        List<PositionVO> positionVOS = new ArrayList<>();
        for (PositionVO positionVO : positionVOList) {

            String conflict = "";
            if (StrUtil.isBlank(positionVO.getCode()) || (!(positionVO.getCode().startsWith("PC")) && !(positionVO.getCode().startsWith("TL")))) {
                conflict += "编号不合法";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            if (StrUtil.isBlank(positionVO.getDealHandNum())) {
                conflict += "成交手数无内容";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                    && !BigDecimalUtil.isBigDecimal(positionVO.getDealHandNum())) {
                conflict += "成交手数不合法";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            //当手数不为0的时候，价格为空
            if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                    && (!new BigDecimal(positionVO.getDealHandNum()).equals(BigDecimal.ZERO))
                    && StrUtil.isBlank(positionVO.getTransactionPrice())) {
                conflict += "成交价格无内容";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }

            if (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                    && !BigDecimalUtil.isBigDecimal(positionVO.getTransactionPrice())) {
                conflict += "成交价格/价差不合法";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            if (!positionVO.getTypeName().equals("转月")
                    && StrUtil.isNotBlank(positionVO.getTransactionPrice())
                    && (BigDecimalUtil.isLessThanZero(new BigDecimal(positionVO.getTransactionPrice()))
                    || (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                    && new BigDecimal(positionVO.getTransactionPrice()).scale() > 2))) {
                conflict += "成交价格不能小于0且要位数不大于2";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            if (positionVO.getTypeName().equals("转月")
                    && (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                    && new BigDecimal(positionVO.getTransactionPrice()).scale() > 2)) {
                conflict += "成交价格位数不大于2";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }

            if ((StrUtil.isNotBlank(positionVO.getDealHandNum())
                    && BigDecimalUtil.isLessThanZero(new BigDecimal(positionVO.getDealHandNum())))
                    || (StrUtil.isNotBlank(positionVO.getDealHandNum())
                    && !BigDecimalUtil.isIntegerValue(new BigDecimal(positionVO.getDealHandNum())))) {
                conflict += "成交手数不能小于0且要为整数";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            PositionEntity positionEntity = positionDao.getPositionByCode(positionVO.getCode().trim());
            if (null == positionEntity) {
                conflict += "编号不存在";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }

            // 成交手数判断
            if (Integer.valueOf(positionVO.getDealHandNum()) > positionEntity.getApplyHandNum().intValue()) {
                conflict += "成交手数大于申请手数";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            if (positionEntity.getStatus() == PositionStatusEnum.UNDONE.getValue()
                    || positionEntity.getStatus() == PositionStatusEnum.DONE.getValue()) {
                conflict += "申请单已成交或未成交";
                positionVOS.add(positionVO.setConflict(conflict));
                continue;
            }
            positionVOS.add(positionVO);
        }
        return Result.success(positionVOS);


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result importFile(MultipartFile uploadFile) {

        log.info("==============================开始处理上传点价成交数据");

        List<PositionVO> positionVOS = new ArrayList<>();
        try {
            positionVOS = EasyPoiUtils.importExcel(uploadFile, 1, 1, PositionVO.class);
            if (CollectionUtil.isEmpty(positionVOS)) {
                return Result.success("导入数据为空！");
            }
        } catch (Exception e) {
            return Result.failure("模板错误");
        }
        int successNum = 0;
        int exceptionNum = 0;
        for (PositionVO positionVO : positionVOS) {
            // 先校验数据

                if (StrUtil.isBlank(positionVO.getCode())
                        || (!(positionVO.getCode().startsWith("PC")) && !(positionVO.getCode().startsWith("TL")))) {
                    exceptionNum++;
                    continue;
                }
                //成交手数不合法
                if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && !BigDecimalUtil.isBigDecimal(positionVO.getDealHandNum())) {
                    exceptionNum++;
                    continue;
                }
                //当手数不为0的时候，价格为空
                if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && (!new BigDecimal(positionVO.getDealHandNum()).equals(BigDecimal.ZERO))
                        && StrUtil.isBlank(positionVO.getTransactionPrice())) {
                    exceptionNum++;
                    continue;
                }
                //成交价格不合法
                if (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                        && !BigDecimalUtil.isBigDecimal(positionVO.getTransactionPrice())) {
                    exceptionNum++;
                    continue;
                }
                //成交手数为空
                if (StrUtil.isBlank(positionVO.getDealHandNum())) {
                    exceptionNum++;
                    continue;
                }
                //成交价格不能小于0且要位数不大于2
                if (!positionVO.getTypeName().equals("转月") && StrUtil.isNotBlank(positionVO.getTransactionPrice())
                        && (BigDecimalUtil.isLessThanZero(new BigDecimal(positionVO.getTransactionPrice()))
                        || (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                        && new BigDecimal(positionVO.getTransactionPrice()).scale() > 2))) {
                    exceptionNum++;
                    continue;
                }
                if (positionVO.getTypeName().equals("转月")
                        && (StrUtil.isNotBlank(positionVO.getTransactionPrice())
                        && new BigDecimal(positionVO.getTransactionPrice()).scale() > 2)) {
                    exceptionNum++;
                    continue;
                }
                //成交手数不能小于0且要为整数
                if ((StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && BigDecimalUtil.isLessThanZero(new BigDecimal(positionVO.getDealHandNum())))
                        || (StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && !BigDecimalUtil.isIntegerValue(new BigDecimal(positionVO.getDealHandNum())))) {
                    exceptionNum++;
                    continue;
                }
                PositionEntity positionEntity = positionDao.getPositionByCode(positionVO.getCode().trim());
                if (null == positionEntity) {
                    exceptionNum++;
                    continue;
                }
                // 成交手数判断(不能超过可申请量)
                if (null != positionVO.getDealHandNum()
                        && Integer.parseInt(positionVO.getDealHandNum()) > positionEntity.getApplyHandNum().intValue()) {
                    exceptionNum++;
                    continue;
                }
                // 已经到待分配或者未成交
                if (positionEntity.getStatus() == PositionStatusEnum.UNDONE.getValue()
                        || positionEntity.getStatus() == PositionStatusEnum.DONE.getValue()) {
                    exceptionNum++;
                    continue;
                }

                // 未成交
                if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && new BigDecimal(positionVO.getDealHandNum()).equals(BigDecimal.ZERO)) {
                    positionEntity.setStatus(PositionStatusEnum.UNDONE.getValue())
                            .setNotDealNum(positionEntity.getApplyNum())
                            .setDealNum(BigDecimal.ZERO)
                    ;
                    positionDao.updateById(positionEntity);
                    successNum++;
                    continue;
                }
                // 成交
                if (StrUtil.isNotBlank(positionVO.getDealHandNum())
                        && StrUtil.isNotBlank(positionVO.getTransactionPrice())) {
                    PositionDTO positionDTO = new PositionDTO();
                    BeanUtils.copyProperties(positionEntity, positionDTO);
                    positionDTO
                            .setDealHandNum(new BigDecimal(positionVO.getDealHandNum()));
                    if (1 == positionEntity.getType()) {
                        positionDTO.setTransactionPrice(new BigDecimal(positionVO.getTransactionPrice()));
                    } else {
                        positionDTO.setTransactionDiffPrice(new BigDecimal(positionVO.getTransactionPrice()));
                    }
                    positionDTO.setStatus(PositionStatusEnum.DONE.getValue());
                    updatePositionStatus(positionDTO);
                    successNum++;
                }
            }

            log.info("==============================处理结束上传点价成交数据:成功{}条，失败{}条", successNum, exceptionNum);
            return Result.success("成功" + successNum + "条，失败" + exceptionNum + "条", null);
        }

        private void recordOperationLog (PositionEntity positionEntity, PositionDTO positionDTO){
            LogBizCodeEnum logBizCodeEnum = null;

            if (PositionStatusEnum.DONE.getValue() == positionDTO.getStatus()) {
                logBizCodeEnum = LogBizCodeEnum.POSITION_TRANSACTION;
            } else if (PositionStatusEnum.DEALING.getValue() == positionDTO.getStatus()) {
                logBizCodeEnum = LogBizCodeEnum.POSITION_APPLY_PENDING;
            } else if (PositionStatusEnum.UNDONE.getValue() == positionDTO.getStatus()) {
                logBizCodeEnum = LogBizCodeEnum.POSITION_NOT_TRANSACTION;
            } else {
                logBizCodeEnum = LogBizCodeEnum.NEW_POSITION_APPLY;
            }


            operationLogFacade.recordOperationLog(
                    new OperationDetailDTO().setBizCode(logBizCodeEnum.getBizCode())
                            .setBizModule(ModuleTypeEnum.PRICE.getModule())
                            .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                            .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                            .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                            .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                            .setOperationName(logBizCodeEnum.getMsg())
                            .setReferBizId(positionEntity.getId())
                            .setReferBizCode(positionEntity.getCode())
                            .setMetaData(JSON.toJSONString(positionEntity))
                            .setData(JSON.toJSONString(positionDTO))
            );
        }


        @Override
        public List<RefreshQueryVO> queryPositionRefreshLog (Integer positionId){
            List<RefreshQueryVO> refreshQueryVOList = new ArrayList<>();

            PositionEntity positionEntity = positionDao.getById(positionId);

            if (null != positionEntity) {
                String refreshPendingInfo = positionEntity.getRefreshPendingInfo();
                String refreshDealingInfo = positionEntity.getRefreshDealingInfo();

                // 待挂单
                if (JSONUtil.isJsonObj(refreshPendingInfo)) {
                    refreshQueryVOList.add(JSON.parseObject(refreshPendingInfo, RefreshQueryVO.class));
                }
                if (JSONUtil.isJsonArray(refreshPendingInfo)) {
                    refreshQueryVOList.addAll(JSON.parseArray(refreshPendingInfo, RefreshQueryVO.class));
                }

                // 待成交
                if (JSONUtil.isJsonObj(refreshDealingInfo)) {
                    refreshQueryVOList.add(JSON.parseObject(refreshDealingInfo, RefreshQueryVO.class));
                }

                if (JSONUtil.isJsonArray(refreshDealingInfo)) {
                    refreshQueryVOList.addAll(JSON.parseArray(refreshDealingInfo, RefreshQueryVO.class));
                }
            }
            // 按照时间排序
            refreshQueryVOList.sort(Comparator.comparing(RefreshQueryVO::getOperateTime));
            return refreshQueryVOList;
        }

        @Override
        public void modify (PositionDTO positionDTO){
            int canResult = this.canPriceOperation(SystemEnum.MAGELLAN.getValue(), -1, positionDTO.getCategory2());
            if (canResult == 0 || canResult == 2) {
                throw new BusinessException(ResultCodeEnum.CAN_NOT_PRICE);
            }

//        if (!this.canTransferMonth(positionDTO)) {
//            throw new BusinessException(ResultCodeEnum.CAN_NOT_TRANSFER_MONTH);
//        }
            PositionEntity positionEntity = positionDao.getById(positionDTO.getId());
            // 当前申请单
            if (null == positionEntity) {
                throw new BusinessException(ResultCodeEnum.POSITION_ERROR);
            }
            PositionEntity originalPositionEntity = new PositionEntity();
            BeanUtils.copyProperties(positionEntity, originalPositionEntity);
            // 只能是待挂单 和待成交
            if (positionEntity.getStatus().equals(PositionStatusEnum.DONE.getValue())
                    || positionEntity.getStatus().equals(PositionStatusEnum.UNDONE.getValue())) {
                throw new BusinessException(ResultCodeEnum.PRICE_APPLY_CAN_NOT_CHANGE);
            }

            // 只能改单或撤单一次
            List<PositionLogEntity> positionLogEntityList = positionLogService.queryLog(positionDTO.getId());
            if (CollectionUtil.isNotEmpty(positionLogEntityList)) {
                throw new BusinessException(ResultCodeEnum.MODIFY_PRICE_APPLY_ONLY_);
            }

            PositionLogEntity positionLogEntity = new PositionLogEntity();
            StringBuilder latestMessage = new StringBuilder();
            LogBizCodeEnum logBizCodeEnum = LogBizCodeEnum.CHANGE_POSITION;
            int changeNum = 0;

            Integer status = positionEntity.getStatus();
            switch (PriceApplyOperationTypeEnum.getByValue(positionDTO.getModifyType())) {
                case CHANGE_APPLY:
                    latestMessage.append(PositionStatusEnum.getByValue(positionEntity.getStatus()).getDescription());
                    latestMessage.append("改单,");
                    if (StringUtils.isNotBlank(positionDTO.getDomainCode())
                            && !positionEntity.getDomainCode().equalsIgnoreCase(positionDTO.getDomainCode())) {
                        latestMessage
                                .append("挂单月份:")
                                .append(positionEntity.getFutureCode())
                                .append(positionEntity.getDomainCode())
                                .append("到")
                                .append(positionDTO.getFutureCode())
                                .append(positionDTO.getDomainCode())
                                .append(System.lineSeparator());
                        changeNum += 1;
                        positionEntity
                                .setChangeDomainCode(positionDTO.getDomainCode())
                                .setChangeFutureCode(positionDTO.getFutureCode())
                                .setModifyType(positionDTO.getModifyType())
                        ;
                        positionLogEntity.setChangeDomainCode(positionDTO.getDomainCode());
                    }

                    if (StringUtils.isNotBlank(positionDTO.getTransferDomainCode())
                            && !positionEntity.getTransferDomainCode().equalsIgnoreCase(positionDTO.getTransferDomainCode())) {
                        latestMessage
                                .append("转入月份:")
                                .append(positionEntity.getTransferFutureCode())
                                .append(positionEntity.getTransferDomainCode())
                                .append("到")
                                .append(positionDTO.getTransferFutureCode())
                                .append(positionDTO.getTransferDomainCode())
                                .append(System.lineSeparator())
                        ;
                        changeNum += 1;
                        positionEntity
                                .setChangeTransferDomainCode(positionDTO.getTransferDomainCode())
                                .setChangeTransferFutureCode(positionDTO.getTransferFutureCode())
                        ;
                        positionLogEntity.setChangeTransferDomainCode(positionDTO.getTransferDomainCode());
                    }
                    if (null != positionDTO.getApplyPrice()
                            && !BigDecimalUtil.isEqual(positionEntity.getApplyPrice(), positionDTO.getApplyPrice())) {
                        latestMessage
                                .append("价格:")
                                .append(positionEntity.getApplyPrice().stripTrailingZeros().toPlainString())
                                .append("到")
                                .append(positionDTO.getApplyPrice().stripTrailingZeros().toPlainString())
                                .append("元")
                                .append(System.lineSeparator())
                        ;
                        changeNum += 1;
                        positionEntity.setChangePrice(positionDTO.getApplyPrice());
                        positionLogEntity.setChangePrice(positionDTO.getApplyPrice());
                    }
                    if (null != positionDTO.getApplyDiffPrice()
                            && !BigDecimalUtil.isEqual(positionEntity.getApplyDiffPrice(), positionDTO.getApplyDiffPrice())) {
                        latestMessage
                                .append("价差:")
                                .append(positionEntity.getApplyDiffPrice().stripTrailingZeros().toPlainString())
                                .append("到")
                                .append(positionDTO.getApplyDiffPrice().stripTrailingZeros().toPlainString())
                                .append("元")
                                .append(System.lineSeparator())
                        ;
                        changeNum += 1;
                        positionEntity.setChangeDiffPrice(positionDTO.getApplyDiffPrice());
                        positionLogEntity.setChangeDiffPrice(positionDTO.getApplyDiffPrice());
                    }

                    if (null != positionDTO.getApplyNum()
                            && !BigDecimalUtil.isEqual(positionEntity.getApplyNum(), positionDTO.getApplyNum())) {
                        BigDecimal originHandNum = positionEntity.getApplyNum() == null ? null : positionEntity.getApplyNum().divide(ConfigurationConstants.BASICS, 0, RoundingMode.HALF_UP);
                        BigDecimal applyHandNum = positionDTO.getApplyNum() == null ? null : positionDTO.getApplyNum().divide(ConfigurationConstants.BASICS, 0, RoundingMode.HALF_UP);
                        latestMessage
                                .append("数量:")
                                .append(originHandNum)
                                .append("手")
                                .append("到")
                                .append(applyHandNum)
                                .append("手")
                                .append(System.lineSeparator())
                        ;
                        changeNum += 1;
                    }
                    BigDecimal changeHandNum = positionDTO.getApplyNum() == null ? null : positionDTO.getApplyNum().divide(ConfigurationConstants.BASICS, 0, RoundingMode.HALF_UP);

                    positionLogEntity
                            .setChangeHandNum(changeHandNum)
                            .setChangeNum(positionDTO.getApplyNum());

                    positionEntity
                            .setChangeHandNum(changeHandNum)
                            .setChangeNum(positionDTO.getApplyNum());

                    logBizCodeEnum = LogBizCodeEnum.CHANGE_POSITION;
                    break;
                case WITHDRAW_APPLY:
                    latestMessage
                            .append(PositionStatusEnum.getByValue(positionEntity.getStatus()).getDescription())
                            .append("撤单")
                            .append(System.lineSeparator())
                    ;
                    changeNum += 1;
                    logBizCodeEnum = LogBizCodeEnum.CANCEL_POSITION;
                    positionEntity.setModifyType(positionDTO.getModifyType());
                    break;
                default:
                    break;
            }
            if (changeNum == 0) {
                throw new BusinessException(ResultCodeEnum.NO_CHANGE);
            }
            //case-1002812 采购头寸转月校验修改，套保平仓改单撤单修改人未更新 Author: wan 2024-10-23 Start
            String currentUserId = JwtUtils.getCurrentUserId();
            String name = employFacade.getEmployById(Integer.valueOf(currentUserId)).getName();
            positionEntity
                    .setStatus(PositionStatusEnum.PENDING.getValue())
                    .setLatestMessage(latestMessage.toString())
                    .setUpdatedBy(Integer.parseInt(currentUserId))
                    .setUpdatedByName(name)
                    .setUpdatedAt(new Date())
            ;
            //case-1002812 采购头寸转月校验修改，套保平仓改单撤单修改人未更新 Author: wan 2024-10-23 end
            positionDao.updateById(positionEntity);
            positionLogEntity
                    .setPositionId(positionDTO.getId())
                    .setPositionCode(positionEntity.getCode())
                    .setType(positionEntity.getType())
                    .setPendingType(positionEntity.getPendingType())
                    .setModifyType(positionDTO.getModifyType())
                    .setApplyHandNum(positionEntity.getApplyHandNum().setScale(0, RoundingMode.HALF_UP))
                    .setApplyNum(positionEntity.getApplyNum())
                    .setApplyPrice(positionEntity.getApplyPrice())
                    .setApplyDiffPrice(positionEntity.getApplyDiffPrice())
                    .setApplyDomainCode(positionEntity.getDomainCode())
                    .setApplyTransferDomainCode(positionEntity.getTransferDomainCode())
                    .setCreatedByName(name)
                    .setCreatedBy(currentUserId)
                    .setUpdatedBy(currentUserId)
                    .setUpdatedByName(name)
                    .setUpdatedAt(new Date())
                    .setCreatedAt(new Date())
            ;
            positionLogService.saveLog(positionLogEntity);

            try {

                log.info("modifySendPriceInMailBegin:{}", "改单/撤单发送站内信");
                sendPriceInmail(positionEntity, positionLogEntity, positionDTO.getModifyType(), status);

                OperationDetailDTO operationDetailDTO = new OperationDetailDTO()
                        .setBizCode(logBizCodeEnum.getBizCode())
                        .setBizModule(ModuleTypeEnum.PRICE.getModule())
                        .setLogLevel(OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue())
                        .setSource(OperationSourceEnum.EMPLOYEE.getValue())
                        .setOperatorType(OperationSourceEnum.EMPLOYEE.getValue())
                        .setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()))
                        .setOperationName(logBizCodeEnum.getMsg())
                        .setReferBizId(positionEntity.getId())
                        .setReferBizCode(positionEntity.getCode())
                        .setMetaData(JSON.toJSONString(positionEntity))
                        .setData(JSON.toJSONString(positionDTO));

                // 记录日志
                operationLogFacade.recordOperationLog(operationDetailDTO);
            } catch (Exception e) {
                log.debug("添加日志错误:{}", e.getMessage());
            }

        }

        @Override
        public void cancel (PositionDTO positionDTO){

        }

        @Override
        public Result queryModifyLog (Integer positionId){
            List<PositionLogEntity> positionLogEntityList = positionLogService.queryLog(positionId);
            List<PositionLogVO> list = positionLogEntityList.stream().map(i -> {
                PositionLogVO positionLogVO = new PositionLogVO();
                BeanUtils.copyProperties(i, positionLogVO);
                PositionEntity positionEntity = positionDao.getById(positionId);
                positionLogVO
                        .setTransactionDomainCode(positionEntity.getTransactionDomainCode())
                        .setTransactionTransferDomainCode(positionEntity.getTransactionTransferDomainCode())
                        .setDealNum(positionEntity.getDealNum())
                        .setFutureCode(positionEntity.getFutureCode())
                        .setTransferFutureCode(positionEntity.getTransferFutureCode())
                        .setTransactionFutureCode(positionEntity.getTransactionFutureCode())
                        .setTransactionTransferDomainCode(positionEntity.getTransactionTransferDomainCode())
                        .setTransactionTransferFutureCode(positionEntity.getTransactionTransferFutureCode())
                        .setChangeFutureCode(positionEntity.getChangeFutureCode())
                        .setChangeTransferFutureCode(positionEntity.getChangeTransferFutureCode())
                ;
                if (positionEntity.getType() != PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                    positionLogVO
                            .setTransactionPrice(positionEntity.getTransactionPrice());
                } else {
                    positionLogVO.setTransactionPrice(positionEntity.getTransactionDiffPrice());
                }

                return positionLogVO;
            }).collect(Collectors.toList());
            return Result.success(list);
        }

        /**
         * 当前时间是否可以申请/改单/撤单
         *
         * @return
         */
        public int canPriceOperation ( int system, int customerId, Integer category2){
            // 当前时间 秒
            DateTime date = DateUtil.date();
            int hour = date.hour(true);
            int passHour = insertHour;
            boolean insertValid = hour >= passHour ? true : false;
            boolean updateValid = hour >= updateHour ? true : false;
            int currentTime = DateUtil.timeToSecond(DateUtil.formatTime(date));
            String categoryIdStr = String.valueOf(category2);
            String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (system == SystemEnum.MAGELLAN.getValue()) {
                List<PricePowerTimeEntity> notCanInsertList = pricePowerTimeService.queryMagellanPricePowerTimeInsertList(categoryIdStr, PricePowerType.CAN_NOT_BOOKED.getValue(), today, insertValid);
                for (PricePowerTimeEntity time : notCanInsertList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 0;
                    }
                }
                List<PricePowerTimeEntity> notCanUpdateList = pricePowerTimeService.queryMagellanPricePowerTimeUpdateList(categoryIdStr, PricePowerType.CAN_NOT_BOOKED.getValue(), today, updateValid);
                for (PricePowerTimeEntity time : notCanUpdateList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 0;
                    }
                }
                List<PricePowerTimeEntity> canInsertList = pricePowerTimeService.queryMagellanPricePowerTimeInsertList(categoryIdStr, PricePowerType.CAN_BE_BOOKED.getValue(), today, insertValid);
                for (PricePowerTimeEntity time : canInsertList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 1;
                    }
                }
                List<PricePowerTimeEntity> canUpdateList = pricePowerTimeService.queryMagellanPricePowerTimeUpdateList(categoryIdStr, PricePowerType.CAN_BE_BOOKED.getValue(), today, updateValid);
                for (PricePowerTimeEntity time : canUpdateList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 1;
                    }
                }
                return 0;
            } else {
                List<PricePowerTimeEntity> notCanInsertList = pricePowerTimeService.queryColumbusPricePowerTimeInsertList(categoryIdStr, PricePowerType.CAN_NOT_BOOKED.getValue(), today, insertValid);
                for (PricePowerTimeEntity time : notCanInsertList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 0;
                    }
                }
                List<PricePowerTimeEntity> notCanUpdateList = pricePowerTimeService.queryColumbusPricePowerTimeUpdateList(categoryIdStr, PricePowerType.CAN_NOT_BOOKED.getValue(), today, updateValid);
                for (PricePowerTimeEntity time : notCanUpdateList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 0;
                    }
                }
                List<PricePowerTimeEntity> canInsertList = pricePowerTimeService.queryColumbusPricePowerTimeInsertList(categoryIdStr, PricePowerType.CAN_BE_BOOKED.getValue(), today, insertValid);
                for (PricePowerTimeEntity time : canInsertList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 1;
                    }
                }
                List<PricePowerTimeEntity> canUpdateList = pricePowerTimeService.queryColumbusPricePowerTimeUpdateList(categoryIdStr, PricePowerType.CAN_BE_BOOKED.getValue(), today, updateValid);
                for (PricePowerTimeEntity time : canUpdateList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        return 1;
                    }
                }
                String priceGradeBusinessCode = GoodsCategoryEnum.getByValue(category2).getCode();
                PriceGradeEntity mApplyNight = priceGradeService.queryPriceGradeByCode(priceGradeBusinessCode + "_APPLY_NIGHT");

                CustomerGradeScoreDTO customerGradeScoreDTO = new CustomerGradeScoreDTO();
                customerGradeScoreDTO.setCustomerId(customerId);
                customerGradeScoreDTO.setCategory2(String.valueOf(category2));
                List<CustomerGradeScoreEntity> customerGradeScoreEntities = customerGradeScoreFacade.queryCustomerGradeScoreList(customerGradeScoreDTO);
                if (customerGradeScoreEntities.isEmpty()) {
                    return 0;
                }
                CustomerGradeScoreEntity customerGradeScoreEntity = customerGradeScoreEntities.get(0);
                //CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailList(customerId, categoryId);
                int gradeScore = -1;
                if (!CollectionUtil.isEmpty(customerGradeScoreEntities)) {
                    gradeScore = StringUtil.isEmpty(customerGradeScoreEntity.getGradeScore()) ? -1 : Integer.valueOf(customerGradeScoreEntity.getGradeScore());
                }
                List<PricePowerTimeEntity> rateList = pricePowerTimeService.queryColumbusPricePowerTimeInsertList(categoryIdStr, PricePowerType.RATE_BOOKED.getValue(), today, insertValid);
                rateList.addAll(
                        pricePowerTimeService.queryColumbusPricePowerTimeUpdateList(categoryIdStr, PricePowerType.RATE_BOOKED.getValue(), today, updateValid)
                );
                for (PricePowerTimeEntity time : rateList) {
                    if (DateUtil.timeToSecond(time.getStartTime().toString()) <= currentTime && currentTime <= DateUtil.timeToSecond(time.getEndTime().toString())) {
                        if (mApplyNight != null && gradeScore != -1) {
                            if (gradeScore >= mApplyNight.getGradeStart())
                                return 1;
                        } else
                            return 2;
                    }
                }
                return 0;
            }
//        List<String> list = ConfigurationConstants.CAN_NOT_PRICE_OPERATION_TIME;
//        for (String time : list) {
//            List<String> stringList = Arrays.asList(time.split("-"));
//            if (DateUtil.timeToSecond(stringList.get(0)) <= currentTime && currentTime <= DateUtil.timeToSecond(stringList.get(1))) {
//                return 0;
//            }
//        }
        }

        /**
         * 判断转入是否可以转入当前合约
         *
         * @param positionDTO
         * @return
         */
        private Boolean canTransferMonth (PositionDTO positionDTO){
            if (null == positionDTO.getTransferDomainCode()) {
                return true;
            }
            if (positionDTO.getTransferDomainCode().trim().equals(positionDTO.getDomainCode().trim())
                    && positionDTO.getType().equals(PriceTypeEnum.TRANSFER_MONTH.getValue())) {
                return false;
            }
            return true;
        }


        /**
         * 发送头寸站内信
         *
         * @param positionEntity
         */
        public void sendPriceInmail (PositionEntity positionEntity, PositionLogEntity positionLogEntity, Integer
        modifyType, Integer status){

            List<RoleEntity> roleEntities = roleFacade.getRoleListByRoleDefId(400);
            if (roleEntities.isEmpty()) {
                return;
            }

            List<String> receivers = new ArrayList<>();
            for (RoleEntity roleEntity : roleEntities) {
                receivers.add(String.valueOf(roleEntity.getId()));
            }

            if (modifyType == PriceApplyOperationTypeEnum.CHANGE_APPLY.getValue()) {
                log.info("modifySendPriceInMailBegin.updateApplyForInMail:{}", "改单发送站内信");
                updateApplyForInmail(positionEntity, receivers, positionLogEntity, status);
            } else {
                log.info("modifySendPriceInMailBegin,addRevocationInMail:{}", "新增/撤单发送站内信");
                addRevocationInmail(positionEntity, receivers, modifyType);
            }

        /*sendWebSocketInMail(receivers.stream()
                .map(Integer::parseInt).collect(Collectors.toList()));*/
        }

        /**
         * 新增撤单站内信
         *
         * @param positionEntity
         * @param receivers
         * @param modifyType
         */
        public void addRevocationInmail (PositionEntity positionEntity, List < String > receivers, Integer modifyType){
            try {
                MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
                messageInfoDTO.setUuid(UUID.randomUUID().toString())
                        .setCategoryId(positionEntity.getCategory2())
                        .setBusinessSceneCode(BusinessSceneEnum.FUTURE_CLOSE_POSITION.getDesc())
                        .setMenuCode(String.valueOf(MenuCodeEnum.POSITION.getValue()))
                        .setBusinessCode(MessageBusinessCodeEnum.POSITION_ADD_REVOCATION_APPLY_FOR_INMAIL.name())
                        .setReferBizId(positionEntity.getId().toString())
                        .setSystem(SystemEnum.MAGELLAN.getValue());


                Map<String, String> mapBizData = new HashMap<>();

                //标题:${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}${applyHandNum!}手 ${applyPrice!}${applyCode!}
                //内容:${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}${applyHandNum!}手${applyPrice!}<a href = '${url}'>${applyCode!}</a>
                //新增/撤单
                mapBizData.put("auditStatus", PriceApplyOperationTypeEnum.WITHDRAW_APPLY.getValue() == modifyType ? "撤单" : "新挂单");
                //申请单类型
                mapBizData.put("priceType", PriceTypeEnum.getByValue(positionEntity.getType()).getDesc());
                //买卖方向
                mapBizData.put("orientation", "1".equalsIgnoreCase(positionEntity.getOrientation()) ? "买" : "卖");

                //期货合约
                if (PriceTypeEnum.PRICING.getValue() == positionEntity.getType()) {
                    mapBizData.put("dominantCode", positionEntity.getFutureCode() + positionEntity.getDomainCode());
                } else {
                    mapBizData.put("dominantCode", positionEntity.getFutureCode() + positionEntity.getDomainCode() + "->" + positionEntity.getTransferFutureCode() + positionEntity.getTransferDomainCode());
                }
                mapBizData.put("closeTypeName", CloseTypeEnum.getByValue(positionEntity.getCloseType()).getDescription());
                //申请手数
                mapBizData.put("applyHandNum", positionEntity.getApplyHandNum().stripTrailingZeros().toPlainString());
                //申请价格
                String applyPrice = "";
                if (PriceTypeEnum.TRANSFER_MONTH.getValue() != positionEntity.getType()) {
                    //点价取价格
                    applyPrice = PendingTypeEnum.PENDING.getValue() == positionEntity.getPendingType() ? positionEntity.getApplyPrice().stripTrailingZeros().toPlainString() : "随盘";
                } else {
                    //转月价差
                    applyPrice = PendingTypeEnum.PENDING.getValue() == positionEntity.getPendingType() ? positionEntity.getApplyDiffPrice().stripTrailingZeros().toPlainString() : "随盘";
                }
                mapBizData.put("applyPrice", applyPrice);
                //申请合约
                //Case:1002855 期货头寸处理没有价格，且需要加上卖方主体的显示 Author:Wan 2024-12-09 start
                //多余字段
                //mapBizData.put("pendingType", PendingTypeEnum.getByValue(positionEntity.getPendingType()).getDescription());
                //Case:1002855 期货头寸处理没有价格，且需要加上卖方主体的显示 Author:Wan 2024-12-09 end

                //Case:1002855 需要加上卖方主体的显示 Author:Wan 2024-12-24 start
                CompanyEntity companyEntity = companyFacade.queryCompanyById(positionEntity.getCompanyId());
                mapBizData.put("companyName", companyEntity.getName());
                //Case:1002855 需要加上卖方主体的显示 Author:Wan 2024-12-24 end
                mapBizData.put("applyCode", positionEntity.getCode());
                messageInfoDTO.setDataMap(mapBizData);
                messageInfoDTO.setReceivers(receivers);
                messageFacade.sendMessage(messageInfoDTO);
            } catch (Exception e) {
                log.error("sendApproveNoticeMessage:{}", e.getMessage());
                e.printStackTrace();
            }
            log.info("modifySendPriceInMailBegin.updateApplyForInMail.finish:{}", "改单发送站内信----完成");
        }

        /**
         * 新增撤单站内信
         *
         * @param positionEntity
         * @param positionLogEntity
         */
        public void updateApplyForInmail (PositionEntity positionEntity, List < String > receivers, PositionLogEntity
        positionLogEntity, Integer status){
            try {
                MessageInfoDTO messageInfoDTO = new MessageInfoDTO();
                messageInfoDTO.setUuid(UUID.randomUUID().toString())
                        .setBusinessSceneCode(BusinessSceneEnum.FUTURE_CLOSE_POSITION.getDesc())
                        .setCategoryId(positionEntity.getCategory2())
                        .setMenuCode(String.valueOf(MenuCodeEnum.POSITION.getValue()))
                        .setBusinessCode(MessageBusinessCodeEnum.POSITION_UPDATE_APPLY_FOR_INMAIL.name())
                        .setReferBizId(positionEntity.getId().toString())
                        .setSystem(SystemEnum.MAGELLAN.getValue());

                Map<String, String> mapBizData = new HashMap<>();
                //标题:${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}${applyHandNum!}手 ${applyPrice!}${applyCode!}
                //内容:改单 ${priceType!};</br>
                //${auditStatus!} ${priceType!} ${dealDirection!}${dominantCode!}${applyHandNum!}手 ${applyPrice!}<a href = '${url}'>${applyCode!}</a>;</br>
                //${ChangePendingDominantCode!}${ChangeTranferDominantCode!}${changeHandNum!}${changePrice!}${changeDiffPrice!}
                //改单:/申请单类型
                mapBizData.put("priceType", PositionStatusEnum.getByValue(status).getDescription() + "新挂单");
                //买卖方向
                mapBizData.put("orientation", "1".equalsIgnoreCase(positionEntity.getOrientation()) ? "买" : "卖");
                //期货合约
                if (PriceTypeEnum.PRICING.getValue() == positionEntity.getType()) {
                    mapBizData.put("dominantCode", positionEntity.getFutureCode() + positionEntity.getDomainCode());
                } else {
                    mapBizData.put("dominantCode", positionEntity.getFutureCode() + positionEntity.getDomainCode() + "->" + positionEntity.getTransferFutureCode() + positionEntity.getTransferDomainCode());
                }
                //申请手数
                mapBizData.put("applyHandNum", positionEntity.getApplyHandNum().stripTrailingZeros().toPlainString());
                String applyPrice = "";
                if (PriceTypeEnum.TRANSFER_MONTH.getValue() != positionEntity.getType()) {
                    //点价取价格
                    applyPrice = PendingTypeEnum.PENDING.getValue() == positionEntity.getPendingType() ? positionEntity.getApplyPrice().stripTrailingZeros().toPlainString() : "随盘";
                } else {
                    //转月价差
                    applyPrice = PendingTypeEnum.PENDING.getValue() == positionEntity.getPendingType() ? positionEntity.getApplyDiffPrice().stripTrailingZeros().toPlainString() : "随盘";
                }
                mapBizData.put("applyPrice", applyPrice);
                //申请合约
                mapBizData.put("pendingType", PendingTypeEnum.getByValue(positionEntity.getPendingType()).getDescription());
                mapBizData.put("applyCode", positionEntity.getCode());
                //Case:1002855 需要加上卖方主体的显示 Author:Wan 2024-12-24 start
                CompanyEntity companyEntity = companyFacade.queryCompanyById(positionEntity.getCompanyId());
                mapBizData.put("companyName", companyEntity.getName());
                //Case:1002855 需要加上卖方主体的显示 Author:Wan 2024-12-24 end
                //挂单月份
                if ((StrUtil.isNotBlank(positionLogEntity.getApplyDomainCode()) && StrUtil.isNotBlank(positionLogEntity.getChangeDomainCode()))
                        || (StrUtil.isNotBlank(positionLogEntity.getApplyTransferDomainCode()) && StrUtil.isNotBlank(positionLogEntity.getChangeTransferDomainCode()))) {

                    StringBuilder builder = new StringBuilder();
                    builder.append(positionEntity.getFutureCode());
                    builder.append(StrUtil.isNotBlank(positionLogEntity.getApplyDomainCode()) ? positionLogEntity.getApplyDomainCode() : positionEntity.getDomainCode());

                    if (PriceTypeEnum.PRICING.getValue() != positionEntity.getType()) {
                        builder.append("->");
                        builder.append(positionEntity.getTransferFutureCode());
                        builder.append(StrUtil.isNotBlank(positionLogEntity.getApplyTransferDomainCode()) ? positionLogEntity.getApplyTransferDomainCode() : positionEntity.getTransferDomainCode());
                    }

                    //builder.append(positionLogEntity.getApplyTransferDomainCode());
                    mapBizData.put("ChangePendingDominantCode", String.valueOf(builder));

                    StringBuilder builder1 = new StringBuilder();
                    builder1.append(" 到 ");
                    builder1.append(positionEntity.getChangeFutureCode());
                    builder1.append(StrUtil.isNotBlank(positionLogEntity.getChangeDomainCode()) ? positionLogEntity.getChangeDomainCode() : positionEntity.getChangeDomainCode());
                    //builder.append(positionLogEntity.getChangeDomainCode());

                    if (PriceTypeEnum.PRICING.getValue() != positionEntity.getType()) {
                        builder1.append("->");
                        builder1.append(positionEntity.getChangeTransferFutureCode());
                        builder1.append(StrUtil.isNotBlank(positionLogEntity.getChangeTransferDomainCode()) ? positionLogEntity.getChangeTransferDomainCode() : positionEntity.getTransferDomainCode());
                    }
                    //builder.append(positionLogEntity.getChangeTransferDomainCode());
                    mapBizData.put("ChangeTranferDominantCode", String.valueOf(builder1));
                }
            /*//转入月份
            if () {

                StringBuilder builder = new StringBuilder();
                builder.append(GoodsCategoryEnum.getByValue(positionEntity.getCategoryId()).getLkgFutureSymbol());
                builder.append(positionLogEntity.getApplyTransferDomainCode());
                builder.append("->");
                builder.append(GoodsCategoryEnum.getByValue(positionEntity.getCategoryId()).getLkgFutureSymbol());
                builder.append(positionLogEntity.getChangeTransferDomainCode());

                mapBizData.put("ChangeTranferDominantCode", String.valueOf(builder));
            }*/
                //数量
                if (!(positionLogEntity.getApplyHandNum().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getChangeHandNum().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getApplyHandNum().compareTo(positionLogEntity.getChangeHandNum()) == 0)) {

                    StringBuilder builder = new StringBuilder();
                    builder.append(positionLogEntity.getApplyHandNum().stripTrailingZeros().toPlainString());
                    builder.append("手 到 ");
                    builder.append(positionLogEntity.getChangeHandNum().stripTrailingZeros().toPlainString());
                    builder.append("手");

                    mapBizData.put("changeHandNum", String.valueOf(builder));
                }
                //价格
                if (null != positionLogEntity.getApplyPrice()
                        && null != positionLogEntity.getChangePrice()
                        && !(positionLogEntity.getApplyPrice().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getChangePrice().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getApplyPrice().compareTo(positionLogEntity.getChangePrice()) == 0)) {

                    StringBuilder builder = new StringBuilder();
                    builder.append("价格:");
                    builder.append(positionLogEntity.getApplyPrice().stripTrailingZeros().toPlainString());
                    builder.append("元 到 ");
                    builder.append(positionLogEntity.getChangePrice().stripTrailingZeros().toPlainString());
                    builder.append("元");

                    mapBizData.put("changePrice", String.valueOf(builder));
                }
                //价差
                if (null != positionLogEntity.getChangeDiffPrice()
                        && null != positionLogEntity.getApplyDiffPrice()
                        && !(positionLogEntity.getChangeDiffPrice().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getApplyDiffPrice().compareTo(BigDecimal.ZERO) == 0)
                        && !(positionLogEntity.getChangeDiffPrice().compareTo(positionLogEntity.getApplyDiffPrice()) == 0)) {

                    StringBuilder builder = new StringBuilder();
                    builder.append("价差:");
                    builder.append(positionLogEntity.getApplyDiffPrice().stripTrailingZeros().toPlainString());
                    builder.append("元 到 ");
                    builder.append(positionLogEntity.getChangeDiffPrice().stripTrailingZeros().toPlainString());
                    builder.append("元");

                    mapBizData.put("changeDiffPrice", String.valueOf(builder));
                }

                messageInfoDTO.setDataMap(mapBizData);
                messageInfoDTO.setReceivers(receivers);
                messageFacade.sendMessage(messageInfoDTO);
            } catch (Exception e) {
                log.error("sendApproveNoticeMessage:{}", e.getMessage());
                e.printStackTrace();
            }
            log.info("modifySendPriceInMailBegin.updateApplyForInMail.finish:{}", "改单发送站内信----完成");
        }


        public void sendWebSocketInMail (List < Integer > roleIdList) {
            List<EmployEntity> employEntities = employFacade.queryEmployByRoleIds(roleIdList);

            for (EmployEntity employEntity : employEntities) {

                Integer inMailCount = messageFacade.queryEmployInMailCount(employEntity.getId());

                WebsocketInMailDTO websocketInMailDTO = new WebsocketInMailDTO();
                websocketInMailDTO.setUserId(employEntity.getId())
                        .setInMailCount(inMailCount);

                messageFacade.pushWebsocketInMailMsg(websocketInMailDTO);
            }

        }
    }
