package com.navigator.future.service.impl;

import com.navigator.common.util.BeanConvertUtils;
import com.navigator.future.dao.PriceApplyDao;
import com.navigator.future.dao.PriceApplyLogDao;
import com.navigator.future.dao.PriceDealDetailDao;
import com.navigator.future.enums.PriceApplyOperationTypeEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.pojo.dto.QueryPriceApplyLogDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.vo.PriceApplyLogVo;
import com.navigator.future.service.IPriceApplyLogService;
import com.navigator.future.service.IPriceDealService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 改撤单记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Service
public class PriceApplyLogServiceImpl implements IPriceApplyLogService {

    @Resource
    private PriceApplyLogDao priceApplyLogDao;
    @Resource
    private PriceApplyDao priceApplyDao;
    @Resource
    private IPriceDealService priceDealService;

    @Override
    public PriceApplyLogEntity getPriceApplyLog(Integer priceApplyId) {
        return priceApplyLogDao.getPriceApplyLog(priceApplyId);
    }

    @Override
    public List<PriceApplyLogVo> getPriceApplyLogPriceApplyId(QueryPriceApplyLogDTO queryPriceApplyLogDTO) {
        List<PriceApplyLogEntity> priceApplyLog = priceApplyLogDao.getPriceApplyLogPriceApplyId(queryPriceApplyLogDTO);
        List<PriceApplyLogVo> priceApplyLogVos = BeanConvertUtils.convert2List(PriceApplyLogVo.class, priceApplyLog);
        return priceApplyLogVos;
    }

    @Override
    public List<PriceApplyLogVo> queryPriceApplyLog(Integer priceApplyId) {
        PriceApplyEntity priceApplyEntity = priceApplyDao.getById(priceApplyId);
        return priceApplyLogDao.queryPriceApplyLog(priceApplyId).stream().map(priceApplyLogEntity -> {


            PriceApplyLogVo priceApplyLogVo = BeanConvertUtils.convert(PriceApplyLogVo.class, priceApplyLogEntity)
                    .setModifyTypeName(PriceApplyOperationTypeEnum.getByValue(priceApplyLogEntity.getType()).getDesc())
                    .setPriceType(priceApplyEntity.getType());

            List<PriceDealDetailEntity> priceDealDetailEntities = priceDealService.queryPriceDealDetailByApplyIdStatus(priceApplyEntity.getId(), PriceStatusEnum.WAIT_ALLOCATE.getValue());
            if (!priceDealDetailEntities.isEmpty()) {

                PriceDealDetailEntity priceDealDetailEntity = priceDealDetailEntities.get(0);
                priceApplyLogVo
                        .setDealNum(priceDealDetailEntity.getDealNum())
                        .setDealPrice(priceDealDetailEntity.getDealPrice())
                        .setDealDiffPrice(priceDealDetailEntity.getTransactionDiffPrice());
            }

            return priceApplyLogVo;
        }).collect(Collectors.toList());
    }
}
