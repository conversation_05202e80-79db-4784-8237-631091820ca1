package com.navigator.future.service.impl;

import cn.hutool.core.util.StrUtil;
import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.enums.AllocateStatusEnum;
import com.navigator.future.enums.PriceStatusEnum;
import com.navigator.future.pojo.dto.CustomerDomainTransDTO;
import com.navigator.future.pojo.dto.CustomerFuturesDTO;
import com.navigator.future.pojo.dto.PriceApplyOperateNumDTO;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceApplyLogEntity;
import com.navigator.future.pojo.vo.ContractsFuturesVO;
import com.navigator.future.service.*;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractEntity;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 14:21
 */
@Service
@Slf4j
public class FuturesDomainServiceImpl implements IFuturesDomainService {

    @Resource
    private ContractFacade contractFacade;
    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private CategoryFacade categoryFacade;
    @Resource
    private IPriceApplyService priceApplyService;
    @Resource
    private CompanyFacade companyFacade;
    @Resource
    private CustomerDetailFacade customerDetailFacade;
    @Resource
    private CEmployFacade cEmployFacade;
    @Resource
    private IPriceApplyLogService priceApplyLogService;
    @Resource
    private IPriceAllocateService priceAllocateService;
    @Resource
    private IFutureTransService futureTransService;


    /**
     * 查询期货信息
     *
     * @param queryContractFuturesDTO
     * @return
     */
    @Override
    public List<ContractsFuturesVO> queryContractsFutures(QueryContractFuturesDTO queryContractFuturesDTO) {
        List<ContractsFuturesVO> contractsFuturesVO = new ArrayList<>();
        if (null == queryContractFuturesDTO.getSalesType()) {
            queryContractFuturesDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        }
        ContractFuturesDTO contractFuturesDTO = BeanConvertUtils.convert(ContractFuturesDTO.class, queryContractFuturesDTO);
        //获取客户信息
        CustomerDTO customerDTO = this.getCustomerMessage(contractFuturesDTO);
        if (StrUtil.isBlank(queryContractFuturesDTO.getCustomerId())) {
            queryContractFuturesDTO.setCustomerId(customerDTO.getId().toString());
        }
//        queryContractFuturesDTO.setCategory2(queryContractFuturesDTO.getGoodsCategoryId().toString());

        // todo 结构化定价数据校验待产品确认
        /*if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == queryContractFuturesDTO.getStatus()) {
            //查询客户配置 TODO 这边的限制有问题
            CustomerDetailBO customerDetailBO = new CustomerDetailBO()
                    .setCustomerId(customerDTO.getId())
                    .setCategory2(String.valueOf(queryContractFuturesDTO.getGoodsCategoryId()));
            List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

            if (customerDetailEntityList.isEmpty()) {
                throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
            }

            CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
            //判断客户是否开启结构化定价 TODO 先注释掉
            if (null == customerDetailEntity || GeneralEnum.NO.getValue().equals(customerDetailEntity.getIsStructure())) {
                throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
            }
        }*/

        if (null != queryContractFuturesDTO.getCompanyId()) {
            CompanyEntity companyEntity = companyFacade.queryCompanyById(queryContractFuturesDTO.getCompanyId());

            contractFuturesDTO = BeanConvertUtils.convert(ContractFuturesDTO.class, queryContractFuturesDTO);
            contractFuturesDTO
                    .setCategory2(Integer.parseInt(queryContractFuturesDTO.getCategory2()))
            ;
            contractsFuturesVO.addAll(contractsFuturesVOList(contractFuturesDTO, companyEntity, customerDTO));

        } else if (null != queryContractFuturesDTO.getCompanyIds() && !queryContractFuturesDTO.getCompanyIds().isEmpty()) {
            for (Integer companyId : queryContractFuturesDTO.getCompanyIds()) {
                CompanyEntity companyEntity = companyFacade.queryCompanyById(companyId);

                contractFuturesDTO = BeanConvertUtils.convert(ContractFuturesDTO.class, queryContractFuturesDTO);
                contractFuturesDTO
                        .setCompanyId(companyEntity.getId())
                        .setCategory2(Integer.parseInt(queryContractFuturesDTO.getCategory2()))
                ;
                contractsFuturesVO.addAll(contractsFuturesVOList(contractFuturesDTO, companyEntity, customerDTO));
            }
        } else {
            List<CompanyEntity> companyEntities = companyFacade.queryCompanyList();

            for (CompanyEntity companyEntity : companyEntities) {
                contractFuturesDTO = BeanConvertUtils.convert(ContractFuturesDTO.class, queryContractFuturesDTO);
                contractFuturesDTO
                        .setCompanyId(companyEntity.getId())
                        .setCategory2(Integer.parseInt(queryContractFuturesDTO.getCategory2()))
                ;
                contractsFuturesVO.addAll(contractsFuturesVOList(contractFuturesDTO, companyEntity, customerDTO));
            }
        }

        return contractsFuturesVO;
    }

    private List<ContractsFuturesVO> contractsFuturesVOList(ContractFuturesDTO contractFuturesDTO, CompanyEntity companyEntity, CustomerDTO customerDTO) {

        List<ContractsFuturesVO> contractsFuturesVO = new ArrayList<>();
        //查询客户所有的期货
        List<ContractEntity> contractDomain = contractFacade.queryContractsFutures(contractFuturesDTO);
        for (ContractEntity contract : contractDomain) {
            //查询出的期货合约
            String domainCode = contract.getDomainCode();
            contractFuturesDTO.setDomainCode(domainCode);
            contractFuturesDTO.setFutureCode(contract.getFutureCode());


            /*//查询供应信息
            CustomerDTO supplier = null;
            if (null != contract.getSupplierId()) {
                contractFuturesDTO.setSupplierId(contract.getSupplierId().toString());
                supplier = customerFacade.getCustomerById(contract.getSupplierId());
            }*/

            //根据期货查询出点价/转月的数量

            /*//查询可点价量
            CustomerFuturesDTO priceNum = this.mayPriceNum(contractFuturesDTO);
            //查询可转月量
            CustomerFuturesDTO transferNum = this.mayTransferNum(contractFuturesDTO);

            BigDecimal canTransferNum = transferNum.getMayTransferNum();
            BigDecimal canPriceNum = priceNum.getMayPriceNum();*/


            CustomerDomainTransDTO customerDomainTransDTO = futureTransService.getCustomerDomainTransInfo(contractFuturesDTO);

            BigDecimal canTransferNum = customerDomainTransDTO.getCanTransferCount().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : customerDomainTransDTO.getCanTransferCount();

            BigDecimal canPriceNum = customerDomainTransDTO.getCanPriceCount().compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : customerDomainTransDTO.getCanPriceCount();

            if (canTransferNum.compareTo(BigDecimal.ZERO) > 0 || canPriceNum.compareTo(BigDecimal.ZERO) > 0) {
                //拼接字段
                ContractsFuturesVO contractsFutures = new ContractsFuturesVO()
                        .setDomainCode(domainCode)
                        .setCustomerName(customerDTO.getName())
                        .setCustomerId(customerDTO.getId())
                        .setCategory2(contract.getCategory2())
                        .setMayTransferNum(canTransferNum)
                        .setCompanyId(companyEntity.getId())
                        .setCompanyShortName(companyEntity.getName())
                        .setMayPriceNum(canPriceNum)
                        .setFutureCode(contract.getFutureCode());
                contractsFuturesVO.add(contractsFutures);
            }
        }

        return contractsFuturesVO;

    }

    /**
     * 查询可点价量
     *
     * @param contractFuturesDTO
     * @return
     */
    @Override
    public CustomerFuturesDTO mayPriceNum(ContractFuturesDTO contractFuturesDTO) {

        if (null == contractFuturesDTO.getSalesType()) {
            contractFuturesDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        }
        CustomerFuturesDTO customerFuturesDTO = new CustomerFuturesDTO();
        //获取客户信息
        CustomerDTO customerDTO = this.getCustomerMessage(contractFuturesDTO);
        if (StrUtil.isBlank(contractFuturesDTO.getCustomerId())) {
            contractFuturesDTO.setCustomerId(customerDTO.getId().toString());
            // todo 结构化定价校验待产品确认
            /*if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus()) {
                //查询客户配置
                CustomerDetailBO customerDetailBO = new CustomerDetailBO()
                        .setCustomerId(customerDTO.getId())
                        .setCategory2(String.valueOf(contractFuturesDTO.getCategory2()))
                        .setCategory3(String.valueOf(contractFuturesDTO.getCategory3()))
                        ;
                List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

                if (customerDetailEntityList.isEmpty()) {
                    throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
                }

                CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
                //判断客户是否开启结构化定价
                if (GeneralEnum.NO.getValue() == customerDetailEntity.getIsStructure()) {
                    throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
                }
            }*/
        }
        CustomerDomainTransDTO customerDomainTransDTO = futureTransService.getCustomerDomainTransInfo(contractFuturesDTO);


        BigDecimal mayPriceNum = customerDomainTransDTO.getCanPriceCount();

        //根据申请单id查询申请单数量
        if (StrUtil.isNotBlank(contractFuturesDTO.getApplyId())) {
            PriceApplyEntity priceApplyEntity = priceApplyService.queryPriceApplyById(Integer.parseInt(contractFuturesDTO.getApplyId()));
            mayPriceNum = mayPriceNum.add(priceApplyEntity.getApplyNum());
        }

        customerDomainTransDTO.setCanPriceCount(mayPriceNum);

        customerFuturesDTO.setCustomerId(customerDTO.getId())
                .setCustomerName(customerDTO.getName())
                .setMayPriceNum(customerDomainTransDTO.getCanPriceCount());

        /*//查询基差合同的转月量
        contractFuturesDTO.setContractType(Arrays.asList(ContractTypeEnum.JI_CHA.getValue(), ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()));

        //根据合约属性查询出合同数据
        List<ContractEntity> contractProcessorEntities = contractFacade.queryContractsFuturesNum(contractFuturesDTO);
        if (contractProcessorEntities == null) {
            return null;
        }
        BigDecimal mayPriceNum = this.mayOperation(contractProcessorEntities, PriceTypeEnum.PRICING.getValue());
        //根据申请单id查询申请单数量
        if (StrUtil.isNotBlank(contractFuturesDTO.getApplyId())) {
            PriceApplyEntity priceApplyEntity = priceApplyService.queryPriceApplyById(Integer.parseInt(contractFuturesDTO.getApplyId()));
            mayPriceNum = mayPriceNum.add(priceApplyEntity.getApplyNum());
        }

        //计算点价/转月申请单中的数量
        List<Integer> type = Arrays.asList(PriceTypeEnum.TRANSFER_MONTH.getValue(),
                PriceTypeEnum.PRICING.getValue());

        mayPriceNum = mayPriceNum.subtract(this.operateNum(contractFuturesDTO, type));
        //查询分配单中的量
        mayPriceNum = mayPriceNum.subtract(this.priceAllocateNum(
                contractFuturesDTO,
                PriceTypeEnum.PRICING.getValue()));

        //总量小于0时==0
        if (mayPriceNum.compareTo(BigDecimal.ZERO) == -1) {
            mayPriceNum = BigDecimal.ZERO;
        }
        customerFuturesDTO.setMayPriceNum(mayPriceNum);*/
        return customerFuturesDTO;
    }

    /**
     * 查询可转月量
     *
     * @param contractFuturesDTO
     * @return
     */
    @Override
    public CustomerFuturesDTO mayTransferNum(ContractFuturesDTO contractFuturesDTO) {
        CustomerFuturesDTO customerFuturesDTO = new CustomerFuturesDTO();

        if (null == contractFuturesDTO.getSalesType()) {
            contractFuturesDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        }

        //获取客户信息
        CustomerDTO customerDTO = this.getCustomerMessage(contractFuturesDTO);
        if (StrUtil.isBlank(contractFuturesDTO.getCustomerId())) {
            contractFuturesDTO.setCustomerId(customerDTO.getId().toString());
            // todo 结构化定价校验待产品确认
            /*if (PriceTypeEnum.STRUCTURE_PRICING.getValue() == contractFuturesDTO.getStatus()) {
                //查询客户配置
                //查询客户配置
                CustomerDetailBO customerDetailBO = new CustomerDetailBO()
                        .setCustomerId(customerDTO.getId())
                        .setCategory2(String.valueOf(contractFuturesDTO.getCategory2()))
                        .setCategory3(String.valueOf(contractFuturesDTO.getCategory3()))
                        ;
                List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO);

                if (customerDetailEntityList.isEmpty()) {
                    throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
                }

                CustomerDetailEntity customerDetailEntity = customerDetailEntityList.get(0);
                //判断客户是否开启结构化定价
                if (GeneralEnum.NO.getValue().equals(customerDetailEntity.getIsStructure())) {
                    throw new BusinessException(ResultCodeEnum.CUSTOMER_IS_STRUCTURE);
                }
            }*/
        }
        CustomerDomainTransDTO customerDomainTransDTO = futureTransService.getCustomerDomainTransInfo(contractFuturesDTO);


        BigDecimal mayOperation = customerDomainTransDTO.getCanTransferCount();

        //根据申请单id查询申请单数量
        if (StrUtil.isNotBlank(contractFuturesDTO.getApplyId())) {
            PriceApplyEntity priceApplyEntity = priceApplyService.queryPriceApplyById(Integer.parseInt(contractFuturesDTO.getApplyId()));
            mayOperation = mayOperation.add(priceApplyEntity.getApplyNum());
        }

        customerDomainTransDTO.setCanTransferCount(mayOperation);

        customerFuturesDTO.setCustomerId(customerDTO.getId())
                .setCustomerName(customerDTO.getName())
                .setMayTransferNum(customerDomainTransDTO.getCanTransferCount());

        /*//查询基差合同的转月量
        contractFuturesDTO.setContractType(Arrays.asList(ContractTypeEnum.JI_CHA.getValue()));
        //根据合约属性查询出合同数据
        List<ContractEntity> contractProcessorEntities = contractFacade.queryContractsFuturesNum(contractFuturesDTO);


        //可操作量
        BigDecimal mayOperation = this.mayOperation(contractProcessorEntities, PriceTypeEnum.TRANSFER_MONTH.getValue());

        List<Integer> type = Arrays.asList(PriceTypeEnum.TRANSFER_MONTH.getValue());
        //计算转月申请单的量
        mayOperation = mayOperation.subtract(this.operateNum(contractFuturesDTO, type));

        //点价是否扣除转月量
        type = Arrays.asList(PriceTypeEnum.PRICING.getValue());
        BigDecimal notEnough = this.notEnough(contractFuturesDTO, type);
        if (notEnough.compareTo(BigDecimal.ZERO) == -1) {
            mayOperation = mayOperation.add(notEnough);
        }

        //查询分配单中的量
        mayOperation = mayOperation.subtract(this.priceAllocateNum(
                contractFuturesDTO,
                PriceTypeEnum.TRANSFER_MONTH.getValue()));

        //计算出的量小于0时==0
        if (mayOperation.compareTo(BigDecimal.ZERO) == -1) {
            mayOperation = BigDecimal.ZERO;
        }
        customerFuturesDTO.setMayTransferNum(mayOperation);*/
        return customerFuturesDTO;
    }

    /**
     * 计算可操作量
     *
     * @param contractProcessorEntities
     * @return
     */
    private BigDecimal mayOperation(List<ContractEntity> contractProcessorEntities, Integer priceType) {

        //总量
        BigDecimal contractNum = BigDecimal.ZERO;
        //可操作量
        BigDecimal mayOperation = BigDecimal.ZERO;
        //已转月吨数
        BigDecimal totalTransferNum = BigDecimal.ZERO;
        //已点量
        BigDecimal totalPriceNum = BigDecimal.ZERO;
        //已变更数量
        BigDecimal totalModifyNum = BigDecimal.ZERO;

        //计算合约下的转月数量
        for (ContractEntity contractEntity : contractProcessorEntities) {

            //查询合同是否还有转月次数
            /*if (contractProcessorEntity.getContractType() == ContractTypeEnum.JI_CHA.getValue()
                    && priceType == PriceTypeEnum.TRANSFER_MONTH.getValue()
                    && contractProcessorEntity.getAbleTransferTimes() <= 0) {
                continue;
            }*/

            //合同总数相加
            contractNum = contractNum.add(contractEntity.getContractNum());
            //已点价量
            totalPriceNum = totalPriceNum.add(contractEntity.getTotalPriceNum());
            //已变更量
            //totalModifyNum = totalModifyNum.add(contractProcessorEntity.getTotalModifyNum());
            //已转月数量相加
            //totalTransferNum = totalTransferNum.add(contractProcessorEntity.getTotalTransferNum());
        }

        //计算合同中的已转月量
        mayOperation = contractNum.subtract(totalTransferNum);
        //减去合同中的已点价量
        mayOperation = mayOperation.subtract(totalPriceNum);
        //减去已变更的数量
        mayOperation = mayOperation.subtract(totalModifyNum);

        return mayOperation;
    }

    /**
     * 计算点价是否够基差暂定价
     *
     * @param contractFuturesDTO
     * @param type
     * @return
     */
    public BigDecimal notEnough(ContractFuturesDTO contractFuturesDTO, List<Integer> type) {

        //查询点价申请单中的量
        BigDecimal pricing = BigDecimal.ZERO;
        if (contractFuturesDTO.getPriceNum() == null) {
            pricing = this.operateNum(contractFuturesDTO, type);
        } else {
            pricing = contractFuturesDTO.getPriceNum();
        }
        //查询基差暂定价合同
        contractFuturesDTO.setContractType(Arrays.asList(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()));
        List<ContractEntity> contractProcessorEntities = contractFacade.queryContractsFuturesNum(contractFuturesDTO);
        //总量
        BigDecimal contractNum = BigDecimal.ZERO;
        //可点价数量
        BigDecimal totalPriceNum = BigDecimal.ZERO;
        //已转月吨数
        BigDecimal totalTransferNum = BigDecimal.ZERO;
        //已变更数量
        BigDecimal totalModifyNum = BigDecimal.ZERO;
        //根据合约属性查询出合同数据
        //计算合约下的转月数量
        for (ContractEntity contractEntity : contractProcessorEntities) {
            //合同总数相加
            contractNum = contractNum.add(contractEntity.getContractNum());
            //以点价量
            totalPriceNum = totalPriceNum.add(contractEntity.getTotalPriceNum());
            //以转月数量
            //totalTransferNum = totalTransferNum.add(contractProcessorEntity.getTotalTransferNum());
            //已变更量
            //totalModifyNum = totalModifyNum.add(contractProcessorEntity.getTotalModifyNum());
        }
        //合同总量减去已点价数量
        contractNum = contractNum.subtract(totalPriceNum);
        //合同总量减去已转月数量
        contractNum = contractNum.subtract(totalTransferNum);
        //减去已变更的数量
        contractNum = contractNum.subtract(totalModifyNum);
        //算出已点价数量
        return contractNum.subtract(pricing);
    }

    /**
     * 查询申请单中的量
     *
     * @param contractFuturesDTO
     * @param type
     * @return
     */
    private BigDecimal operateNum(ContractFuturesDTO contractFuturesDTO, List<Integer> type) {
        PriceApplyOperateNumDTO priceApplyOperateNumDTO = new PriceApplyOperateNumDTO()
                .setType(type)
                .setCategoryId(contractFuturesDTO.getGoodsCategoryId())
                .setCustomerId(Integer.parseInt(contractFuturesDTO.getCustomerId()))
                .setDominantCode(contractFuturesDTO.getDomainCode());

        BigDecimal operateNum = BigDecimal.ZERO;

        //根据条件查询出申请表信息
        List<PriceApplyEntity> priceApplyEntities = priceApplyService.queryPriceApplyOperateNum(priceApplyOperateNumDTO);
        //判断是否为空
        if (priceApplyEntities == null) {
            return BigDecimal.ZERO;
        }
        for (PriceApplyEntity priceApply : priceApplyEntities) {
            //成交待分配
            if (priceApply.getStatus() == PriceStatusEnum.WAIT_ALLOCATE.getValue()) {

                //未分配量
                operateNum = operateNum.add(priceApply.getDealNum().subtract(priceApply.getAllocateNum()));

                //operateNum = operateNum.subtract(this.ContractProcessorEntity(priceApply.getId()));
            } else if (priceApply.getStatus() == PriceStatusEnum.WAIT_PENDING.getValue() || priceApply.getStatus() == PriceStatusEnum.WAIT_TRANSACTION.getValue()) {
                //查询最新改单数据
                //if (priceApply.getAuditStatus() == ApplyAuditStatusEnum.CHANGE_WAIT_AUDIT.getValue()) {
                if (priceApply.getLatestMessage().contains("改单")) {
                    //根据申请表id查询 改撤单记录表
                    PriceApplyLogEntity priceApplyLogEntity = priceApplyLogService.getPriceApplyLog(priceApply.getId());
                    if (priceApplyLogEntity.getChangeNum().compareTo(priceApply.getApplyNum()) > 0) {
                        operateNum = operateNum.add(priceApplyLogEntity.getChangeNum());
                    } else {
                        //已成交的申请单成交量相加
                        BigDecimal applyNum = priceApply.getApplyNum();
                        operateNum = operateNum.add(applyNum);
                    }
                } else {
                    //已成交的申请单成交量相加
                    BigDecimal applyNum = priceApply.getApplyNum();
                    operateNum = operateNum.add(applyNum);
                }
            }
        }
        return operateNum;
    }

    /**
     * 根据申请单id查询出分配单信息
     *
     * @param priceApplyId
     * @return
     */
    private BigDecimal priceAllocateByPriceApplyIdNum(Integer priceApplyId) {
        BigDecimal operateNum = BigDecimal.ZERO;
        List<PriceAllocateEntity> priceAllocateEntities = priceAllocateService.getAllocateByPriceApplyId(priceApplyId);
        if (priceAllocateEntities == null) {
            return BigDecimal.ZERO;
        }
        //申请单中的量相加
        for (PriceAllocateEntity priceAllocate : priceAllocateEntities) {
            operateNum = operateNum.add(priceAllocate.getAllocateNum());
        }
        return operateNum;
    }


    /**
     * 根据合约查询出点单申请中的量
     *
     * @param contractFuturesDTO
     * @return
     */
    private BigDecimal priceAllocateNum(ContractFuturesDTO contractFuturesDTO, Integer priceType) {
        PriceApplyOperateNumDTO priceApplyOperateNumDTO = new PriceApplyOperateNumDTO()
                .setType(Arrays.asList(PriceTypeEnum.TRANSFER_MONTH.getValue(), PriceTypeEnum.PRICING.getValue()))
                .setCategoryId(contractFuturesDTO.getGoodsCategoryId())
                .setCustomerId(Integer.parseInt(contractFuturesDTO.getCustomerId()))
                .setDominantCode(contractFuturesDTO.getDomainCode());

        BigDecimal allocateNum = BigDecimal.ZERO;

        //查询客户合约下的申请单id
        List<PriceApplyEntity> priceApplyEntities = priceApplyService.queryPriceApplyOperateNum(priceApplyOperateNumDTO);

        for (PriceApplyEntity priceApplyEntity : priceApplyEntities) {
            //根据申请单id 查询出分配单信息
            List<PriceAllocateEntity> priceAllocateEntity = priceAllocateService.getAllocateByDominantCode(priceApplyEntity.getId());
            if (priceAllocateEntity == null) {
                return BigDecimal.ZERO;
            }
            allocateNum = allocateNum.add(this.priceAllocateTemporaryNum(priceAllocateEntity, priceType));

            /*for (PriceAllocateEntity priceAllocate : priceAllocateEntity) {


                if (AllocateStatusEnum.WAIT_AUDIT.getValue() == priceAllocate.getStatus()) {
                    allocateNum = allocateNum.add(priceAllocate.getAllocateNum());
                }
            }*/
        }


        return allocateNum;
    }

    /**
     * 分配单中的量相加
     *
     * @param priceAllocateEntity
     * @param priceType
     * @return
     */
    public BigDecimal priceAllocateTemporaryNum(List<PriceAllocateEntity> priceAllocateEntity, Integer priceType) {
        //申请单点价量
        BigDecimal allocateNum = BigDecimal.ZERO;
        //申请单中基差量
        BigDecimal diffPriceNum = BigDecimal.ZERO;
        //基差暂定价
        BigDecimal tentativeDiffPriceNum = BigDecimal.ZERO;
        //审核通过的量相加
        for (PriceAllocateEntity priceAllocate : priceAllocateEntity) {
            if (AllocateStatusEnum.WAIT_AUDIT.getValue() == priceAllocate.getStatus()) {
                if (priceType != PriceTypeEnum.TRANSFER_MONTH.getValue()) {
                    allocateNum = allocateNum.add(priceAllocate.getAllocateNum());
                } else {
                    ContractEntity contractEntity = contractFacade.getContractById(priceAllocate.getContractId());
                    if (contractEntity.getContractType() != ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()) {
                        diffPriceNum = diffPriceNum.add(priceAllocate.getAllocateNum());
                    } else {
                        tentativeDiffPriceNum = tentativeDiffPriceNum.add(priceAllocate.getAllocateNum());
                    }
                }
            }
        }


        //转月数量查询限制
        if (diffPriceNum.add(tentativeDiffPriceNum).compareTo(BigDecimal.ZERO) > 0) {
            if (diffPriceNum.compareTo(tentativeDiffPriceNum) >= 0) {
                allocateNum = allocateNum.add(diffPriceNum);
            } else {
                allocateNum = allocateNum.add(tentativeDiffPriceNum);
            }

        }

        return allocateNum;
    }

    /**
     * 查询客户信息
     *
     * @param contractFuturesDTO
     * @return
     */
    public CustomerDTO getCustomerMessage(ContractFuturesDTO contractFuturesDTO) {

        //查询客户信息
        Integer customerId = 0;
        if (null != contractFuturesDTO.getSystem() && SystemEnum.COLUMBUS.getValue() == contractFuturesDTO.getSystem()) {
            customerId = Integer.parseInt(contractFuturesDTO.getCustomerId());
            CustomerEntity customerEntity = customerFacade.queryCustomerById(customerId);
            if (DisableStatusEnum.DISABLE.getValue() == customerEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.COMPANY_STSTUS_DISABLE);
            }

            CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));
            if (DisableStatusEnum.DISABLE.getValue() == cEmployEntity.getStatus()) {
                throw new BusinessException(ResultCodeEnum.EMPLOY_FORBIDDEN);
            }
        } else {
            customerId = Integer.parseInt(contractFuturesDTO.getCustomerId());
        }
        //根据客户id 查询出客户信息
        return customerFacade.getCustomerById(customerId);
    }
}
