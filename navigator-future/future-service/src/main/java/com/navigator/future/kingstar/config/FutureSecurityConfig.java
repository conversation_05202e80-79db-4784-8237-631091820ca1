package com.navigator.future.kingstar.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

@Slf4j
@EnableWebSecurity
public class FutureSecurityConfig extends WebSecurityConfigurerAdapter {

    @Value("${kingstar.esbToNavigator.authentication.api-key}")
    private String apiKey;

    @Value("${kingstar.esbToNavigator.authentication.api-value}")
    private String apiValue;

    @Value("${kingstar.esbToNavigator.authentication.api-matchers}")
    private String apiMatchers;

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        // 基础配置，允许访问 "/kingstar/callback" 的请求进行认证
        http.authorizeRequests()
                .antMatchers(apiMatchers).authenticated()
                .and()
                .csrf().disable();

        // 使用 API Key 认证
        log.info("kingstar Using API Key authentication");
        // 启用 API Key 认证
        http.addFilterBefore(new ApiKeyAuthenticationFilter(apiKey, apiValue, apiMatchers), UsernamePasswordAuthenticationFilter.class);
    }
}
