package com.navigator.future.service.impl;

import com.navigator.future.dao.PositionLogDao;
import com.navigator.future.pojo.entity.PositionLogEntity;
import com.navigator.future.service.IPositionLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class PositionLogServiceImpl implements IPositionLogService {

    @Autowired
    private PositionLogDao positionLogDao;

    @Override
    public List<PositionLogEntity> queryLog(Integer positionId) {
       return positionLogDao.queryLog(positionId);
    }

    @Override
    public void saveLog(PositionLogEntity positionLogEntity) {
        positionLogDao.save(positionLogEntity);
    }


}
