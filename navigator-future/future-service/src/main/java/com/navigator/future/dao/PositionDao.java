package com.navigator.future.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.annotation.Dao;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.service.BaseDaoImpl;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.future.mapper.PositionMapper;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import com.navigator.future.pojo.entity.PositionEntity;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Dao
public class PositionDao extends BaseDaoImpl<PositionMapper, PositionEntity> {

    public IPage<PositionEntity> queryPageByPositionQueryDTO(Page<PositionEntity> page, PositionQueryDTO positionDTO) {
        String createByName = null;
        if (StringUtils.isNotBlank(positionDTO.getCreateByName())) {
            createByName = positionDTO.getCreateByName().trim();
        }

        String domainCode = null;
        if (StringUtils.isNotBlank(positionDTO.getDomainCode())) {
            domainCode = positionDTO.getDomainCode().trim();
        }
        return page(page, Wrappers.<PositionEntity>lambdaQuery()
                .eq(StringUtils.isNotBlank(positionDTO.getCompanyId()), PositionEntity::getCompanyId, positionDTO.getCompanyId())
                .like(StringUtils.isNotBlank(positionDTO.getCreateByName()), PositionEntity::getCreatedByName, "%" + createByName + "%")
                .like(StringUtils.isNotBlank(positionDTO.getDomainCode()), PositionEntity::getDomainCode, "%" + domainCode + "%")
                .eq(PositionEntity::getCategory2, positionDTO.getCategory2())
                .eq(PositionEntity::getStatus, positionDTO.getStatus())
                .gt(StringUtils.isNotBlank(positionDTO.getCreatedAtStart()), PositionEntity::getCreatedAt, DateTimeUtil.parseTimeStamp0000(positionDTO.getCreatedAtStart()))
                .lt(StringUtils.isNotBlank(positionDTO.getCreatedAtEnd()), PositionEntity::getCreatedAt, DateTimeUtil.parseTimeStamp2359(positionDTO.getCreatedAtEnd()))
                .eq(PositionEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
                .orderByDesc(PositionEntity::getUpdatedAt)
        );
    }

    public List<PositionEntity> queryListByStatus(List<Integer> statusList) {
        return list(Wrappers.<PositionEntity>lambdaQuery()
                .in(PositionEntity::getStatus, statusList)
                //.eq(PositionEntity::getCategoryId, categoryId)
                .eq(PositionEntity::getIsDeleted, IsDeletedEnum.NOT_DELETED.getValue())
        );
    }

    public PositionEntity getPositionByCode(String code) {
        List<PositionEntity> positionEntities = this.baseMapper.selectList(
                Wrappers.<PositionEntity>lambdaQuery()
                        .eq(PositionEntity::getCode, code)
        );
        return positionEntities.isEmpty() ? null : positionEntities.get(0);
    }
}
