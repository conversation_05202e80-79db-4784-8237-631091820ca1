package com.navigator.future.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 16:01
 */
@Data
@Accessors(chain = true)
public class PriceApplyOperateNumDTO implements Serializable {

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价）")
    private List<Integer> type;

    @ApiModelProperty(value = "状态（1.待挂单 2.待成交 3.成交待分配  4.未成交）")
    private Integer status;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "分配状态（1 未分配 2 部分分配 3 全部分配）")
    private Integer allocateStatus;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "业务类型")
    private String futureCode;
}
