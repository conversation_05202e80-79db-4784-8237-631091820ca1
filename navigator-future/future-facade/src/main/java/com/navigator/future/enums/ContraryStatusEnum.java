package com.navigator.future.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/7
 */

@Getter
public enum ContraryStatusEnum {

    NOT_CONTRARY(0, "未已撤回"),
    CONTRARY(1, "已撤回"),
    ;

    int value;
    String description;

    ContraryStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static ContraryStatusEnum getByValue(int value) {
        for (ContraryStatusEnum en : ContraryStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ContraryStatusEnum.CONTRARY;
    }
}
