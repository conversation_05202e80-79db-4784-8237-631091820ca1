package com.navigator.future.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/1/6 13:43
 */
@Data
@Accessors(chain = true)
public class PriceApplyVO {


    @Excel(name = "序号", orderNum = "1")
    private Integer sort;

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "申请单id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @Excel(name = "二级品类", orderNum = "8", width = 25)
    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价）")
    private Integer type;

    @ApiModelProperty(value = "采销类型  1:采购  2,销售")
    private Integer salesType;

    @Excel(name = "操作类型", orderNum = "6")
    @ApiModelProperty(value = "操作类型名称（1.点价 2.转月 3.反点价）")
    private String typeName;

    @Excel(name = "申请编号", orderNum = "2", width = 25)
    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty("客户id")
    private Integer customerId;

    @Excel(name = "客户名称", orderNum = "4", width = 25)
    @ApiModelProperty("客户名称")
    private String customerName;

    @Excel(name = "期货合约", orderNum = "5", width = 15)
    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "二级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @ApiModelProperty(value = "转入合约")
    private String tranferDominantCode;

    @ApiModelProperty(value = "转入合约代码")
    private String tranferFutureCode;

    @ApiModelProperty(value = "合同ID(反点价)")
    private Integer contractId;

    @ApiModelProperty(value = "合同编号(反点价)")
    private String contractCode;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 暂定价合同 4 基差暂定价合同 5结构化定价）")
    private Integer contractType;

    @ApiModelProperty(value = "状态（1.待挂单 2.待成交 3.成交待分配  4.未成交）")
    private Integer status;

    @ApiModelProperty(value = "结构化定价结构类型")
    private String StructureType;

    @ApiModelProperty(value = "挂单类型(1随盘，2挂单)")
    private Integer pendingType;

    @Excel(name = "点价类型", orderNum = "7")
    @ApiModelProperty(value = "挂单类型名称(1随盘，2挂单)")
    private String pendingTypeName;

    @ApiModelProperty(value = "申请手数")
    private Integer applyHandNum;

    @Excel(name = "申请手数（手）", orderNum = "9", width = 15)
    @ApiModelProperty(value = "手数")
    private String applyHand;

    @Excel(name = "买卖方向", orderNum = "11")
    private String orientation;

    @ApiModelProperty(value = "申请数量（点价数量）")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "审核状态（100.无需审核 200.撤单待审核 201.撤单审核通过 202.撤单审核驳回 300.改单待审核 301.改单审核通过 302.改单审核驳回）")
    private Integer auditStatus;

    @ApiModelProperty(value = "申请价格（挂单价格）")
    @Excel(name = "挂单价格", numFormat = "0.00", orderNum = "10", width = 15)
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请挂单价差")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "未定价量")
    private BigDecimal notPriceNum;

    @Excel(name = "成交手数", orderNum = "13", width = 15)
    @ApiModelProperty(value = "成交手数")
    private String dealHandNum;

    @ApiModelProperty(value = "成交数量")
    private String dealNum;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum;

    @Excel(name = "成交价格", orderNum = "14")
    @ApiModelProperty(value = "成交价格/价差")
    private String transactionPrice;

    @ApiModelProperty(value = "成交价差")
    private BigDecimal transactionDiffPrice;

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "未分配量")
    private BigDecimal notAllocateNum;

    @Excel(name = "最新动态", orderNum = "12")
    @ApiModelProperty(value = "最新动态")
    private String latestMessage;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @ApiModelProperty(value = "冲突")
    private String conflict;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体简称")
    @Excel(name = "卖方主体", orderNum = "3", width = 25)
    private String companyShortName;

    @ApiModelProperty(value = "成交撤回次数")
    private Integer dealContraryNum;

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "传输状态")
    private String pendingResult;

    @ApiModelProperty(value = "改撤单结果")
    private String changeResult;

}
