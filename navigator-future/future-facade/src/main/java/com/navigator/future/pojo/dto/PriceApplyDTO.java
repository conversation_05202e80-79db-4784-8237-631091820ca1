package com.navigator.future.pojo.dto;

import com.navigator.future.pojo.entity.PriceApplyEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022年3月3日14:53:09
 */
@Data
@Accessors(chain = true)
public class PriceApplyDTO extends PriceApplyEntity implements Serializable {

    @ApiModelProperty("操作类型（1.改单 2.撤单）")
    private Integer modifyType;

    @ApiModelProperty("触发系统")
    private String triggerSys;

    @ApiModelProperty("系统")
    private Integer system;


}
