package com.navigator.future.enums;

import lombok.Getter;

/**
 * KingStar 指令状态枚举
 */
@Getter
public enum KingStarInstructStatusEnum {
    PENDING_ORDER(1, "待挂单", PriceStatusEnum.WAIT_PENDING),
    PENDING_TRANSACTION(2, "待成交", PriceStatusEnum.WAIT_TRANSACTION),
    NOT_TRANSACTION(3, "未成交", PriceStatusEnum.NOT_TRANSACTION),
    TRADED_PENDING_ALLOCATION(4, "成交待分配", PriceStatusEnum.WAIT_ALLOCATE),
    PARTIALLY_TRADED(5, "部分成交", PriceStatusEnum.WAIT_ALLOCATE);

    private final int code;
    private final String description;
    private final PriceStatusEnum priceStatus;

    KingStarInstructStatusEnum(int code, String description, PriceStatusEnum priceStatus) {
        this.code = code;
        this.description = description;
        this.priceStatus = priceStatus;
    }

    public static KingStarInstructStatusEnum getByCode(int code) {
        for (KingStarInstructStatusEnum status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }

    public static int getPriceStatusByCode(String code) {
        for (KingStarInstructStatusEnum status : values()) {
            if (status.code == Integer.parseInt(code)) {
                return status.priceStatus.getValue();
            }
        }
        throw new IllegalArgumentException("Invalid code: " + code);
    }
}