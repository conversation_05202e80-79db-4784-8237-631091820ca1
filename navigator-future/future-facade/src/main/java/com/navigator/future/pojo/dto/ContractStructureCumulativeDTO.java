package com.navigator.future.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ContractStructureCumulativeDTO {

    //累积释放量
    private BigDecimal notDealNum = BigDecimal.ZERO;

    //累积成交量
    private BigDecimal dealNum = BigDecimal.ZERO;

    //累积累积返还金额
    private BigDecimal cashReturn = BigDecimal.ZERO;
}
