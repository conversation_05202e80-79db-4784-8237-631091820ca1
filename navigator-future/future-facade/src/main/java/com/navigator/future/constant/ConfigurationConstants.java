package com.navigator.future.constant;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public final class ConfigurationConstants {
    private ConfigurationConstants() {
    }


    public static final BigDecimal HAND_RATE = new BigDecimal("1.09");

    public static final BigDecimal HAND_BASICS = BigDecimal.TEN;

    public static final BigDecimal BASICS = ConfigurationConstants.HAND_BASICS.multiply(ConfigurationConstants.HAND_RATE);


    public static final List<String> CAN_NOT_PRICE_OPERATION_TIME = new ArrayList<>();

    static {
        CAN_NOT_PRICE_OPERATION_TIME.add("8:50:00-9:00:00");
        CAN_NOT_PRICE_OPERATION_TIME.add("11:26:00-11:30:59");
        CAN_NOT_PRICE_OPERATION_TIME.add("13:26:00-13:30:59");
        CAN_NOT_PRICE_OPERATION_TIME.add("14:56:00-15:00:59");
        CAN_NOT_PRICE_OPERATION_TIME.add("20:50:00-22:59:59");
    }

}
