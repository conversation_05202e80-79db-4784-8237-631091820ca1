package com.navigator.future.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 点价单状态
 *
 * <AUTHOR>
 * @date 2022/1/5 11:51
 */
@Getter
public enum PriceStatusEnum {

    WAIT_PENDING(1, "待挂单"),
    WAIT_TRANSACTION(2, "待成交"),
    WAIT_ALLOCATE(3, "待分配"),
    NOT_TRANSACTION(4, "未成交"),
    STRUCTURING(5, "结构化定价中"),
    PRICING(6, "点价中"),
    CONTRARY(15, "撤回"),

    ;

    int value;
    String description;

    PriceStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PriceStatusEnum getByValue(int value) {
        for (PriceStatusEnum en : PriceStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PriceStatusEnum.WAIT_PENDING;
    }

    public static boolean BoolPriceStatus(String priceStatus) {
        List<Integer> status = Arrays.stream(priceStatus.split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
        for (Integer s : status) {
            if (PriceStatusEnum.WAIT_ALLOCATE.getValue() == s || PriceStatusEnum.NOT_TRANSACTION.getValue() == s) {
                return true;
            }
        }
        return false;
    }

}
