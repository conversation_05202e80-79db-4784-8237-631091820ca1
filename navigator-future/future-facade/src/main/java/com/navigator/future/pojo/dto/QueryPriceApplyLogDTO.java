package com.navigator.future.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 16:44
 */
@Data
@Accessors(chain = true)
public class QueryPriceApplyLogDTO {

    @ApiModelProperty(value = "操作类型（1.改单 2.撤单）")
    private Integer type;

    @ApiModelProperty(value = "点/转/反申请单ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "审核状态（1.审核中 2.通过 3.驳回）")
    private Integer auditStatus;
}
