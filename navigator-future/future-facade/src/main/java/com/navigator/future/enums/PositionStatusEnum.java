package com.navigator.future.enums;

import lombok.Getter;

@Getter
public enum PositionStatusEnum {
    /**
     *
     */
    PENDING(1, "待挂单"),
    DEALING(2, "待成交"),
    DONE(3, "已成交"),
    UNDONE(4, "未成交"),
    ;

    int value;
    String description;

    PositionStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PositionStatusEnum getByValue(int value) {
        for (PositionStatusEnum en : PositionStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PositionStatusEnum.PENDING;
    }
}
