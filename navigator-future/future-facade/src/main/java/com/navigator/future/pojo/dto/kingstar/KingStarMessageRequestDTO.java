package com.navigator.future.pojo.dto.kingstar;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 点价/移仓指令请求参数对象(消息队列)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KingStarMessageRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户编码
     */
    private String userCode;

    /**
     * requestId
     */
    private Integer requestId;

    /**
     * 指令ID（申请单号，订单唯一）
     */
    private String instructId;

    /**
     * KingStar 请求 DTO
     */
    private KingStarRequestDTO requestDTO;

    /**
     * 延迟消息的标识（除改撤单）
     */
    private String delayedMessageId;

}