package com.navigator.future.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/1/6 14:31
 */
@Getter
public enum PriceApplyTypeEnum {

    NORMAL_APPLY(1,"正常申请"),
    MODIFY_APPLY(2,"改单撤单"),
    STRUCTURE_APPLY(5,"结构化定价申请"),

    ;

    int value;
    String description;

    PriceApplyTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PriceApplyTypeEnum getByValue(int value) {
        for (PriceApplyTypeEnum en : PriceApplyTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PriceApplyTypeEnum.NORMAL_APPLY;
    }
}
