package com.navigator.future.enums;

import lombok.Getter;

/**
 * 申请单审核状态
 * <AUTHOR>
 * @date 2022/1/5 11:57
 */
@Getter
public enum ApplyAuditStatusEnum {

    AUDIT(1,"审核中"),
    PASS(2,"通过"),
    REJECT(3,"驳回"),

    NOT_AUDIT(100,"无需审核"),
    //case-1002812 并发操作的事务控制问题（改单撤单校验） Author: wan 2024-10-29 Start
    REVERSE_WAIT_AUDIT(101,"操作中"),
    //case-1002812 并发操作的事务控制问题（改单撤单校验） Author: wan 2024-10-29 end
    WITHDRAW_WAIT_AUDIT(200,"撤单待审核"),
    WITHDRAW_AUDIT_PASS(201,"撤单审核通过"),
    WITHDRAW_AUDIT_REJECT(202,"撤单审核驳回"),
    CHANGE_WAIT_AUDIT(300,"改单待审核"),
    CHANGE_AUDIT_PASS(301,"改单审核通过"),
    CHANGE_WAIT_REJECT(302,"改单审核驳回"),


    APPLY_SUCCEED(10000,"申请提交成功"),
    APPLY_HANG_SUCCEED(10001,"申请已挂单"),
    APPLY_DEAL_SUCCEED(10002,"申请单已成交")
    ;

    int value;
    String desc;

    ApplyAuditStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ApplyAuditStatusEnum getByValue(int value) {
        for (ApplyAuditStatusEnum en : ApplyAuditStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ApplyAuditStatusEnum.NOT_AUDIT;
    }

}
