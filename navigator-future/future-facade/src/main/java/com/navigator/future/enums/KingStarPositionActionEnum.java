package com.navigator.future.enums;

import lombok.Getter;

/**
 * 开平方向枚举
 */
@Getter
public enum KingStarPositionActionEnum {
    OPEN(0, "开仓"),
    CLOSE(1, "平仓"),
    CLOSE_TODAY(2, "平今仓"),
    AUTO(3, "自动开平");

    private final int value;
    private final String description;

    KingStarPositionActionEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static KingStarPositionActionEnum fromValue(int value) {
        for (KingStarPositionActionEnum action : values()) {
            if (action.value == value) {
                return action;
            }
        }
        throw new IllegalArgumentException("未知的开平方向值: " + value);
    }
}