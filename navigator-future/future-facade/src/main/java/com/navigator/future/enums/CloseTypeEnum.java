package com.navigator.future.enums;

import lombok.Getter;

@Getter
public enum CloseTypeEnum {

    /**
     *
     */
    ONE(1, "一口价合同平仓"),
    TWO(2, "采购套保"),
    THREE(3, "榨利头寸套保"),
    FOUR(4, "其他"),
    ;

    int value;
    String description;

    CloseTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static CloseTypeEnum getByValue(int value) {
        for (CloseTypeEnum en : CloseTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return CloseTypeEnum.FOUR;
    }
}
