package com.navigator.future.pojo.entity.kingstar;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.future.enums.KingStarBusinessTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * KingStar 请求记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_ks_sync_request")
@ApiModel(value = "KingStarSyncRequestEntity对象", description = "KingStar 请求记录表")
public class KingStarSyncRequestEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "指令id-申请单号,订单唯一（uuid）")
    private String instructId;

    @ApiModelProperty(value = "业务单据id")
    private Integer bizId;

    @ApiModelProperty(value = "业务单据号，本次为申请单号")
    private String bizCode;

    /**
     * 业务模块
     * {@link PriceTypeEnum}
     */
    @ApiModelProperty(value = "业务类型")
    private Integer bizType;

    /**
     * 处理类型
     * {@link KingStarBusinessTypeEnum}
     */
    @ApiModelProperty(value = "处理类型")
    private Integer operationType;

    @ApiModelProperty(value = "同步时间")
    private String syncTime;

    @ApiModelProperty(value = "同步状态")
    private String syncStatus;

    @ApiModelProperty(value = "接口同步的userCode")
    private String userCode;

    @ApiModelProperty(value = "接口同步的token")
    private String token;

    @ApiModelProperty(value = "同步次数")
    private Integer tryTimes;

    @ApiModelProperty(value = "请求的url")
    private String requestUrl;

    @ApiModelProperty(value = "传输原始信息")
    private String requestInfo;

    @ApiModelProperty(value = "返回信息")
    private String responseInfo;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "接口信息传输用户")
    private String createdBy;

    @ApiModelProperty(value = "接口信息最后修改用户")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "交易所")
    @TableField(exist = false)
    private String exchange;

    @ApiModelProperty(value = "时间条件单的同步时间")
    private String conditionSyncTime;

}
