package com.navigator.future.pojo.dto.kingstar;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 点价/移仓指令请求参数对象
 */
@Data
@Accessors(chain = true)
public class KingStarRequestDTO {
    /**
     * 指令ID（申请单号，订单唯一）
     */
    private String instructId;

    /**
     * 申请人ID（记录留痕）
     */
    private String applyUserId;

    /**
     * 申请人名称（记录留痕）
     */
    private String applyUserName;

    /**
     * 申请公司（HMS组合ID映射）
     */
    private String account;

    /**
     * 指令类型（1=点价，2=点价移仓）
     */
    private Integer type;

    /**
     * 指令下达时间（格式：yyyyMMddHHmmss）
     */
    private String timestamp;

    /**
     * 合同号（可选）
     */
    private String contractNo;

    /**
     * 备注（可选）
     */
    private String remark;

    /**
     * 接口唯一标识符（可选，每次调用传唯一值）
     */
    private String uuId;

    /**
     * 拓展参数（可选，预留扩展使用）
     */
    private String expend;

    /**
     * 公司主体
     */
    private String entity;

    /**
     * 物料类型
     */
    private String product;

    /**
     * 个券明细列表（必填）
     * - 点价：单条
     * - 移仓：需A平→B开，顺序不可颠倒
     * - 标准组合合约：首个合约价格为组合价
     * - 非标准组合合约：套利多腿分别下单
     */
    private List<InstructStk> instructStkList;

    /**
     * 个券明细子对象
     */
    @Data
    public static class InstructStk {

        /**
         * 个券合约代码（如 j2509、SPD PF510&PF511）
         */
        private String stkCode;

        /**
         * 个券价格（可选）
         * - 有值：使用该价格
         * - 无值：使用市价
         */
        private BigDecimal price;

        /**
         * 个券手数（必填）
         */
        private Integer amt;

        /**
         * 买卖方向（必填）
         * - 买入：0
         * - 卖出：1
         */
        private String bsType;

        /**
         * 开平方向（必填）
         * - 开仓：0
         * - 平仓：1
         * - 平今仓：2
         * - 自动开平：3
         */
        private String kpType;

        /**
         * 个卷价格类型(必填)
         * - 指定价：1
         * - 市价：3
         */
        private String priceType;
    }
}