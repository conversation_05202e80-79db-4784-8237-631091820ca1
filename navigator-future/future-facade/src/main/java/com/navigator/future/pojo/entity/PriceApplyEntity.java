package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.bisiness.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 点价申请表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("dbf_price_apply")
@ApiModel(value = "PriceApplyEntity对象", description = "点价申请表")
public class PriceApplyEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "客户ID")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "买卖方向")
    private String orientation;

    /**
     * {@link PriceTypeEnum}
     */
    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价 4定价 5结构化定价）")
    private Integer type;

    @ApiModelProperty(value = "采销类型  1:采购  2,销售")
    private Integer salesType;

    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "转入合约")
    private String tranferDominantCode;

    @ApiModelProperty(value = "转入合约代码")
    private String tranferFutureCode;

    @ApiModelProperty(value = "合同ID(反点价)")
    private Integer contractId;

    @ApiModelProperty(value = "状态（1.待挂单 2.待成交 3.成交待分配  4.未成交）")
    private Integer status;

    @ApiModelProperty(value = "审核状态（100.无需审核 200.撤单待审核 201.撤单审核通过 202.撤单审核驳回 300.改单待审核 301.改单审核通过 302.改单审核驳回）")
    private Integer auditStatus;

    @ApiModelProperty(value = "挂单类型(1随盘，2挂单)")
    private Integer pendingType;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @ApiModelProperty(value = "申请数量（点价数量）")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格（挂单价格）")
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请挂单价差")
    @Min(value = 1, message = "申请挂单价差必须大于0")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "成交手数")
    private Integer dealHandNum;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "成交价格")
    @Min(value = 1, message = "成交价格必须大于0")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "成交价差")
    @Min(value = 1, message = "成交价差差必须大于0")
    private BigDecimal transactionDiffPrice;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "未分配量")
    private BigDecimal notAllocateNum;

    @ApiModelProperty(value = "分配状态（1 未分配 2 部分分配 3 全部分配）")
    private Integer allocateStatus;

    @ApiModelProperty(value = "最新动态")
    private String latestMessage;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @ApiModelProperty(value = "申请人id")
    private Integer applyPersonId;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    /*============================结构化定价信息============================*/
    @ApiModelProperty(value = "结构化定价结构类型")
    private Integer structureType;

    @ApiModelProperty(value = "敲出价格")
    private BigDecimal maxPrice;

    @ApiModelProperty(value = "增强价格")
    private BigDecimal minPrice;

    @ApiModelProperty(value = "1单位数量")
    private BigDecimal unitNum;

    @ApiModelProperty(value = "结构化定价起始时间")
    private Date startTime;

    @ApiModelProperty(value = "结构化定价结束时间")
    private Date endTime;

    @ApiModelProperty(value = "总交易日")
    private Integer totalDay;

    @ApiModelProperty(value = "最新定价日")
    private String latestDealDay;

    @ApiModelProperty(value = "逻辑删除  0:未删除 1:删除")
    private Integer isDeleted;

    @ApiModelProperty(value = "父记录Id")
    private Integer parentId = 0;

    @ApiModelProperty(value = "系统")
    private Integer system = 0;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "首次待挂单刷新信息")
    private String refreshPendingInfo;

    @ApiModelProperty(value = "首次待成交刷新信息")
    private String refreshDealingInfo;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    @ApiModelProperty(value = "成交撤回次数")
    private Integer dealContraryNum;

    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;
    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 end

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "开平方向（开、平、平今）")
    private String positionAction;

    @ApiModelProperty(value = "接口状态: 挂单中 改单中 撤单中")
    private Integer interfaceStatus;

    @ApiModelProperty(value = "挂单结果")
    private String pendingResult;

    @ApiModelProperty(value = "改撤单结果")
    private String changeResult;

    @ApiModelProperty(value = "最新的hms系统指令ID")
    private String hmsInstruct;

}
