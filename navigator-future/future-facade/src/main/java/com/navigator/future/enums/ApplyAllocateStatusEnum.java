package com.navigator.future.enums;

import lombok.Getter;

/**
 * 申请单分配状态
 * <AUTHOR>
 * @date 2022/1/5 13:41
 */
@Getter
public enum ApplyAllocateStatusEnum {

    NOT_ALLOCATE(1,"未分配"),
    PART_ALLOCATE(2,"部分分配"),
    ALL_ALLOCATE(3,"全部分配"),
    ;

    int value;
    String desc;

    ApplyAllocateStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ApplyAllocateStatusEnum getByValue(int value) {
        for (ApplyAllocateStatusEnum en : ApplyAllocateStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ApplyAllocateStatusEnum.NOT_ALLOCATE;
    }

}
