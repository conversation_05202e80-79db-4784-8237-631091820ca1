package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbf_price_deal_detail")
@ApiModel(value = "PriceDealDetailEntity对象", description = "点价结果表")
public class PriceDealDetailEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "点价申请ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价 4结构化定价）")
    private Integer type;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "释放数量（不成交数量）")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "1单位数量")
    private BigDecimal unitNum;

    @ApiModelProperty(value = "收盘价")
    private BigDecimal closingPrice;

    @ApiModelProperty(value = "成交价")
    private BigDecimal dealPrice;

    @ApiModelProperty(value = "成交日")
    private String dealDate;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "成交手数")
    private Integer dealHandNum;

    @ApiModelProperty(value = "状态（3.成交待分配  4.未成交 ）")
    private Integer status;

    @ApiModelProperty(value = "成交价差")
    private BigDecimal transactionDiffPrice;

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "分配状态（1 未分配 2 部分分配 3 全部分配）")
    private Integer allocateStatus;

    @ApiModelProperty(value = "未分配量")
    private BigDecimal notAllocateNum;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "未成交手数")
    private Integer notDealHandNum;

    @ApiModelProperty(value = "申请单code")
    private String applyCode;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "转入合约")
    private String tranferDominantCode;

    @ApiModelProperty(value = "转入合约代码")
    private String tranferFutureCode;

    @ApiModelProperty(value = "结构化定价合同ID")
    private Integer structureContractId;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @ApiModelProperty(value = "当日返现总金额")
    private BigDecimal dayTotalCashReturn;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;
    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 start

    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;
}
