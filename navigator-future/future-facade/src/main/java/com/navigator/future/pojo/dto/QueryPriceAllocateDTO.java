package com.navigator.future.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2022/1/5 17:16
 */
@Data
public class QueryPriceAllocateDTO {

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价）")
    private Integer type;

    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty(value = "1:通过,2审核中,3:修改并通过,4:审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "子合同编号")
    private String subcontractCode;

    @ApiModelProperty(value = "合同id")
    private String contractId;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty
    private String deliveryFactoryCode;

    private String status;

    // 客户Id
    private String customerId;

    @ApiModelProperty("系统")
    private Integer system;

    @ApiModelProperty(value = "主体id")
    private List<Integer> companyIds;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;


}
