package com.navigator.future.enums;

import lombok.Getter;

/**
 * 回调处理状态枚举
 * 区分系统内部处理 KingStar 回调数据的结果
 */
@Getter
public enum KingStarCallbackProcessStatus {

    RECEIVED("received", "已接收，待处理"),
    PARSED_SUCCESS("parsed_success", "解析成功"),
    PARSED_FAILED("parsed_failed", "解析失败"),
    BIZ_SUCCESS("biz_success", "业务处理成功"),
    BIZ_FAILED("biz_failed", "业务处理失败"),
    DUPLICATE("duplicate", "重复回调，已忽略");

    private final String code;
    private final String description;

    KingStarCallbackProcessStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据 code 获取枚举对象
     */
    public static KingStarCallbackProcessStatus fromCode(String code) {
        for (KingStarCallbackProcessStatus status : values()) {
            if (status.code.equalsIgnoreCase(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知回调处理状态: " + code);
    }
}