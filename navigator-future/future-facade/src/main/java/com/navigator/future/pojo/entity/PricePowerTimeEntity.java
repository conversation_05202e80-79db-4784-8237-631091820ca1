package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.navigator.future.enums.PricePowerType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * <p>
 * 持仓记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbf_price_power_time")
@ApiModel(value = "PricePowerTimeEntity对象", description = "点价权限时间表")
public class PricePowerTimeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "开始时间")
    private Time startTime;

    @ApiModelProperty(value = "结束时间")
    private Time endTime;

    @ApiModelProperty(value = "麦哲伦挂单类型")
    private Integer magellanPricePowerType;

    @ApiModelProperty(value = "哥伦布挂单类型")
    private Integer columPricePowerType;

    @ApiModelProperty(value = "品种ID列表")
    private String categoryIdList;

    @ApiModelProperty(value = "品种名称列表")
    private String categoryNameList;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "更新人名称")
    private String updatedByName;

    @ApiModelProperty(value = "新建人名称")
    private String createdByName;

    @ApiModelProperty(value = "创建时间所在日期字符串")
    private String createdDate;

    @ApiModelProperty(value = "更新时间所在日期字符串")
    private String updatedDate;

    @ApiModelProperty(value = "新增更新状态")
    private Integer addUpdateState;


}
