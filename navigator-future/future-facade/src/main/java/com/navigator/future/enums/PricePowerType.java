package com.navigator.future.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22 18:18
 */
@Getter
public enum PricePowerType {

    CAN_BE_BOOKED(0,  "可挂单"),
    CAN_NOT_BOOKED(1,  "不可挂单"),
    RATE_BOOKED(2, "评级可挂单"),
    ;


    int value;
    String description;


    PricePowerType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PricePowerType getByValue(int value) {
        for (PricePowerType en : PricePowerType.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PricePowerType.CAN_NOT_BOOKED;
    }
}
