package com.navigator.future.enums;

import lombok.Getter;

/**
 * 申请单挂单类型
 * <AUTHOR>
 * @date 2022/1/5 13:37
 */
@Getter
public enum PendingTypeEnum {

    FOLLOW_LARGE_CAP(1,"随盘"),
    PENDING(2,"挂单"),
    ;

    int value;
    String description;

    PendingTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static PendingTypeEnum getByValue(int value) {
        for (PendingTypeEnum en : PendingTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PendingTypeEnum.FOLLOW_LARGE_CAP;
    }

}
