package com.navigator.future.pojo.vo;

import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.future.pojo.entity.PriceDealInfoEntity;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class PriceDealInfoVO extends PriceDealInfoEntity {

    BigDecimal notAssignedNum;
    String priceTypeName;

    public BigDecimal getNotAssignedNum() {
        return getDealNum().subtract(getAssignedNum());
    }

    public String getPriceTypeName() {
        return PriceTypeEnum.getByValue(getPriceType()).getDesc();
    }
}
