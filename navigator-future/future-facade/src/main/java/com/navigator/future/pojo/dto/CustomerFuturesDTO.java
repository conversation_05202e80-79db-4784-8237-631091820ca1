package com.navigator.future.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 13:35
 */
@Data
@Accessors(chain = true)
public class CustomerFuturesDTO {

    //客户id
    private Integer customerId;
    //客户名称
    private String customerName;
    //可点吨数
    private BigDecimal mayPriceNum;
    //可转吨数
    private BigDecimal mayTransferNum;
    //业务线
    private String buCode;
}
