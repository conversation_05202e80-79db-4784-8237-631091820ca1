package com.navigator.future.facade;

import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 18:41
 */
@FeignClient(name = "navigator-future-service")
@Component
public interface FuturesDomainFacade {

    /**
     * 查询期货信息(待点价)
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/queryContractsFutures")
    Result queryContractsFutures(@RequestBody QueryContractFuturesDTO queryContractFuturesDTO);

    /**
     * 查询可转月量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayTransferNum")
    Result mayTransferNum(@RequestBody ContractFuturesDTO contractFuturesDTO);

    /**
     * 查询可点价量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayPriceNum")
    Result mayPriceNum(@RequestBody ContractFuturesDTO contractFuturesDTO);

    /**
     * 结构化定价校验数量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/structurePriceNum")
    Result structurePriceNum(@RequestParam("contractId") Integer contractId);


    /**
     * 获取客户合约交易信息
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/getCustomerDomainTransInfo")
    Result getCustomerDomainTransInfo(@RequestBody ContractFuturesDTO contractFuturesDTO);
}
