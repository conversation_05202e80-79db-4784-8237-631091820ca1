package com.navigator.future.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/30
 */

@Data
@Accessors(chain = true)
public class QueryContractFuturesDTO {

    //客户id
    private String customerId;
    //品种id
    private Integer goodsCategoryId;
    //二级品类
    private String category2;
    //三级品类
    private String category3;
    //期货代码
    private String futureCode;
    //可点价量
    private BigDecimal priceNum;
    //期货合约
    private String domainCode;
    //合同类型（1.采购 2.销售）
    private Integer salesType;
    //操作系统
    private Integer system;
    //点价类型
    private Integer priceType;
    //买方id
    private String supplierId;
    //申请单id
    private String applyId;
    //员工id
    private String employId;
    //合同类型
    private List<Integer> contractType;
    //查询类型
    private Integer status = 0;
    //结构化定价开始日
    private Timestamp priceBeginTime;
    //主体id
    private Integer companyId;
    //主体ids
    private List<Integer> companyIds;
}
