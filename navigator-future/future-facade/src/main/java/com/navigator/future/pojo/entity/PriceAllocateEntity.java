package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 点价分配表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("dbf_price_allocate")
@ApiModel(value="PriceAllocateEntity对象", description="点价分配表")
public class PriceAllocateEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "点价申请id")
    private Integer priceApplyId;

    @ApiModelProperty(value = "成交单id")
    private Integer priceDealId;

    @ApiModelProperty(value = "申请编号")
    private String priceApplyCode;

    @ApiModelProperty(value = "分配状态（1.分配待审核 2.分配审核通过 3.分配审核驳回）")
    private Integer status;

    @ApiModelProperty(value = "分配状态（ 1:通过,2审核中,3:修改并通过,4:审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "原期期货代码")
    private String rawFutureCode;

    @ApiModelProperty(value = "原期货合约")
    private String rawDominantCode;

    @ApiModelProperty(value = "申请类型（1.点价 2.转月 3.反点价）")
    private Integer priceApplyType;

    @ApiModelProperty(value = "分配类型（1.部分 2.全部）")
    private Integer type;

    @ApiModelProperty(value = "分配合同id")
    private Integer contractId;

    @ApiModelProperty(value = "分配合同编号")
    private String contractCode;

    @ApiModelProperty(value = "分配子合同id")
    private Integer subcontractId;

    @ApiModelProperty(value = "分配子合同编号")
    private String subcontractCode;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "审核原因")
    private String auditReason;

    @ApiModelProperty(value = "客户id")
    private Integer customerId;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "采销类型")
    private Integer salesType;

    @ApiModelProperty(value = "审核人id")
    private Integer reviewerId;

    @ApiModelProperty(value = "审核人名称")
    private String reviewerName;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date auditTime;

    @ApiModelProperty(value = "结构化定价合同ID")
    private Integer structureContractId;

    @ApiModelProperty(value = "操作系统(1:麦哲伦 2:哥伦布)")
    private Integer system;

    @ApiModelProperty(value = "分配单来源")
    private Integer source;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "主体名称")
    private String companyName;

    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 start
    @ApiModelProperty(value = "一级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;
    //多品类V1 头寸添加多品类字段 Author:Wan 2024-07-01 end

    @ApiModelProperty(value = "业务线")
    private String buCode;
}
