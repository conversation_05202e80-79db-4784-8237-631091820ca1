package com.navigator.future.enums;

import lombok.Getter;

/**
 * 申请单变更类（改单/撤单）
 * <AUTHOR>
 * @date 2022/1/5 13:44
 */
@Getter
public enum PriceApplyOperationTypeEnum {

    NORMAL_APPLY(0,"正常申请"),
    CHANGE_APPLY(1,"改单"),
    WITHDRAW_APPLY(2,"撤单");

    int value;
    String desc;

    PriceApplyOperationTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static PriceApplyOperationTypeEnum getByValue(int value) {
        for (PriceApplyOperationTypeEnum en : PriceApplyOperationTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PriceApplyOperationTypeEnum.CHANGE_APPLY;
    }
}
