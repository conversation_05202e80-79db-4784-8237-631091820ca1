package com.navigator.future.pojo.dto;

import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/1 10:54
 */
@Data
@Accessors(chain = true)
public class PriceDealDetailDTO extends PriceDealDetailEntity {

    @ApiModelProperty(value = "点价申请ID")
    private Integer priceApplyId;

    //结构化定价参数
    List<PriceDealDetailEntity> priceDealDetailEntities;

}
