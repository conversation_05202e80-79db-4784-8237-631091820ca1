package com.navigator.future.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 刷新查询的信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-10
 */
@Data
@Accessors(chain = true)
public class RefreshQueryVO {

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operateTime;

    /**
     * 操作内容
     */
    private String operateContent;
}
