package com.navigator.future.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2022/1/6 16:35
 */
@Data
@Accessors(chain = true)
public class AuditPriceAllocateDTO {

    // 审核结果  2 分配审核通过  3 分配审核驳回
    private String auditResult;

    // 合同号
    private String contractId;

    // 拒绝理由
    private String refuseReason;

    // 分配单Id
    private String priceAllocateId;

    //修改并通过
    private String updatePass;



}
