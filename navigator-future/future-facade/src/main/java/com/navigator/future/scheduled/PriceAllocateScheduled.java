package com.navigator.future.scheduled;

import com.navigator.future.facade.PriceAllocateFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/30 17:02
 */
@Component
@Slf4j
public class PriceAllocateScheduled {

    @Resource
    private PriceAllocateFacade priceAllocateFacade;


    //@Scheduled(cron = "0 30,35 16 * * ?")
    //@Scheduled(cron = "*/60 * * * * ?")
    private void automaticGenDistributionOrder(){
        log.info("============================================================");
        log.info("启动定时任务");
        priceAllocateFacade.automaticGenDistributionOrder();
        log.info("定时任务结束");
        log.info("============================================================");
    }
}
