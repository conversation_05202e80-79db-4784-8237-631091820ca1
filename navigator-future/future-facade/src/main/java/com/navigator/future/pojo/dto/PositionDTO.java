package com.navigator.future.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PositionDTO {
    @ApiModelProperty(value = "id")
    private Integer id;

    @ApiModelProperty(value = "操作类型;1.点价 2.转月")
    private Integer type;

    @ApiModelProperty(value = "买卖方向 1.买 2.卖")
    private String orientation;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "转入期货代码")
    private String transferFutureCode;

    @ApiModelProperty(value = "转入合约")
    private String transferDomainCode;

    @ApiModelProperty(value = "状态;1.待挂单 2.待成交 3.已成交  4.未成交")
    private Integer status;

    @ApiModelProperty(value = "挂单类型;1随盘 2挂单")
    private Integer pendingType;

    @ApiModelProperty(value = "平仓类型;1一口价合同平仓 2采购套保 3榨利头寸套保 4其他")
    private Integer closeType;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格")
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请挂单价差")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "成交手数")
    private BigDecimal dealHandNum;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "成交价格")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "成交价差")
    private BigDecimal transactionDiffPrice;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @Autowired
    private List<Integer> idList;

    @ApiModelProperty("操作类型（1.改单 2.撤单）")
    private Integer modifyType;

    @ApiModelProperty(value = "成交合约")
    private String transactionDomainCode;

    @ApiModelProperty(value = "成交合约编号")
    private String transactionFutureCode;

    @ApiModelProperty(value = "成交转入合约")
    private String transactionTransferDomainCode;

    @ApiModelProperty(value = "成交转入合约编号")
    private String transactionTransferFutureCode;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;
}
