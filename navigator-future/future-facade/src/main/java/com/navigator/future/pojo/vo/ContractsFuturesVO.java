package com.navigator.future.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 11:34
 */
@Data
@Accessors(chain = true)
public class ContractsFuturesVO {

    //合约
    private String domainCode;
    //客户id
    private Integer customerId;
    //客户名称
    private String customerName;
    //卖方id
    private Integer supplierId;
    //卖方名称
    private String supplierName;
    //品种id
    private Integer category3;
    //主体id
    private Integer companyId;
    //主体名称
    private String companyShortName;
    //品种名称
    private String category3Name;
    //可点吨数
    private BigDecimal mayPriceNum;
    //可转吨数
    private BigDecimal mayTransferNum;
    //业务类型
    private String buCode;
    //二级品类
    private Integer category2;
    //期货代码
    private String futureCode;

}
