package com.navigator.future.pojo.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2022/1/6 11:26
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class DistributionDTO {

    // 申请单Id
    @NotBlank(message = "申请单不能为空")
    private String applyId;

    // 操作类型
    @NotBlank(message = "操作类型不能为空")
    private String operationType;

    // 期货合约
    @NotBlank(message = "期货合约不能为空")
    private String domainCode;

    //客户Id
    @NotBlank(message = "客户Id不能为空")
    private String customerId;

    //操作系统
    private Integer system;

    //操作系统
    private Integer type;

    //成交单id
    private Integer dealId;

    //分配状态（ 1:通过,2审核中,3:修改并通过,4:审核驳回
    private Integer auditStatus;

    //申请单id
    private Integer priceAllId;

    //品种id
    private Integer categoryId;

    //修改并通过
    private String updatePass;

    //自动分配
    private String voluntarily;

    //分配子合同id
    private Integer subcontractId;

    //分配子合同编号
    private String subcontractCode;

    @Valid
    private List<Distribution> distributions;

    @Data
    public static class Distribution {

        // 合同Id
        @NotBlank(message = "合同Id不能为空")
        private String contractId;

        // 分配量
        @NotNull(message = "分配数量不能为空")
        private BigDecimal allocateNum;
    }
}
