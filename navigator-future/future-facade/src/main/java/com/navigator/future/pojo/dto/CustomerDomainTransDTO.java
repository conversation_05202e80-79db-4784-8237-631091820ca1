package com.navigator.future.pojo.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class CustomerDomainTransDTO {
    Integer customerId;
    String domainCode = "";
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 Start
    //合同可点价量
    BigDecimal pricedCount = BigDecimal.ZERO;
    //合同可转月量
    BigDecimal transferredCount = BigDecimal.ZERO;
    //申请单总量
    BigDecimal applyCount = BigDecimal.ZERO;
    //成交单总量
    BigDecimal dealCount = BigDecimal.ZERO;
    //分配单总量
    BigDecimal allocateCount = BigDecimal.ZERO;
    //case-1002885 头寸待挂单界面查询慢,代码优化 Author: wan 2025-01-02 end
    //可定价量
    BigDecimal canPriceCount = BigDecimal.ZERO;
    //可转月量
    BigDecimal canTransferCount = BigDecimal.ZERO;
}
