package com.navigator.future.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/19
 */
@Getter
@AllArgsConstructor
public enum ContraryCauseEnum {
    MISTAKE_REPLENISHMENT(1, "补单错误（重复挂单/价格错误/数量错误）"),
    MISTAKE_OPERATE(2, "错误操作 （成交状态/成交数量/成交价格错误）"),
    MISTAKE_AUDIT(3, "错误审核（误操作审核通过/未跟客户确认分配）"),
    CUSTOMER_CASE(4, "客户原因"),
    ;

    Integer value;
    String description;

    public static ContraryCauseEnum getByValue(int value) {
        for (ContraryCauseEnum en : ContraryCauseEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ContraryCauseEnum.CUSTOMER_CASE;
    }
}
