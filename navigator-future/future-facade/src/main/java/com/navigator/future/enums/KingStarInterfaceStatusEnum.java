package com.navigator.future.enums;

import lombok.Getter;

@Getter
public enum KingStarInterfaceStatusEnum {
    PENDING(1, "挂单中"),
    MODIFYING(2, "改单中"),
    CANCELING(3, "撤单中"),
    COMPLETED(4, "已完成"),
    NOT_CALL_INTERFACE(5, "不调用接口-失败后不调用");

    private final int value;
    private final String description;

    KingStarInterfaceStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static KingStarInterfaceStatusEnum getByValue(int value) {
        for (KingStarInterfaceStatusEnum status : values()) {
            if (status.value == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
}