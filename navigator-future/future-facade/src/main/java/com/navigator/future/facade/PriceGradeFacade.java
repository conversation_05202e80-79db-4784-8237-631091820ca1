package com.navigator.future.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "navigator-future-service")
public interface PriceGradeFacade {

    @PostMapping("/future/updatePriceGrade")
    Result<Boolean> updatePriceGrade(@RequestBody PriceGradeEntity priceGradeEntity);

    @PostMapping("/future/queryPriceGradeList")
    Result queryPriceGradeList(QueryDTO<PriceGradeEntity> queryDTO);

}
