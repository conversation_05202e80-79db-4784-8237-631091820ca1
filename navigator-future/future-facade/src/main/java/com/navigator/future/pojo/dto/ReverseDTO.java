package com.navigator.future.pojo.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: No Description
 * Created by YuYong on 2022/3/1 17:51
 */
@Data
public class ReverseDTO {

    // 转入月份（转入期货合约）
    private String reverseMonth;

    // 反点价数量
    private BigDecimal reverseNum;

    // 反点价价格
    private BigDecimal reversePrice;

    // 反点价手续费
    private BigDecimal reverseServiceCharge;

    //合同id
    private String contractId;

    // 转入合约代码
    private String tranferFutureCode;

    // 申请手数
    private BigDecimal applyHandNum;

}
