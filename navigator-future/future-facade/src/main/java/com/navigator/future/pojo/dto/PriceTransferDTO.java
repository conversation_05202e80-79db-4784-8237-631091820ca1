package com.navigator.future.pojo.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * Description: No Description
 * Created by YuYong on 2022/2/28 11:16
 */
@Data
public class PriceTransferDTO {

    // 转入月份（转入期货合约）
    private String transferMonth;

    //转入合约代码
    private String transferFutureCode;

    // 点价或转月数量
    private BigDecimal priceTransferNum;

    // 转月、定价价格
    private BigDecimal transferPrice;

    // 转月手续费
    private BigDecimal transferServiceCharge;

    // 类型（1、点价 2、转月）
    private Integer type;

    private String contractId;

}
