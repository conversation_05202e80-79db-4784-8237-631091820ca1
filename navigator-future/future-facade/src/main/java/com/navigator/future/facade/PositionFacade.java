package com.navigator.future.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.PositionDTO;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@FeignClient(name = "navigator-future-service")
public interface PositionFacade {

    @PostMapping("/position/save")
    Result save(@RequestBody PositionDTO positionDTO);

    @PostMapping("/position/queryPositionList")
    Result queryPositionList(@RequestBody QueryDTO<PositionQueryDTO> positionQueryDTO);

    @GetMapping("/position//queryPositionDetail")
    Result queryPositionDetail(@RequestParam("id") Integer id);

    @PostMapping("/position/updatePositionStatus")
    Result updatePositionStatus(@RequestBody PositionDTO positionDTO);

    @PostMapping("/position/dealBatch")
    Result dealBatch(@RequestBody PositionDTO positionDTO);

    @GetMapping("/position/export")
    Result export(@RequestParam("categoryId") Integer categoryId);

    @PostMapping(value = "/position/check", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result check(@RequestPart("file") MultipartFile file);

    @PostMapping(value = "/position/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importFile(@RequestPart("file") MultipartFile file);

    @PostMapping("/exportSubmitList")
    Result exportSubmitList(@RequestBody PositionQueryDTO positionQueryDTO);

    @GetMapping("/queryPositionRefreshLog")
    Result queryPositionRefreshLog(@RequestParam("positionId") Integer positionId);

    @PostMapping("/modify")
    Result modify(@RequestBody PositionDTO positionDTO);

    @PostMapping("/cancel")
    Result cancel(@RequestBody PositionDTO positionDTO);

    @GetMapping("/queryModifyLog")
    Result queryModifyLog(@RequestParam("positionId") Integer positionId);
}
