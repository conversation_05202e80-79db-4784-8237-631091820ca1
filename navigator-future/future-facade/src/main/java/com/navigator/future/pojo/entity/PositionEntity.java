package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 持仓记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbf_position")
@ApiModel(value = "PositionEntity对象", description = "持仓记录表")
public class PositionEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "操作类型;1.点价 2.转月")
    private Integer type;

    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "转入期货代码")
    private String transferFutureCode;

    @ApiModelProperty(value = "转入合约")
    private String transferDomainCode;

    @ApiModelProperty(value = "状态;1.待挂单 2.待成交 3.已成交  4.未成交")
    private Integer status;

    @ApiModelProperty(value = "挂单类型;1随盘 2挂单")
    private Integer pendingType;

    @ApiModelProperty(value = "平仓类型;1一口价合同平仓 2采购套保 3榨利头寸套保 4其他")
    private Integer closeType;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格")
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请挂单价差")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "成交价格")
    private BigDecimal transactionPrice;

    @ApiModelProperty(value = "成交价差")
    private BigDecimal transactionDiffPrice;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "买卖方向")
    private String orientation;

    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "逻辑删除  0:启用 1:禁用")
    private Integer isDeleted;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "首次待挂单刷新信息")
    private String refreshPendingInfo;

    @ApiModelProperty(value = "首次待成交刷新信息")
    private String refreshDealingInfo;

    @ApiModelProperty(value = "最新动态")
    private String latestMessage;

    @ApiModelProperty(value = "成交合约编号")
    private String transactionFutureCode;

    @ApiModelProperty(value = "成交合约")
    private String transactionDomainCode;

    @ApiModelProperty(value = "成交转入合约编号")
    private String transactionTransferFutureCode;

    @ApiModelProperty(value = "成交转入合约")
    private String transactionTransferDomainCode;

    @ApiModelProperty(value = "更改成交合约")
    private String changeDomainCode;

    @ApiModelProperty(value = "更改成交合约代码")
    private String changeFutureCode;

    @ApiModelProperty(value = "更改转入合约")
    private String changeTransferDomainCode;

    @ApiModelProperty(value = "更改成交合约代码")
    private String changeTransferFutureCode;

    @ApiModelProperty("操作类型（1.改单 2.撤单）")
    private Integer modifyType;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "主体id")
    private Integer companyId;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;
}
