package com.navigator.future.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@Data
@Accessors(chain = true)
public class TradingConfigVO {

    //期货代码
    private String futureCode;
    //期货合约集合
    private List<String> domainCodes;
    //交易所
    private String exchange;
    //品种名称
    private String categoryName;

}
