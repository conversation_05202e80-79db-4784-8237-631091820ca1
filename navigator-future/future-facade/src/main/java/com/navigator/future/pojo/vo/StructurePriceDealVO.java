package com.navigator.future.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class StructurePriceDealVO {

    @Excel(name = "结构化合同编号", orderNum = "1")
    private String contractCode;

    @Excel(name = "卖方主体", orderNum = "2")
    private String companyShortName;

    @Excel(name = "客户名称", orderNum = "3")
    private String customerName;

    @ApiModelProperty(value = "结构类型")
    private Integer structureType;

    @Excel(name = "结构类型", orderNum = "4")
    private String structureTypeStr;

    @Excel(name = "二级品类", orderNum = "5")
    private String categoryName;

    @Excel(name = "标的期货", orderNum = "6")
    private String domainCode;

    @Excel(name = "定价开始日期", orderNum = "7")
    private String priceStartDate;

    @Excel(name = "定价到期日", orderNum = "8")
    private String priceEndDate;

    @Excel(name = "定价价格（元/吨）", orderNum = "9")
    private String dealPrice;

    @Excel(name = "定价量（吨）", orderNum = "10")
    private String dealNum;

    @Excel(name = "交易日", orderNum = "11")
    private String dealDate;

    @Excel(name = "释放量（吨）", orderNum = "12")
    private String notDealNum;

    @Excel(name = "当日现金返还总金额（元）", orderNum = "13")
    private String dayTotalCashReturn;

    @ApiModelProperty(value = "冲突")
    private String conflict;
}
