package com.navigator.future.pojo.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 18:24
 */
@Data
@Accessors(chain = true)
public class ApplyAllocateStatusVO {

    //待点价
    private Integer waitPricing;
    //待挂单
    private Integer waitPending;
    //待成交
    private Integer waitTransaction;
    //待分配
    private Integer waitAllocate;
    //未成交
    private Integer notTransaction;
    //分配待审核
    private Integer waitAudit;
    //分配审核通过
    private Integer auditPass;
    //分配审核驳回
    private Integer auditReject;
}
