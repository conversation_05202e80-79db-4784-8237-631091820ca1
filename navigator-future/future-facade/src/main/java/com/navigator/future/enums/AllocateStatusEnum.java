package com.navigator.future.enums;

import lombok.Getter;

import java.util.List;

/**
 * 分配单状态
 *
 * <AUTHOR>
 * @date 2022/1/5 13:50
 */
@Getter
public enum AllocateStatusEnum {

    WAIT_AUDIT(1,"分配待审核"),
    AUDIT_PASS(2,"分配审核通过"),
    AUDIT_REJECT(3,"分配审核驳回"),
    CONTRARY(15,"撤回"),
    ;

    int value;
    String desc;

    AllocateStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AllocateStatusEnum getByValue(int value) {
        for (AllocateStatusEnum en : AllocateStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return AllocateStatusEnum.WAIT_AUDIT;
    }


    public static boolean getBooleanByValue(List<Integer> status) {

        if(status.isEmpty()){
            return false;
        }

        for(Integer s:status){
            if (s == AllocateStatusEnum.AUDIT_PASS.getValue()) {
                return true;
            }
        }

        return false;
    }
}
