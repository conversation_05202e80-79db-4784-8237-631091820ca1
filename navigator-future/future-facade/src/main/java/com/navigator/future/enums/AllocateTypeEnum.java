package com.navigator.future.enums;

import lombok.Getter;

/**
 * 分配单分配合同类型（分配部分/全部）
 * <AUTHOR>
 * @date 2022/1/5 13:53
 */
@Getter
public enum AllocateTypeEnum {

    PART(1,"部分"),
    ALL(2,"全部"),
    ;

    int value;
    String desc;

    AllocateTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static AllocateTypeEnum getByValue(int value) {
        for (AllocateTypeEnum en : AllocateTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return AllocateTypeEnum.PART;
    }

}
