package com.navigator.future.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/22 18:18
 */
@Getter
public enum  AllocateSourceEnum {

    UPDATE_PASS(0,  "修改并通过"),
    CUSTOMER(1,  "客户"),
    BUSINESS(2, "商务"),
    VOLUNTARILY(3, "自动分配"),
    ;


    int value;
    String description;


    AllocateSourceEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }
}
