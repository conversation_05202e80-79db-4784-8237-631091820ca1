package com.navigator.future.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.*;
import com.navigator.future.pojo.entity.PriceAllocateEntity;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2022/1/6 14:44
 */
@FeignClient(name = "navigator-future-service")
public interface PriceAllocateFacade {

    /**
     * 点、转 分配生成分配单
     *
     * @param distributionDTO
     * @return
     */
    @PostMapping("/genDistributionOrder")
    Result genDistributionOrder(@RequestBody DistributionDTO distributionDTO);

    /**
     * 根据条件查询分配单（分页）
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryPriceAllocates")
    Result queryPriceAllocates(@RequestBody QueryDTO<QueryPriceAllocateDTO> queryDTO);

    /**
     * 分配明细查询
     */
    @GetMapping("/getPriceAllocateDetail")
    Result getPriceAllocateDetail(@RequestParam("applyCode") String applyCode);

    /**
     * 判断该合约下是否有成交待分配的申请单和分配待审核的分配单
     *
     * @param dominantCode
     * @param categoryId
     * @return
     */
    @GetMapping("/judgeExistsAllocatesAndOrder")
    boolean judgeExistsAllocatesAndOrder(@RequestParam("customerId") String customerId, @RequestParam("dominantCode") String dominantCode, @RequestParam("categoryId") Integer categoryId);

    /**
     * 审核点价和转月的分配单
     *
     * @param auditPriceAllocateDTO
     * @return
     */
    @PostMapping("/auditPriceAllocate")
    Result auditPriceAllocate(@RequestBody List<AuditPriceAllocateDTO> auditPriceAllocateDTO);

    /**
     * 查询某合同下分配待审核数量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getSumPriceAllocateOfContract")
    BigDecimal getSumPriceAllocateOfContract(@RequestParam("contractId") String contractId);

    /**
     * 成交申请单自动分配
     *
     * @return
     */
    @GetMapping("/automaticGenDistributionOrder")
    Result automaticGenDistributionOrder();

    /**
     * 根据分配单id 查询分配单
     *
     * @param allocateId
     * @return
     */
    @GetMapping("/getPriceAllocateById")
    PriceAllocateEntity getPriceAllocateById(@RequestParam("allocateId") String allocateId);


    /**
     * 采购合同点价、转月
     *
     * @param priceTransferDTOs
     * @return
     */
    @PostMapping("/purchaseContractPriceTransfer")
    Result purchaseContractPriceTransfer(@RequestBody List<PriceTransferDTO> priceTransferDTOs);


    /**
     * 结构化定价查询分配记录
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryPriceAllocateRecord")
    Result queryPriceAllocateRecord(@RequestBody QueryDTO<QueryPriceAllocateDTO> queryDTO);


    /**
     * 采购合同反点价
     *
     * @param reverseDTO
     * @return
     */
    @PostMapping("/purchaseContractReverse")
    Result purchaseContractReverse(@RequestBody ReverseDTO reverseDTO);

    /**
     * 修改并通过
     *
     * @param distributionDTO
     * @return
     */
    @PostMapping("/modifyAllocates")
    Result modifyAllocates(@RequestBody DistributionDTO distributionDTO);


    /**
     * 已操作量
     *
     * @param priceApplyOperateNumDTO
     * @return
     */
    @PostMapping("/hasOperation")
    BigDecimal hasOperation(@RequestBody PriceApplyOperateNumDTO priceApplyOperateNumDTO);


    /**
     * 结构化定价成交
     *
     * @param priceDealDetailDTO
     * @return
     */
    @PostMapping("/structuringPriceDeal")
    Result structuringPriceDeal(@RequestBody PriceDealDetailDTO priceDealDetailDTO);


    @GetMapping("/priceApplyMigration")
    void priceApplyMigration();


    @PostMapping("/getPriceDealOrdersByDominantCode")
    boolean getPriceDealOrdersByDominantCode(@RequestParam("customerId") String customerId, @RequestParam("domainCode") String domainCode, @RequestParam("categoryId") Integer categoryId);


    /**
     * 根据结构化定价Id查询分配单
     *
     * @param contractStructureId
     * @return
     */
    @GetMapping("/getAllocateByContractStructureId")
    List<PriceAllocateEntity> getAllocateByContractStructureId(@RequestParam("contractStructureId") Integer contractStructureId);


    /**
     * 根据合同id 查询分配单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getByContractId")
    List<PriceAllocateEntity> getByContractId(@RequestParam(value = "contractId", required = false) Integer contractId);


    /**
     * 根据合同id判断是否有未分配单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getNotAllocateByContractId")
    boolean getNotAllocateByContractId(@RequestParam(value = "contractId") Integer contractId);


    @PostMapping("/priceAllocateContrary")
    Result priceAllocateContrary(@RequestBody ApplyContraryDTO applyContraryDTO);
}
