package com.navigator.future.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.navigator.common.util.BigDecimalSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2022/1/6 15:10
 */
@Data
@Accessors(chain = true)
public class PriceAllocateVO {

    // 分配单Id
    private String allocateId;

    // 成交单Id
    private String dealId;

    // 点价申请id
    private String priceApplyId;

    @ApiModelProperty(value = "申请编号")
    private String priceApplyCode;

    // 盘面成交价
    private BigDecimal platePrice;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "申请类型（1.点价 2.转月 3.反点价）")
    private Integer priceApplyType;

    @ApiModelProperty(value = "分配状态（1.分配待审核 2.分配审核通过 3.分配审核驳回）")
    private Integer status;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "分配时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date allocatesCreatedAt;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "客户Id")
    private String customerId;

    @ApiModelProperty(value = "分配合同id")
    private Integer contractId;

    @ApiModelProperty(value = "分配合同编号")
    private String contractCode;

    @ApiModelProperty(value = "分配子合同id")
    private Integer subcontractId;

    @ApiModelProperty(value = "分配子合同编号")
    private String subcontractCode;

    @ApiModelProperty(value = "分配子合同类型")
    private Integer subcontractType;

    @ApiModelProperty(value = "工厂主体id")
    private Integer belongCustomerId;

    @ApiModelProperty(value = "交货工厂编码")
    private String deliveryFactoryCode;

    @ApiModelProperty(value = "合同类型（1 一口价合同 2 基差合同 3 点价合同）")
    private Integer contractType;

    @ApiModelProperty(value = "含税单价")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "基差价（元）")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal extraPrice;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "分配状态（ 1:通过,2审核中,3:修改并通过,4:审核驳回")
    private Integer auditStatus;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    @ApiModelProperty(value = "账套编码")
    private String siteCode;

    @ApiModelProperty(value = "账套名称")
    private String siteName;

    @ApiModelProperty(value = "结构化定价合同ID")
    private Integer structureContractId;

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum;

    @ApiModelProperty(value = "分配量")
    private BigDecimal allocateNum;

    @ApiModelProperty(value = "交货工厂名称")
    private String deliveryFactoryName;

    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "创建人")
    private Integer createdBy;

    @ApiModelProperty(value = "申请人")
    private String priceAppBy;

    //操作系统(1:麦哲伦 2:哥伦布)
    private Integer system;

    // 合同所属商务姓名
    private String businessPersonName;

    @ApiModelProperty(value = "审核人名称")
    private String reviewerName;

    @ApiModelProperty(value = "操作人名称")
    private String operationName;

    @ApiModelProperty(value = "审核原因")
    private String auditReason;

    @ApiModelProperty(value = "转入合约")
    private String tranferDominantCode;

    @ApiModelProperty(value = "转入期货代码")
    private String tranferFutureCode;

    @ApiModelProperty(value = "主体id")
    private String companyId;

    @ApiModelProperty(value = "主体简称")
    private String companyShortName;

    @ApiModelProperty(value = "合同协议状态")
    private Integer contractSignStatus;

    private Integer allowCancel;

    //撤回次数
    private Integer dealContraryNum;

    @ApiModelProperty(value = "二级品类")
    private String category1;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "三级品类")
    private String category3;

    @ApiModelProperty(value = "业务线")
    private String buCode;

}
