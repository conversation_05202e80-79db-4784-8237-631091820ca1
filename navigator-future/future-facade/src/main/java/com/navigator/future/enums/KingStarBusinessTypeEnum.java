package com.navigator.future.enums;


import lombok.Getter;

@Getter
public enum KingStarBusinessTypeEnum {
    PRICING(1, "点价"),
    PRICING_TRANSFER(2, "点价移仓"),
    CANCEL(3, "撤单"),
    MODIFY(4, "改单"),
    REVERSE_PRICING(5, "反点价"),
    REVERSE_PRICING_TRANSFER(6, "反点价移仓"),
    PACKAGE_CLOSING(7, "套包平仓"),
    PACKAGE_CLOSING_TRANSFER(8, "套包平仓移仓"),
    ARBITRAGE_CLOSING(9, "套利平仓"),
    ARBITRAGE_CLOSING_TRANSFER(10, "套利平仓移仓");

    private final int value;
    private final String description;

    KingStarBusinessTypeEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static KingStarBusinessTypeEnum getByValue(int value) {
        for (KingStarBusinessTypeEnum type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        return PRICING;
    }

    public static KingStarBusinessTypeEnum fromValue(int value) {
        for (KingStarBusinessTypeEnum type : values()) {
            if (type.value == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的业务类型值: " + value);
    }
}