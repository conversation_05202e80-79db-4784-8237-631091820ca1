package com.navigator.future.pojo.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 15:25
 */
@Data
@Accessors(chain = true)
public class PriceApplyLogVo {
    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "点/转/反申请单ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "申请编号")
    private String priceApplyCode;

    @ApiModelProperty(value = "操作类型（1.改单 2.撤单）")
    private Integer type;

    @ApiModelProperty("点价类型")
    private Integer priceType;

    @ApiModelProperty(value = "操作类型名称")
    private String modifyTypeName;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @ApiModelProperty(value = "申请数量（点价数量）")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格（挂单价格）")
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请价差")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "成交价格")
    private BigDecimal dealPrice;

    @ApiModelProperty(value = "成交价差")
    private BigDecimal dealDiffPrice;

    @ApiModelProperty(value = "成交数据量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "审核状态（1.审核中 2.通过 3.驳回）")
    private Integer auditStatus;

    @ApiModelProperty(value = "备注（驳回原因）")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    @ApiModelProperty(value = "改撤单结果")
    private String changeResult;

}
