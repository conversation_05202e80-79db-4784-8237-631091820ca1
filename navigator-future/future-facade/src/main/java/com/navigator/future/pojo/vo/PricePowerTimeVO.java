package com.navigator.future.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.sql.Time;
import java.util.Date;

/**
 * <p>
 * 挂单区间时间表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-26
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbf_price_power_time")
@ApiModel(value = "PricePowerTimeVO对象", description = "挂单区间时间表")
public class PricePowerTimeVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "开始时间", orderNum = "1")
    private Time startTime;

    @Excel(name = "结束时间", orderNum = "2")
    private Time endTime;

    @Excel(name = "麦哲伦挂单类型", orderNum = "3")
    private String magellanPricePowerTypeStr;

    @Excel(name = "哥伦布挂单类型", orderNum = "4")
    private String columPricePowerTypeStr;

    @Excel(name = "二级品类", orderNum = "5")
    private String categoryNameList;


    @Excel(name = "新建人名称", orderNum = "6")
    private String createdByName;

    @Excel(name = "创建时间", orderNum = "7")
    private String createdAt;

    @Excel(name = "更新人名称", orderNum = "8")
    private String updatedByName;

    @Excel(name = "更新时间", orderNum = "9")
    private String updatedAt;
}
