package com.navigator.future.pojo.entity.kingstar;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * KingStar 接口回调表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-04
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbi_ks_sync_callback")
@ApiModel(value = "KingStarSyncCallbackEntity对象", description = "KingStar 接口回调表")
public class KingStarSyncCallbackEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id主键 自增长")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "指令id-申请单号,订单唯一")
    private String instructId;

    @ApiModelProperty(value = "指令状态")
    private Integer instructStatus;

    @ApiModelProperty(value = "KingStar系统回传信息")
    private String ackData;

    @ApiModelProperty(value = "回执信息（成功/失败+描述）")
    private String returnMsg;

    @ApiModelProperty(value = "回调处理状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

}
