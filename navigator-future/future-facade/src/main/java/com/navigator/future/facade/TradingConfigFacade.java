package com.navigator.future.facade;

import com.navigator.common.dto.Result;
import com.navigator.future.pojo.entity.TradingConfigEntity;
import com.navigator.future.pojo.vo.TradingConfigVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@FeignClient(name = "navigator-future-service")
public interface TradingConfigFacade {

    /**
     * 查询所有
     *
     * @return
     */
    @GetMapping("/queryTradingConfigList")
    Result queryTradingConfigList();

    /**
     * 根据期货代码查询
     *
     * @param futureCode
     * @return
     */
    @GetMapping("/getDomainTypeByCategoryCode")
    TradingConfigVO getDomainTypeByCategoryCode(@RequestParam(value = "futureCode") String futureCode);


    @GetMapping("/getDomainTypeEntityByCategoryCode")
    List<TradingConfigEntity> getDomainTypeEntityByCategoryCode(@RequestParam(value = "futureCode") String getFutureCode);

    /**
     * 获取二级品类下的合约
     *
     * @param category2
     * @return
     */
    @GetMapping("/getDomainTypeByCategory2")
    Result getDomainTypeByCategory2(@RequestParam(value = "category2") String category2);


}
