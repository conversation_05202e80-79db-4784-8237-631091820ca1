package com.navigator.future.enums;

import com.navigator.bisiness.enums.TTTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/31
 */

@Getter
@AllArgsConstructor
public enum SendNotDealInmailTimeEnum {

    TIME1("08:30", " 17:00:00", " 08:29:59"),
    TIME2("09:00", " 08:30:30", " 08:59:59"),
    TIME3("15:30", " 09:00:00", " 15:59:59"),
    TIME4("17:00", " 15:30:00", " 16:59:59"),
    ;

    String sendTime;
    String startTime;
    String endTime;

    public static SendNotDealInmailTimeEnum getSendTime(String sendTime) {
        for (SendNotDealInmailTimeEnum sendNotDealInmailTimeEnum : SendNotDealInmailTimeEnum.values()) {
            if (sendNotDealInmailTimeEnum.getSendTime().equals(sendTime)) {
                return sendNotDealInmailTimeEnum;
            }
        }
        return null;
    }

    public static boolean isSendTime(String sendTime) {
        for (SendNotDealInmailTimeEnum sendNotDealInmailTimeEnum : SendNotDealInmailTimeEnum.values()) {
            if (sendNotDealInmailTimeEnum.getSendTime().equals(sendTime)) {
                return true;
            }
        }
        return false;
    }


}
