package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 改撤单记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-05
 */
@Data
@Accessors(chain = true)
@TableName("dbf_price_apply_log")
@ApiModel(value = "PriceApplyLogEntity对象", description = "改撤单记录表")
public class PriceApplyLogEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "点/转/反申请单ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "申请编号")
    private String priceApplyCode;

    @ApiModelProperty(value = "操作类型（1.改单 2.撤单）")
    private Integer type;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @ApiModelProperty(value = "申请数量（点价数量）")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格（挂单价格）")
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请价差")
    private BigDecimal applyDiffPrice;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "审核状态（1.审核中 2.通过 3.驳回）")
    private Integer auditStatus;

    @ApiModelProperty(value = "备注（驳回原因）")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建人")
    private String createdByName;

    @ApiModelProperty(value = "更新人")
    private String updatedByName;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @TableField(exist = false)
    @ApiModelProperty(value = "挂单类型(1随盘，2挂单)")
    private Integer pendingType;
}
