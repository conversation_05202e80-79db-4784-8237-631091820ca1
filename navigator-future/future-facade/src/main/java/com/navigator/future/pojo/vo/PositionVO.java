package com.navigator.future.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class PositionVO {

    @Excel(name = "序号", orderNum = "1")
    private Integer sort;

    @ApiModelProperty(value = "自增ID")
    private Integer id;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @Excel(name = "二级品类", orderNum = "9")
    @ApiModelProperty(value = "品类名称")
    private String categoryName;

    @ApiModelProperty(value = "一级品类")
    private Integer category1;

    @ApiModelProperty(value = "二级品类")
    private Integer category2;

    @ApiModelProperty(value = "三级品类")
    private Integer category3;

    @ApiModelProperty(value = "货品名称")
    private String commodityName;

    @Excel(name = "操作类型", orderNum = "6")
    @ApiModelProperty(value = "操作类型名称（1.点价 2.转月）")
    private String typeName;

    @ApiModelProperty(value = "操作类型;1.点价 2.转月")
    private Integer type;

    @Excel(name = "申请编号", orderNum = "2", width = 25)
    @ApiModelProperty(value = "申请编号")
    private String code;

    @Excel(name = "LDC主体", orderNum = "3", width = 15)
    @ApiModelProperty(value = "LDC主体")
    private String companyName;

    @Excel(name = "期货合约", orderNum = "4", width = 15)
    @ApiModelProperty(value = "期货合约")
    private String domainCode;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    @ApiModelProperty(value = "转入合约")
    @Excel(name = "转入合约", orderNum = "5", width = 15)
    private String transferDomainCode;

    @ApiModelProperty(value = "转入期货代码")
    private String transferFutureCode;

    @ApiModelProperty(value = "状态;1.待挂单 2.待成交 3.已成交  4.未成交")
    private Integer status;

    @ApiModelProperty(value = "挂单类型;1随盘 2挂单")
    private Integer pendingType;

    @Excel(name = "点价类型", orderNum = "8")
    @ApiModelProperty(value = "挂单类型名称(1随盘，2挂单)")
    private String pendingTypeName;

    @Excel(name = "平仓类型名称", orderNum = "7", width = 25)
    @ApiModelProperty(value = "平仓类型名称;1一口价合同平仓 2采购套保 3榨利头寸套保 4其他")
    private String closeTypeName;

    @ApiModelProperty(value = "平仓类型;1一口价合同平仓 2采购套保 3榨利头寸套保 4其他")
    private Integer closeType;

    @ApiModelProperty(value = "申请手数")
    private BigDecimal applyHandNum;

    @Excel(name = "申请手数（手）", orderNum = "10", width = 15)
    @ApiModelProperty(value = "手数")
    private String applyHand;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "申请价格")
    @Excel(name = "挂单价格", orderNum = "11", width = 15)
    private BigDecimal applyPrice;

    @ApiModelProperty(value = "申请挂单价差")
    private BigDecimal applyDiffPrice;

    @Excel(name = "成交手数", orderNum = "13", width = 15)
    @ApiModelProperty(value = "成交手数")
    private String dealHandNum;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @Excel(name = "成交价格/价差", orderNum = "14")
    @ApiModelProperty(value = "成交价格")
    private String transactionPrice;

    @ApiModelProperty(value = "成交价差")
    private String transactionDiffPrice;

    @ApiModelProperty(value = "未成交数量")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "买卖方向")
    private String orientation;

    @Excel(name = "买卖方向", orderNum = "12")
    @ApiModelProperty(value = "买卖方向")
    private String orientationName;

    @Excel(name = "下单人", orderNum = "15")
    @ApiModelProperty(value = "创建人姓名")
    private String createdByName;

    @ApiModelProperty(value = "未成交原因")
    private String cancelReason;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;

    @ApiModelProperty(value = "冲突")
    private String conflict;

    @ApiModelProperty(value = "创建时间")
    @Excel(name = "申请时间", orderNum = "16")
    private String createdAtTime;

    @ApiModelProperty(value = "更新人姓名")
    private String updatedByName;

    @ApiModelProperty(value = "更新人")
    private Integer updatedBy;

    @ApiModelProperty(value = "最新动态")
    private String latestMessage;

    @ApiModelProperty(value = "成交合约")
    private String transactionDomainCode;

    @ApiModelProperty(value = "成交合约编号")
    private String transactionFutureCode;

    @ApiModelProperty(value = "更改期货合约")
    private String changeFutureCode;

    @ApiModelProperty(value = "更改转入合约")
    private String changeTransferFutureCode;

    @ApiModelProperty(value = "更改期货合约")
    private String changeDomainCode;

    @ApiModelProperty(value = "更改转入合约")
    private String changeTransferDomainCode;

    @ApiModelProperty(value = "改单价格")
    private BigDecimal changePrice;

    @ApiModelProperty(value = "改单价差")
    private BigDecimal changeDiffPrice;

    @ApiModelProperty(value = "改单点价/转月数量")
    private BigDecimal changeNum;

    @ApiModelProperty(value = "改单点价/转月手数")
    private BigDecimal changeHandNum;

    @ApiModelProperty(value = "更改手数")
    private String changeHand;

    @ApiModelProperty(value = "业务线")
    private String buCode;
}
