package com.navigator.future.facade;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.pojo.vo.PricePowerTimeVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@FeignClient(name = "navigator-future-service")
public interface PricePowerTimeFacade {

    @PostMapping("/future/savePricePowerTime")
    Result<Boolean> savePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime);

    @PostMapping("/future/updatePricePowerTime")
    Result<Boolean> updatePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime);

    @PostMapping("/future/deletePricePowerTime")
    Result<Boolean> deletePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime);

    @PostMapping("/future/queryPricePowerTimeList")
    Result queryPricePowerTimeList(@RequestBody QueryDTO<PricePowerTimeEntity> queryDTO);

    @GetMapping("/future/exportPricePowerTimeList")
    List<PricePowerTimeVO> exportPricePowerTimeList(@RequestParam(value = "categoryIdList", required = false) String categoryIdList);
}
