package com.navigator.future.pojo.bo;

import com.navigator.bisiness.enums.PriceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/6 14:24
 */
@Data
public class PriceApplyBO implements Serializable {

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "品类id")
    private Integer categoryId;

    @ApiModelProperty(value = "二级品类")
    private String category2;

    @ApiModelProperty(value = "期货代码")
    private String futureCode;

    /**
     * {@link PriceTypeEnum}
     */
    @ApiModelProperty(value = "操作类型（1.点价 2.转月 3.反点价 4.定价 5.结构化定价）")
    private Integer type;

    @ApiModelProperty(value = "申请类型（1正常申请，2改单撤单）")
    private Integer applyType;

    @ApiModelProperty(value = "申请编号")
    private String code;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty("客户id")
    private Integer customerId;

    @ApiModelProperty(value = "挂单价格")
    private Integer applyPrice;

    /**
     * 1 是等于0 2是大于0
     */
    @ApiModelProperty(value = "分配量类型")
    private Integer allocateNumType;

    @ApiModelProperty(value = "申请开始时间")
    private Date applyStartTime;

    @ApiModelProperty(value = "申请结束时间")
    private Date applyEndTime;

    @ApiModelProperty(value = "状态（1.待挂单 2.待成交 3.成交待分配  4.未成交）")
    private String status;

    @ApiModelProperty(value = "操作类型列表")
    private List<Integer> typeList;

    @ApiModelProperty(value = "正在定价")
    private boolean isStructurePricing = false;

    @ApiModelProperty(value = "1采购,2销售")
    private Integer salesType;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "主体id")
    private List<Integer> companyIds;

}
