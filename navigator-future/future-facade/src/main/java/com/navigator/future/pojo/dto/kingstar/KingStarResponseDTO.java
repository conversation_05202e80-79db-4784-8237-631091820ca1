package com.navigator.future.pojo.dto.kingstar;

import com.navigator.common.enums.ResultCodeEnum;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <p>
 * KingStar ResponseDTO
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-15
 */
@Data
@Builder
@Accessors(chain = true)
public class KingStarResponseDTO {
    private Integer code;
    private String message;
    private Object data;

    public static <T> KingStarResponseDTO success(T data) {
        return KingStarResponseDTO.builder()
                .code(ResultCodeEnum.OK.getCode())
                .message(ResultCodeEnum.OK.getMsg())
                .data(data)
                .build();
    }

    public static KingStarResponseDTO success() {
        return success(null);
    }

    public static KingStarResponseDTO failure(ResultCodeEnum resultCodeEnum) {
        return KingStarResponseDTO.builder()
                .code(resultCodeEnum.getCode())
                .message(resultCodeEnum.getMsg())
                .build();
    }

    public static KingStarResponseDTO failure(String message) {
        return KingStarResponseDTO.builder()
                .code(ResultCodeEnum.SYSTEM_DEAL_FAILURE.getCode())
                .message(message)
                .build();
    }
}
