package com.navigator.future.pojo.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/22 14:30
 */
@Data
@Accessors(chain = true)
public class PriceApplyExportVO {

    @Excel(name = "客户名称", orderNum = "1")
    private String customerName;

    @Excel(name = "期货合约", orderNum = "2")
    private String dominantCode;

    @Excel(name = "操作类型", orderNum = "3")
    private String priceApplyType;

    @Excel(name = "挂单量", orderNum = "4")
    private BigDecimal applyNum;

    @Excel(name = "成交量", orderNum = "5")
    private BigDecimal dealNum;

    @Excel(name = "成功分配量", orderNum = "6")
    private BigDecimal allocateNum;

    @Excel(name = "下单人", orderNum = "7")
    private String createdBy;
}
