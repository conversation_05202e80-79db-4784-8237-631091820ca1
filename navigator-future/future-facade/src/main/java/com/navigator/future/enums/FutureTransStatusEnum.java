package com.navigator.future.enums;

import lombok.Getter;

@Getter
public enum FutureTransStatusEnum {
    APPLYING(1, "申请中"),
    TRADING(2, "交易中"),
    COMPLATE(3, "已完成"),
    ;

    int value;
    String desc;

    FutureTransStatusEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static FutureTransStatusEnum getByValue(Integer value) {
        if (null == value) return FutureTransStatusEnum.COMPLATE;
        for (FutureTransStatusEnum en : FutureTransStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return FutureTransStatusEnum.COMPLATE;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
