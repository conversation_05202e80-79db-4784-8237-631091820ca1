package com.navigator.future.pojo.dto.kingstar;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * 期货系统回调 - 指令状态通知请求对象
 */
@Data
public class KingStarCallbackDTO {

    /**
     * 指令ID（现货订单号，订单唯一）
     */
    private String instructId;

    /**
     * hms系统指令ID（逗号分隔多个指令）
     */
    private String hmsInstruct;

    /**
     * 指令状态：
     * 1=待挂单、2=待成交、3=未成交、4=成交待分配、5=部分成交
     */
    private String instructStatus;

    /**
     * 触发推送时间（格式：yyyyMMddHHmmss）
     */
    private String timestamp;

    /**
     * 指令个券明细
     */
    private List<InstructStk> instructStkList;

    @Data
    public static class InstructStk {
        /**
         * 数据类型
         */
        private String dataType;

        /**
         * 个券合约（如 j2509、SPD PF510&PF511）
         */
        private String stkCode;

        /**
         * 总手数
         */
        private Integer amt;

        /**
         * 已成手数
         */
        private Integer dealAmt;

        /**
         * 未成手数
         */
        private Integer unDealAmt;

        /**
         * 成交价格
         */
        private BigDecimal price;
    }
}
