package com.navigator.future.pojo.dto;

import com.navigator.bisiness.enums.PriceTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/5 16:54
 */
@Data
public class DomainFuturesDTO implements Serializable {

    //客户id
    private Integer customerId;
    //期货合约
    private String domainCode;
    //品种id
    private Integer goodsCategoryId;

    /**
     * {@link PriceTypeEnum}
     * 点价/转月
     */
    private Integer priceTyp;
}
