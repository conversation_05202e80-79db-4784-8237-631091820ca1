package com.navigator.future.pojo.vo;

import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PriceDealDetailVO extends PriceDealDetailEntity {

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "结构类型")
    private String contractType;

    @ApiModelProperty(value = "期货合约")
    private String dominantCode;

    @ApiModelProperty(value = "结构化定价结构类型")
    private Integer structureType;

    @ApiModelProperty(value = "总交易日")
    private Integer totalDay;

    @ApiModelProperty(value = "结构化定价结构类型")
    private String structureName;
}