package com.navigator.future.facade;

import com.azure.core.annotation.Post;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.ApplyContraryDTO;
import com.navigator.future.pojo.dto.NotDealDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.dto.StructurePriceApplyDTO;
import com.navigator.future.pojo.entity.PriceApplyEntity;
import com.navigator.future.pojo.entity.PriceDealDetailEntity;
import com.navigator.future.pojo.vo.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/5 18:08
 */
@FeignClient(name = "navigator-future-service")
public interface PriceApplyFacade {

    /**
     * 查询申请单申请明细
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/getpriceApplyDetail")
    Result getpriceApplyDetail(@RequestParam("priceApplyId") Integer priceApplyId);

    /**
     * 申请点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/future/priceApply")
    Result<Integer> priceApply(@RequestBody PriceApplyDTO priceApplyDTO);


    /**
     * 改/撤审核
     *
     * @param priceApplyId
     * @param status
     * @return
     */
    /*@GetMapping("/future/priceApplyUpdateAudit")
    Result priceApplyUpdateAudit(@RequestParam(value = "priceApplyId") Integer priceApplyId,
                                 @RequestParam(value = "status") Integer status,
                                 @RequestParam(value = "memo") String memo);*/

    /**
     * magellan 查询点转反状态数量
     *
     * @param customerId
     * @return
     */
    @GetMapping("/future/magellanApplyAllocateStatus")
    Result magellanApplyAllocateStatus(@RequestParam(value = "customerId") Integer customerId,
                                       @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                       @RequestParam(value = "salesType ", required = false) Integer salesType);

    /**
     * columbus 查询点转反状态数量
     *
     * @param employId
     * @param goodsCategoryId
     * @return
     */
    @GetMapping("/future/columbusApplyAllocateStatus")
    Result columbusApplyAllocateStatus(@RequestParam(value = "employId") Integer employId,
                                       @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                       @RequestParam(value = "salesType ", required = false) Integer salesType);

    /**
     * 申请改单/撤单  点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/modifyPriceApply")
    Result modifyPriceApply(@RequestBody PriceApplyDTO priceApplyDTO);

    /**
     * 分页查询申请单信息
     *
     * @param applyBOQueryDTO
     * @param systemEnum
     * @return
     */
    @PostMapping("/queryPriceApply")
    Result queryPriceApply(@RequestBody QueryDTO<PriceApplyBO> applyBOQueryDTO, @RequestParam("systemEnum") SystemEnum systemEnum);

    /**
     * 查询申请单的改撤单记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceApplyLog")
    Result queryPriceApplyLog(@RequestParam("priceApplyId") Integer priceApplyId);

    /**
     * 查询申请单刷新记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceApplyRefreshLog")
    Result queryPriceApplyRefreshLog(@RequestParam("priceApplyId") Integer priceApplyId);


    @GetMapping("/queryPriceDealDetail")
    Result<List<PriceDealDetailVO>> queryPriceDealDetail(@RequestParam("priceApplyId") Integer priceApplyId);

    /**
     * 查询申请单成交
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceDealInfo")
    Result<List<PriceDealInfoVO>> queryPriceDealInfo(@RequestParam("priceApplyId") Integer priceApplyId);


    /**
     * 根据id查询交易明细表
     *
     * @param priceDealDetailId
     * @return
     */
    @GetMapping("/priceDealDealDetailById")
    PriceDealDetailEntity priceDealDealDetailById(@RequestParam("priceDealDetailId") Integer priceDealDetailId);


    /**
     * 点价员申请单批量挂单
     */
    @GetMapping("/batchPending")
    Result batchPending(@RequestParam("ids") List<Integer> ids);


    /**
     * getAllocationDetails查询申请单的合同分配明细
     *
     * @param applyId
     * @return
     */
    @PostMapping("/getAllocationDetails")
    Result getAllocationDetails(@RequestParam("applyId") String applyId);

    /**
     * 下载申请单信息
     *
     * @return
     */
    @GetMapping("/exportPriceApply")
    List<PriceApplyVO> exportPriceApply();

    /**
     * 下载结构化申请单信息
     *
     * @return
     */
    @GetMapping("/exportStructurePriceApply")
    List<StructurePriceDealVO> exportStructurePriceApply();

    /**
     * 校验申请单数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/checkPriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result checkPriceApply(@RequestPart("file") MultipartFile file);


    @PostMapping(value = "/checkStructurePriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result checkStructurePriceApply(@RequestPart("file") MultipartFile file);

    /**
     * 上传申请单成交数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importPriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importPriceApply(@RequestPart("file") MultipartFile file);

    @PostMapping(value = "/importStructurePriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result importStructurePriceApply(MultipartFile file);

    /**
     * 可反点价数量
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/mayReversePricing")
    Result mayReversePricing(@RequestBody PriceApplyDTO priceApplyDTO);

    /**
     * 确认成交
     *
     * @param priceApplyDTO
     */
    @PostMapping("/priceApplyDeal")
    Result priceApplyDeal(@RequestBody PriceApplyDTO priceApplyDTO);


    @PostMapping("/structurePriceApplyDeal")
    Result structurePriceApplyDeal(@RequestBody StructurePriceApplyDTO structurePriceApplyDTO);


    /**
     * 批量不成交
     *
     * @param notDealDTOS
     * @return
     */
    @PostMapping("/batchNotDeal")
    Result batchNotDeal(@RequestBody List<NotDealDTO> notDealDTOS);

    /**
     * 根据合同id查询是否存在未成交的申请单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getNotDealByContractId")
    Result getNotDealByContractId(@RequestParam("contractId") Integer contractId);

    /**
     * 根据合同id查询是否存在申请单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/judgeExistApply")
    Result judgeExistApply(@RequestParam("contractId") Integer contractId);


    /**
     * 根据合同id关闭结构化定价申请单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/closePriceApplyByContractId")
    Result closePriceApplyByContractId(@RequestParam(value = "contractId") Integer contractId);


    @GetMapping("/autoStructurePriceDeal")
    void autoStructurePriceDeal();

    @GetMapping("/structurePriceApplyDeal")
    Result<String> structurePriceApplyDeal(@RequestParam("priceApplyId") Integer priceApplyId, @RequestParam("tradeDay") Date tradeDay);


    /**
     * 导出头寸报表
     *
     * @param priceApplyBO
     */
    @PostMapping("/getPriceApplyExport")
    Result getPriceApplyExport(@RequestBody PriceApplyBO priceApplyBO);

    /**
     * 查询头寸报表
     *
     * @param priceApplyBO
     */
    @PostMapping("/getPriceApplyTheReportList")
    List<PriceApplyExportVO> getPriceApplyTheReportList(@RequestBody PriceApplyBO priceApplyBO);

    @GetMapping("/queryPriceApplyByContractId")
    List<PriceApplyEntity> queryPriceApplyByContractId(@RequestParam("contractId") Integer contractId);

    @GetMapping("/getDealListByStructureContractId")
    Result getDealListByStructureContractId(@RequestParam(value = "contractId") Integer contractId);

    @PostMapping("/priceApplyWithdraw")
    Result priceApplyWithdraw(@RequestBody ApplyContraryDTO applyContraryDTO);


    @GetMapping("/sendNotDealInmail")
    void sendNotDealInmail();

    /**
     * 根据id撤回成交单
     *
     * @param applyContraryDTO
     * @return
     */
    @PostMapping("/priceDealContrary")
    Result priceDealContrary(@RequestBody ApplyContraryDTO applyContraryDTO);

}
