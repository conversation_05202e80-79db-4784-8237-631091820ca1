package com.navigator.future.pojo.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("dbf_price_deal_info")
@ApiModel(value="PriceDealInfoEntity对象", description="")
public class PriceDealInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "自增ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "点价申请ID")
    private Integer priceApplyId;

    @ApiModelProperty(value = "成交单id")
    private Integer dealDetailId;

    @ApiModelProperty(value = "合同id")
    private Integer contractId;

    @ApiModelProperty(value = "点价类型（1.点价 2.转月 3.反点价 4结构化定价）")
    private Integer priceType;

    @ApiModelProperty(value = "当日返现总金额")
    private BigDecimal dayTotalCashReturn;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal applyNum;

    @ApiModelProperty(value = "成交数量")
    private BigDecimal dealNum;

    @ApiModelProperty(value = "释放数量（不成交数量）")
    private BigDecimal notDealNum;

    @ApiModelProperty(value = "分配数量")
    private BigDecimal assignedNum;

    @ApiModelProperty(value = "成交价")
    private BigDecimal dealPrice;

    @ApiModelProperty(value = "成交日")
    private String dealDate;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedAt;


}
