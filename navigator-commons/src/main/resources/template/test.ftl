<!-- TP005修改-大合同 -->
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TP005修改-大合同</title>
    <style>
        div,p,span,br,table,tr,td,th,tbody,thead,tfoot {page-break-inside: avoid !important;}
        .temp-contract-content{
            width: 95%;
            margin: 0 auto;
            line-height: 2em;
            box-sizing: border-box;
            padding:0;
            font-family: 'SimSun';
        }
        .temp-contract-content .term-num{
            width: 35px;
            text-align: center;
        }
        .temp-contract-content .bold {
            font-weight: bold;
        }
        .temp-contract-content .fill {
            -webkit-box-flex: 1;
            -webkit-flex:1;
            flex:1
        }
        .temp-contract-content .term-row{
            /* padding-left:2em */
        }
        .temp-contract-content .padding-em{
            padding-left: 2em;
        }
        .temp-contract-content .column {
            display: -webkit-box;
            display: flex;
            align-items: center;
            flex-direction: column;
        }
        .temp-contract-content .row {
            display: -webkit-box;
            display: flex;
            flex-direction: row;
        }
        .temp-contract-content .start {
            align-items: flex-start;
        }
        .temp-contract-content .center {
            -webkit-box-pack: center;
            justify-content: center;
        }
        .temp-contract-content .between {
            -webkit-box-pack: justify;
            justify-content: space-between;
            text-align: left;
        }
        .temp-contract-content .t-left{
            float: left;
        }
        .temp-contract-content .t-right{
            float: right;
        }
        .temp-contract-content .clear{
            clear: both;
        }
        .temp-contract-content .absolute-center{
            position:absolute;
            left:0px;
            right:0px;
            text-align: center;
        }
        .temp-contract-content .relative{
            position: relative;
        }
        .temp-contract-content .text-align{
            text-align: center;
        }
        .temp-contract-content .t-sign{
            width:180px;
            position: absolute;
            top:-20px;
            left:50px
        }
        .temp-contract-content .t-hidden{
            visibility: hidden;
        }
        .temp-contract-content .term-item-bold{
            width:90px;
            padding-right:10px;
        }
        .temp-contract-content .term-content{
            width:100%;
            display: inline-block;
        }
        .temp-contract-content .sub-title{
            line-height: 0.8em;
        }
        .temp-contract-content .term-table{
            width:90%;
            margin:0 auto;
            border: 1px solid #000;

            border-collapse: collapse;
        }
        .temp-contract-content .term-table td , .temp-contract-content .term-table tr{
            text-align: center;
            border: 1px solid #000

        }
        .temp-contract-content .term-table td{
            height: 32px;
        }
        .temp-contract-content .code-image{
            height: 60px;
        }
        .temp-contract-content .code-image img{
            height:100%
        }

    </style>
</head>
<body>
<div class="temp-contract-content">
    <div class="t-left code-image">
        <img src="${ewm!}" alt="条形码">
    </div>
    <div class="t-right code-image">
        <img src="${txm!}" alt="二维码">
    </div>
    <div class="clear row">
        <div class="code-image">
            <img src="https://csm4vnvgsto001.blob.core.chinacloudapi.cn/test/test/upload/magellan/ldclogo.png?sp=r&st=2022-06-04T13:07:07Z&se=2122-06-04T21:07:07Z&sv=2020-08-04&sr=b&sig=jNQBAeME57HG6fhGoz1Vi%2BC%2F8bs3R5wMF2XpWZ4E%2BOs%3D" alt="logo">
        </div>
    </div>
    <div>
        <div class="term-content text-align bold">补充协议${xyb!}</div>
    </div>
    <div>
        <div class="term-content">日期：${ttxr!}</div>
    </div>
    <div>
        <div class="term-content">合同号：${no!}</div>
    </div>
    <br />
    <div>
        <div class="term-content">经双方同意，现将${doc!}签订的${noy!}合同进行如下变更：</div>
    </div>
    <#assign num=0/>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryFactoryCode')
    )>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <div class="term-content fill">
                <div class="term-row">合同数量${mt!}吨，${mt!}吨全部变更为${jhgc!}工厂交货。</div>
            </div>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryFactoryCode')
    ||templateCondition.modifyList?seq_contains('goodsPackageId')||templateCondition.modifyList?seq_contains('goodsSpecId')||templateCondition.modifyList?seq_contains('shipWarehouseId'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&templateCondition.deliveryFactoryCode=="ZJG" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合企业标准(标准编号：Q/320582 LDC1）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="YZ" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合江苏中海粮油工业有限公司企业标准(标准编号：Q/321182 56032488X 005）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="ZS" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合舟山中海粮油工业有限公司企业标准(标准编号：Q/ZHLY 01）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="TJ" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!}±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合企业标准(标准编号：Q/LDCTJ 10）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="LY" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!}±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合企业标准(标准编号：Q/320582 LDC1）及卫生指标标准（标准编号:GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="DG" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!}±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%,其他指标符合企业标准(标准编号：Q/LDCDG 01）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.deliveryFactoryCode=="ZZY" ><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">粗蛋白${eg!}±0.5%，水分≤13%,尿素酶活性≤0.3 U/g,氢氧化钾蛋白质溶解度≥70%，其他指标符合企业标准(标准编号：Q/ZZYSY  3）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>
                </div>
            <#else><div class="term-cotent fill">
                <div class="bold">质量指标变更为：</div>
                <div class="term-row">蛋白${eg!}±0.5%， 水分≤13%, 脲酶活性≤0.3 ，可溶性蛋白≥70%，其他指标符合国标规定。</div>
                </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('packageWeight')||templateCondition.modifyList?seq_contains('goodsPackageId'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <div class="term-cotent fill">
                <div class="bold">包装及标识要求变更为：</div>
                <div class="term-row">${ag!}。</div>
                <div class="term-row">买方于再次销售货物时，应严格遵守国家有关农业转基因生物安全管理及标识管理的要求，对本协议项下货物进行明确且合法的转基因标识。在交货地点当货物装车/装船后，由买方执行对转基因生物安全的监管和执行。</div>
            </div>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('contractType')||templateCondition.modifyList?seq_contains('unitPrice')||templateCondition.modifyList?seq_contains('deliveryType')||templateCondition.modifyList?seq_contains('priceEndTime'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&templateCondition.contractType==1><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨，含税价，${prx!}</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==2>  <div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 大连商品交易所（DCE）${hy!}豆粕合约 (M${hyj!})+${jcj!}RMB（下称“合同价格”），${prx!}</div>
                <div class="term-row">作价期限：买方必须在${djj!}前作价完毕或转月（仅允许转月一次）。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==1 && templateCondition.priceEndType==1><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价，${prx!}</div>
                <div class="term-row">本合同最终价格的确认方式为【期货点价价格+基差】，若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                <div class="term-row">买方须在${djj!}前确认最终价格。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==1 && templateCondition.priceEndType==1> <div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价。</div>
                <div class="term-row">本合同最终定价公式为：大连商品交易所（下称“DCE”）${hy!}豆粕合约（M${hyj!}）+${jcj!}RMB，${prx!}</div>
                <br />
                <div class="term-row">买方须在${djj!}前作价完毕，确认最终定价。</div>
                <div class="term-row">若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==1 && templateCondition.priceEndType==2> <div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价。</div>
                <div class="term-row">本合同最终定价公式为：大连商品交易所（下称“DCE”）${hy!}豆粕合约（M${hyj!}）+${jcj!}RMB，${prx!}</div>
                <br />
                <div class="term-row">买方须在提货后${djj!}个自然日内作价完毕，确认最终定价。</div>
                <div class="term-row">若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==2 && templateCondition.priceEndType==1><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价。</div>
                <div class="term-row">本合同最终定价公式为：大连商品交易所（下称“DCE”）${hy!}豆粕合约（M${hyj!}）+${jcj!}RMB+${yf!}RMB。</div>
                <br />
                <div class="term-row">买方须在${djj!}前作价完毕，确认最终定价。</div>
                <div class="term-row">若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==2 && templateCondition.priceEndType==2><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价。</div>
                <div class="term-row">本合同最终定价公式为：大连商品交易所（下称“DCE”）${hy!}豆粕合约（M${hyj!}）+${jcj!}RMB，${prx!}</div>
                <br />
                <div class="term-row">买方须在${djj!}前作价完毕，确认最终定价。</div>
                <div class="term-row">若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==1 && templateCondition.priceEndType==2><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价，${prx!}</div>
                <div class="term-row">本合同最终价格的确认方式为【期货点价价格+基差】，若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                <div class="term-row">买方须在提货后${djj!}个自然日内确认最终价格。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==2 && templateCondition.priceEndType==1><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价，${prx!}</div>
                <div class="term-row">本合同最终价格的确认方式为【期货点价价格+基差+运费】，若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                <div class="term-row">买方须在${djj!}前确认最终价格。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==2 && templateCondition.priceEndType==2><div class="term-cotent fill">
                <div><span class="bold">合同价格变更为：</span> 单价为人民币${pr!}元/吨（含税价），以上价格为暂定价，${prx!}</div>
                <div class="term-row">本合同最终价格的确认方式为【期货点价价格+基差+运费】，若买方逾期未作价，就未作价部分合约，卖方有权强行平仓以直接锁定期货价格，买方应承担卖方因此产生的损失、风险和责任。</div>
                <div class="term-row">买方须在提货后${djj!}个自然日内确认最终价格。</div>
                </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryStartTime')||templateCondition.modifyList?seq_contains('deliveryEndTime')||templateCondition.modifyList?seq_contains('shipWarehouseId')||templateCondition.modifyList?seq_contains('destination')||templateCondition.modifyList?seq_contains('deliveryType'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、交货时间</div>
            <#if templateCondition??&&templateCondition.deliveryType==1> <div class="term-cotent fill">
                <div><span class="bold">交货时间/地点:</span> 交货地点：${dd!}。</div>
                <div class="term-row">地址：${ds!}。</div>
                <div class="term-row">交货期限：${po!}（包含起止两日）</div>
            </div>
            <#elseif templateCondition??&&templateCondition.deliveryType==2><div class="term-cotent fill">
                <div><span class="bold">发货时间/交货地点:</span> 交货地点：送到${py!}（“指定地点”）。</div>
                <div class="term-row">地址：${ds!}。</div>
                <div class="term-row">发货期限：${po!}（包含起止两日）</div>
                </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('contractType')||templateCondition.modifyList?seq_contains('deliveryType')
    ||templateCondition.modifyList?seq_contains('paymentType'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&templateCondition.contractType==2 && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式：</span>买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span>买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。买方付清全额货款后方可提货。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。
                    若买方在提货期内提取未作价部分货物，经卖方书面同意后，需提前按照【买方提出提货要求日的上一交易日期期货收盘价+合同约定基差】价格的110%支付履约保证金后方可提货。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span>买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。买方付清全额货款后方可提货。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span>卖方应于发货期限内将货物按照交货运输方式安排发运。仅在买方承运人出具加盖买方公章或合同章或相应业务章的提货介绍信（电子扫描件同样有效）后，卖方方可交付货物。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达交货地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式：</span>卖方应于发货期限内将货物按照交货运输方式安排发运。仅在买方承运人出具加盖买方公章或合同章或相应业务章的提货介绍信（电子扫描件同样有效）后，卖方方可交付货物。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达交货地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.deliveryType==2 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
                <div class="term-row">若买方在提货期内提取未作价部分货物，经卖方书面同意后，需提前按照【买方提出提货要求日的上一交易日期期货收盘价+合同约定基差】价格的110%支付履约保证金后方可提货。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方依据本合同第7条暂定价支付足额货款并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 卖方应于发货期限内将货物按照交货运输方式安排发运。卖方应于交货期限内将货物按照交货运输方式运送到指定地点。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达指定地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4 && templateCondition.deliveryType==2 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 卖方应于发货期限内将货物按照交货运输方式安排发运。仅在买方依据本合同第7条暂定价支付足额货款并出具加盖买方公章或合同章或相应业务章的提货介绍信（电子扫描件同样有效）后，卖方方可交付货物。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达交货地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方依据本合同第7条暂定价支付足额货款并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span> 买方应于交货期限内于指定地点提取货物。仅在买方支付足额定金（如适用）并出具加盖买方公章或合同章或相应业务章的发货时间及交货地点的计划安排（电子扫描件同样有效）后，卖方方可安排发运货物。货物装上买方的运输工具后视为货物交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span>卖方应于发货期限内将货物按照交货运输方式安排发运。卖方应于交货期限内将货物按照交货运输方式运送到指定地点。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达指定地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==3 && templateCondition.deliveryType==2 && templateCondition.paymentType==2>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.deliveryType==2 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">交货方式变更为：</span>卖方应于发货期限内将货物按照交货运输方式安排发运。仅在买方承运人出具加盖买方公章或合同章或相应业务章的提货介绍信（电子扫描件同样有效）后，卖方方可交付货物。交货时卖方应出具货物交接单，在买方承运人签署后，卖方即按合同约定完成了货物交付。若买方或买方承运人未能在货到指定地点并卸货完毕后的4小时内签收货物交接单，则视为货物在到达交货地点之时（以卖方承运人确认为准）即完成交付。货物交付前的所有费用和风险由卖方承担，货物交付后的所有费用和风险由买方承担。</div>
            </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryType')||templateCondition.modifyList?seq_contains('deliveryFactoryCode'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&templateCondition.deliveryType==1><br  />
            <#elseif templateCondition??&&templateCondition.deliveryType==2><div class="term-cotent fill">
                <div><span class="bold">交货运输方式变更为：</span>卡车/船舶。买方应最晚在提货期前一周确定交货运输方式并告知卖方。运输方式一旦确定不得更改。若买方有必要更改，经卖方同意后，买方应承担由此更改引起的全部损失和费用，包括但不限于卡车空返费、船舶空返费等。</div>
            </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryType')||templateCondition.modifyList?seq_contains('deliveryFactoryCode')||templateCondition.modifyList?seq_contains('goodsPackageId')||templateCondition.modifyList?seq_contains('weightCheck')||templateCondition.modifyList?seq_contains('packageWeight'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <div class="term-cotent fill">
                <div><span class="bold">重量检验变更为：</span>${pe!}</div>
            </div>
        </div>
    </#if>
    <#if templateCondition??&&templateCondition.modifyList?seq_contains('shipWarehouseId')>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、质量检验变更为</div>
            <#if templateCondition??&&['ZJG','LY','ZS','DG','ZZY','TJ']?seq_contains(templateCondition.deliveryFactoryCode)><br />
            <#elseif templateCondition??><div class="fill row start">
                <div class="term-item-bold t-hidden"><span class="bold">质量检验变更为：</span></div>
                <div class="fill">
                    <div class="term-row">质量以卖方工厂品质检验为准。如有质量异议，买方应在收货后3个工作日内书面提出，逾期视为货物符合本合同要求。未经卖方书面确认，买方不得使用或处置有质量异议的货物，否则视为买方默认该部分质量符合合同标准；对质量有异议的货物，由买、卖双方共同抽样交双方认可的检验机构（通过国家CNAS认证的检验机构，以卖方提供的仲裁机构名单中选择）检验，并以此为最终检验结果。如检验合格，检验费由买方承担；如检验不合格，卖方承担检验费，卖方可选择免费更换不合格货物或降价或其他合理方案，但不承担其它义务和责任。</div>
                    <div class="term-row">如确认质量或数量超出合同规定限值，买方不得单方面退货。卖方赔偿计算如下：</div>
                    <div class="term-row">1. 水分：（确认值 - 13%）×不合格品重量×卖方工厂交货价。</div>
                    <div class="term-row">2. 蛋白：（合同规定蛋白含量 - 确认值）×不合格品重量×卖方工厂交货价/合同规定蛋白量。</div>
                    <div class="term-row">3. 粗纤维：（确认值 – ${gqbz!}）×不合格品重量×卖方工厂交货价。</div>
                    <br />
                    <div class="term-row">定义： 卖方工厂交货价指 EXW 工厂价格，不含运费、起吊费、送货费、高温费等费用。</div>
                </div>
                </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('contractType')||templateCondition.modifyList?seq_contains('creditDays')||templateCondition.modifyList?seq_contains('deliveryType')
    ||templateCondition.modifyList?seq_contains('paymentType')||
    templateCondition.modifyList?seq_contains('deliveryFactoryCode'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&(templateCondition.contractType == 2 || templateCondition.contractType == 1) && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应于每批货物提货后的${mes!}个自然日内向卖方全额支付该批货物的货款，以货物交接凭证的出具日期起算。</div>
                <div class="term-row">货款的最终结算须依据合同最终价格、所有已交货的货物重量进行。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 4 || templateCondition.contractType == 3) && templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应于每批货物提货后的${mes!}个自然日内向卖方全额支付该批货物的货款，以货物交接凭证的出具日期起算。</div>
                <div class="term-row">货款的最终结算须依据合同最终价格、所有已交货的货物重量进行。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 4 || templateCondition.contractType == 3) && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应在提货前1个工作日支付该批货物的全额货款。卖方在收到货款后向买方交付货物。</div>
                <div class="term-row">货款的最终结算须依据合同最终价格、所有已交货的货物重量进行。相关余款的支付不得晚于买方最后一批货物的接货日后三个工作日。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 4 || templateCondition.contractType == 3) && templateCondition.deliveryType==2 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应于每批货物交付（定义见第8条）前1个工作日支付该批货物的全额货款。卖方在收到货款后向买方交付货物。</div>
                <div class="term-row">货款的最终结算须依据合同最终价格、所有已交货的货物重量进行。相关余款的支付不得晚于买方最后一批货物的接货日后三个工作日。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 4 || templateCondition.contractType == 3) && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应于每批货物交付（定义见第8条）后的${mes!}个自然日内向卖方全额支付该批货物的货款，以货物交接单的签收日期起算。</div>
                <div class="term-row">货款的最终结算须依据合同最终价格、所有已交货的货物重量进行。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 2 || templateCondition.contractType == 1) && templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应在提货前1个工作日支付该批货物的全额货款。卖方在收到货款后向买方交付货物。</div>
                <div class="term-row">货款的最终结算须依据合同价格、所有已交货的货物重量进行。相关余款的支付不得晚于买方最后一批货物的接货日后三个工作日。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 2 || templateCondition.contractType == 1) && templateCondition.deliveryType==2 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应在每批货物交付（定义见第8条）前1个工作日支付该批货物的全额货款。卖方在收到货款后向买方交付货物。</div>
                <div class="term-row">货款的最终结算须依据合同价格、所有已交货的货物重量进行。相关余款的支付不得晚于买方最后一批货物的接货日后三个工作日。</div>
                </div>
            <#elseif templateCondition??&&(templateCondition.contractType == 2 || templateCondition.contractType == 1) && templateCondition.deliveryType==2 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">付款变更为：</span>买方以银行转账的方式向卖方支付货款。卖方银行信息如下：</div>
                <div class="term-row">开户名称：${me!}</div>
                <div class="term-row">开户行：${kh!}</div>
                <div class="term-row">账号：${zh!}</div>
                <div class="term-row">买方应于每批货物交付（定义见第8条）后的${mes!}个自然日内向卖方全额支付该批货物的货款，以货物交接单的签收日期起算。</div>
                <div class="term-row">货款的最终结算须依据合同价格、所有已交货的货物重量进行。</div>
                </div>
            </#if>
        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('contractType') ||
    templateCondition.modifyList?seq_contains('paymentType') || templateCondition.modifyList?seq_contains('depositAmount') || templateCondition.modifyList?seq_contains('addedDepositRate'))>
        <div class="row start">
            <div class="bold">${num+1}<#assign num=num+1/> 、</div>
            <#if templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate==0 && templateCondition.depositAmount==0 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>1) 在买方作价于DCE成交后至买方提货前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(2) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate==0 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==1>  <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方全额支付履约保证金，即（合同升贴水＋合同签订当日DCE M${hyj!}闭市价）×合同吨数（不计溢短量）×${mr!},在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2) 在买方作价于DCE成交后至买方提货前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(3) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate gt 0 && templateCondition.depositAmount==0 && templateCondition.paymentType==1>  <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方开始点价后一个工作日内但不超过三个自然日按照点价价格支付点价吨位对应货款的${dmr!}的履约保证金，分批点价，分批补足。履约保证金全部到达卖方账户后，买方方可按交（提）货时间付款提货，买方支付的履约保证金只可冲抵经卖方最终结算确定的买方最后一笔应付款。</div>
                <div class="term-row">(2) 在买方作价于DCE成交后至买方提货前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(3) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate gt 0 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==1> <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方全额支付履约保证金，即（合同升贴水＋合同签订当日DCE  M${hyj!}闭市价）×合同吨数（不计溢短量）×${mr!}。在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2) 买方开始点价后一个工作日内但不超过三个自然日按照点价价格支付点价吨位对应货款的${dmr!}的履约保证金，分批点价，分批补足。履约保证金全部到达卖方账户后，买方方可按交（提）货时间付款提货，买方支付的履约保证金只可冲抵经卖方最终结算确定的买方最后一笔应付款。</div>
                <div class="term-row">(3) 在买方作价于DCE成交后至买方提货前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(4) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate==0 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方全额支付履约保证金,即（合同升贴水＋合同签订当日DCE  M${hyj!}闭市价）×合同吨数（不计溢短量）×${mr!}。在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2) 在买方作价于DCE 成交后至买方向卖方全额支付货款前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(3) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate gt 0 && templateCondition.depositAmount==0 && templateCondition.paymentType==2>  <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方开始点价后一个工作日内但不超过三个自然日按照点价价格支付点价吨位对应货款的${dmr!}的履约保证金，分批点价，分批补足。履约保证金全部到达卖方账户后，买方方可按交（提）货时间付款提货，买方支付的履约保证金只可冲抵经卖方最终结算确定的买方最后一笔应付款。</div>
                <div class="term-row">(2) 在买方作价于DCE成交后至买方向卖方全额支付货款前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(3) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate gt 0 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==2> <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方全额支付履约保证金，即（合同升贴水＋合同签订当日DCE M${hyj!}闭市价）×合同吨数（不计溢短量）×${mr!}。在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2)  买方开始点价后一个工作日内但不超过三个自然日按照点价价格支付点价吨位对应货款的${dmr!}的履约保证金，分批点价，分批补足。履约保证金全部到达卖方账户后，买方方可按交（提）货时间付款提货，买方支付的履约保证金只可冲抵经卖方最终结算确定的买方最后一笔应付款。</div>
                <div class="term-row">(3) 在买方作价于DCE 成交后至买方向卖方全额支付货款前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">(4) 就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==2 && templateCondition.addedDepositRate==0 && templateCondition.depositAmount==0 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>（1）在买方作价于DCE 成交后至买方向卖方全额支付货款前的DCE任一交易日中，如买方已作价部分但未付全款部分的加权平均单价每下跌150元或以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方追加履约保证金。追加履约保证金的计算方法为：【（买方作价已在DCE成交的加权平均价格 –当日合同所对应期货的DCE收盘价格）×买方已作价但未付全款吨数】。</div>
                <div class="term-row">（2）就本合同项下货物分批作价的，若未作价部分的基差高于当日卖方基差报价150元以上，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内按照实际差额向卖方追加履约保证金。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==4> <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>在买方完成作价前，如豆粕市场成交价格高于本合同暂定价格，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付未定价部分货物的履约保证金。履约保证金的计算方法为：【（当日卖方成交价格 –本合同暂定价格）×买方未作价吨数】。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==3><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>在买方完成作价前，如豆粕市场价格自合同成交日起累计上涨超过5%，买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付未定价部分货物的履约保证金。履约保证金的计算方法为：【（当日卖方成交价格 –合同成交日卖方成交价格）×买方未作价吨数】。</div>
            </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.depositAmount==0 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>在合同签订后至买方提货前，如市场价格每下跌超过合同价格的${bzjzj!}，卖方有权就未提货部分向买方追加同等比例的履约保证金。买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付追加部分的履约保证金。（市场价格以卖方当日报价为准）。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==1><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方支付全额货款的${mr!}作为履约保证金。在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2) 在合同签订后至买方提货前，如市场价格每下跌超过合同价格的${bzjzj!}，卖方有权就未提货部分向买方追加同等比例的履约保证金。买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付追加部分的履约保证金。（市场价格以卖方当日报价为准）。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.depositAmount==0 && templateCondition.paymentType==2><div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>在合同签订后至买方向卖方付全款提货前，如市场价格每下跌超过合同价格的${bzjzj!}，卖方有权就未提货部分向买方追加同等比例的履约保证金。买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付追加部分的履约保证金。（市场价格以卖方当日报价为准）。</div>
                </div>
            <#elseif templateCondition??&&templateCondition.contractType==1 && templateCondition.depositAmount gt 0 && templateCondition.paymentType==2> <div class="term-cotent fill">
                <div><span class="bold">履约保证金变更为：</span>(1) 买方应于${jzfk!}前(如遇法定节假日或休息日,则顺延至其后的第一个工作日)向卖方支付全额货款的${mr!}作为履约保证金。在履约期间，买方始终保持履约保证金不低于未付款部分货物货值的${mr!}。履约保证金如有不足，买方应在接到卖方追加履约保证金通知后的一个工作日但不超过三个自然日内补齐。保证金在最后一笔合同货款中充抵。</div>
                <div class="term-row">(2) 在合同签订后至买方向卖方付全款提货前，如市场价格每下跌超过合同价格的${bzjzj!}，卖方有权就未提货部分向买方追加同等比例的履约保证金。买方应在接到卖方通知后的一个工作日内但不超过三个自然日内向卖方支付追加部分的履约保证金。（市场价格以卖方当日报价为准）。</div>
            </div>
            </#if>

        </div>
    </#if>
    <#if templateCondition??&&(templateCondition.modifyList?seq_contains('deliveryType')||templateCondition.modifyList?seq_contains('paymentType')||templateCondition.modifyList?seq_contains('deliveryFactoryCode'))>
    <div class="row start">
        <div class="bold">${num+1}<#assign num=num+1/> 、</div>
        <#if templateCondition??&&templateCondition.deliveryType==1 && templateCondition.paymentType==1><div class="term-cotent fill">
        <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。买方无论因何原因未能按期付款（包括履约保证金和追加履约保证金），在分批交付的情况下，卖方有权选择终止继续发货或另行择期交货。</div>
    </div>
    <#elseif templateCondition??&&templateCondition.deliveryType==1 && templateCondition.paymentType==2><div class="term-cotent fill">
        <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。</div>
    </div>
    <#elseif templateCondition??&&templateCondition.deliveryType==2 && templateCondition.paymentType==2 && ['ZJG','YZ','ZS','ZZY']?seq_contains(templateCondition.deliveryFactoryCode)><div class="term-cotent fill">
        <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。</div>
        <div class="term-row">如由卖方代办运输，船到卸货码头后，包干卸货时间为：</div>
        <div class="term-row">300吨单船载货量以下，3个晴天自然日；</div>
        <div class="term-row">300-500吨单船载货量，5个晴天自然日；</div>
        <div class="term-row">500-800吨单船载货量，6个晴天自然日；</div>
        <div class="term-row">800吨以上，8个晴天自然日。</div>
        <div class="term-row">如果买方所用时间超出包干卸货时间产生滞期，滞期在5个自然日以内（包括5个自然日），卖方有权按照实际延误时间和提单数量收取1元/天/吨的滞期费；滞期超过5个自然日，卖方有权按照实际延误时间和提单数量收取2元/天/吨的滞期费。卖方将有关滞期费结算单据提供给买方后，买方须在5个工作日内付款。</div>
    </div>
    <#elseif templateCondition??&&templateCondition.deliveryType==2 && templateCondition.paymentType==1 && ['ZJG','YZ','ZS','ZZY']?seq_contains(templateCondition.deliveryFactoryCode)><div class="term-cotent fill">
        <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。买方无论因何原因未能按期付款（包括履约保证金和追加履约保证金），在分批交付的情况下，卖方有权选择终止继续发货或另行择期交货。</div>
        <div class="term-row">如由卖方代办运输，船到卸货码头后，包干卸货时间为：</div>
        <div class="term-row">300吨单船载货量以下，3个晴天自然日；</div>
        <div class="term-row">300-500吨单船载货量，5个晴天自然日；</div>
        <div class="term-row">500-800吨单船载货量，6个晴天自然日；</div>
        <div class="term-row">800吨以上，8个晴天自然日。</div>
        <div class="term-row">如果买方所用时间超出包干卸货时间产生滞期，滞期在5个自然日以内（包括5个自然日），卖方有权按照实际延误时间和提单数量收取1元/天/吨的滞期费；滞期超过5个自然日，卖方有权按照实际延误时间和提单数量收取2元/天/吨的滞期费。卖方将有关滞期费结算单据提供给买方后，买方须在5个工作日内付款。</div>
    </div>
    <#elseif templateCondition??&&templateCondition.deliveryType==2 && templateCondition.paymentType==2 && !['ZJG','YZ','ZS']?seq_contains(templateCondition.deliveryFactoryCode)><div class="term-cotent fill">
            <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。
            </div>
            <#elseif templateCondition??&&templateCondition.deliveryType==2 && templateCondition.paymentType==1 && !['ZJG','YZ','ZS']?seq_contains(templateCondition.deliveryFactoryCode)><div class="term-cotent fill">
                <div><span class="bold">迟延付款/提货责任变更为：</span>买方无论因任何原因迟延收货/提货，买方都应对由此导致的装运、交付延误产生的费用及损失承担责任；卖方有权按照实际延误天数，以2元/天/吨的费率向买方收取违约赔偿金，包括但不限于卖方因此支出的利息损失、仓储、保险等费用。此外，若买方在规定的提货期满仍未能全部或部分完成提货/收货，就未完成提货/收货的部分，卖方有权选择终止交货或另行择期交货。</div><div class="term-row">买方无论因何原因未能按期付款（包括履约保证金和追加履约保证金），在分批交付的情况下，卖方有权选择终止继续发货或另行择期交货。</div>
            </div>
            </#if>

        </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('deliveryType')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <#if templateCondition??&&templateCondition.deliveryType==1><div class="term-cotent fill">
                    <div><span class="bold">所有权及风险转移变更为：</span>在货物交付且应付给卖方的任何最终结算款付清之前，卖方拥有货物所有权；分批提货时，可分批结算，并部分转移已付款并已交付部分的货物所有权。与货物有关的所有风险自交付转移给买方。</div>
                </div>
                <#elseif templateCondition??&&templateCondition.deliveryType==2><div class="term-cotent fill">
                    <div><span class="bold">所有权及风险转移变更为：</span>在货物交付且应付给卖方的任何最终结算款付清之前，卖方拥有货物所有权；分批送货时，可分批结算，并部分转移已付款并已交付部分的货物所有权。与货物有关的所有风险自交付转移给买方。</div>
                </div>
                </#if>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('contractType')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <#if templateCondition??&&[1,2]?seq_contains(templateCondition.contractType)><div class="column start">
                    <div class="term-content"><span class="bold">终止变更为：</span>本合同可由以下原因终止：</div>
                    <div class="term-content">(1) 双方协议解除；</div>
                    <div class="term-content">(2) 卖方由于以下原因通知解除：若买方在应付款项到期后十四（14）日内未付款；买方明确表示不履行本合同；除本合同另有约定外，买方延迟履行或有其他违约行为合计超过十五（15）日的；法律规定的其他情形。因以上原因解除合同的，买方应赔偿给卖方因货物价格变化所导致的任何市场损失及其他损失（包括但不限于卖方仓储和人力成本、诉讼费用、律师费用、差旅费用）。已付及应付的全部预付款和履约保证金（包括追加的履约保证金）将作为违约金不予退回；</div>
                    <div class="term-content">(3) 若发生不可抗力且其已持续60天以上，任何一方均可在不可抗力持续期间内通知对方终止本合同，且不必向对方承担任何责任。</div>
                    <div class="term-content">(4) 法律规定的其他情形。</div>
                </div>
                <#elseif templateCondition??&&templateCondition.contractType==4> <div class="column start">
                    <div class="term-content"><span class="bold">终止变更为：</span>本合同可由以下原因终止：</div>
                    <div class="term-content">(1) 双方协议解除；</div>
                    <div class="term-content">(2) 卖方由于以下原因通知解除：若买方在应付款项到期后十四（14）日内未付款；买方明确表示不履行本合同；买方延迟履行或有其他违约行为导致本合同目的无法实现；法律规定的其他情形。因以上原因解除合同的，买方应赔偿给卖方因货物价格变化所导致的任何市场损失及其他损失（包括但不限于卖方成本损失、诉讼费用、律师费用、差旅费用等），并将违约部分货款的30%作为违约金支付给卖方。卖方将有权直接从已付的货款及履约保证金中扣除违约金及违约损失。</div>
                    <div class="term-content">(3) 若发生不可抗力且其已持续60天以上，任何一方均可在不可抗力持续期间内通知对方终止本合同，且不必向对方承担任何责任。</div>
                    <div class="term-content">(4) 法律规定的其他情形。</div>
                </div>
                <#elseif templateCondition??&&templateCondition.contractType==3>   <div class="column start">
                    <div class="term-content"><span class="bold">终止变更为：</span>本合同可由以下原因终止：</div>
                    <div class="term-content">(1) 双方协议解除；</div>
                    <div class="term-content">(2) 卖方由于以下原因通知解除：若买方在应付款项到期后十四（14）日内未付款；买方明确表示不履行本合同；买方延迟履行或有其他违约行为导致本合同目的无法实现；法律规定的其他情形。因以上原因解除合同的，买方应赔偿给卖方因货物价格变化所导致的任何市场损失及其他损失（包括但不限于卖方成本损失、诉讼费用、律师费用、差旅费用等）。已付的货款和履约保证金将作为违约金不予退回；</div>
                    <div class="term-content">(3) 若发生不可抗力且其已持续60天以上，任何一方均可在不可抗力持续期间内通知对方终止本合同，且不必向对方承担任何责任。</div>
                    <div class="term-content">(4) 法律规定的其他情形。</div>
                </div>
                </#if>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('paymentType')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <#if templateCondition??&&templateCondition.paymentType==1><div class="term-cotent fill">
                    <div><span class="bold">责任及限制变更为：</span>因买方自身或其代表或承运人过错而导致的所有损失及收费、开支、滞期费及其他费用（包括但不限于将货物运回卖方仓库或码头的运输费和保险费），如造成卖方损失，买方还应赔偿卖方的损失。卖方在本合同项下所负责任总额最高不超过本合同总价。</div>
                    <div class="term-row">无论买方因何原因未能按照本合同规定向卖方付款，买方每延期一天，卖方有权按照实际延误天数以“延期支付款项*0.05%/天”的比例向买方收取因付款延误而遭受的利息损失。</div>
                </div>
                <#elseif templateCondition??&&templateCondition.paymentType==2><div class="term-cotent fill">
                    <div><span class="bold">责任及限制变更为：</span>因买方自身或其代表或承运人过错而导致的所有损失及收费、开支、滞期费及其他费用（包括但不限于将货物运回卖方仓库或码头的运输费和保险费），如造成卖方损失，买方还应赔偿卖方的损失。卖方在本合同项下所负责任总额最高不超过本合同总价。</div>
                </div>
                </#if>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('priceEndTime')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="column start">
                    <div class="term-content"><span class="bold">作价期限变更为：</span>买方必须在${djj!}前作价完毕或转月（仅允许转月一次）。逾期未作价部分，卖方有权强行平仓或强行解约定赔，买方应承担因此产生的损失（包括但不限于卖方的市场损失、卖方仓储和人力成本、诉讼费用、律师费用、差旅费用）、风险和责任。</div>
                </div>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('deliveryFactoryCode')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="column start">
                    <div class="term-content bold">通知变更为:</div>
                    <div class="term-content padding-em">除本合同中有特别规定的外，本合同项下发出的所有通知和联系都应由各方的授权人以电子邮件或快递或邮寄方式送达至对方授权代表，双方在此明确同意各自授权代表有权代表各方通过合同书、电子邮件或快递或邮寄方式有形地表现所载内容的形式做出任何与本合同签订、更改及履行等有关的意思表示。</div>
                    <br />
                    <div class="term-content padding-em">本合同项下双方授权代表的联系方式如下:</div>
                    <div class="term-content padding-em">(i) 卖方: ${me!}</div>
                    <div class="term-content padding-em">地址：${mads!}</div>
                    <div class="term-content padding-em">收件人: ${mfox!}</div>
                    <div class="term-content padding-em">电子邮箱: ${mema!}</div>
                    <div class="term-content padding-em">电话号码: ${mmbo!}</div>
                    <div class="term-content padding-em">(ii)买方: ${na!}</div>
                    <div class="term-content padding-em">地址：${ads!}</div>
                    <div class="term-content padding-em">收件人：${fox!}</div>
                    <div class="term-content padding-em">电子邮箱：${ema!}</div>
                    <div class="term-content padding-em">电话号码: ${mbo!}</div>
                    <div class="term-content padding-em">一方以电子邮件向其他方发出的通知或联系函件，于发出当日视为送达其他方；一方以快递方式向其他方发出的通知或联系函件，于发出次日视为送达其他方。为避免歧义，本合同中所指书面通知，均包含按照本条约定的电子邮件、快递和邮寄的方式。</div>
                </div>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('complaintDiscountPrice')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="term-content fill">
                    <div class="term-row">买卖双方经协商达成如下协议：</div>
                    <div class="term-row">双方同意从${no!}的货款中扣除下列投诉单号所对应的销售折让费用：</div>
                    <table class="term-table">
                        <thead>
                        <tr>
                            <td>投诉单号</td>
                            <td>
                                销售折让原因
                            </td>
                            <td>
                                销售折让金额(元)
                            </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>${tsdh!}</td>
                            <td>${wkdl!}</td>
                            <td>${zqf!}</td>
                        </tr>
                        <tr>
                            <td>合计<td>
                            <td></td>
                            <td>${zqfz!}</td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="term-row">扣除以上销售折让金额后，${no!}合同单价由${xpr!}变更为${pr!}元/吨（含税价）；${prx!}。</div>
                </div>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('delayPrice')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="term-content fill">
                    <div class="term-row">买卖双方经协商达成如下协议：</div>
                    <div class="term-row">双方同意从${no!}的货款中收取下列滞期船名所对应的滞期费用：</div>
                    <table class="term-table">
                        <thead>
                        <tr>
                            <td>滞期船名</td>
                            <td>
                                数量
                            </td>
                            <td>
                                滞期费(元)
                            </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>${tsdh!}</td>
                            <td>${wkdl!}</td>
                            <td>${zqf!}</td>
                        </tr>
                        <tr>
                            <td>合计<td>
                            <td></td>
                            <td>${zqfz!}</td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="term-row">收取以上滞期费用后，${no!}合同单价由${xpr!}变更为${pr!}元/吨（含税价）；${prx!}。</div>
                </div>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('businessPrice')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="term-content fill">
                    <div class="term-row">经协商，双方同意将贵司${sxrq!}的赊销利息人民币金额${sxlx!}元加入合同${no!}，因此该合同单价由${xpr!}变更为${pr!}元/吨（含税价）；${prx!}。</div></div>
            </div>
        </#if>
        <#if templateCondition??&&templateCondition.modifyList?seq_contains('compensationPrice')>
            <div class="row start">
                <div class="bold">${num+1}<#assign num=num+1/> 、</div>
                <div class="term-content fill">
                    <div class="term-row">买卖双方经协商达成如下协议：</div>
                    <div class="term-row">双方同意从的${no!}的货款中扣除下列吨袋补贴：</div>
                    <table class="term-table">
                        <thead>
                        <tr>
                            <td>
                                <div>发货日期</div>
                            </td>
                            <td>
                                <div>发运数量</div>
                                <div>(吨)</div>
                            </td>
                            <td>
                                <div>吨袋补贴金额</div>
                                <div>(元)</div>
                            </td>
                            <td>
                                <div>吨袋破损</div>
                                <div>(条)</div>
                            </td>
                            <td>
                                <div>吨袋赔偿金额</div>
                                <div>(元)</div>
                            </td>
                            <td>
                                <div>运费补贴</div>
                                <div>(元)</div>
                            </td>
                            <td>
                                <div>赔偿金额</div>
                                <div>(元)</div>
                            </td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td>合计<td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="term-row">扣除以上吨袋补贴后，${no!}合同单价由${xpr!}变更为${pr!}元/吨（含税价）；${prx!}。</div>
                </div>
            </div>
        </#if>
        <br />
        <div class="term-cotent fill">
            <div class="term-row">该合同项下其他条款保持不变。 </div>
            <div class="term-row">以上特此确认。</div>
        </div>
        <div class="row between" style="margin-top:20px;padding-bottom: 60px;">
            <div class="relative">
                <div class="term-row" style="margin-top:40px;">卖方：${me!} </div>
                <div class="term-row" style="margin-top:30px;">盖章：${lgz!}</div>

            </div>
            <div style="margin-right:150px;">
                <div class="term-row" style="margin-top:40px;">买方：${na!} </div>
                <div class="term-row" style="margin-top:30px;">盖章：${kgz!}</div>

            </div>
        </div>
    </div>
</body>
</html>