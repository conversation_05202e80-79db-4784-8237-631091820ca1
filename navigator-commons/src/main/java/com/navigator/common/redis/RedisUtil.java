package com.navigator.common.redis;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.navigator.common.exception.BusinessException;
import org.springframework.data.redis.connection.RedisZSetCommands;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * redis 工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@Component
public class RedisUtil {

    @Resource
    RedisTemplate redisTemplate;

    /**
     * 获取缓存的key
     *
     * @param fields 字段名
     * @return String
     */
    public static String getCacheKey(String... fields) {
        StringBuilder sb = new StringBuilder();
        for (String field : fields) {
            sb.append(field == null ? "" : field);
            sb.append(":");
        }
        return StrUtil.trim(sb.toString(), ':');
    }

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据key获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    // ============================String(字符串)=============================

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }

    public String getString(String key) {
        String value = "";
        try {
            Object o = redisTemplate.opsForValue().get(key);
            if (null != o) {
                value = (String) o;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return value;
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 递增
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    public long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }
    // ================================Hash(哈希)=================================

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    /**
     * HashGet
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return 值
     */
    public Object hget(String key, String item) {
        return redisTemplate.opsForHash().get(key, item);
    }

    /**
     * 获取hashKey对应的所有键值
     *
     * @param key 键
     * @return 对应的多个键值
     */
    public Map<Object, Object> hmget(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * HashSet
     *
     * @param key 键
     * @param map 对应多个键值
     * @return true 成功 false 失败
     */
    public boolean hmset(String key, Map<String, Object> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * HashSet 并设置时间
     *
     * @param key  键
     * @param map  对应多个键值
     * @param time 时间(秒)
     * @return true成功 false失败
     */
    public boolean hmset(String key, Map<String, Object> map, long time) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 向一张hash表中放入数据,如果不存在将创建
     *
     * @param key   键
     * @param item  项
     * @param value 值
     * @param time  时间(秒) 注意:如果已存在的hash表有时间,这里将会替换原有的时间
     * @return true 成功 false失败
     */
    public boolean hset(String key, String item, Object value, long time) {
        try {
            redisTemplate.opsForHash().put(key, item, value);
            if (time > 0) {
                expire(key, time);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除hash表中的值
     *
     * @param key  键 不能为null
     * @param item 项 可以使多个 不能为null
     */
    public void hdel(String key, Object... item) {
        redisTemplate.opsForHash().delete(key, item);
    }

    /**
     * 判断hash表中是否有该项的值
     *
     * @param key  键 不能为null
     * @param item 项 不能为null
     * @return true 存在 false不存在
     */
    public boolean hHasKey(String key, String item) {
        return redisTemplate.opsForHash().hasKey(key, item);
    }

    /**
     * hash递增 如果不存在,就会创建一个 并把新增后的值返回
     *
     * @param key  键
     * @param item 项
     * @param by   要增加几(大于0)
     * @return
     */
    public double hincr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, by);
    }
    // ============================Set(集合)=============================

    /**
     * hash递减
     *
     * @param key  键
     * @param item 项
     * @param by   要减少记(小于0)
     * @return
     */
    public double hdecr(String key, String item, double by) {
        return redisTemplate.opsForHash().increment(key, item, -by);
    }

    /**
     * 根据key获取Set中的所有值
     *
     * @param key 键
     * @return
     */
    public Set<Object> sGet(String key) {
        try {
            return redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据value从一个set中查询,是否存在
     *
     * @param key   键
     * @param value 值
     * @return true 存在 false不存在
     */
    public boolean sHasKey(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将数据放入set缓存
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSet(String key, Object... values) {
        try {
            return redisTemplate.opsForSet().add(key, values);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 将set数据放入缓存
     *
     * @param key    键
     * @param time   时间(秒)
     * @param values 值 可以是多个
     * @return 成功个数
     */
    public long sSetAndTime(String key, long time, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().add(key, values);
            if (time > 0)
                expire(key, time);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取set缓存的长度
     *
     * @param key 键
     * @return
     */
    public long sGetSetSize(String key) {
        try {
            return redisTemplate.opsForSet().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }
    // ===============================List(列表)=================================

    /**
     * 移除值为value的
     *
     * @param key    键
     * @param values 值 可以是多个
     * @return 移除的个数
     */
    public long setRemove(String key, Object... values) {
        try {
            Long count = redisTemplate.opsForSet().remove(key, values);
            return count;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 获取list缓存的内容
     *
     * @param key   键
     * @param start 开始
     * @param end   结束 0 到 -1代表所有值
     * @return
     */
    public List<Object> lGet(String key, long start, long end) {
        try {
            return redisTemplate.opsForList().range(key, start, end);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 获取list缓存的长度
     *
     * @param key 键
     * @return
     */
    public long lGetListSize(String key) {
        try {
            return redisTemplate.opsForList().size(key);
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 通过索引 获取list中的值
     *
     * @param key   键
     * @param index 索引 index>=0时， 0 表头，1 第二个元素，依次类推；index<0时，-1，表尾，-2倒数第二个元素，依次类推
     * @return
     */
    public Object lGetIndex(String key, long index) {
        try {
            return redisTemplate.opsForList().index(key, index);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, Object value) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, Object value, long time) {
        try {
            redisTemplate.opsForList().rightPush(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @return
     */
    public boolean lSet(String key, List<Object> value) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 将list放入缓存
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒)
     * @return
     */
    public boolean lSet(String key, List<Object> value, long time) {
        try {
            redisTemplate.opsForList().rightPushAll(key, value);
            if (time > 0)
                expire(key, time);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 根据索引修改list中的某条数据
     *
     * @param key   键
     * @param index 索引
     * @param value 值
     * @return
     */
    public boolean lUpdateIndex(String key, long index, Object value) {
        try {
            redisTemplate.opsForList().set(key, index, value);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 移除N个值为value
     *
     * @param key   键
     * @param count 移除多少个
     * @param value 值
     * @return 移除的个数
     */
    public long lRemove(String key, long count, Object value) {
        try {
            Long remove = redisTemplate.opsForList().remove(key, count, value);
            return remove;
        } catch (Exception e) {
            e.printStackTrace();
            return 0;
        }
    }

    // ============================ZSet=============================

    /**
     * 根据key模糊查询所有匹配的key
     *
     * @param key
     * @return
     */
    public Set<String> keys(String key) {
        return redisTemplate.keys(key);
    }

    /**
     * ZADD [KEY] [SCORE1] [MEMBER1] [SCORE2] [MEMBER2]......
     * 将所有指定成员添加到键为key有序集合（sorted set）里面。 添加时可以指定多个分数/成员（score/member）对。
     * 如果指定添加的成员已经是有序集合里面的成员，则会更新改成员的分数（scrore）并更新到正确的排序位置。
     *
     * @param key
     * @param value
     * @param score
     * @return boolean
     */
    public boolean zadd(String key, Object value, double score) {
        Boolean add = redisTemplate.boundZSetOps(key).add(value, score);
        return Boolean.TRUE.equals(add);
    }

    /**
     * ZADD [KEY] [NX|XX] [CH] [INCR] [SCORE1] [MEMBER1] [SCORE2] [MEMBER2]......
     * 将所有指定成员添加到键为key有序集合（sorted set）里面。 添加时可以指定多个分数/成员（score/member）对。
     * 如果指定添加的成员已经是有序集合里面的成员，则会更新改成员的分数（scrore）并更新到正确的排序位置。
     * <p>
     * XX: 仅仅更新存在的成员，不添加新成员。
     * NX: 不更新存在的成员。只添加新成员。
     * CH: 修改返回值为发生变化的成员总数，原始是返回新添加成员的总数 (CH 是 changed 的意思)。
     * 更改的元素是新添加的成员，已经存在的成员更新分数。 所以在命令中指定的成员有相同的分数将不被计算在内。
     * 注：在通常情况下，ZADD返回值只计算新添加成员的数量。
     * INCR: 当ZADD指定这个选项时，成员的操作就等同ZINCRBY命令，对成员的分数进行递增操作。
     *
     * @param key
     * @param valueMap
     * @return boolean 新增>0 返回 true, 只是更新的话返回值=0, 也认为是true
     */
    public boolean zadd(String key, Map<Object, Double> valueMap) {
        Set<ZSetOperations.TypedTuple<Object>> tuples = new HashSet<>();
        for (Map.Entry<Object, Double> entry : valueMap.entrySet()) {
            ZSetOperations.TypedTuple<Object> tuple = new DefaultTypedTuple<>(entry.getKey(), entry.getValue());
            tuples.add(tuple);
        }

        Long add = redisTemplate.boundZSetOps(key).add(tuples);
        return Objects.nonNull(add) && add >= 0;
    }

    /**
     * ZRANGE [KEY] [NX|XX] [CH] [INCR] [START] [END]
     * 返回有序集key中成员member的排名。其中有序集成员按score值递增(从小到大)顺序排列。排名以0为底，也就是说，score值最小的成员排名为0
     * <p>
     * XX: 仅仅更新存在的成员，不添加新成员。
     * NX: 不更新存在的成员。只添加新成员。
     * CH: 修改返回值为发生变化的成员总数，原始是返回新添加成员的总数 (CH 是 changed 的意思)。
     * 更改的元素是新添加的成员，已经存在的成员更新分数。 所以在命令中指定的成员有相同的分数将不被计算在内。
     * 注：在通常情况下，ZADD返回值只计算新添加成员的数量。
     * INCR: 当ZADD指定这个选项时，成员的操作就等同ZINCRBY命令，对成员的分数进行递增操作。
     *
     * @param key
     * @param start
     * @param end
     * @return Set
     */
    public Set<Object> zrange(String key, long start, long end) {
        return redisTemplate.boundZSetOps(key).range(start, end);
    }

    /**
     * ZRANGE [KEY] [START] [END] WITHSCORES
     * 返回有序集key中成员member和对应分数的排名。其中有序集成员按score值递增(从小到大)顺序排列。排名以0为底，也就是说，score值最小的成员排名为0
     *
     * @param key
     * @param start
     * @param end
     * @return List<Map < Object, Double>>
     */
    public List<Map<Object, Double>> zrangeWithScore(String key, long start, long end) {
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisTemplate.boundZSetOps(key).rangeWithScores(start, end);
        if (Objects.isNull(typedTuples)) {
            return null;
        }
        List<Map<Object, Double>> list = new ArrayList<>();
        for (ZSetOperations.TypedTuple<Object> tuple : typedTuples) {
            Map<Object, Double> member = new HashMap<>();
            member.put(tuple.getValue(), tuple.getScore());
            list.add(member);
        }
        return list.size() == 0 ? null : list;
    }

    /**
     * ZRANGEBYLEX [KEY] [MIN|(MIN [MAX|(MAX [LIMIT offset count]
     * exp: zrangebylex zset ["\"aa\"" ["\"c\"" limit 1 3
     * 返回指定成员区间内的成员，按成员字典正序排序, 分数必须相同.
     * LIMIT	否	返回结果是否分页,指令中包含LIMIT后offset、count必须输入
     * offset	否	返回结果起始位置
     * count	否	返回结果数量
     *
     * @param key
     * @param min
     * @param includeMin 是否包含边界 true: min <= ? false: min < ?
     * @param max
     * @param includeMax 是否包含边界 true: ? <= max; false: ? < max
     * @return Set
     */
    public Set<Object> zrangeByLex(String key, Object min, boolean includeMin, Object max, boolean includeMax) {
        RedisZSetCommands.Range range = new RedisZSetCommands.Range();
        // min<? | min<=?
        if (includeMin) {
            range.gte(Objects.requireNonNull(min));
        } else {
            range.gt(Objects.requireNonNull(min));
        }
        // ?<max | ?<=max
        if (includeMax) {
            range.lte(Objects.requireNonNull(max));
        } else {
            range.lt(Objects.requireNonNull(max));
        }

        return redisTemplate.boundZSetOps(key).rangeByLex(range);
    }

    /**
     * ZRANGEBYSCORE [KEY] [MIN] [MAX] [LIMIT offset count] 默认包含边界，不想包含边界使用 (MIN (MAX
     * 返回key的有序集合中的分数在min和max之间的所有元素（包括分数等于max或者min的元素）。元素被认为是从低分到高分排序的。
     * 具有相同分数的元素按字典序排列（这个根据redis对有序集合实现的情况而定，并不需要进一步计算）。
     *
     * @param key
     * @param min
     * @param max
     * @return Set
     */
    public Set<Object> zrangeByScore(String key, double min, double max) {
        return redisTemplate.boundZSetOps(key).rangeByScore(min, max);
    }

    /**
     * ZRANGEBYSCORE [KEY] [MIN] [MAX] WITHSCORES [LIMIT offset count] 默认包含边界，不想包含边界使用 (MIN (MAX
     * 返回有序集key中成员member和对应分数的排名。其中有序集成员按score值递增(从小到大)顺序排列。排名以0为底，也就是说，score值最小的成员排名为0
     *
     * @param key
     * @param min
     * @param max
     * @return List<Map < Object, Double>>
     */
    public List<Map<Object, Double>> zrangeByScoreWithScore(String key, double min, double max) {
        Set<ZSetOperations.TypedTuple<Object>> typedTuples = redisTemplate.boundZSetOps(key).rangeByScoreWithScores(min, max);
        if (Objects.isNull(typedTuples)) {
            return null;
        }
        List<Map<Object, Double>> list = new ArrayList<>();
        for (ZSetOperations.TypedTuple<Object> tuple : typedTuples) {
            Map<Object, Double> member = new HashMap<>();
            member.put(tuple.getValue(), tuple.getScore());
            list.add(member);
        }
        return list.size() == 0 ? null : list;
    }

    /**
     * ZRANK [KEY] [MEMBER KEY]
     * 返回有序集key中成员member的排名。其中有序集成员按score值递增(从小到大)顺序排列。排名以0为底，也就是说，score值最小的成员排名为0。
     *
     * @param key
     * @param memberKey
     * @return long
     */
    public long zrank(String key, Object memberKey) {
        Long rank = redisTemplate.boundZSetOps(key).rank(Objects.requireNonNull(memberKey));
        return Objects.nonNull(rank) ? rank : -1;
    }

    /**
     * ZREM [KEY] [MEMBER KEY]......
     * 返回的是从有序集合中删除的成员个数，不包括不存在的成员. 当key存在，但是其不是有序集合类型，就返回一个错误
     *
     * @param key
     * @param memberKey
     * @return 删除个数
     * @throws
     */
    public long zrem(String key, Object... memberKey) {
        Long remove = redisTemplate.boundZSetOps(key).remove((Object) memberKey);
        return Objects.nonNull(remove) ? remove : 0;
    }

    /**
     * ZCARD [KEY]
     * key存在的时候，返回有序集的元素个数，否则返回0。
     *
     * @param key
     * @return
     */
    public long zcard(String key) {
        Long size = redisTemplate.boundZSetOps(key).size();
        return Objects.nonNull(size) ? size : 0;
    }

    /**
     * ZCOUNT [KEY] [MIN] [MAX]
     * 返回有序集key中，score值在min和max之间(默认包括score值等于min或max)的成员个数, 默认包含边界。想要不包含边界使用 (MIN (MAX
     *
     * @param key
     * @param min
     * @param max
     * @return 成员个数
     */
    public long zcount(String key, double min, double max) {
        Long count = redisTemplate.boundZSetOps(key).count(min, max);
        return Objects.nonNull(count) ? count : 0;
    }

    /**
     * ZSCORE [KEY] [MEMBER KEY]
     * 返回有序集key中，成员member的score值。如果member元素不是有序集key的成员，或key不存在，返回nil。
     *
     * @param key
     * @param memberKey
     * @return 不存在返回 null
     */
    public Double zscore(String key, Object memberKey) {
        return redisTemplate.boundZSetOps(key).score(Objects.requireNonNull(memberKey));
    }

    /**
     * ZINCRBY [KEY] [INCREMENT] [MEMEBER KEY]
     * 为有序集key的成员member的score值加上增量increment。如果key中不存在member，就在key中添加一个member，
     * score是increment（就好像它之前的score是0.0）。如果key不存在，就创建一个只含有指定member成员的有序集合。
     * 当key不是有序集类型时，返回一个错误。
     *
     * @param key
     * @param memberKey
     * @param increment 加数
     * @return
     */
    public Double zincrby(String key, double increment, Object memberKey) {
        return redisTemplate.boundZSetOps(key).incrementScore(Objects.requireNonNull(memberKey), increment);
    }

    /**
     * 分页查询zset数据，zset的数据score需要是从1开始递增
     *
     * @param key
     * @param pageNum
     * @param pageSize
     * @return
     */
    public Set zSetGetByPage(String key, int pageNum, int pageSize) {
        try {
            if (redisTemplate.hasKey(key)) {
                int start = (pageNum - 1) * pageSize;
                int end = pageNum * pageSize - 1;
                Long size = redisTemplate.opsForZSet().size(key);
                if (end > size) {
                    end = -1;
                }
                return redisTemplate.opsForZSet().range(key, start, end);
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * redis实现分布式锁
     *
     * @param key
     * @return
     */
    public boolean setNx(String key, long time) {
        // 自定义 lua 脚本
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>("return redis.call('setnx',KEYS[1], ARGV[1])", Long.class);

        Long result = (Long) redisTemplate.execute(redisScript, Collections.singletonList(key), "1");

        // 返回1,表示设置成功,拿到锁
        if (ObjectUtil.equals(result, 1L)) {
            expire(key, time);
            return true;
        }
        return false;
    }

    /**
     * 校验key的合法性
     *
     * @param key
     */
    public void checkKey(String key) {
        if (StrUtil.isBlank(key)) {
            throw new BusinessException("key为空！");
        }
        if (!key.contains(":")) {
            throw new BusinessException("为了规范缓存，请对key进行归类，以:分割！");
        }
    }

    /**
     * 递增
     *
     * @param key       键
     * @param step      递增量
     * @param liveHours 生存时间
     * @return Long
     */
    @SuppressWarnings("WeakerAccess")
    public Long incr(String key, int step, long liveHours) {
        this.checkKey(key);
        ValueOperations<String, Long> seqValueOperations = redisTemplate.opsForValue();
        Long seq = seqValueOperations.increment(key, step);
        if (seq == 1 && liveHours > 0) {
            redisTemplate.expire(key, liveHours, TimeUnit.HOURS);
        }
        return seq;
    }

    /**
     * redis的hash结构：缓存map对象
     *
     * @param key 键
     * @param map map对象
     */
    public void setMap(String key, Map<String, String> map) {
        this.checkKey(key);
        redisTemplate.opsForHash().putAll(key, map);
    }

    /**
     * redis的hash结构：缓存map对象的具体项
     *
     * @param key       键
     * @param itemKey   具体项key
     * @param itemValue 具体项值
     */
    public void setItemOfMap(String key, String itemKey, String itemValue) {
        this.checkKey(key);
        redisTemplate.opsForHash().put(key, itemKey, itemValue);
    }

    /**
     * redis的hash结构：
     * 获取map对象的具体项
     *
     * @param key     键
     * @param itemKey 具体项key
     * @return String
     */
    public String getItemOfMap(String key, String itemKey) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        return hashOperations.get(key, itemKey);
    }

    /**
     * redis的hash结构：删除map对象的具体项
     *
     * @param key      键
     * @param itemKeys 具体项key
     * @return Long
     */
    public Long delItemOfMap(String key, List<String> itemKeys) {
        if (CollUtil.isNotEmpty(itemKeys)) {
            HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
            return hashOperations.delete(key, itemKeys.toArray());
        }
        return null;
    }

    /**
     * 删除缓存
     *
     * @param key 键
     */
    public void delete(String key) {
        redisTemplate.delete(key);
    }
}
