package com.navigator.common.sequence;

import cn.hutool.core.util.StrUtil;
import com.navigator.common.enums.SequenceEnum;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * 序列号生成
 *
 * @Author: lincl
 * @Date: 2018/12/3 10:51
 */
@Component
@Slf4j
@Deprecated
public class SequenceUtil {

    private final SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
    private final SimpleDateFormat yySdf = new SimpleDateFormat("yy");
    private final SimpleDateFormat yymmSdf = new SimpleDateFormat("yyMM");
    private final SimpleDateFormat yymmddhhhmmssSdf = new SimpleDateFormat("yyyyMMddHHmmss");
    private static final Random random = new Random();

    @Resource
    private RedisUtil redisUtil;

    /**
     * 获取下一个序列号
     *
     * @param length
     * @param liveHours
     * @param sequenceEnum
     * @param fields
     * @return
     */
    private String nextSeq(int length, int liveHours, SequenceEnum sequenceEnum, String... fields) {
        String redisKey = "SN:" + sequenceEnum.name() + ":" + RedisUtil.getCacheKey(fields);
        long seq = redisUtil.incr(redisKey, 1, liveHours);
        String result = StrUtil.join("", fields) + StrUtil.fillBefore(seq + "", '0', length);
        log.info("生成序列号：{}={}", sequenceEnum.getName(), result);
        return result;
    }

    /**
     * 仓单采购合同号	"交易所+品种+采购+分配年份+四位递增码 例如：DCEMP240001"
     *
     * @param exchange
     * @param category
     * @return
     */
    public String generateWarrantPurchaseContractCode(String exchange, String category) {
        // BUGFIX：case-1003185 仓单合同回购，新采购合同编号生成不正确  Author: Mr 2025-04-30 Start
        // String yymm = yymmSdf.format(new Date());
        String yymm = new SimpleDateFormat("yyyyMM").format(new Date());
        // BUGFIX：case-1003185 仓单合同回购，新采购合同编号生成不正确  Author: Mr 2025-04-30 End
        return nextSeq(4, -1, SequenceEnum.WARRANT_PURCHASE_CONTRACT_CODE, exchange, category, "P", yymm);
    }

    /**
     * 生成销售TT新编号
     *
     * @return
     */
    public String generateSpotSalesTTCode() {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        return "SC" + DateTimeUtil.formatDateTimeValue() + code;
    }

    /**
     * 生成采购TT新编号
     *
     * @return
     */
    public String generateSpotPurchaseTTCode() {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        return "PC" + DateTimeUtil.formatDateTimeValue() + code;
    }

    /**
     * 生成子TT编号
     */
    public String generateSpotChildTTCode(String parentTTCode) {
        // 如果parentTTCode 不包含"-",则生成-001
        if (!parentTTCode.contains("-")) {
            return parentTTCode + "-001";
        }
        String[] split = parentTTCode.split("-");

        String redisKey = "SN:" + SequenceEnum.CHILD_TT_CODE.name() + ":" + RedisUtil.getCacheKey(split[0], "-");

        // 如果缓存中没有该key，则设置缓存
        if (redisUtil.get(redisKey) == null) {
            redisUtil.set("SN:" + SequenceEnum.CHILD_TT_CODE.name() + ":" + RedisUtil.getCacheKey(split[0], "-"), Integer.parseInt(split[1]));
        }

        return nextSeq(3, -1, SequenceEnum.CHILD_TT_CODE, split[0], "-");
    }

}
