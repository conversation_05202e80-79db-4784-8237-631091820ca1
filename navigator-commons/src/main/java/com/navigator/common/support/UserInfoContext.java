package com.navigator.common.support;

import com.navigator.common.dto.CurrentUserInfoDTO;

/**
 * 当前登录用户信息
 */
public class UserInfoContext {

    private static ThreadLocal<CurrentUserInfoDTO> userInfo = new ThreadLocal<CurrentUserInfoDTO>();

    private UserInfoContext() {
    }

    /**
     * 获取登录用户信息
     *
     * @return
     */
    public static CurrentUserInfoDTO getUser() {
        return userInfo.get();
    }

    public static void setUser(CurrentUserInfoDTO user) {
        userInfo.set(user);
    }

    /**
     * 获取当前登录用户的名称
     *
     * @return
     */
    public static String getUsername() {
        CurrentUserInfoDTO currentUserInfoDTO = UserInfoContext.userInfo.get();
        if (currentUserInfoDTO == null) {
            return null;
        } else {
            return currentUserInfoDTO.getUsername();
        }
    }

    public static void unload() {
        userInfo.remove();
    }
}
