package com.navigator.common.serviceBus;

import com.azure.spring.autoconfigure.jms.AzureServiceBusJMSProperties;
import com.azure.spring.autoconfigure.jms.ConnectionStringResolver;
import com.azure.spring.autoconfigure.jms.ServiceBusKey;
import lombok.extern.slf4j.Slf4j;
import org.apache.qpid.jms.JmsConnectionFactory;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.config.JmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;

import javax.jms.ConnectionFactory;
import javax.jms.Session;

@Slf4j
@EnableJms
@Configuration
@ConditionalOnProperty(name = "servicebus.jms.enabled", havingValue = "true", matchIfMissing = false)
public class JmsConfig {

    @Value("${spring.jms.listener.concurrency:1-5}")
    private String concurrency;

    @Value("${spring.jms.listener.receive-timeout:30000}")
    private Long receiveTimeout;

    @Value("${spring.jms.listener.recovery-interval:5000}")
    private Long recoveryInterval;

    /**
     * 创建优化的JMS连接工厂
     * 简化配置，提高稳定性
     */
    @Bean
    public ConnectionFactory jmsConnectionFactory(AzureServiceBusJMSProperties busJMSProperties) {
        final String connectionString = busJMSProperties.getConnectionString();
        final String clientId = busJMSProperties.getTopicClientId();
        final int idleTimeout = busJMSProperties.getIdleTimeout();

        final ServiceBusKey serviceBusKey = ConnectionStringResolver.getServiceBusKey(connectionString);

        // 增强连接配置，添加重连和心跳机制
        final String remoteUri = String.format(
                "amqps://%s?amqp.idleTimeout=%d&amqp.traceFrames=true&jms.connectTimeout=30000&jms.sendTimeout=30000&jms.requestTimeout=30000&amqp.maxFrameSize=1048576",
                serviceBusKey.getHost(),
                idleTimeout
        );

        try {
            // 创建JMS连接工厂
            CachingConnectionFactory cachingConnectionFactory = getCachingConnectionFactory(serviceBusKey, remoteUri, clientId);

            log.info("JMS连接工厂初始化完成，Host: {}, 空闲超时: {}ms",
                    serviceBusKey.getHost(), idleTimeout);
            return cachingConnectionFactory;

        } catch (Exception e) {
            log.error("创建JMS连接工厂失败", e);
            throw new RuntimeException("JMS连接工厂初始化失败", e);
        }
    }

    @NotNull
    private static CachingConnectionFactory getCachingConnectionFactory(ServiceBusKey serviceBusKey, String remoteUri, String clientId) {
        JmsConnectionFactory jmsConnectionFactory = new JmsConnectionFactory(
                serviceBusKey.getSharedAccessKeyName(),
                serviceBusKey.getSharedAccessKey(),
                remoteUri
        );
        jmsConnectionFactory.setClientID(clientId + "-optimized");

        // 使用缓存连接工厂，但配置保守参数
        CachingConnectionFactory cachingConnectionFactory = new CachingConnectionFactory(jmsConnectionFactory);

        // 启用生产者缓存以提高消息发送性能（默认值为 true）
        cachingConnectionFactory.setCacheProducers(true);

        // 禁用消费者缓存以提高消息消费的可靠性，避免状态异常
        cachingConnectionFactory.setCacheConsumers(false);

        // 设置会话缓存的大小为 10，限制缓存的会话数量
        cachingConnectionFactory.setSessionCacheSize(10);

        // 启用异常时的自动重连功能，确保连接的稳定性
        cachingConnectionFactory.setReconnectOnException(true);
        return cachingConnectionFactory;
    }

    @Bean
    public JmsListenerContainerFactory<?> cuckooJmsListenerContainerFactory(ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        // 消息手动确认
        factory.setSessionAcknowledgeMode(Session.CLIENT_ACKNOWLEDGE);

        // 是否使用事务
        // true:一次 JMS 会话中处理多个 JMS 消息时，只有所有的 JMS 消息都处理完毕并成功提交事务后，才视为处理成功
        // false:一次 JMS 会话中处理多个 JMS 消息时，每个消息都会立即被处理，不会等待其他消息的提交结果。
        factory.setSessionTransacted(true);

        // 保守的并发设置，避免资源竞争
        factory.setConcurrency(concurrency);

        // 设置恢复间隔 - 连接断开后的重连间隔
        factory.setRecoveryInterval(recoveryInterval);

        // 设置接收超时
        factory.setReceiveTimeout(receiveTimeout);

        // 错误处理器 - 关键：不抛出异常
        factory.setErrorHandler(this::handleJmsError);

        return factory;
    }

    /**
     * 创建优化的JMS监听器容器工厂
     * 防止重启和资源泄漏
     */
    @Bean
    public JmsListenerContainerFactory<?> futureJmsListenerContainerFactory(ConnectionFactory connectionFactory) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);

        // 消息确认模式 - 使用客户端确认
        factory.setSessionAcknowledgeMode(Session.AUTO_ACKNOWLEDGE);

        // 禁用事务以避免死锁问题
        factory.setSessionTransacted(false);

        // 保守的并发设置，避免资源竞争
        factory.setConcurrency(concurrency);

        // 设置恢复间隔 - 连接断开后的重连间隔
        factory.setRecoveryInterval(recoveryInterval);

        // 设置接收超时
        factory.setReceiveTimeout(receiveTimeout);

        // 错误处理器 - 关键：不抛出异常
        factory.setErrorHandler(this::handleJmsError);

        log.info("Future JMS监听器容器工厂初始化完成，并发数: {}, 恢复间隔: {}ms", concurrency, recoveryInterval);

        return factory;
    }

    /**
     * 提供连接字符串Bean，供延迟消息发送器使用
     */
    @Bean("serviceBusConnectionString")
    public String serviceBusConnectionString(AzureServiceBusJMSProperties busJMSProperties) {
        return busJMSProperties.getConnectionString();
    }

    /**
     * JMS错误处理方法，避免容器重启
     */
    private void handleJmsError(Throwable throwable) {
        log.error("JMS消息处理异常，但不会导致容器重启: {}", throwable.getMessage());

        // 记录详细的异常信息用于问题排查
        if (throwable.getCause() != null) {
            log.error("异常根因: {}", throwable.getCause().getMessage());
        }

        // 关键：不抛出异常，避免导致容器重启
        // 让Spring JMS的内置重试机制处理
    }

}
