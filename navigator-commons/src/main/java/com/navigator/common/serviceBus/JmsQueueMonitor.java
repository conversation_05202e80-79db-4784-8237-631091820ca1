package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@RequiredArgsConstructor
public class JmsQueueMonitor {
    
    @Value("${jms.monitor.enabled:true}")
    private boolean monitorEnabled;
    
    @Value("${jms.monitor.queue.max-pending:100}")
    private long maxPendingMessages;
    
    @Value("${jms.monitor.alert.threshold:3}")
    private int alertThreshold;
    
    // 记录每个队列的消息处理统计
    private final Map<String, AtomicLong> processedCount = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> lastProcessedCount = new ConcurrentHashMap<>();
    private final Map<String, Integer> stagnantCount = new ConcurrentHashMap<>();
    
    /**
     * 记录消息处理
     */
    public void recordMessageProcessed(String queueName) {
        if (!monitorEnabled) return;
        
        processedCount.computeIfAbsent(queueName, k -> new AtomicLong(0))
                     .incrementAndGet();
    }
    
    /**
     * 定期检查队列活跃度
     */
    @Scheduled(fixedDelayString = "${jms.monitor.check.interval:120000}")
    public void checkQueueActivity() {
        if (!monitorEnabled) return;
        
        processedCount.forEach((queueName, currentCount) -> {
            long lastCount = lastProcessedCount.computeIfAbsent(queueName, k -> new AtomicLong(0))
                                              .get();
            
            if (currentCount.get() == lastCount) {
                // 消息处理停滞
                int stagnant = stagnantCount.merge(queueName, 1, Integer::sum);
                
                if (stagnant >= alertThreshold) {
                    log.error("队列 {} 消息处理停滞超过{}次检查，可能存在监听异常", queueName, stagnant);
                    triggerQueueAlert(queueName, stagnant);
                }
            } else {
                // 有消息处理，重置停滞计数
                stagnantCount.put(queueName, 0);
                lastProcessedCount.get(queueName).set(currentCount.get());
            }
        });
    }
    
    private void triggerQueueAlert(String queueName, int stagnantCount) {
        log.error("队列监控预警: queue={}, 停滞次数={}, 建议检查JMS监听器状态", 
                 queueName, stagnantCount);
        
        // 集成告警系统
        // alertService.sendAlert("JMS队列异常", 
        //     String.format("队列%s消息处理停滞，建议重启服务", queueName));
    }
}