package com.navigator.common.serviceBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.jms.ConnectionFactory;
import java.time.LocalDateTime;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
@Component
@RequiredArgsConstructor
public class JmsHealthChecker implements HealthIndicator {
    
    @Value("${jms.health.check.enabled:true}")
    private boolean healthCheckEnabled;
    
    @Value("${jms.health.check.timeout:30}")
    private int healthCheckTimeoutSeconds;
    
    @Value("${jms.health.check.max-failures:3}")
    private int maxConsecutiveFailures;
    
    private final ConnectionFactory connectionFactory;
    private final JmsTemplate jmsTemplate;
    
    private final AtomicLong consecutiveFailures = new AtomicLong(0);
    private volatile LocalDateTime lastSuccessTime = LocalDateTime.now();
    private volatile boolean isHealthy = true;
    
    @Override
    public Health health() {
        if (!healthCheckEnabled) {
            return Health.up().withDetail("status", "disabled").build();
        }
        
        try {
            // 检查连接状态
            connectionFactory.createConnection().close();
            
            consecutiveFailures.set(0);
            lastSuccessTime = LocalDateTime.now();
            isHealthy = true;
            
            return Health.up()
                    .withDetail("lastSuccess", lastSuccessTime)
                    .withDetail("consecutiveFailures", 0)
                    .build();
                    
        } catch (Exception e) {
            long failures = consecutiveFailures.incrementAndGet();
            isHealthy = failures < maxConsecutiveFailures;
            
            log.error("JMS健康检查失败 (第{}次): {}", failures, e.getMessage());
            
            return Health.down()
                    .withDetail("error", e.getMessage())
                    .withDetail("consecutiveFailures", failures)
                    .withDetail("lastSuccess", lastSuccessTime)
                    .build();
        }
    }
    
    @Scheduled(fixedDelayString = "${jms.health.check.interval:60000}")
    public void performHealthCheck() {
        if (!healthCheckEnabled) return;
        
        Health health = health();
        if (health.getStatus().equals(Health.DOWN.getStatus())) {
            long failures = consecutiveFailures.get();
            if (failures >= maxConsecutiveFailures) {
                log.error("JMS连续{}次健康检查失败，触发服务重启预警", failures);
                triggerRestartAlert();
            }
        }
    }
    
    private void triggerRestartAlert() {
        // 发送预警通知
        log.error("JMS服务异常，建议重启服务！连续失败次数: {}", consecutiveFailures.get());
        
        // 可以集成你们的告警系统
        // alertService.sendAlert("JMS服务异常", "连续健康检查失败，建议重启服务");
        
        // 如果配置了自动重启，可以调用重启逻辑
        if (isAutoRestartEnabled()) {
            performGracefulRestart();
        }
    }
    
    private boolean isAutoRestartEnabled() {
        // 从配置中读取是否启用自动重启
        return false; // 默认不自动重启，需要人工干预
    }
    
    private void performGracefulRestart() {
        log.warn("执行JMS服务优雅重启...");
        // 实现优雅重启逻辑
        System.exit(1); // 让容器管理器重启服务
    }
}