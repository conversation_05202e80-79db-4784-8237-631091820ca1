package com.navigator.common.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.util.List;

public abstract class BaseDaoPlus<T> implements DaoPlus<T> {

    /**
     * 当前泛型真实类型的Class
     */
    protected Class<T> modelClass;

    @Autowired
    BaseMapper<T> myMapper;

    public BaseDaoPlus() {
        ParameterizedType pt = (ParameterizedType) this.getClass().getGenericSuperclass();
        modelClass = (Class<T>) pt.getActualTypeArguments()[0];
    }

    /**
     * 保存实体
     *
     * @param entity
     * @return
     */
    @Override
    public int save(T entity) {

        return myMapper.insert(entity);
    }

    /**
     * 保存多个实体类
     *
     * @param entities
     */
    @Override
    public void saveList(List<T> entities) {
        for (T entity : entities) {
            this.save(entity);
        }
    }

    /**
     * 逻辑删除更新isDeleted为1
     *
     * @param id
     * @return
     */
    @Override
    public int deleteById(Integer id) {
        T entity = this.findById(id);
        if (entity != null) {
            return myMapper.updateById(entity);
        }
        return 0;
    }

    /**
     * 根据条件逻辑删除数据
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public int deleteByWrapper(QueryWrapper<T> queryWrapper) {
        List<T> tList = myMapper.selectList(queryWrapper);
        int del = 0;
        for (T entity : tList) {
            if (entity != null) {
                del = myMapper.updateById(entity) + del;
            }
        }
        return del;
    }

    /**
     * 更新数据
     *
     * @param entity
     * @return
     */
    @Override
    public int update(T entity) {
        return myMapper.updateById(entity);
    }

    /**
     * 根据条件更新数据
     *
     * @param entity
     * @param updateWrapper
     * @return
     */
    @Override
    public int updateByUpdateWrapper(T entity, UpdateWrapper<T> updateWrapper) {
        return myMapper.update(entity, updateWrapper);
    }

    /**
     * 根据id查询数据
     *
     * @param id
     * @return
     */
    @Override
    public T findById(Integer id) {
        QueryWrapper<T> queryWrapper = new QueryWrapper<>();
        return myMapper.selectOne(queryWrapper);
    }

    /**
     * 获取所有数据
     *
     * @return
     */
    @Override
    public List<T> findAllByWrapper(QueryWrapper<T> queryWrapper) {
        return myMapper.selectList(queryWrapper);
    }

    /**
     * 获取一条数据
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public T findOneByWrapper(QueryWrapper<T> queryWrapper) {
        return myMapper.selectOne(queryWrapper);
    }

    /**
     * 根据条件获取数据总数
     *
     * @param queryWrapper
     * @return
     */
    @Override
    public Integer findCountByWrapper(QueryWrapper<T> queryWrapper) {
        return myMapper.selectCount(queryWrapper);
    }

    /**
     * 分页查询未被删除的数据
     *
     * @param queryWrapper
     * @param page
     * @return
     */
    @Override
    public IPage<T> findPageByQueryWrapper(QueryWrapper<T> queryWrapper, Integer page, Integer pageSize) {
        Page<T> objects = null;
        if (page != null && pageSize != null) {
            objects = new Page<>(page, pageSize);
        } else {
            objects = new Page<>(1, 10);
        }
        return myMapper.selectPage(objects, queryWrapper);
    }
}
