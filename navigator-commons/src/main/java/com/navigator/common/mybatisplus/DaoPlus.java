package com.navigator.common.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

public interface DaoPlus<T> extends BaseMapper {

    //保存/新增记录
    int save(T entity);

    //保存/新增多条记录
    void saveList(List<T> entities);

    // 根据主键ID查询记录
    T findById(Integer id);

    // 获取所有符合条件的数据
    List<T> findAllByWrapper(QueryWrapper<T> queryWrapper);

    // 根据实体值获取记录
    T findOneByWrapper(QueryWrapper<T> queryWrapper);

    // 根据实体值获取记录总数
    Integer findCountByWrapper(QueryWrapper<T> queryWrapper);


    // 根据ID更新记录
    int update(T model);

    // 根据实体值和条件更新记录
    int updateByUpdateWrapper(T entity, UpdateWrapper<T> updateWrapper);


    //根据Id删除记录
    int deleteById(Integer id);

    //根据条件删除
    int deleteByWrapper(QueryWrapper<T> queryWrapper);

    // 分页
    IPage<T> findPageByQueryWrapper(QueryWrapper<T> queryWrapper, Integer page,Integer pageSize);
}
