package com.navigator.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <p>
 * 临时权限DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-27
 */
@Data
@Accessors(chain = true)
public class TempPermissionDTO {

    @ApiModelProperty(value = "客户状态")
    private Boolean customerStatus;

    @ApiModelProperty(value = "客户白名单列表")
    private List<String> customerNameList;

}
