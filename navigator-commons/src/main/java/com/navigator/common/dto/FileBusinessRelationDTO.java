package com.navigator.common.dto;

import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.enums.FileCategoryType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-06-04 15:19
 */
@Data
@Accessors(chain = true)
public class FileBusinessRelationDTO {
    /**
     * 附件文件ID集合
     */
    private List<Integer> fileIdList;
    /**
     * 来源ID
     */
    private Integer bizId;
    /**
     * 文件来源类型
     * {@link FileCategoryType}
     */
    private Integer categoryType;
    /**
     * 业务模块()
     * {@link ModuleTypeEnum}
     */
    private String moduleType;
}
