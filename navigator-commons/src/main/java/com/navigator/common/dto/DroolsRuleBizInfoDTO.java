package com.navigator.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class DroolsRuleBizInfoDTO {

    Integer flag = 1;
    //全部数据（传参）
    Map<String, Object> mapBizData = new HashMap<>();
    //匹配到的规则编号列表（返回结果字段）
    List<String> matchRules = new ArrayList<>();
    //匹配到的规则信息（返回命中规则的结果）
    List<DroolsRuleDataDTO> matchRuleList = new ArrayList<>();
}
