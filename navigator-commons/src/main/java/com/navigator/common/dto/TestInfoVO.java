package com.navigator.common.dto;

import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.lang.reflect.Field;

/**
 * <AUTHOR>
 * @since 2021-03-18 15:22
 */
@Data
@Accessors(chain = true)
public class TestInfoVO implements Serializable {

    String userName;
    String time;
    String address;

    public String build(String template) {
        Field[] fields = this.getClass().getDeclaredFields();
        for (Field field : fields) {
            String fieldName = field.getName();
            String fieldValue = "";
            try {
                fieldValue = JSONUtil.toJsonStr(field.get(this));
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            String flagChar = "#" + fieldName + "#";
            template = template.replace(flagChar, fieldValue);
        }
        return template;
    }

}
