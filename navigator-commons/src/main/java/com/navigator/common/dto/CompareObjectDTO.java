package com.navigator.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CompareObjectDTO {
    private String name;
    private String before;
    private String after;
    /**
     * 1. 用户 2. 系统
     */
    private String source;

    private String updateTime;

    private String signId;

    private String contractId;

    private String sourceContractId;
}
