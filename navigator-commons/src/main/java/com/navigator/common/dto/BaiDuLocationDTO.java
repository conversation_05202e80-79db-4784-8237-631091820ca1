package com.navigator.common.dto;

import lombok.Data;

@Data
public class BaiDuLocationDTO {
    private String address;
    private ContentDTO content;
    private int status;

    @Data
    public static class ContentDTO {
        private String address;
        private AddressDetailDTO addressDetail;
        private PointDTO point;

        @Data
        public static class AddressDetailDTO {
            private String city;
            private int cityCode;
            private String province;
        }

        @Data
        public static class PointDTO {
            //经度lng
            private double x;
            //纬度lat
            private double y;
        }
    }
}
