package com.navigator.common.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2020-06-03 11:38
 */
@Data
@Accessors(chain = true)
public class TestFileBaseInfoDTO {
    /**
     * 文件地址路径
     */
    private String attachUrl;
    /**
     * 文件名称
     */
    private String attachName;

    private String eg;
    /**
     * 文件原名称
     */
    private String originalName;
    /**
     * 文件字节数
     */
    private Integer size;
    /**
     * 文件大小（B、KB、MB）
     */
    private String sizeInfo;
    /**
     * 文件后缀名
     */
    private String attachStuff;

    /**
     * 文件记录入库ID
     */
    private Integer id;

    /**
     * 文件类型
     */
    private Integer attachType;
    /**
     * 文件类型
     */
    private String attachTypeName;

    private BigDecimal depositAmount;

    private MessageFormDTO messageFormDTO;

//    private MessageFormDTO messageFormDTO;

}
