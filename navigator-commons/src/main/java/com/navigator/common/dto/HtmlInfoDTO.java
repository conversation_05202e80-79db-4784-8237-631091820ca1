package com.navigator.common.dto;

import com.navigator.bisiness.enums.ModuleTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * Html 信息DTO
 *
 * <AUTHOR> Mr.
 * @since 2021/12/13
 */
@Accessors(chain = true)
@Data
public class HtmlInfoDTO {
    /**
     * 文件地址
     */
    private String htmlUrl;
    /**
     * 文件内容
     */
    private String htmlContent;
    /**
     * 业务模块()
     * {@link ModuleTypeEnum}
     */
    private String moduleType;
    /**
     * 合同编号
     */
    private String contractCode = " ";
    /**
     * TT编号
     */
    private String ttCode = " ";

    private Integer companyId = 1;
}
