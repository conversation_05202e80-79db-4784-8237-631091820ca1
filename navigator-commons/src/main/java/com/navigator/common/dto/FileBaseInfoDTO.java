package com.navigator.common.dto;

import com.navigator.common.enums.FileServiceTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2020-06-03 11:38
 */
@Data
@Accessors(chain = true)
public class FileBaseInfoDTO {
    /**
     * 文件地址路径
     */
    private String attachUrl;
    /**
     * 文件名称
     */
    private String attachName;
    /**
     * 文件原名称
     */
    private String originalName;
    /**
     * 文件字节数
     */
    private Integer size;
    /**
     * 文件大小（B、KB、MB）
     */
    private String sizeInfo;
    /**
     * 文件后缀名
     */
    private String attachStuff;

    /**
     * 文件记录入库ID
     */
    private Integer id;

    /**
     * 文件类型
     */
    private Integer attachType;
    /**
     * 文件类型
     */
    private String attachTypeName;

    /**
     * 文件存储类型
     * {@link FileServiceTypeEnum}
     */
    private Integer fsType;

    private String errorMsg;

    private String fileViewUrl;

    private String fileHostUrl;
}
