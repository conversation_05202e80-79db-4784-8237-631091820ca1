package com.navigator.common.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractApproveBizInfoDTO implements Serializable {


    private Integer maxTotalAmount = 50000000;
    private Integer minTotalAmount = 20000000;
    private Integer deliveryDueMonthLimit = 12;
    private Integer remainContractNumLimit = 32;

    private Integer ruleResult = 1011;
    private StringBuilder ruleMemo = new StringBuilder();


    private Integer category2;
    @ApiModelProperty(value = "采/销")
    private Integer salesType;
    @ApiModelProperty(value = "TT类型")
    private Integer ttType;
    @ApiModelProperty(value = "交易类型")
    private Integer tradeType;
    @ApiModelProperty(value = "业务线")
    private String buCode;

    @ApiModelProperty(value = "合同类型")
    private Integer contractType;
    @ApiModelProperty(value = "原合同类型")
    private Integer sourceContractType;


    private BigDecimal totalAmount = BigDecimal.ZERO;
    @ApiModelProperty(value = "基差价（元）")
    private BigDecimal extraPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "蛋白价差")
    private BigDecimal proteinDiffPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "运费")
    private BigDecimal transportPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "精炼价差")
    private BigDecimal refineDiffPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "商务补贴")
    private BigDecimal businessPrice = BigDecimal.ZERO;
    @ApiModelProperty(value = "其他补贴")
    private BigDecimal otherPrice = BigDecimal.ZERO;


    @ApiModelProperty(value = "蛋白价差变化")
    private boolean proteinDiffPriceChanged = false;
    @ApiModelProperty(value = "运费变化")
    private boolean transportPriceChanged = false;
    @ApiModelProperty(value = "精炼价差变化")
    private boolean refineDiffPriceChanged = false;
    @ApiModelProperty(value = "商务补贴变化")
    private boolean businessPriceChanged = false;
    @ApiModelProperty(value = "其他补贴变化")
    private boolean otherPriceChanged = false;


    @ApiModelProperty(value = "交提货日期变化")
    private boolean deliveryEndTimeChanged = false;
    @ApiModelProperty(value = "客户主体变化（同集团）")
    private boolean customerChanged = false;
    @ApiModelProperty(value = "客户主体变化（不同集团）")
    private boolean customerGroupChanged = false;

    @ApiModelProperty(value = "基差价差低于阈值")
    private boolean lowExtraPrice = false;

    @ApiModelProperty(value = "交期超限")
    private boolean deliveryLong = false;

    @ApiModelProperty(value = "基差合同变更为基差暂定价合同")
    private boolean contract2To4 = false;
    @ApiModelProperty(value = "暂定价合同")
    private boolean newContract3 = false;

    @ApiModelProperty(value = "剩余可提数量超限")
    private boolean remainMuch = false;
    @ApiModelProperty(value = "补充协议类型 -1:原合同TT不审批不生成协议   0: 普通拆分(新合同) 1: 数量变更补充协议(原合同)")
    private Integer addedSignatureType = 0;
    @ApiModelProperty(value = "开始交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartTime;

    @ApiModelProperty(value = "截止交货时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndTime;

    @ApiModelProperty(value = "TT创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date ttCreatedAt = new Date();

    @ApiModelProperty(value = "合同总量（吨）")
    private BigDecimal contractNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "已开单量")
    @TableField(exist = false)
    private BigDecimal totalBillNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "已提总量（吨）")
    private BigDecimal totalDeliveryNum = BigDecimal.ZERO;

    @ApiModelProperty(value = "签订日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date signDate;

    @ApiModelProperty(value = "仓单交易类型(1.交易所交割仓单 2.线下交易所仓单合同 3.交易所仓单交易平台)")
    private Integer warrantTradeType;


}
