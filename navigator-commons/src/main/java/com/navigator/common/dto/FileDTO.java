package com.navigator.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FileDTO {
    //功能
    private int project;
    //系统
    private int system;
    //目录
    private int catalog;
    //路径
    private String filepath;
    //文件名称
    private String fileName;
    //源目录
    private String sourceCatalog;
    //目的目录
    private String destinationCatalog;

    public FileDTO(int system, int project, int catalog) {

        this.project = project;
        this.system = system;
        this.catalog = catalog;
    }
}
