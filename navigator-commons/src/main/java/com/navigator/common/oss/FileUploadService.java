package com.navigator.common.oss;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.*;
import com.navigator.common.config.properties.OssProperties;
import com.navigator.common.dto.FileDTO;
import com.navigator.common.enums.OssEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.file.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.Base64;
import java.util.Date;

@Slf4j
@Component
public class FileUploadService {

    @Resource
    private OssProperties ossProperties;

    private static String ERP_ABSOLUTE_URL = "TIANTOUCAI/erp/";

    private static String TRADE_ABSOLUTE_URL = "TIANTOUCAI/trade/";

    private static String WX_TRADE_ABSOLUTE_URL = "TIANTOUCAI/wx-trade/";

    /**
     * 上传
     *
     * @param uploadFile
     * @return
     *//*
    public FileDTO upload(MultipartFile uploadFile, FileDTO fileDTO) {

        OSS ossClient = null;

        try {
            String originalFilename = uploadFile.getOriginalFilename();
            generateFilePath(originalFilename, fileDTO);
            String filepath = fileDTO.getFilepath();
            String bucketName = ossProperties.getBucketNameErp();

            // 上传到阿里云
            byte[] bytes = uploadFile.getBytes();
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(getContentType(filepath.substring(filepath.lastIndexOf("."))));
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, filepath, new ByteArrayInputStream(bytes), objectMetadata);
            log.info(this.getClass().getSimpleName() + "upload", putObjectResult);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "upload", e);
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL.getCode(), ResultCodeEnum.FILE_UPLOAD_FAIL.getMsg());
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }

        return fileDTO;
    }

*/
    /**
     * 上传
     *
     * @param file
     * @return
     */
    public FileDTO upload(String file, FileDTO fileDTO) {
        OSS ossClient = null;
        try {
            int i = file.indexOf(";base64,");
            String realFile = file.substring(i + 8);

            generateFilePath(DateUtil.now() + ".png", fileDTO);
            String filepath = fileDTO.getFilepath();
            String bucketName = ossProperties.getBucketNameErp();

            // 上传到阿里云
            //对内容进行base64解码
            byte[] bytes = Base64.getMimeDecoder().decode(realFile);
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(getContentType(filepath.substring(filepath.lastIndexOf("."))));
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            PutObjectResult putObjectResult = ossClient.putObject(bucketName, filepath, new ByteArrayInputStream(bytes), objectMetadata);
            log.info(this.getClass().getSimpleName() + "upload", putObjectResult);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "upload", e);
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL, ResultCodeEnum.FILE_UPLOAD_FAIL.getMsg());
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }

        return fileDTO;
    }


    /**
     * 删除文件
     *
     * @param objectName
     * @return
     */
    public int delete(String objectName) {
        OSS ossClient = null;
        try {
            // 根据BucketName,objectName删除文件
            String bucketName = ossProperties.getBucketNameErp();
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            ossClient.deleteObject(bucketName, objectName);
            return 1;
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "delete", e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
        return 0;
    }

    /**
     * 下载文件
     *
     * @param os
     * @param objectName
     * @throws IOException
     */
    public OSSObject exportOssFile(OutputStream os, String objectName) {
        OSS ossClient = null;
        try {
            // ossObject包含文件所在的存储空间名称、文件名称、文件元信息以及一个输入流。
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            return ossClient.getObject(ossProperties.getBucketNameErp(), objectName);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "exportOssFile", e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
        return null;
    }

    /**
     * 获得url链接
     *
     * @param key
     * @return
     */
    public String getUrl(String key) {
        OSS ossClient = null;
        // 设置URL过期时间为10年  3600l* 1000*24*365*10
        Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24);
        // 生成URL
        String bucketName = ossProperties.getBucketNameErp();
        try {
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            URL url = ossClient.generatePresignedUrl(bucketName, key, expiration);
            if (url != null) {
                return url.toString();
            }
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "getUrl", e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }
        return "";
    }

    /**
     * 拷贝文件
     *
     * @param fileDTO
     * @return
     */
    public CopyObjectResult copyObject(FileDTO fileDTO) {
        String bucketName = ossProperties.getBucketNameErp();
        String sourceCatalog = fileDTO.getSourceCatalog();
        String destinationCatalog = fileDTO.getDestinationCatalog();

        CopyObjectResult result = null;
        OSS ossClient = null;
        try {
            String sourceObjectName = getAbsoluteFilePath(fileDTO, sourceCatalog);
            String destinationObjectName = getAbsoluteFilePath(fileDTO, destinationCatalog);


            // 创建CopyObjectRequest对象。
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(bucketName, sourceObjectName, bucketName, destinationObjectName);

            // 设置新的文件元信息。
//            ObjectMetadata meta = new ObjectMetadata();
//            meta.setContentType(getContentType(fileName));
//            copyObjectRequest.setNewObjectMetadata(meta);
            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(), ossProperties.getAccessKeyId(), ossProperties.getAccessKeySecret());
            // 复制文件。
            result = ossClient.copyObject(copyObjectRequest);
            System.out.println("ETag: " + result.getETag() + " LastModified: " + result.getLastModified());
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "copyObject", e);
        } finally {
            if (ossClient != null) {
                // 关闭OSSClient。
                ossClient.shutdown();
            }
        }

        return result;
    }

    /**
     * 拿到品类文件夹的图片
     */
    public String getCategoryUrl(String url) {
        try {
            String categoryUrl = ERP_ABSOLUTE_URL + "category/" + this.getFolder(url);
            return this.getUrl(categoryUrl);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "getCategoryUrl", e);
        }
        return "";
    }

    public String getPorkOfferUrl(String fileName) {
        try {
            String categoryUrl = ERP_ABSOLUTE_URL + "porkPrice/" + this.getFolder(fileName);
            return this.getUrl(categoryUrl);
        } catch (Exception e) {
            log.error(this.getClass().getSimpleName() + "getCategoryUrl", e);
        }
        return "";
    }


    //私有方法==============

    private String getAbsoluteFilePath(FileDTO fileDTO, String catalog) {
        StringBuilder filepath = new StringBuilder();
        String fileName = fileDTO.getFileName();

        //系统
        String systemValue = OssEnum.getName(fileDTO.getSystem());
        if (!StrUtil.isBlank(systemValue)) {
            filepath.append(systemValue);
        }

        //项目
        String projectValue = OssEnum.getName(fileDTO.getProject());
        if (!StrUtil.isBlank(projectValue)) {
            filepath.append("/").append(projectValue);
        }
        //功能
        String categoryValue = OssEnum.getName(fileDTO.getCatalog());
        if (StrUtil.isNotBlank(categoryValue)) {
            filepath.append("/").append(categoryValue);
        }

        filepath.append("/").append(FileUtil.getFolder(fileName));


        return filepath.toString();
    }

    private static String getContentType(String FilenameExtension) {
        if (FilenameExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        }
        if (FilenameExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        }
        if (FilenameExtension.equalsIgnoreCase(".jpeg") ||
                FilenameExtension.equalsIgnoreCase(".jpg") ||
                FilenameExtension.equalsIgnoreCase(".png")) {
            return "image/jpg";
        }
        if (FilenameExtension.equalsIgnoreCase(".html")) {
            return "text/html";
        }
        if (FilenameExtension.equalsIgnoreCase(".txt")) {
            return "text/plain";
        }
        if (FilenameExtension.equalsIgnoreCase(".vsd")) {
            return "application/vnd.visio";
        }
        if (FilenameExtension.equalsIgnoreCase(".pptx") ||
                FilenameExtension.equalsIgnoreCase(".ppt")) {
            return "application/vnd.ms-powerpoint";
        }
        if (FilenameExtension.equalsIgnoreCase(".docx") ||
                FilenameExtension.equalsIgnoreCase(".doc")) {
            return "application/msword";
        }
        if (FilenameExtension.equalsIgnoreCase(".xml")) {
            return "text/xml";
        }
        if (FilenameExtension.equalsIgnoreCase(".pdf")) {
            return "application/pdf";
        }
        return "image/jpg";
    }

    /**
     * 生成文件名
     *
     * @param originalFilename
     * @param fileDTO
     * @return
     */
    private void generateFilePath(String originalFilename, FileDTO fileDTO) {

        StringBuilder filepath = new StringBuilder();

        //系统
        String systemValue = OssEnum.getName(fileDTO.getSystem());
        if (StrUtil.isBlank(systemValue)) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY, ResultCodeEnum.FILE_EMPTY.getMsg());
        }
        filepath.append(systemValue);

        //项目
        String projectValue = OssEnum.getName(fileDTO.getProject());
        if (StrUtil.isBlank(projectValue)) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY, ResultCodeEnum.FILE_EMPTY.getMsg());
        }
        filepath.append("/").append(projectValue);

        //功能
        String catalogValue = OssEnum.getName(fileDTO.getCatalog());
        if (StrUtil.isBlank(catalogValue)) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY, ResultCodeEnum.FILE_EMPTY.getMsg());
        }
        filepath.append("/").append(catalogValue);

        //文件名称
        String fileName = FileUtil.getFileName(originalFilename);
        String sourceFileName = DateFormatUtils.format(new Date(), "yyyy-MM-dd") + "/" + fileName;
        filepath.append("/").append(sourceFileName);


        //生成全称
        fileDTO.setFilepath(filepath.toString());
        fileDTO.setFileName(fileName);

    }

    /**
     * /2020-06-19-13-49-36-695e18ba13304.jpg
     * 资源在oss上面
     *
     * @param str
     * @return
     */
    private String getFolder(String str) {
        if (StrUtil.isNotBlank(str)) {
            return str.substring(0, 10) + "/" + str;
        }
        return str;
    }

}
