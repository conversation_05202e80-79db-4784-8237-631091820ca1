package com.navigator.common.util.html2pdf;

import lombok.extern.slf4j.Slf4j;

/**
 * Description: cmd服务类
 * Created by <PERSON><PERSON><PERSON> on 2021/11/24 19:02
 */
@Slf4j
public class CmdUtil {
    /**
     * 执行cmd命令
     *
     * @param cmdStr 命令字符串
     * @return 成功失败
     */
    public static boolean execute(String cmdStr) {
        // 利用Runtime输出流读取
        Runtime rt = Runtime.getRuntime();
        try {
            log.info("Command: {}", cmdStr);
            Process p = rt.exec(cmdStr);
            StreamGobblerUtil errorGobbler = new StreamGobblerUtil(p.getErrorStream(),
                    "ERROR");
            // 开启屏幕标准错误流
            errorGobbler.start();
            StreamGobblerUtil outGobbler = new StreamGobblerUtil(p.getInputStream(),
                    "STDOUT");
            // 开启屏幕标准输出流
            outGobbler.start();
            int w = p.waitFor();
            int v = p.exitValue();
            if (w == 0 && v == 0) {
                return true;
            }
        } catch (InterruptedException e){
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return false;
    }
}
