package com.navigator.common.util.file;

import com.microsoft.azure.storage.CloudStorageAccount;
import com.microsoft.azure.storage.SharedAccessAccountPolicy;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.blob.BlobInputStream;
import com.microsoft.azure.storage.blob.CloudBlobClient;
import com.microsoft.azure.storage.blob.CloudBlobContainer;
import com.microsoft.azure.storage.blob.CloudBlockBlob;
import com.navigator.common.config.properties.AzureBlobProperties;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.FileServiceTypeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Component
@RefreshScope
@Slf4j
public class AzureBlobUtil {
    @Resource
    private AzureBlobProperties azureBlobProperties;

    /**
     * 单文件上传
     * filePath:文件存储子路径 xxx/xxxx
     * contentType:文件打开格式{@link com.navigator.common.enums.BlobFileContextEnum}
     */
    public FileBaseInfoDTO upload(MultipartFile file, String filePath, String contentType) {
        if (file.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
        }
        log.info("Blob文件上传配置信息：" + FastJsonUtils.getBeanToJson(azureBlobProperties));
        Callable<FileBaseInfoDTO> task = new Callable<FileBaseInfoDTO>() {
            @Override
            public FileBaseInfoDTO call() throws Exception {
                String oldName = null;
                Long size = 0L;
                if (file != null) {
                    oldName = file.getOriginalFilename();
                    size = file.getSize();
                }
                //使用TmFileUtil文件上传工具获取文件的各种信息
                //优化文件大小
                String sizeString = readableFileSize(size);
                //获取文件后缀名
                String stuff = "";
                if (oldName != null)
                    stuff = getFileStuff(oldName);
                String uuid = UUID.randomUUID().toString().replaceAll("-", "");
                //文件最终上传的位置
                String newFileName = oldName == null ? "" : uuid + "_" + oldName.replaceAll("[\\s\\\\/:\\*\\?\\\"<>\\|]", "").replaceAll("%", "").replaceAll("#", "").replaceAll("@", "");

                CloudBlobClient blobClient = getBlobClient();
                String containPath = null;
                String url = null;
                if (StringUtils.isNotBlank(filePath)) {
                    containPath = getPreUrlWithFilePath(filePath);
                    url = filePath + "/" + newFileName;
                } else {
                    containPath = getPreUrl();
                    url = newFileName;
                }
                CloudBlockBlob blockBlob = createBlob(blobClient, newFileName, containPath);
                if (StringUtils.isNotBlank(contentType)) {
                    blockBlob.getProperties().setContentType(contentType);
                }
                if (BlobFileContextEnum.getAllViewTypeList().contains(stuff)) {
                    blockBlob.getProperties().setContentType(BlobFileContextEnum.getByTypeInfo(stuff).getFileType());
                }

                InputStream inputStream = null;
                if (null != file) {
                    inputStream = file.getInputStream();
                    blockBlob.upload(inputStream, file.getSize());
                    if (null != inputStream)
                        inputStream.close();
                }

                return new FileBaseInfoDTO().setAttachName(newFileName)
                        .setOriginalName(oldName)
                        .setAttachUrl(url)
                        .setSize(size.intValue())
                        .setSizeInfo(sizeString)
                        .setFsType(FileServiceTypeEnum.BLOB.getValue())
                        .setAttachStuff(stuff);
            }
        };
        return executeTask(task);
    }

    /**
     * 根据url地址上传
     * filePath:文件存储子路径 xxx/xxxx
     * fileName:全文件名 例:xx.pdf
     * contentType:文件打开格式{@link com.navigator.common.enums.BlobFileContextEnum}
     */
    public FileBaseInfoDTO uploadByUrl(String url, String filePath, String fileName, String contentType) {
        String errorMsg = "";
//        ZipUtil zipUtil = new ZipUtil();
//        InputStream inputStream = null;
//        try {
//            inputStream = zipUtil.getInputStreamFromURL(url);
//        } catch (IOException e) {
//            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL);
//        }
//        byte[] bytes = zipUtil.readInputStream(inputStream);
//        int length = bytes.length;
//        InputStream inputStream1 = new ByteArrayInputStream(bytes);
        byte[] fileBytes = FileDownloadUtil.downLoadFileBytesByUrl(url);
        if (null == fileBytes) {
            return null;
        }
        int length = fileBytes.length;
        InputStream inputStream1 = new ByteArrayInputStream(fileBytes);
        CloudBlobClient blobClient = getBlobClient();
        String containPath = null;
        if (StringUtils.isNotBlank(filePath)) {
            containPath = getPreUrlWithFilePath(filePath);
        } else {
            containPath = getPreUrl();
        }
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        //文件最终上传的位置
        String newFileName = uuid + "_" + fileName;
        CloudBlockBlob blockBlob = createBlob(blobClient, newFileName, containPath);
        if (StringUtils.isNotBlank(contentType)) {
            blockBlob.getProperties().setContentType(contentType);
        }
        try {
            blockBlob.upload(inputStream1, length);
            inputStream1.close();
        } catch (IOException | StorageException e) {
            errorMsg = "文件生成异常";
            log.error("签章文件存储失败:文件名:{},{}", fileName, e);
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        return new FileBaseInfoDTO()
                .setAttachName(newFileName)
                .setOriginalName(fileName)
                .setAttachUrl(filePath + uuid + "_" + fileName)
                .setSize(length)
                .setAttachStuff(FilePathType.PDF.getValue())
                .setErrorMsg(errorMsg)
                .setFsType(FileServiceTypeEnum.BLOB.getValue());
    }

    /**
     * 根据流上传
     * filePath:文件存储子路径 xxx/xxxx
     * fileName:全文件名 例:xx.pdf
     * contentType:文件打开格式{@link com.navigator.common.enums.BlobFileContextEnum}
     */
    public FileBaseInfoDTO uploadByInputStream(InputStream inputStream, String filePath, String fileName, String contentType) {
        ZipUtil zipUtil = new ZipUtil();
        byte[] bytes = zipUtil.readInputStream(inputStream);
        int fileSize = bytes.length;
        InputStream inputStream1 = new ByteArrayInputStream(bytes);
        CloudBlobClient blobClient = getBlobClient();
        String containPath = null;
        if (StringUtils.isNotBlank(filePath)) {
            containPath = getPreUrlWithFilePath(filePath);
        } else {
            containPath = getPreUrl();
        }
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        String newFileName = uuid + "_" + fileName;
        if (contentType.equals(BlobFileContextEnum.CSV.getFileType())) {
            newFileName = fileName;
        }
        CloudBlockBlob blockBlob = createBlob(blobClient, newFileName, containPath);
        if (StringUtils.isNotBlank(contentType)) {
            blockBlob.getProperties().setContentType(contentType);
        }
        try {
            blockBlob.upload(inputStream1, fileSize);
            inputStream1.close();
        } catch (StorageException | IOException e) {
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        String attachUrl = StringUtils.isNotBlank(filePath) ? filePath + "/" + newFileName : newFileName;
        String stuff = getFileStuff(fileName);
        return new FileBaseInfoDTO().setAttachName(newFileName)
                .setOriginalName(fileName)
                .setAttachUrl(attachUrl)
                .setFsType(FileServiceTypeEnum.BLOB.getValue())
                .setAttachStuff(stuff);
    }


    public FileBaseInfoDTO uploadWhiteByInputStream(InputStream inputStream, String filePath, String fileName, String contentType) {
        ZipUtil zipUtil = new ZipUtil();
        byte[] bytes = zipUtil.readInputStream(inputStream);
        int fileSize = bytes.length;
        InputStream inputStream1 = new ByteArrayInputStream(bytes);
        CloudBlobClient blobClient = getBlobClient();
        String containPath = null;
        if (StringUtils.isNotBlank(filePath)) {
            containPath = getPreUrlWithFilePath(filePath);
        } else {
            containPath = getPreUrl();
        }
        String newFileName = "white_" + fileName;
        CloudBlockBlob blockBlob = createBlob(blobClient, newFileName, containPath);
        if (StringUtils.isNotBlank(contentType)) {
            blockBlob.getProperties().setContentType(contentType);
        }
        try {
            blockBlob.upload(inputStream1, fileSize);
            inputStream1.close();
        } catch (StorageException | IOException e) {
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        String attachUrl = StringUtils.isNotBlank(filePath) ? filePath + "/" + newFileName : newFileName;
        String stuff = getFileStuff(fileName);
        return new FileBaseInfoDTO().setAttachName(newFileName)
                .setOriginalName(fileName)
                .setAttachUrl(attachUrl)
                .setFsType(FileServiceTypeEnum.BLOB.getValue())
                .setAttachStuff(stuff);
    }

    /**
     * 单文件下载
     * filePath:文件存储子路径 xxx/xxxx
     */
    public void download(String filePath, HttpServletResponse response) {
        CloudBlockBlob blockBlob1 = getCloudBlockBlob(filePath);
        String blobName = getBlobName(filePath);

        // 清空response
        response.reset();

        // 设置response的Header
        OutputStream outputStream = null;
        try {
            outputStream = new BufferedOutputStream(response.getOutputStream());
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(blobName.substring(blobName.indexOf("_") + 1), "UTF-8"));
            response.setContentType("application/octet-stream;charset=UTF-8");
        } catch (IOException e) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }

        //执行下载
        try {
            blockBlob1.download(outputStream);
        } catch (StorageException e) {
            response.reset();
            response.setContentType("application/json;charset=UTF-8");
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        try {
            outputStream.close();
        } catch (IOException e) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }

    }

    /**
     * 多文件打包成zip下载
     * filePathList: 文件次级路径集合
     * zipName:压缩文件名
     */
    public void downloadZip(List<String> filePathList, String zipName, HttpServletResponse response) {
        try {
            String downloadZipName = URLEncoder.encode(zipName, "UTF-8");
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ZipOutputStream zos = new ZipOutputStream(bos);
            ZipUtil zipUtil = new ZipUtil();
            String hostUrl = getHostUrl();
            List<String> fileNameList = new ArrayList<>();
            int i = 1;
            String sasToken = getSharedAccessSignature();
            for (String filePath : filePathList) {
                log.info("文件地址1" + filePath);
                String url = null;
                String fileName = null;
                int index = filePath.lastIndexOf("/");

                //判断路径层级
                if (index >= 0) {
                    String uFileName = filePath.substring(index + 1);
                    fileName = uFileName.substring(uFileName.indexOf("_") + 1);
                    String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
                    String pathDir = filePath.substring(0, index);
                    url = hostUrl + pathDir + "/" + fileNameEncode;
                } else {
                    fileName = filePath.substring(filePath.indexOf("_") + 1);
                    String fileNameEncode = URLEncoder.encode(filePath, "UTF-8");
                    url = hostUrl + fileNameEncode;
                    log.info("文件地址2" + url);
                }
                log.info("文件地址3" + url);
                //重命名相同名字文件
                if (fileNameList.contains(fileName)) {
                    int stuffIndex = fileName.lastIndexOf(".");
                    String stuff = fileName.substring(stuffIndex);
                    String name = fileName.substring(0, stuffIndex);
                    fileName = name + "(" + i + ")" + stuff;
                    i++;
                }
                fileNameList.add(fileName);
                zos.putNextEntry(new ZipEntry(fileName));
                log.info("文件地址4" + url);
//                byte[] bytes = zipUtil.getFileFromURL(url);
                byte[] bytes = FileDownloadUtil.downLoadFileBytesByUrl(url + sasToken);
                zos.write(bytes, 0, bytes.length);
                zos.closeEntry();
            }
            zos.close();
            // 设置强制下载不打开
            response.setContentType("application/force-download");

//            // 设置压缩包名
//            response.addHeader("Content-Disposition", "attachment;fileName=" + downloadZipName);
            InputStream inputStream = new ByteArrayInputStream(bos.toByteArray());
            File tempFile = new File(zipName);
            FileBaseInfoDTO zipBaseInfoDTO = uploadByInputStream(inputStream, tempFile.getParent(), tempFile.getName(), BlobFileContextEnum.ZIP.getFileType());
            this.download(zipBaseInfoDTO.getAttachUrl(), response);
//            OutputStream os = response.getOutputStream();
//            os.write(bos.toByteArray());
//            os.close();
        } catch (FileNotFoundException ex) {
            log.error("FileNotFoundException", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        } catch (Exception ex) {
            log.error("Exception", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
    }
//
//    public static void main(String[] args) {
//        try {
//
//            String filePath = "https://ldcblob.blob.core.chinacloudapi.cn/navigator/dev/fileRecord/upload/file/magellan/2022-08-03/c545a7ff20614be59d1762cb3171a28a_微信图片_20220718134443.jpg?ss=b&sig=yr4Sog54y41e%2FK1tYL2XAJ%2BsKOXBxkkf2mj8S7hU7Po%3D&st=2022-08-03T08%3A29%3A21Z&se=2022-08-06T04%3A00%3A00Z&sv=2019-02-02&srt=sco&sp=r&sr=sco";
//            log.info("文件地址1" + filePath);
//            String url = null;
//            String fileName = null;
//            String sasToken = filePath.substring(filePath.indexOf("?ss="));
//            log.info("sasToken" + sasToken);
//            int index = filePath.lastIndexOf("/");
//            log.info("index:"+index);
//
//            //判断路径层级
//            if (index >= 0) {
//                String uFileName = filePath.substring(index + 1, filePath.indexOf("?ss="));
//                log.info("uFileName" + uFileName);
//                String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
//                String pathDir = filePath.substring(0, index);
//                url = pathDir + "/" + fileNameEncode + sasToken;
//            } else {
//                fileName = filePath.substring(filePath.indexOf("_") + 1);
//                String fileNameEncode = URLEncoder.encode(filePath, "UTF-8");
//                url = fileNameEncode;
//                log.info("文件地址2" + url);
//            }
//            log.info("文件地址3" + url);
//            ZipUtil zipUtil = new ZipUtil();
//            byte[] bytes = zipUtil.getFileFromURL(url);
////            System.out.println(Arrays.toString(bytes));
//        } catch (Exception ex) {
//            log.error("Exception", ex);
//            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
//        }
//    }

    /**
     * 多文件打包成zip下载
     * filePathList: 文件次级路径集合
     * zipName:压缩文件名
     */
    public void downloadLocalZip(List<String> filePathList, String zipName, HttpServletResponse response) {
        try {
            String downloadZipName = URLEncoder.encode(zipName, "UTF-8");
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ZipOutputStream zos = new ZipOutputStream(bos);
            ZipUtil zipUtil = new ZipUtil();
            String hostUrl = getHostUrl();
            List<String> fileNameList = new ArrayList<>();
            int i = 1;
            for (String filePath : filePathList) {
                String url = null;
                String fileName = null;
                int index = filePath.lastIndexOf("/");

                //判断路径层级
                if (index >= 0) {
                    String uFileName = filePath.substring(index);
                    fileName = uFileName.substring(uFileName.indexOf("_") + 1);
                    String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
                    String pathDir = filePath.substring(0, index);
                    url = hostUrl + pathDir + "/" + fileNameEncode;
                } else {
                    fileName = filePath.substring(filePath.indexOf("_") + 1);
                    String fileNameEncode = URLEncoder.encode(filePath, "UTF-8");
                    url = hostUrl + fileNameEncode;
                }

                //重命名相同名字文件
                if (fileNameList.contains(fileName)) {
                    int stuffIndex = fileName.lastIndexOf(".");
                    String stuff = fileName.substring(stuffIndex);
                    String name = fileName.substring(0, stuffIndex);
                    fileName = name + "(" + i + ")" + stuff;
                    i++;
                }
                fileNameList.add(fileName);
                zos.putNextEntry(new ZipEntry(fileName));
                byte[] bytes = zipUtil.getFileFromURL(url);
                zos.write(bytes, 0, bytes.length);
                zos.closeEntry();
            }
            zos.close();
            // 设置强制下载不打开
            response.setContentType("application/force-download");

            // 设置压缩包名
            response.addHeader("Content-Disposition", "attachment;fileName=" + downloadZipName);
            OutputStream os = response.getOutputStream();
            os.write(bos.toByteArray());
            os.close();
        } catch (FileNotFoundException ex) {
            log.error("FileNotFoundException", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        } catch (Exception ex) {
            log.error("Exception", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
    }

    @NotNull
    private String getBlobName(String filePath) {
        int index = filePath.lastIndexOf("/");
        String uBlobName;
        String blobName;
        if (index >= 0) {
            uBlobName = filePath.substring(index + 1);
        } else {
            uBlobName = filePath;
        }
        blobName = uBlobName.substring(uBlobName.indexOf("_") + 1);
        return blobName;
    }


    /**
     * 单文件删除
     */
    public void delete(String filePath) {
        CloudBlockBlob blockBlob1 = getCloudBlockBlob(filePath);
        try {
            blockBlob1.deleteIfExists();
        } catch (StorageException e) {
            throw new BusinessException(ResultCodeEnum.DELETE_FAIL);
        }
    }

    /**
     * 获取块blob对象
     */
    @NotNull
    private CloudBlockBlob getCloudBlockBlob(String filePath) {
        CloudBlobClient blobClient = getBlobClient();
        int index = filePath.lastIndexOf("/");
        String uBlobName;
        String dir;
        if (index >= 0) {
            String pathDir = filePath.substring(0, index);
            dir = getPreUrlWithFilePath(pathDir);
            uBlobName = filePath.substring(index + 1);
        } else {
            dir = getPreUrl();
            uBlobName = filePath;
        }
        return createBlob(blobClient, uBlobName, dir);
    }

    @NotNull
    private CloudBlockBlob createBlob(CloudBlobClient blobClient, String blobName, String containPath) {
        CloudBlobContainer container = null;
        CloudBlockBlob cloudBlockBlob;
        try {
            container = blobClient.getContainerReference(containPath);
            cloudBlockBlob = container.getBlockBlobReference(blobName);
        } catch (URISyntaxException | StorageException e) {
            throw new BusinessException(ResultCodeEnum.FILE_SERVER_ERROR);
        }
        return cloudBlockBlob;
    }

    /**
     * 获取文件访问url域名路径
     */
    public String getHostUrl() {
        String host = azureBlobProperties.getHost();
        String env = azureBlobProperties.getEnv();
        String containName = azureBlobProperties.getContainName();
        return host + "/" + containName + "/" + env + "/";
    }


    private FileBaseInfoDTO executeTask(Callable<FileBaseInfoDTO> task) {
        ExecutorService executorService = Executors.newFixedThreadPool(3);
        Future<FileBaseInfoDTO> future = executorService.submit(task);
        try {
            //设置超时时间
            return future.get(3, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("Interrupted!", e);
            Thread.currentThread().interrupt();
            throw new BusinessException(e);
        } catch (TimeoutException e) {
            throw new BusinessException(ResultCodeEnum.FILE_UPLOAD_FAIL);
        } catch (Exception e) {
            throw new BusinessException(e);
        } finally {
            executorService.shutdown();
        }
    }

    private CloudBlobClient getBlobClient() {
        String secretKey = getSecretKey();
        CloudStorageAccount storageAccount = null;
        try {
            storageAccount = CloudStorageAccount.parse(secretKey);
            return storageAccount.createCloudBlobClient();
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.FILE_SERVER_ERROR);
        }
    }

    public String getSharedAccessSignature() {
        String secretKey = getSecretKey();
        CloudStorageAccount storageAccount = null;
        try {
            storageAccount = CloudStorageAccount.parse(secretKey);
            SharedAccessAccountPolicy sharedAccessAccountPolicy = new SharedAccessAccountPolicy();
            sharedAccessAccountPolicy.setSharedAccessStartTime(new Date());
            sharedAccessAccountPolicy.setSharedAccessExpiryTime(DateTimeUtil.addRealDays(3));
            log.info("72hours question the start hour is {},end hour is {}"
                    , sharedAccessAccountPolicy.getSharedAccessStartTime().getHours(),
                    sharedAccessAccountPolicy.getSharedAccessExpiryTime().getHours());
            sharedAccessAccountPolicy.setResourceTypeFromString("soc");
            sharedAccessAccountPolicy.setPermissionsFromString("r");
            sharedAccessAccountPolicy.setServiceFromString("b");
            String signature = "?" + storageAccount.generateSharedAccessSignature(sharedAccessAccountPolicy);
            log.info("72hours question the signature is {}", signature);
            return signature;
        } catch (Exception e) {
            throw new BusinessException(ResultCodeEnum.FILE_SERVER_ERROR);
        }
    }

    private String getSecretKey() {
        return "DefaultEndpointsProtocol=https;" +
                "AccountName=" + azureBlobProperties.getAccountName() + ";AccountKey=" + azureBlobProperties.getAccountKey() + ";EndpointSuffix=core.chinacloudapi.cn";
    }

    /**
     * 文件字节数转换为文件大小
     *
     * @param size 文件字节数
     * @return 换算的文件大小结果（B、KB、MB）
     */
    private static String readableFileSize(long size) {
        if (size <= 0) {
            return "0";
        }
        final String[] units = new String[]{"B", "kB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.##").format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    /**
     * 获取文件后缀名
     *
     * @param oldName
     * @return
     */
    private static String getFileStuff(String oldName) {
        return oldName == null ? "" : oldName.substring(oldName.lastIndexOf(".") + 1);
    }

    private String getPreUrl() {
        return azureBlobProperties.getContainName() + "/" + azureBlobProperties.getEnv();
    }

    private String getPreUrlWithFilePath(String filePath) {
        return azureBlobProperties.getContainName() + "/" + azureBlobProperties.getEnv() + "/" + filePath;
    }

    //    /**
//     * 查询文件列表
//     *
//     */
//    public List<String> blobList(String extraPath) throws URISyntaxException, StorageException {
//        CloudBlobClient blobClient = getBlobClient();
//        String filePath = getPreUrlWithFilePath(extraPath);
//        CloudBlobContainer container = blobClient.getContainerReference(filePath);
//        List<String> list = new ArrayList<>();
//        for (ListBlobItem blobItem : container.listBlobs()) {
//            String path = blobItem.getUri().getPath();
//            String substring = path.substring(path.lastIndexOf("/") + 1);
//            list.add(substring);
//        }
//        return list;
//    }


//    /**
//     * 查询container列表
//     */
//    public List<String> listContainers(String prefix) {
//        List<String> containersList = new ArrayList<>();
//        CloudBlobClient blobClient = getBlobClient();
//        for (final CloudBlobContainer container : blobClient.listContainers(prefix)) {
//            containersList.add(container.getName());
//        }
//        return containersList;
//    }

    /**
     * 将csv文件转化为文件流
     *
     * @param filePath fileRecord/export/contract/2022-07-13/
     * @param blobName 023f0452af604744afa2bee578e5a2a4_20220713_180903767.csv
     * @return
     */
    public InputStreamReader csvFileToStream(String filePath, String blobName) {
        String containPath;
        if (StringUtils.isNotBlank(filePath)) {
            containPath = getPreUrlWithFilePath(filePath);
        } else {
            containPath = getPreUrl();
        }
        CloudBlobClient blobClient = getBlobClient();
        InputStreamReader inputStreamReader = null;
        BlobInputStream blobInputStream = null;
        try {
            CloudBlobContainer container = blobClient.getContainerReference(containPath);
            CloudBlockBlob blockBlob = container.getBlockBlobReference(blobName);
            blobInputStream = blockBlob.openInputStream();
            inputStreamReader = new InputStreamReader(blobInputStream, "UTF-8");
        } catch (Exception e) {
            log.info("blob找不到对应的文件：" + filePath + blobName);
        }/* finally {
            if (null != blobInputStream) {
                try {
                    blobInputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }*/
        return inputStreamReader;
    }

//    public static void main(String[] args) {
//        String url = "https://wesign-prod-r.signit.vip:10443/WSID_LINK_00000180a8d380f54a1eb5bdb07d0001/download-file?token=9de8292b0400423fb876e67f7b7414fd";
//        String filePath = "fileRecord/generate/contract\\pdf\\2022-05-09\\5622\\";
//        String fileName = "TJIBSBMS2203516_电子合同（客户签章）.pdf";
//        String contentType = BlobFileContextEnum.PDF.getFileType();
//        AzureBlobUtil azureBlobUtil = new AzureBlobUtil();
//        azureBlobUtil.uploadByUrl(url, filePath, fileName, contentType);
//    }

    public FileBaseInfoDTO uploadWhiteMark(MultipartFile file, String filePath, String contentType, InputStream inputStream, String fileName) {
        if (file.isEmpty()) {
            throw new BusinessException(ResultCodeEnum.FILE_EMPTY);
        }
        Callable<FileBaseInfoDTO> task = new Callable<FileBaseInfoDTO>() {
            @Override
            public FileBaseInfoDTO call() throws Exception {
                String oldName = null;
                Long size = 0L;
                if (file != null) {
                    oldName = file.getOriginalFilename();
                    size = file.getSize();
                }
                //使用TmFileUtil文件上传工具获取文件的各种信息
                //优化文件大小
                String sizeString = readableFileSize(size);
                //获取文件后缀名
                String stuff = "";
                if (oldName != null)
                    stuff = getFileStuff(oldName);
                //文件最终上传的位置
                //String newFileName = oldName == null ? "" : "white_"+uuid + "_" + oldName.replaceAll("[\\s\\\\/:\\*\\?\\\"<>\\|]", "").replaceAll("%", "").replaceAll("#", "").replaceAll("@", "");
                String newFileName = "white_" + fileName;

                CloudBlobClient blobClient = getBlobClient();
                String containPath = null;
                String url = null;
                if (StringUtils.isNotBlank(filePath)) {
                    containPath = getPreUrlWithFilePath(filePath);
                    url = filePath + "/" + newFileName;
                } else {
                    containPath = getPreUrl();
                    url = newFileName;
                }
                CloudBlockBlob blockBlob = createBlob(blobClient, newFileName, containPath);
                if (StringUtils.isNotBlank(contentType)) {
                    blockBlob.getProperties().setContentType(contentType);
                }
                if (BlobFileContextEnum.getAllViewTypeList().contains(stuff)) {
                    blockBlob.getProperties().setContentType(BlobFileContextEnum.getByTypeInfo(stuff).getFileType());
                }

                blockBlob.upload(inputStream, file.getSize());
                inputStream.close();

//                return new FileBaseInfoDTO().setAttachName(newFileName)
//                        .setOriginalName(oldName)
//                        .setAttachUrl(url)
//                        .setSize(size.intValue())
//                        .setSizeInfo(sizeString)
//                        .setFsType(FileServiceTypeEnum.BLOB.getValue())
//                        .setAttachStuff(stuff);
                return null;
            }

        };
        return executeTask(task);
    }

    /**
     * 下载合同关闭容器的文件
     *
     * @param filePath 文件路径
     * @param response 请求
     */
    public void downloadCloseFile(String filePath, HttpServletResponse response) {
        String blobName = getBlobName(filePath);
        response.reset();

        try (OutputStream outputStream = new BufferedOutputStream(response.getOutputStream())) {
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(blobName.substring(blobName.indexOf("_") + 1), "UTF-8"));
            response.setContentType("application/octet-stream;charset=UTF-8");

            getCloseBlockBlob(filePath).download(outputStream);
        } catch (IOException | StorageException e) {
            response.reset();
            response.setContentType("application/json;charset=UTF-8");
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
    }

    /**
     * 获取关闭容器的文件流
     *
     * @param filePath 文件路径
     * @return 文件流
     */
    public InputStreamReader closeCsvFileToStream(String filePath) {
        if (StringUtils.isNotBlank(filePath)) {
            try {
                BlobInputStream blobInputStream = getCloseBlockBlob(filePath).openInputStream();

                return new InputStreamReader(blobInputStream, "UTF-8");
            } catch (Exception e) {
                log.info("blob找不到对应的文件：" + filePath);
            }
        }
        return null;
    }

    @NotNull
    private CloudBlockBlob getCloseBlockBlob(String filePath) {
        CloudBlobClient blobClient = getBlobClient();
        int index = filePath.lastIndexOf("/");
        String dir = azureBlobProperties.getCloseContainName();
        String uBlobName = (index >= 0) ? filePath.substring(index + 1) : filePath;

        return createBlob(blobClient, uBlobName, dir);
    }
}
