package com.navigator.common.util.http;

import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.Map;

@Slf4j
@Component
public class OkHttpUtil {
    @Resource
    private OkHttpClient okHttpClient;

    /**
     * POST异步JSON请求
     *
     * @param url      请求地址
     * @param body     请求体
     * @param headers  请求头
     * @param callback 回调接口
     */
    public void postJsonAsync(String url, String body, Map<String, String> headers, Callback callback) {
        Request.Builder builder = new Request.Builder()
                .url(url);
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                builder.addHeader(key, headers.get(key));
            }
        }
        if (StringUtils.isNotEmpty(body)) {
            builder.post(RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), body));
        }
        Request request = builder.build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(callback);
    }

    public String postJsonWithHeader(String url, String body, Map<String, Object> headers) throws IOException {
        Request.Builder builder = new Request.Builder()
                .url(url);
        if (StringUtils.isNotEmpty(body)) {
            builder.post(RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), body));
        }
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                builder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = builder.build();
        return executeRequestUrl(request);
    }

    public String executeRequestUrl(Request request) throws IOException {
        Call call = this.okHttpClient.newCall(request);
        Response response = call.execute();
        String result = response.body().string();
        response.body().close();
        return result;
    }

    public String postJson(String url, String json) throws IOException {
        RequestBody requestBody = FormBody.create(MediaType.parse("application/json;charset=utf-8"), json);
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        return executeRequestUrl(request);
    }

    /**
     * POST请求表单参数
     *
     * @param url     请求地址
     * @param headers 请求头
     * @param params  请求参数
     * @return
     * @throws IOException
     */
    public String postFormByHeader(String url, Map<String, Object> headers, Map<String, Object> params) throws IOException {


        FormBody.Builder formBuilder = new FormBody.Builder();
        for (String key : params.keySet()) {
            formBuilder.add(key, String.valueOf(params.get(key)));
        }
        RequestBody requestBody = formBuilder.build();
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-type", "application/x-www-form-urlencoded");
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = requestBuilder.build();
        return executeRequestUrl(request);
    }

    /**
     * POST同步请求表单参数
     *
     * @param url    请求地址
     * @param params 请求参数
     * @return
     * @throws IOException
     */
    public String postFormUrlencoded(String url, Map<String, Object> params) throws IOException {
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key, String.valueOf(params.get(key)));
        }
        RequestBody requestBody = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-type", "application/x-www-form-urlencoded")
                .build();
        return executeRequestUrl(request);
    }

    /**
     * POST异步表单参数胡请求
     *
     * @param url      请求地址
     * @param params   请求参数
     * @param callback 异步回调接口
     */
    public void postFormAsync(String url, Map<String, String> params, Callback callback) {
        FormBody.Builder builder = new FormBody.Builder();
        for (String key : params.keySet()) {
            builder.add(key, params.get(key));
        }
        RequestBody requestBody = builder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .addHeader("Content-type", "application/x-www-form-urlencoded")
                .build();
        Call call = okHttpClient.newCall(request);
        call.enqueue(callback);
    }

    /**
     * GET不带参请求
     *
     * @param url
     * @return
     * @throws IOException
     */
    public Response getWithHeader(String url, Map<String, Object> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url);
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = requestBuilder.build();
//        return executeRequestUrl(request);
        Call call = this.okHttpClient.newCall(request);
        return call.execute();
    }

    /**
     * GET不带参请求
     *
     * @param url
     * @return
     * @throws IOException
     */
    public Response getResponseByHeader(String url, Map<String, Object> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url);
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = requestBuilder.build();
        Call call = this.okHttpClient.newCall(request);
        return call.execute();
    }
    /**
     * GET带参请求
     *
     * @param url
     * @return
     * @throws IOException
     */
    /*public Response getResponseByHeader(String url, Map<String, Object> headers,String body) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url);
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        if (StringUtils.isNotEmpty(body)) {
            requestBuilder.get(RequestBody.create(MediaType.parse("application/json;charset=UTF-8"), body));
        }
        Request request = requestBuilder.build();
        Call call = this.okHttpClient.newCall(request);
        return call.execute();
    }*/

    /**
     * GET不带参请求
     *
     * @param url
     * @return
     * @throws IOException
     */
    public String get(String url) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .build();
        return executeRequestUrl(request);
    }

    /**
     * POST请求表单参数
     *
     * @param url     请求地址
     * @param headers 请求头
     * @param file    请求参数
     * @return
     * @throws IOException
     */
    public String postFormDataByHeader(String url, Map<String, Object> headers, Map<String, Object> params, File file) throws IOException {

        MultipartBody.Builder requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM);
        RequestBody body = RequestBody.create(MediaType.parse("pdf/*"), file);
        requestBody.addFormDataPart("file", file.getName(), body);
        for (String key : params.keySet()) {
            requestBody.addFormDataPart(key, String.valueOf(params.get(key)));
        }
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody.build());
//                .addHeader("Content-type", "application/x-www-form-urlencoded");
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = requestBuilder.build();
        return executeRequestUrl(request);
    }

    /**
     * POST请求表单参数
     *
     * @param url     请求地址
     * @param headers 请求头
     * @return
     * @throws IOException
     */
    public String postFormDataInfoByHeader(String url, Map<String, Object> headers, Map<String, Object> params) throws IOException {

        MultipartBody.Builder requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM);
        if (!CollectionUtils.isEmpty(params)) {
            for (String key : params.keySet()) {
                requestBody.addFormDataPart(key, String.valueOf(params.get(key)));
            }
        }
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody.build());
//                .addHeader("Content-type", "application/x-www-form-urlencoded");
        if (!CollectionUtils.isEmpty(headers)) {
            for (String key : headers.keySet()) {
                requestBuilder.addHeader(key, String.valueOf(headers.get(key)));
            }
        }
        Request request = requestBuilder.build();
        return executeRequestUrl(request);
    }

    public String postXmlWithHeader(String url, String body) throws IOException {
        Request.Builder builder = new Request.Builder()
                .url(url);
        if (StringUtils.isNotEmpty(body)) {
            builder.post(RequestBody.create(MediaType.parse("application/xml;charset=GBK"), body));
        }
        Request request = builder.build();
        return executeRequestUrl(request);
    }


    /**
     * get请求 传输body参数
     *
     * @param url      请求地址
     * @param param    请求参数
     * @param headers  请求头
     * @param encoding 字符格式 UTF-8
     * @return
     * @throws Exception
     */
    public String sendJsonByGetReq(String url, String param, Map<String, Object> headers, String encoding) throws Exception {

        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = null;

        try {
            client = HttpClients.createDefault();

            HttpGetWithEntity httpGetWithEntity = new HttpGetWithEntity(url);
            HttpEntity httpEntity = new StringEntity(param, ContentType.APPLICATION_JSON);
            httpGetWithEntity.setEntity(httpEntity);

            if (!headers.isEmpty()) {
                for (String key : headers.keySet()) {
                    httpGetWithEntity.addHeader(key, String.valueOf(headers.get(key)));
                }
            }

            //执行请求操作，并拿到结果（同步阻塞）
            CloseableHttpResponse response = client.execute(httpGetWithEntity);
            //获取结果实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                //按指定编码转换结果实体为String类型
                body = EntityUtils.toString(entity, encoding);
            }
            //释放链接
            response.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (client != null)
                client.close();
            client = null;
        }
        return body;
    }


    /**
     * post 传输body参数
     *
     * @param url      请求地址
     * @param param    请求参数
     * @param headers  请求头
     * @param encoding 字符格式 UTF-8
     * @return
     * @throws Exception
     */
    public String sendJsonByPostReq(String url, String param, Map<String, Object> headers, String encoding) throws Exception {

        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = null;
        try {
            client = HttpClients.createDefault();

            HttpPost httpPost = new HttpPost(url);
            HttpEntity httpEntity = new StringEntity(param, ContentType.APPLICATION_JSON);
            httpPost.setEntity(httpEntity);

            if (!headers.isEmpty()) {
                for (String key : headers.keySet()) {
                    httpPost.addHeader(key, String.valueOf(headers.get(key)));
                }
            }

            //执行请求操作，并拿到结果（同步阻塞）
            CloseableHttpResponse response = client.execute(httpPost);
            //获取结果实体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                //按指定编码转换结果实体为String类型
                body = EntityUtils.toString(entity, encoding);
            }
            //释放链接
            response.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (client != null)
                client.close();
            client = null;
        }

        return body;
    }

}
