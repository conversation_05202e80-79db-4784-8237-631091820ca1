package com.navigator.common.util;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FastJsonUtils {


    /**
     * 功能描述：把JSON数据转换成指定的java对象
     *
     * @param jsonData JSON数据
     * @param clazz    指定的java对象
     * @return 指定的java对象
     */
    public static <T> T getJsonToBean(String jsonData, Class<T> clazz) {
        return JSON.parseObject(jsonData, clazz);
    }

    /**
     * 功能描述：把java对象转换成JSON数据
     *
     * @param object java对象
     * @return JSON数据
     */
    public static String getBeanToJson(Object object) {
        return JSON.toJSONString(object);
    }

    /**
     * 功能描述：把JSON数据转换成指定的java对象列表
     *
     * @param jsonData JSON数据
     * @param clazz    指定的java对象
     * @return List<T>
     */
    public static <T> List<T> getJsonToList(String jsonData, Class<T> clazz) {
        return JSON.parseArray(jsonData, clazz);
    }

    /**
     * 功能描述：把JSON数据转换成较为复杂的List<Map<String, Object>>
     *
     * @param jsonData JSON数据
     * @return List<Map < String, Object>>
     */
    public static List<Map<String, Object>> getJsonToListMap(String jsonData) {
        return JSON.parseObject(jsonData, new TypeReference<List<Map<String, Object>>>() {
        });
    }

    /**
     * 功能描述：把JSON数据转换成较为复杂的Map<String, Object>
     *
     * @param jsonData JSON数据
     * @return List<Map < String, Object>>
     */
    public static Map<String, Object> getJsonToMap(String jsonData) {
        return JSON.parseObject(jsonData);
    }

    /**
     * 功能描述：把单个字段转成JSON字符串
     *
     * @param propertyName  属性名称
     * @param propertyValue 属性值
     * @return JSON字符串
     */
    public static String getPropertyToJson(String propertyName, String propertyValue) {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(propertyName, propertyValue);
        return JSONUtil.toJsonStr(dataMap);
    }

}
