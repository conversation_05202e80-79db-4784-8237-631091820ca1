package com.navigator.common.util.html2pdf;


import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.lang3.RandomStringUtils;

import java.io.File;

/**
 * <p>
 * 文件路径工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
public class FilePathUtil {

    /**
     * 生成文件相对路径
     * 规则：{通用目录}/{合同号}_{类型}_uuid.html
     *
     * @param contractId       合同Id
     * @param contractCode     合同编号
     * @param fileCategoryType 文件文本类型：电子合同（原件）
     * @param filePathType     文件类型：html、pdf
     * @return
     */
    public static String genFileRelativePath(String contractId, String contractCode, String fileCategoryType, FilePathType filePathType) {
        return getCommonFilePath(ModuleTypeEnum.CONTRACT, filePathType, contractId)
                + genContractFileName(contractCode, fileCategoryType, filePathType.getValue());
    }

    /**
     * 生成文件相对路径
     * 规则：{通用目录}/{合同号}_{类型}_uuid.html
     *
     * @param contractId       合同Id
     * @param contractCode     合同编号
     * @param fileCategoryType 文件文本类型：电子合同（原件）
     * @param filePathType     文件类型：html、pdf
     * @return
     */
    public static String genHuskyFileRelativePath(String contractId, String contractCode, String fileCategoryType, FilePathType filePathType) {
        return getCommonFilePath(ModuleTypeEnum.CONTRACT, filePathType, contractId)
                + genContractFileName(contractCode + "-husky", fileCategoryType, filePathType.getValue());
    }

    /**
     * 生成文件名称
     *
     * @param contractCode     合同code
     * @param fileCategoryType 文件分类
     * @param fileSuffix       文件后缀
     * @return
     */
    public static String genContractFileName(String contractCode, String fileCategoryType, String fileSuffix) {
        return contractCode + "-" + fileCategoryType + "-"
                + RandomStringUtils.random(5, false, true)
                + "." + fileSuffix;
    }

    /**
     * 易企签签章完成生成文件名称
     *
     * @param contractCode     合同code
     * @param fileCategoryType 文件分类
     * @param fileSuffix       文件后缀
     * @return
     */
    public static String genContractSignatureFileName(String contractCode, String fileCategoryType, String fileSuffix) {
        return contractCode + "_" + fileCategoryType + "." + fileSuffix;
    }

    /**
     * 生成Pdf文件名称
     *
     * @param bizId    业务ID
     * @param fileName 文件名称
     * @return
     */
    public static String getPdfPath(String bizId, String fileName) {
        return getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.PDF, bizId) + fileName;
    }

    /**
     * 获取通用文件路径
     *
     * @param moduleTypeEnum 业务模块
     * @param filePathType   文件类型（pdf、html、zip）
     * @param bizId          (业务ID)
     * @return
     */
    public static String getCommonFilePath(ModuleTypeEnum moduleTypeEnum, FilePathType filePathType, String bizId) {
        return FileConstant.FILE_GENERATE + moduleTypeEnum.getModule() + File.separator + filePathType.getValue() +
                File.separator + DateTimeUtil.formatDateString() + File.separator + bizId + File.separator;
    }


    /**
     * 生成文件相对路径
     * 规则：{通用目录}/{合同号}_{类型}_uuid.html
     *
     * @param filePathType 文件类型：html、pdf
     * @return
     */
    public static String genLoginPath(FilePathType filePathType, String userId) {
        return getCommonFilePath(ModuleTypeEnum.LOGIN, filePathType, userId)
                + genLoginFileName(userId, filePathType.getValue());
    }

    /**
     * 生成文件名称
     *
     * @param fileSuffix 文件后缀
     * @return
     */
    public static String genLoginFileName(String userId, String fileSuffix) {
        return userId + "_"
                + RandomStringUtils.random(5, false, true)
                + "." + fileSuffix;
    }

    /**
     * 生成Pdf文件名称
     *
     * @param bizId    业务ID
     * @param fileName 文件名称
     * @return
     */
    public static String getLoginPdfPath(String bizId, String fileName) {
        return getCommonFilePath(ModuleTypeEnum.LOGIN, FilePathType.PDF, bizId) + fileName;
    }
}
