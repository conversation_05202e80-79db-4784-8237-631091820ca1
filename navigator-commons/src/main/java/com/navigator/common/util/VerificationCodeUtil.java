package com.navigator.common.util;

import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.MessageFormDTO;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import static java.lang.Boolean.TRUE;

@Slf4j
@Component
public class VerificationCodeUtil {

    @Resource
    RedisUtil redisUtil;
    @Resource
    private MessageUtils messageUtils;
    @Value("${sms.app_id}")
    private Integer appId;
    @Value("${sms.app_key}")
    private String appKey;
    @Value("${sms.sms_sign}")
    private String smsSign;
    @Value("${sms.nation_code}")
    private String nationCode;
    @Value("${sms.magellan_login_templateId}")
    private Integer magellanLoginTemplateId;
    @Resource
    private CommonProperties commonProperties;

    public String sendVerificationCode(String mobileNo, String sendPreCode, String preCode) {
        //1.校验手机号码
//        if (!PhoneVerifyUtil.isPhoneLegal(mobileNo)) {
//            throw new BusinessException(ResultCodeEnum.PHONE_ERROR);
//        }
        //2.判读是否重复获取验证码
        String codeSendKey = sendPreCode + mobileNo;
        String sendCode = redisUtil.getString(codeSendKey);
        if (StringUtils.isNotBlank(sendCode)) {
            throw new BusinessException(ResultCodeEnum.VERIFY_CODE_REPEATED);
        }
        //生成随机验证码
        String verificationCode = CodeGeneratorUtil.genVerificationCode(6);
        String key = preCode + mobileNo;
        log.info("手机号:{},验证码:{},key值:{}", mobileNo, verificationCode, key);
        // 发送短信验证码-开启短信验证
        if (commonProperties.getCaptcha() != null && commonProperties.getCaptcha()) {
            sendVerificationCodeMessage(mobileNo, verificationCode);
        }
        log.info("-----------------验证码发送完成");
        redisUtil.set(codeSendKey, mobileNo, RedisConstants.USER_CODE_TIME_SEND);
        redisUtil.set(key, verificationCode, RedisConstants.USER_CODE_TIME);
        return verificationCode;
    }

    /**
     * 发送短信验证码
     *
     * @param mobileNo
     * @param verificationCode
     */
    private void sendVerificationCodeMessage(String mobileNo, String verificationCode) {
        try {
            log.info("--------------appId:" + appId.toString());
            log.info("--------------appKey:" + appKey);
            log.info("--------------nationCode:" + nationCode);
            log.info("--------------smsSign:" + smsSign);

            MessageFormDTO messageForm = new MessageFormDTO()
                    .setAppId(appId)
                    .setAppKey(appKey)
                    .setNationCode(nationCode)
                    .setSmsSign(smsSign)
//                    .setTemplateId(RedisConstants.SMS_REGISTER_CODE_TEMPLATE_ID)
                    .setTemplateId(magellanLoginTemplateId)
                    .setMobiles(mobileNo)
                    .setCaptcha(verificationCode);
            messageUtils.sendMessage(TRUE, messageForm);
        } catch (Exception e) {
            log.warn(e.toString());
        }
    }
}
