package com.navigator.common.util.http;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OkSslUtil {



   public static HostnameVerifier hv = new HostnameVerifier() {
        @Override
        public boolean verify(String urlHostName, SSLSession session) {
            System.out.println("Warning: URL Host: " + urlHostName + " vs. "
                    + session.getPeerHost());
            return true;
        }

    };

    public static void trustAllHttpsCertificates() throws Exception {
        TrustManager[] trustAllCerts = new TrustManager[1];
        trustAllCerts[0] = (TrustManager) new TrustAllManager();
        String agreement = "SSL";
        SSLContext sc = SSLContext.getInstance(agreement);
        sc.init(null, trustAllCerts, null);
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());

    }
    private static class TrustAllManager implements X509TrustManager {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return null;
        }
        @Override
        public void checkServerTrusted(X509Certificate[] certs, String authType) throws CertificateException{
            if (certs != null && authType.equals("chain")) {
                throw new CertificateException();
            } else {
                int i = 1;
            }
        }
        @Override
        public void checkClientTrusted(X509Certificate[] certs, String authType) throws CertificateException{
            if (certs != null && authType.equals("chain")) {
                throw new CertificateException();
            } else {
                int i = 1;
            }
        }
    }


}
