package com.navigator.common.util;

import com.github.qcloudsms.SmsMultiSender;
import com.github.qcloudsms.SmsResultBase;
import com.github.qcloudsms.SmsSingleSender;
import com.github.qcloudsms.httpclient.HTTPException;
import com.navigator.common.dto.MessageFormDTO;
import com.navigator.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * <AUTHOR>
 * @since 2020-06-08 14:30
 */
@Service
@Slf4j
public class MessageUtils {
    private static final Logger logger = LoggerFactory.getLogger(MessageUtils.class);

//    /**
//     * 发送短信
//     *
//     * @param isSingle     是否单发
//     * @param templateId   模板ID
//     * @param mobileNoList 发短信用户手机号
//     * @param captchaList  发送拼接内容
//     * @return 短信发送结果
//     * @throws Exception
//     */
//    public SmsResultBase sendMessageInfo(Boolean isSingle, Integer templateId,
//                                        List<String> mobileNoList, List<String> captchaList) throws Exception {
//        MessageFormDTO messageForm = new MessageFormDTO()
//                .setAppId(appId)
//                .setAppKey(appKey)
//                .setNationCode(nationCode)
//                .setSmsSign(smsSign)
//                .setTemplateId(templateId)
//                .setMobiles(StringUtils.join(mobileNoList, ";"))
//                .setCaptcha(StringUtils.join(captchaList, ";"));
//        return this.sendMessage(isSingle, messageForm);
//    }

    /**
     * 按模板发送短信 支持单发和群发
     *
     * @param isSingle 是否单发 true: 单发，false: 群发
     * @param form     需要发送的短信内容及收信人手机号
     * @throws Exception 发送失败时捕获的异常信息
     */
    public SmsResultBase sendMessage(Boolean isSingle, MessageFormDTO form) throws Exception {
        log.info(form.getAppId() + form.getAppKey());
        String regex = ";";
        String[] params = form.getCaptcha().split(regex);
        String[] phoneNumbers = form.getMobiles().split(regex);
        SmsResultBase result;
        try {
            // 是否单发
            if (isSingle) {
                SmsSingleSender ssender = new SmsSingleSender(form.getAppId(), form.getAppKey());
                result = ssender.sendWithParam(form.getNationCode(), phoneNumbers[0], form.getTemplateId(), params, form.getSmsSign(), "", "");
            } else {
                SmsMultiSender msender = new SmsMultiSender(form.getAppId(), form.getAppKey());
                result = msender.sendWithParam(form.getNationCode(), phoneNumbers, form.getTemplateId(), params, form.getSmsSign(), "", "");
            }
        } catch (HTTPException e) {
            e.printStackTrace();
            throw new CustomException("HTTP响应码错误");
        } catch (IOException e) {
            e.printStackTrace();
            throw new CustomException("网络IO错误");
        }
        return result;
    }
}
