package com.navigator.common.util;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.time.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

/**
 * <p>
 * 编码生成的工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-25
 */
@Component
public class CodeGeneratorUtil {
    @Autowired
    protected RedisUtil redisUtil;

    static Random random = new Random();

    /**
     * 生成验证码(仅数字)
     *
     * @param capacity
     * @return
     */
    public static String genVerificationCode(int capacity) {
        String str = "0123456789";
        StringBuilder sb = new StringBuilder(capacity);
        for (int i = 0; i < capacity; i++) {
            char ch = str.charAt(random.nextInt(str.length()));
            sb.append(ch);
        }
        return sb.toString();
    }

    /**
     * 父合同编号规则：卖方主体简称+SBM（豆粕合约）+S（销售合同/P采售合同）+当年年份后两位+5位递增码
     *
     * @param sellerName    卖方主体简称
     * @param goodsCategory 商品品类
     * @param salesType     销售合同类型
     * @param increaseCode  递增码
     * @return
     */
    public static String genNewContractCode(String sellerName, String goodsCategory, String salesType, Long increaseCode) {

        // 年份的后两位(SimpleDateFormat非线程安全，使用 java8 的 DateTimeFormatter 替代)
        String yearLast = LocalDate.now().format(DateTimeFormatter.ofPattern("yy"));

        return sellerName+ goodsCategory + salesType + yearLast + String.format("%05d", increaseCode);
    }

    /**
     * LKG合同编号规则：
     * 销售合同：
     * 豆粕合同编号规则：L+卖方主体简称+SBM（豆粕合约）+S销售合同+当年年份后两位+5位递增码
     * 豆油合同编号规则：L+卖方主体简称+SBO（豆粕合约）+S销售合同+当年年份后两位+5位递增码
     * 采购合同：
     * 豆粕合同编号规则：L+买方主体简称+SBM（豆粕合约）+P采购合同+当年年份后两位+5位递增码
     * 豆油合同编号规则：L+买方主体简称+SBO（豆粕合约）+P采购合同+当年年份后两位+5位递增码
     *
     * @param sellerName    卖方主体简称
     * @param goodsCategory 商品品类
     * @param salesType     销售合同类型
     * @return
     */
    public static String genLKGContractCode(String sellerName, String goodsCategory, String salesType, Long increaseCode) {

        // 年份的后两位(SimpleDateFormat非线程安全，使用 java8 的 DateTimeFormatter 替代)
        String yearLast = LocalDate.now().format(DateTimeFormatter.ofPattern("yy"));

        return "L" + sellerName + goodsCategory + salesType + yearLast + String.format("%05d", increaseCode);
    }

    /**
     * 子合同编号规则：原合同编号-递增
     *
     * @param contractCode 父合同编号
     * @param increaseCode 递增码
     * @return
     */
    public static String genSonContractCode(String contractCode, Integer increaseCode) {
        return contractCode + "-" + String.format("%03d", increaseCode);
    }

    public static String genCargoRightsContractCode(String contractCode, Integer increaseCode) {
        return contractCode + "-T" + String.format("%03d", increaseCode);
    }


    /**
     * 根据采销类型生成新仓单合同新编号
     *
     * @return
     */
    public static String genContractNewCode(String exchangeCode,String futureCode,ContractSalesTypeEnum salesTypeEnum) {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        if(ContractSalesTypeEnum.PURCHASE.equals(salesTypeEnum)){
            return exchangeCode + futureCode + "P" +DateTimeUtil.formatMonthValue() + code;
        }else {
            return exchangeCode + futureCode + "S" +DateTimeUtil.formatMonthValue() + code;
        }
    }

    /**
     * 根据采销类型生成TT新编号
     *
     * @return
     */
    public static String genTTNewCodeBySaleType(ContractSalesTypeEnum salesTypeEnum) {
        if(ContractSalesTypeEnum.PURCHASE.equals(salesTypeEnum)){
            return genPurchaseTTNewCode();
        }else {
            return genSalesTTNewCode();
        }
    }


    /**
     * 生成销售TT新编号
     *
     * @return
     */
    public static String genSalesTTNewCode() {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        return "SC" + DateTimeUtil.formatDateTimeValue() + code;
    }

    /**
     * 生成采购TT新编号
     *
     * @return
     */
    public static String genPurchaseTTNewCode() {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        return "PC" + DateTimeUtil.formatDateTimeValue() + code;
    }


    /**
     * 生成TT子编号
     *
     * @return
     */
    public String genTTSonCode(String preKey, String code) {
        String key = preKey + code;
        int index = code.indexOf("-");
        if (index == -1) {
            redisUtil.incr(key, 1L);
            code = code + "-001";
        } else {
            key = preKey + code.substring(0, index);
            long num = redisUtil.incr(key, 1L);
            String subCode = String.format("%03d", num);
            code = code.substring(0, index + 1) + subCode;
        }
        return code;
    }

    /**
     * 生成
     *
     * @return
     */
    public static String genPositionCode() {
        int num = random.nextInt(9999);
        String code = String.format("%04d", num);
        return "PC" + DateTimeUtil.formatDateValue(new Date()) + code;
    }

    public static String genAnnouncementCode(Integer systemId, Integer id) {
        String code = "A"
                + SystemEnum.getByValue(systemId).getShortName()
                + Calendar.getInstance().getWeekYear()
                + String.format("%03d", id);
        return code;
    }

    public static String genFileRecordCode(Integer systemId, Integer id) {
        String code = "F"
                + SystemEnum.getByValue(systemId).getShortName()
                + Calendar.getInstance().getWeekYear()
                + String.format("%03d", id);
        return code;
    }
}
