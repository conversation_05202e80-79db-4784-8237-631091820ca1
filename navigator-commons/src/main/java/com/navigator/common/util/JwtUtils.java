package com.navigator.common.util;

import com.navigator.common.enums.SystemUserEnum;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.DatatypeConverter;
import java.util.Date;

/**
 * Description: No Description
 * Created by <PERSON>Yong on 2021/10/27 15:28
 */
@Slf4j
@Component
public class JwtUtils {

    //常量
    public static final long EXPIRE = 1000 * 60 * 60 * 24L;                     // token过期时间
    public static final long REFRESH_EXPIRE = 1000 * 60 * 60 * 24 * 30L;

    public static final String SECRET_KEY = "ukc8BDbRigUDaY6pZFfWus2jZWLPHO"; //秘钥
    public static final String REFRESH_SECRET_KEY = "ukc8BDbRigUDaY6pZFfWus2jAADASzxzc"; //秘钥

    @Value("${token.magellan_expire}")
    private Integer magellanExpire;
    @Value("${token.columbus_expire}")
    private Integer columbusExpire;
    @Value("${system.admin.userId}")
    private String SYSTEM_ADMIN_USERID;
    private static String adminUserId;

    @PostConstruct
    public void init() {
        adminUserId = SYSTEM_ADMIN_USERID;
    }

    //生成token字符串的方法
    public String getJwtTokenByEmail(String id, String email, boolean isMagellan) {
        Integer expire = isMagellan ? magellanExpire : columbusExpire;
        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setSubject("navigator")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expire))
                .claim("id", id)
                .claim("email", email)
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)
                .compact();
    }

    public String getJwtTokenByPhone(String id, String phone, boolean isMagellan) {
        Integer expire = isMagellan ? magellanExpire : columbusExpire;
        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setSubject("navigator")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + expire))
                .claim("id", id)
                .claim("phone", phone)
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)
                .compact();
    }


    public static String getRefreshTokenByEmail(String id, String email) {
        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setSubject("navigator")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + REFRESH_EXPIRE))
                .claim("id", id)
                .claim("email", email)
                .signWith(SignatureAlgorithm.HS256, REFRESH_SECRET_KEY)
                .compact();
    }

    public static String getRefreshTokenByPhone(String id, String phone) {
        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setSubject("navigator")
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + REFRESH_EXPIRE))
                .claim("id", id)
                .claim("phone", phone)
                .signWith(SignatureAlgorithm.HS256, REFRESH_SECRET_KEY)
                .compact();
    }


    /**
     * 判断token是否存在与有效
     *
     * @param jwtToken
     * @return
     */
    public static boolean checkToken(String jwtToken) {
        if (StringUtils.isEmpty(jwtToken)) return false;
        try {
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(jwtToken);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 判断token是否存在与有效
     *
     * @param request
     * @return
     */
    public static boolean checkToken(HttpServletRequest request) {
        try {
            String jwtToken = request.getHeader("token");
            if (StringUtils.isEmpty(jwtToken)) return false;
            Jwts.parser().setSigningKey(SECRET_KEY).parseClaimsJws(jwtToken);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 根据token字符串获取会员id
     *
     * @param request
     * @return
     */
    public static String getMemberIdByJwtToken(HttpServletRequest request) {
        Claims claims = parse(request);
        return claims == null ? "" : (String) claims.get("id");
    }

    /**
     * 根据token字符串获取会员id
     *
     * @param request
     * @return
     */
    public static Date getExpirationByJwtToken(HttpServletRequest request) {
        Claims claims = parse(request);
        return claims == null ? new Date() : claims.getExpiration();
    }

    /**
     * 根据token字符串获取会员username
     *
     * @param request
     * @return
     */
    public static String getEmailByJwtToken(HttpServletRequest request) {
        Claims claims = parse(request);
        return claims == null ? "" : (String) claims.get("email");
    }


    /**
     * 根据token字符串获取会员username
     *
     * @param request
     * @return
     */
    public static String getPhoneByJwtToken(HttpServletRequest request) {
        Claims claims = parse(request);
        return claims == null ? "" : (String) claims.get("phone");
    }

    /**
     * 解析JWT
     *
     * @return 解析成功返回Claims对象，解析失败返回null
     */
    public static Claims parse(HttpServletRequest request) {

        String token = request.getHeader("token");

        // 如果是空字符串直接返回null
        if (!StringUtils.hasLength(token)) {
            return null;
        }

        Claims claims = null;
        // 解析失败了会抛出异常，所以我们要捕捉一下。token过期、token非法都会导致解析失败
        try {
            claims = Jwts.parser()
                    .setSigningKey(SECRET_KEY)
                    .parseClaimsJws(token)
                    .getBody();
        } catch (JwtException e) {
            log.error("token解析失败:{}", e.toString());
        }
        return claims;
    }


    /**
     * 解析JWT
     *
     * @return 解析成功返回Claims对象，解析失败返回null
     */
    public static Claims parseRefreshToken(String refreshToken) {
        // 如果是空字符串直接返回null
        if (!StringUtils.hasLength(refreshToken)) {
            return null;
        }
        Claims claims = null;
        // 解析失败了会抛出异常，所以我们要捕捉一下。token过期、token非法都会导致解析失败
        try {
            claims = Jwts.parser()
                    .setSigningKey(REFRESH_SECRET_KEY)
                    .parseClaimsJws(refreshToken)
                    .getBody();
        } catch (JwtException e) {
            log.error("refreshToken解析失败:{}", e.toString());
        }
        return claims;
    }

    /**
     * 获取用户id
     *
     * @param jwt
     * @return
     */
    public static String getUserId(String jwt) {
        try {
            Claims claims = null;
            claims = Jwts.parser()
                    .setSigningKey(DatatypeConverter.parseBase64Binary(SECRET_KEY))
                    .parseClaimsJws(jwt).getBody();

            return (String) claims.get("id");
        } catch (Exception e) {
            throw e;
        }
    }

    public static String getCurrentUserId() {
        String userId = "";
        HttpServletRequest request = getRequest();
        if (null != request) {
            String jwt = request.getHeader("token");
            if (org.apache.commons.lang3.StringUtils.isNotBlank(jwt)) {
                userId = getUserId(jwt);
            }
        }
        if ("".equals(userId)) {
            log.info("=====================================================userId:{}-----adminUserId:{}", userId, adminUserId);
            userId = adminUserId;
            log.info("=====================================================userId:{}-----adminUserId:{}", userId, adminUserId);
        }
        return userId;
    }

    /**
     * 获取request
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes == null ? null : requestAttributes.getRequest();
    }

    /**
     * 获取token 当请求头中不含有token时返回空
     *
     * @return
     */
    public static String getToken() {
        HttpServletRequest request = getRequest();
        String token = "";
        if (null != request) {
            token = request.getHeader("token");
        }
        return token;

    }


}
