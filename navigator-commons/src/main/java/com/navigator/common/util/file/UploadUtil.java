package com.navigator.common.util.file;


import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;

public class UploadUtil {

    public static Result upload(MultipartFile file, String userId) {
        if (file.isEmpty()) {
            return Result.failure();
        }
        String originalFilename = file.getOriginalFilename();
        if(null == originalFilename)
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        if (!originalFilename.contains("\\")) {
            int i;
        }
        String extension = "." + FilenameUtils.getExtension(originalFilename);
        String newFileName = FilenameUtils.getBaseName(originalFilename) + userId
                + extension;
        String dataDir = getDataDir(userId);
        if (dataDir == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        File dataFile = new File(dataDir);
        if (!dataFile.exists()) {
            dataFile.mkdirs();
        }
        try {
            File transferFile = new File(dataFile, newFileName);
            if (null != transferFile)
                file.transferTo(transferFile);
        } catch (IOException e) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        return Result.success(dataDir + "/" + newFileName);
    }

    private static String getDataDir(String userId) {
        String path = null;
        try {
            path = ResourceUtils.getURL("classpath:").getPath() + "static/files/";
        } catch (FileNotFoundException e) {
            return null;
        }
        String dataDir = path + userId;
        return dataDir;
    }

    public static void download(String fileName, String openStyle, String userId, HttpServletResponse response) throws IOException {
        String globalPath = getDataDir(userId) + "/";
        String extension = "." + FilenameUtils.getExtension(fileName);
        String newFileName = FilenameUtils.getBaseName(fileName) + userId + extension;
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(new File(globalPath, newFileName));
            if ("attachment".equals(openStyle)) {
                //以附件形式下载
                response.setHeader("content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "utf-8"));
            }
            ServletOutputStream os = response.getOutputStream();
            IOUtils.copy(fis, os);
            IOUtils.closeQuietly(fis);
            IOUtils.closeQuietly(os);
        }catch (IOException e){
            e.printStackTrace();
        }finally {
            try{
                if(fis!=null)
                    fis.close();
            }catch (IOException e){
                e.printStackTrace();
            }
            fis = null;
        }
    }

    public static Result delete(String fileName, String userId) {
        String globalPath = getDataDir(userId) + "/";
        String extension = "." + FilenameUtils.getExtension(fileName);
        String newFileName = FilenameUtils.getBaseName(fileName) + userId + extension;
        File file = new File(globalPath, newFileName);
        if (file.exists() && file.isFile()) {
            boolean delete = file.delete();
            if(!delete){
            }
        }
        return Result.success();
    }
}
