package com.navigator.common.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 笛卡尔积
 *
 * <AUTHOR>
 */
public class CartesianProductUtil {

    /**
     * 生成笛卡尔积
     *
     * @param lists
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> cartesianProduct(List<List<T>> lists) {
        List<List<T>> result = new ArrayList<>();
        if (lists.isEmpty()) {
            result.add(new ArrayList<>());
            return result;
        }
        List<T> firstList = lists.get(0);
        List<List<T>> remainingLists = lists.subList(1, lists.size());
        List<List<T>> temp = cartesianProduct(remainingLists);
        for (T element : firstList) {
            for (List<T> item : temp) {
                List<T> newItem = new ArrayList<>(item);
                newItem.add(0, element);
                result.add(newItem);
            }
        }
        return result;
    }

    public static void main(String[] args) {
        List<List<Integer>> lists = new ArrayList<>();
        lists.add(Arrays.asList(1, 2));
        lists.add(Arrays.asList(3, 4));
        lists.add(Arrays.asList(5, 6));
        lists.add(Arrays.asList(5, 6));

        List<List<Integer>> product = cartesianProduct(lists);
        for (List<Integer> list : product) {
            System.out.println(list);
        }
    }
}