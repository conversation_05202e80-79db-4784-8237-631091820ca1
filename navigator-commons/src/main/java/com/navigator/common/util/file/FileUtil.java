package com.navigator.common.util.file;

import cn.hutool.core.util.StrUtil;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class FileUtil {

    /**
     * 获取文件后缀
     *
     * @param fileName
     * @return
     */
    public static String getSuffix(String fileName) {
        return fileName.substring(fileName.lastIndexOf("."));
    }

    /**
     * 生成新的文件名
     *
     * @param fileOriginName 源文件名
     * @return
     */
    public static String getFileName(String fileOriginName) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss");
        String fileNamePre = format.format(new Date());
        String fileNameFix = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 13);
        String fileName = fileNamePre + "-" + fileNameFix;
        return fileName + getSuffix(fileOriginName);
    }

    /**
     * 获取文件名称
     *
     * @param str
     * @return
     */
    public static String getFolder(String str) {
        if (StrUtil.isNotBlank(str)) {
            return str.substring(0, 10) + "/" + str;
        }
        return str;
    }


    /**
     * 创建目录(如果目录存在就删掉目录)
     *
     * @param destDirName 目标目录路径
     * @return
     */
    public static boolean createDir(String destDirName) {
        File dir = new File(destDirName);
        if (dir.exists()) {// 判断目录是否存在
            System.out.println("目标目录已存在!");
            //return false;
//            if(FileUtil.deleteDirectory(destDirName)){
//               return createDir(destDirName);
//            }
            return false;
        }
        System.out.println("已删除原目录并重新创建!");
        if (!destDirName.endsWith(File.separator)) {// 结尾是否以"/"结束
            destDirName = destDirName + File.separator;
        }
        if (dir.mkdirs()) {// 创建目标目录
            System.out.println("创建目录成功！" + destDirName);
            return true;
        } else {
            System.out.println("创建目录失败！");
            return false;
        }
    }
}

