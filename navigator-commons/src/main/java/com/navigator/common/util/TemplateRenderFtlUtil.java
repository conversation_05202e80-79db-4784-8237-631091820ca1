package com.navigator.common.util;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;

import java.util.Map;

public class TemplateRenderFtlUtil {

    private static TemplateEngine templateEngine;

    static {
        templateEngine = TemplateUtil.createEngine(new TemplateConfig("template", TemplateConfig.ResourceMode.CLASSPATH));
    }

    /**
     * 渲染模板
     *
     * @param map
     * @param templateName
     * @return
     */
    public static String templateRender(Map map, String templateName) {
        Template template = templateEngine.getTemplate(templateName);
        return template.render(map);
    }
}
