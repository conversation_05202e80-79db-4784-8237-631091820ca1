package com.navigator.common.util.file;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONException;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.enums.FileServiceTypeEnum;
import com.navigator.common.util.time.DateTimeUtil;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.http.entity.ContentType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2020-06-02 17:43
 */
public class FileUploadUtil {

    /**
     * 单文件上传
     *
     * @param file
     * @return
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    public static FileBaseInfoDTO singleUpload(MultipartFile file)
            throws IllegalStateException, IOException, JSONException {
        if (!file.isEmpty()) {
            String newPath = FileConstant.FILE_UPLOAD + DateTimeUtil.formatDateString(DateTime.now());
            //定义文件
            File parentFile = new File(newPath);
            judeDirExists(parentFile);
            //上传文件
            return uploadFile(newPath, parentFile, file);
        } else {
            return null;
        }
    }

    /**
     * 多文件上传
     *
     * @param files
     * @param request
     * @return
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    public static List<FileBaseInfoDTO> multiUpload(MultipartFile[] files, HttpServletRequest request)
            throws IllegalStateException, IOException, JSONException {
        if (files.length > 0) {
            //创建这个集合保存所有文件的信息
            List<FileBaseInfoDTO> listMap = new ArrayList<>();
            String newPath = FileConstant.FILE_UPLOAD + DateTimeUtil.formatDateString(DateTime.now());
            //定义文件
            File parentFile = new File(newPath);
            judeDirExists(parentFile);
            //循环多次上传多个文件
            for (MultipartFile file : files) {
                //创建map对象保存每一个文件的信息
                FileBaseInfoDTO fileBaseInfoDTO = uploadFile(newPath, parentFile, file);
                listMap.add(fileBaseInfoDTO);
            }
            //以json方式输出到页面
            return listMap;
        } else {
            return null;
        }
    }

    /**
     * 统一上传文件
     *
     * @param newPath
     * @param parentFile
     * @param file
     * @return
     * @throws IOException
     */
    private static FileBaseInfoDTO uploadFile(String newPath, File parentFile, MultipartFile file) throws IOException {
        String oldName = "";
        Long size = 0L;
        if (file != null) {
            oldName = file.getOriginalFilename();
            size = file.getSize();
        }
        if(oldName == null)
            return null;

        //使用TmFileUtil文件上传工具获取文件的各种信息
        //优化文件大小
        String sizeString = readableFileSize(size);
        //获取文件后缀名
        String stuff = "";
        if(!oldName.equals(""))
            stuff = getFileStuff(oldName);
        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        //文件最终上传的位置
        String newFileName = uuid + "_" + oldName;
        String url = newPath + File.separator + newFileName;
        //文件传输，parent文件
        System.out.println(parentFile.getAbsolutePath() + File.separator + newFileName);
        if(null!=file)
            file.transferTo(new File(parentFile.getAbsolutePath() + File.separator + newFileName));
        return new FileBaseInfoDTO().setAttachName(newFileName)
                .setOriginalName(oldName)
                .setAttachUrl(url)
                .setSize(size.intValue())
                .setSizeInfo(sizeString)
                .setFsType(FileServiceTypeEnum.LOCAL_SERVER.getValue())
                .setAttachStuff(stuff);
    }

    /**
     * 获取文件后缀名
     *
     * @param oldName
     * @return
     */
    private static String getFileStuff(String oldName) {
        if (null == oldName || oldName.equals(""))
            return "";
        return oldName.substring(oldName.lastIndexOf("."));
    }

    /**
     * 判断文件夹，并创建
     *
     * @param file 要创建的文件
     */
    public static void judeDirExists(File file) {
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * 文件字节数转换为文件大小
     *
     * @param size 文件字节数
     * @return 换算的文件大小结果（B、KB、MB）
     */
    private static String readableFileSize(long size) {
        if (size <= 0) {
            return "0";
        }
        final String[] units = new String[]{"B", "kB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return new DecimalFormat("#,##0.##").format(size / Math.pow(1024, digitGroups)) + " " + units[digitGroups];
    }

    /**
     * 写入文件
     *
     * @param target
     * @param src
     * @throws IOException
     */
    public static void write(String filePath, String target, InputStream src) throws IOException {

        //定义文件
        File parentFile = new File(filePath);
        judeDirExists(parentFile);

        FileOutputStream os = null;
        try {
            os = new FileOutputStream(target);
            byte[] buf = new byte[1024];
            int len;
            if(os != null) {
                while (-1 != (len = src.read(buf))) {
                    os.write(buf, 0, len);
                }
            }
            os.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (os != null) {
                    os.close();
                }
            }catch (IOException e){
                e.printStackTrace();
            }
            os = null;
        }
    }

    /**
     * url转变为 MultipartFile对象
     *
     * @param url
     * @return
     */
    public static MultipartFile createFileItem(String url, String fileName) throws Exception {
        FileItem item = null;
        try {
            HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setReadTimeout(30000);
            conn.setConnectTimeout(30000);
            //设置应用程序要从网络连接读取数据
            conn.setDoInput(true);
            conn.setRequestMethod("GET");
            if (conn.getResponseCode() == HttpURLConnection.HTTP_OK) {
                InputStream is = conn.getInputStream();

                FileItemFactory factory = new DiskFileItemFactory(16, null);
                String textFieldName = "file";
                item = factory.createItem(textFieldName, ContentType.APPLICATION_OCTET_STREAM.toString(), false, fileName);
                OutputStream os = item.getOutputStream();

                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = is.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                is.close();
            }
        } catch (IOException e) {
            throw new RuntimeException("文件下载失败", e);
        }

        return new CommonsMultipartFile(item);
    }

    /**
     * 根据文件路径转化为MultipartFile
     *
     * @param filePath
     * @return
     */
    public static MultipartFile createFileItem(String filePath) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        String textFieldName = "textField";
        int num = filePath.lastIndexOf(".");
        String extFile = filePath.substring(num);
        FileItem item = factory.createItem(textFieldName, "text/plain", true,
                "MyFileName" + extFile);
        File newfile = new File(filePath);
        int bytesRead = 0;
        byte[] buffer = new byte[8192];
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(newfile);
            OutputStream os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192))
                    != -1) {
                os.write(buffer, 0, bytesRead);
            }
            os.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if(fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            fis = null;
        }
        return new CommonsMultipartFile(item);
    }

    /**
     * 生成Html文件在本地
     *
     * @param htmlInfoDTO
     * @return FileBaseInfoDTO
     */
    public static void genHtml(HtmlInfoDTO htmlInfoDTO) {
        // 将html暂存到/tmp/htmlFile
        String htmlUrl = htmlInfoDTO.getHtmlUrl();
        String htmlString = htmlInfoDTO.getHtmlContent();

        // 判断是否存在目录
        String htmlPath = new File(htmlUrl).getAbsolutePath();
        FileUtil.mkParentDirs(htmlPath);
        try {
            PrintStream printStream = new PrintStream(new FileOutputStream(htmlPath), true);
            printStream.print(htmlString);
            printStream.flush();
            printStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

    }

}
