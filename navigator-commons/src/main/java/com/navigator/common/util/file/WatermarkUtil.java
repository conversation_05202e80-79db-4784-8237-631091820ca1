package com.navigator.common.util.file;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Element;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.*;
import com.navigator.common.dto.WatermarkDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.swing.*;
import java.awt.*;
import java.io.*;


@Component
@RefreshScope
@Slf4j
public class WatermarkUtil {
    static float alphax = 0.05f;
    static float alphat = 0.2f;

    public static void getWatermark(BufferedOutputStream bos, InputStream input, String waterMarkName) {
        try {
            StringBuilder reversed = new StringBuilder(waterMarkName).reverse();
            //String[] waterMarkContents = reversed.toString().split("\\|\\|");
            String[] waterMarkContents = splitString(reversed.toString(), 60);
            PdfReader reader = new PdfReader(input);
            PdfStamper stamper = new PdfStamper(reader, bos);
            int total = reader.getNumberOfPages() + 1;
            PdfContentByte content;
            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", BaseFont.EMBEDDED);
//            BaseFont base = BaseFont.createFont("/SIMYOU.TTF", BaseFont.IDENTITY_H,BaseFont.NOT_EMBEDDED);

            int interval = 40;
            // 获取水印文字的最大高度和宽度
            int textH = 0, textW = 0;
            for (int j = 0; j < waterMarkContents.length; j++) {
                JLabel label = new JLabel();
                label.setText(waterMarkContents[j]);
                FontMetrics metrics = label.getFontMetrics(label.getFont());
                if (textH < metrics.getHeight()) {
                    textH = metrics.getHeight();
                }
                if (textW < metrics.stringWidth(label.getText())) {
                    textW = metrics.stringWidth(label.getText());
                }


                // 水印透明度
                PdfGState gs = new PdfGState();
                gs.setFillOpacity(0.02f);

                Rectangle pageSizeWithRotation = null;

                for (int i = 1; i < total; i++) {
                    // 下方水印
                    content = stamper.getUnderContent(i);
                    content.beginText();
                    // 水印颜色
                    content.setColorFill(BaseColor.GRAY);
                    // 水印字体
                    content.setFontAndSize(base, 10);

                    // 水印透明度
                    content.setGState(gs);

                    pageSizeWithRotation = reader.getPageSizeWithRotation(i);
                    float pageHeight = pageSizeWithRotation.getHeight();
                    float pageWidth = pageSizeWithRotation.getWidth();


                    for (int height = interval + textH; height < pageHeight; height = height + textH * 6) {
                        for (int width = interval + textW; width < pageWidth + textW; width = width + textW * 2) {
                            // 将分段的字段进行输出编写
                            for (int z = waterMarkContents.length - 1; z >= 0; z--) {
                                StringBuilder reversed1 = new StringBuilder(waterMarkContents[z]).reverse();
                                content.showTextAligned(Element.ALIGN_LEFT, reversed1.toString(), width - textW,
                                        30 * (z + 1) + 300, 10);
                            }
                        }
                    }

                    content.endText();
                }

                //            stamper.close();
                //            reader.close();

            }

            for (int j = 0; j < waterMarkContents.length; j++) {
                JLabel label = new JLabel();
                label.setText(waterMarkContents[j]);
                FontMetrics metrics = label.getFontMetrics(label.getFont());
                if (textH < metrics.getHeight()) {
                    textH = metrics.getHeight();
                }
                if (textW < metrics.stringWidth(label.getText())) {
                    textW = metrics.stringWidth(label.getText());
                }


                // 水印透明度
                PdfGState gs = new PdfGState();
                gs.setFillOpacity(0.02f);

                Rectangle pageSizeWithRotation = null;

                for (int i = 1; i < total; i++) {
                    // 下方水印
                    content = stamper.getUnderContent(i);
                    content.beginText();
                    // 水印颜色
                    content.setColorFill(BaseColor.GRAY);
                    // 水印字体
                    content.setFontAndSize(base, 10);

                    // 水印透明度
                    content.setGState(gs);

                    pageSizeWithRotation = reader.getPageSizeWithRotation(i);
                    float pageHeight = pageSizeWithRotation.getHeight();
                    float pageWidth = pageSizeWithRotation.getWidth();


                    for (int height = interval + textH; height < pageHeight; height = height + textH * 6) {
                        for (int width = interval + textW; width < pageWidth + textW; width = width + textW * 2) {
                            // 将分段的字段进行输出编写
                            for (int z = waterMarkContents.length - 1; z >= 0; z--) {
                                StringBuilder reversed1 = new StringBuilder(waterMarkContents[z]).reverse();
                                content.showTextAligned(Element.ALIGN_LEFT, reversed1.toString(), width - textW,
                                        30 * (z + 1), 10);
                            }
                        }
                    }

                    content.endText();
                }

                //            stamper.close();
                //            reader.close();

            }


            PdfGState gs2 = new PdfGState();
            gs2.setFillOpacity(alphat);
            for (int i = 1; i < total; i++) {
                // 下方水印
                content = stamper.getOverContent(i);
                //            content.beginText();
                //            // 水印颜色
                //            content.setColorFill(BaseColor.GRAY);
                //            // 水印字体
                //            content.setFontAndSize(base, 12);
                //            // 文本大小
                //            content.setTextMatrix(70, 200);
                //            // 水印透明度
                //            content.setGState(gs2);
                //            // 水印位置
                //            content.showTextKerned(waterMarkName);
                //            content.endText();

                // 上方水印
                content.beginText();
                content.setColorFill(BaseColor.GRAY);
                content.setFontAndSize(base, 24);
                content.setTextMatrix(70, 200);
                content.setGState(gs2);
                content.showTextAligned(Element.ALIGN_CENTER, "签署样张", 60, 800, 10);
                content.endText();
            }


            for (int j = 0; j < waterMarkContents.length; j++) {
                JLabel label = new JLabel();
                label.setText(waterMarkContents[j]);
                FontMetrics metrics = label.getFontMetrics(label.getFont());
                if (textH < metrics.getHeight()) {
                    textH = metrics.getHeight();
                }
                if (textW < metrics.stringWidth(label.getText())) {
                    textW = metrics.stringWidth(label.getText());
                }


                // 水印透明度
                PdfGState gs = new PdfGState();
                gs.setFillOpacity(alphax);

                Rectangle pageSizeWithRotation = null;

                for (int i = 1; i < total; i++) {
                    // 下方水印
                    content = stamper.getUnderContent(i);
                    content.beginText();
                    // 水印颜色
                    content.setColorFill(BaseColor.GRAY);
                    // 水印字体
                    content.setFontAndSize(base, 10);

                    // 水印透明度
                    content.setGState(gs);

                    pageSizeWithRotation = reader.getPageSizeWithRotation(i);
                    float pageHeight = pageSizeWithRotation.getHeight();
                    float pageWidth = pageSizeWithRotation.getWidth();


                    for (int height = interval + textH; height < pageHeight; height = height + textH * 6) {
                        for (int width = interval + textW; width < pageWidth + textW; width = width + textW * 2) {
                            // 将分段的字段进行输出编写
                            for (int z = waterMarkContents.length - 1; z >= 0; z--) {
                                StringBuilder reversed1 = new StringBuilder(waterMarkContents[z]).reverse();
                                content.showTextAligned(Element.ALIGN_LEFT, reversed1.toString(), width - textW,
                                        30 * (z + 1) + 600, 10);
                            }
                        }
                    }

                    content.endText();
                }

                stamper.close();
                reader.close();

            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("获取水印异常2" + e.getMessage());
        }
    }


    public WatermarkDTO generateWaterMark(MultipartFile file, Integer companyId) {
        WatermarkDTO watermarkDTO = new WatermarkDTO();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(outputStream);
        InputStream inputStream = null;
        try {
            inputStream = file.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("HtmlToPdf加水印异常1" + e.getMessage());
        }
        // BUGFIX：case-1003219 路易达孚（天津）食品科技水印为广州富凌 Author: Mr 2025-05-19 Start
        String text = "本文件为草稿，仅供参考。双方在本文件上签字盖章的，不具有要约邀请、要约、承诺或任何其他类型的法律效力或约束力。特此说明。";
//        if (companyId == 1) {
//            text = "本文件为签署样张，仅作为路易达孚（天津）国际贸易有限公司（“路易达孚”）的客户进行合同签约前内部审批流程的参考性文件使用，客户应通过与路易达孚另行约定的方式完成合同文本的正式签署。客户或其他第三方在本签署样张上直接签字盖章的，对路易达孚不具有法律约束力。";
//        } else {
//            text = "本文件为签署样张，仅作为广州富凌食品科技有限公司（“富凌公司”）的客户进行合同签约前内部审批流程的参考性文件使用，客户应通过与富凌公司另行约定的方式完成合同文本的正式签署。客户或其他第三方在本签署样张上直接签字盖章的，对富凌公司不具有法律约束力。";
//        }
        // BUGFIX：case-1003219 路易达孚（天津）食品科技水印为广州富凌 Author: Mr 2025-05-19 End
        log.info("水印内容：" + text);
        int desiredLength = 270;
        String watermark = StringUtils.rightPad(text, desiredLength);

        getWatermark(bufferedOutputStream, inputStream, watermark);
        // 将字节数组输出流的内容转换为输入流
        log.info("");
        MultipartFile multipartFile = null;
        try {
            multipartFile = new MockMultipartFile("file", new ByteArrayInputStream(outputStream.toByteArray()));
            watermarkDTO.setMultipartFile(multipartFile);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("水印生成失败3:{}",e);
        }
        InputStream inputStream1 = null;
        try {
            inputStream1 = multipartFile.getInputStream();
            watermarkDTO.setInputStream(inputStream1);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("水印生成失败4:{}",e);
        }
        return watermarkDTO;
    }

    public static String[] splitString(String str, int chunkSize) {
        int length = str.length();
        int numOfChunks = (int) Math.ceil((double) length / chunkSize);
        String[] chunks = new String[numOfChunks];
        int index = 0;
        for (int i = 0; i < length; i += chunkSize) {
            int endIndex = Math.min(i + chunkSize, length);
            chunks[index] = str.substring(i, endIndex);
            index++;
        }
        return chunks;
    }
}
