package com.navigator.common.util;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Map工具类
 *
 * <AUTHOR>
 */
public class MapUtil extends cn.hutool.core.map.MapUtil {

    /**
     * 获取字符串
     *
     * @param map
     * @param key
     * @return
     */
    public static String getString(Map map, Object key) {
        String str = MapUtil.getStr(map, key);
        if (str != null) {
            str = str.trim();
        }
        return str;
    }

    /**
     * 获取字符串列表
     *
     * @param map
     * @param key
     * @param separator
     * @return
     */
    public static List<String> getStringList(Map map, Object key, String separator) {
        String str = getString(map, key);
        if (str != null) {
            return StringUtil.split(str, separator);
        }
        return null;
    }

    /**
     * 获取数据
     *
     * @param map
     * @param key
     * @return
     */
    public static BigDecimal getBigDecimal(Map map, Object key) {
        String str = getString(map, key);
        if (StringUtil.isNotNullBlank(str)) {
            return new BigDecimal(str.trim());
        }
        return null;
    }
}
