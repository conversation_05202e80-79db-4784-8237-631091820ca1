package com.navigator.common.util;

import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.ContractTradeTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;

public class TTHandlerUtil {


    /*public static String getTTProcessor(Integer salesType, Integer ttType, Integer subGoodsCategoryId) {
        return GoodsCategoryEnum.getByValue(subGoodsCategoryId).getCode()
                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_TT_" + TTTypeEnum.getCodeByValue(ttType);
    }*/

    public static String getTTProcessor(Integer salesType, Integer ttType, Integer subGoodsCategoryId) {
        return "SBM_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_TT_" + TTTypeEnum.getCodeByValue(ttType);
    }

    /*public static String getTTProcessorByTradeType(Integer salesType, Integer tradeType, Integer subGoodsCategoryId) {
        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeType);
        TTTypeEnum ttTypeEnum = contractTradeTypeEnum.getTTType();
        return GoodsCategoryEnum.getByValue(subGoodsCategoryId).getCode()
                + "_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_TT_" + ttTypeEnum.getCode();
    }*/

    public static String getTTProcessorByTradeType(Integer salesType, Integer tradeType, Integer subGoodsCategoryId) {
        ContractTradeTypeEnum contractTradeTypeEnum = ContractTradeTypeEnum.getByValue(tradeType);
        TTTypeEnum ttTypeEnum = contractTradeTypeEnum.getTTType();
        return "SBM_" + ContractSalesTypeEnum.getByValue(salesType).getDirectCode()
                + "_TT_" + ttTypeEnum.getCode();
    }

}
