package com.navigator.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.navigator.common.util.time.DateTimeUtil;

import java.util.Date;

public class BusinessUtil {

    /**
     * 判断是否超远期合同
     *
     * @param domainCode
     * @param deliveryEndTime
     * @return
     */
    public static boolean isOverForwardContract(String domainCode, Date deliveryEndTime) {
        /*if (!Arrays.asList(1, 5, 9).contains(DateUtil.month(deliveryEndTime) + 1)) {
            return false;
        }*/
        boolean result = false;
        if (ObjectUtil.isEmpty(deliveryEndTime)) {
            return result;
        }
        result = DateUtil.betweenMonth(DateTimeUtil.parseDateValue("20" + domainCode + "01"), deliveryEndTime, false) >= 4;
        return result;
    }
}
