package com.navigator.common.util;

import com.navigator.common.enums.CalcTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.navigator.common.constant.GlobalConstant.SCALE_NUMBER;

public class BigDecimalUtil {

    public static String format(BigDecimal v, int scale, boolean fullZero) {
        return format(v, scale, RoundingMode.DOWN, fullZero);
    }

    /**
     * Navigator统一使用这个方法
     *
     * @param v
     * @return
     */
    public static String format(BigDecimal v) {
        return format(v, SCALE_NUMBER, RoundingMode.DOWN, false);
    }

    public static String formatPriceCN(String priceType, String price, String preInfo, String stuffInfo) {
        if (StringUtils.isBlank(price) || "0".equals(price)) {
            return "";
        }
        return preInfo + priceType + price + "元/吨" + stuffInfo;
    }

    public static String formatDiffPriceCN(String priceType, String price, String preInfo, String stuffInfo) {
        if (StringUtils.isBlank(price) || "0".equals(price)) {
            price = "0";
        }
        return preInfo + priceType + price + "元/吨" + stuffInfo;
    }


    public static String formatPriceZY(String priceType, String price, String preInfo, String stuffInfo) {
        if (StringUtils.isBlank(price) || "0".equals(price)) {
            return "";
        }
        return preInfo + priceType + price + "元/吨" + stuffInfo;
    }


    public static BigDecimal min(BigDecimal... vn) {
        BigDecimal result = BigDecimal.valueOf(Long.parseLong("9999999999999999"));
        for (BigDecimal v : vn) {
            v = null == v ? BigDecimal.ZERO : v;
            if (v.compareTo(result) < 0) {
                result = v;
            }
        }
        result = result.stripTrailingZeros();
        return result;
    }

    public static BigDecimal max(BigDecimal... vn) {
        BigDecimal result = BigDecimal.valueOf(Long.parseLong("-9999999999999999"));
        for (BigDecimal v : vn) {
            v = null == v ? BigDecimal.ZERO : v;
            if (v.compareTo(result) > 0) {
                result = v;
            }
        }
        result = result.stripTrailingZeros();
        return result;
    }

    public static boolean isZero(BigDecimal v) {
        boolean result = false;
        if (null == v || v.compareTo(BigDecimal.ZERO) == 0) {
            result = true;
        }
        return result;
    }

    public static boolean isEqual(BigDecimal... vn) {
        boolean result = true;
        BigDecimal t = null;
        for (BigDecimal v : vn) {
            if (null == v) {
                result = false;
                break;
            }
            if (null == t) {
                t = v;
            }
            if (v.compareTo(t) != 0) {
                result = false;
                break;
            }
        }
        return result;
    }

    public static boolean isEqual(BigDecimal d1, BigDecimal d2) {
        String s1 = d1.stripTrailingZeros().toPlainString();
        String s2 = d2.stripTrailingZeros().toPlainString();
        if (s1.equals(s2)) {
            return true;
        }
        return false;
    }

    public static boolean isEqual(int scale, BigDecimal... vn) {
        boolean result = true;
        BigDecimal t = null;
        for (BigDecimal v : vn) {
            if (null == v) {
                result = false;
                break;
            }
            v = v.setScale(scale, RoundingMode.DOWN);
            if (null == t) {
                t = v;
            }
            if (v.compareTo(t) != 0) {
                result = false;
                break;
            }
        }
        return result;
    }

    public static boolean isGreater(int scale, BigDecimal v1, BigDecimal v2) {
        int chk = compare(scale, v1, v2);
        return chk > 0;
    }

    public static boolean isGreater1(BigDecimal v1, BigDecimal v2) {
        return v1.compareTo(v2) > 0;
    }

    public static boolean isGreater(BigDecimal v1, BigDecimal v2) {
        return isGreater(-1, v1, v2);
    }

    public static boolean isGreaterOrEqual(int scale, BigDecimal v1, BigDecimal v2) {
        int chk = compare(scale, v1, v2);
        return chk >= 0;
    }

    public static boolean isGreaterOrEqual(BigDecimal v1, BigDecimal v2) {
        return isGreaterOrEqual(-1, v1, v2);
    }

    public static boolean isGreaterThanZero(BigDecimal v1) {
        return isGreater(-1, v1, BigDecimal.ZERO);
    }

    public static boolean isGreaterEqualThanZero(BigDecimal v1) {
        return isGreaterOrEqual(-1, v1, BigDecimal.ZERO);
    }


    public static boolean isLess(int scale, BigDecimal v1, BigDecimal v2) {
        int chk = compare(scale, v1, v2);
        return chk < 0;
    }

    public static boolean isLess(BigDecimal v1, BigDecimal v2) {
        return isLess(-1, v1, v2);
    }

    public static boolean isLessOrEqual(int scale, BigDecimal v1, BigDecimal v2) {
        int chk = compare(scale, v1, v2);
        return chk <= 0;
    }

    public static boolean isLessOrEqual(BigDecimal v1, BigDecimal v2) {
        return isLessOrEqual(-1, v1, v2);
    }

    public static boolean isLessThanZero(BigDecimal v1) {
        return isLess(-1, v1, BigDecimal.ZERO);
    }

    public static boolean isLessEqualThanZero(BigDecimal v1) {
        return isLessOrEqual(-1, v1, BigDecimal.ZERO);
    }

    private static int compare(int scale, BigDecimal v1, BigDecimal v2) {
        if (null == v1 || null == v2) {
            return -1;
        }
        if (scale > 0) {
            v1 = v1.setScale(scale, RoundingMode.DOWN);
            v2 = v2.setScale(scale, RoundingMode.DOWN);
        }

        return v1.compareTo(v2);
    }

    public static BigDecimal parseZero(String s) {
        BigDecimal v = BigDecimal.ZERO;
        if (null == s || s.trim().length() == 0) return BigDecimal.ZERO;
        try {
            v = new BigDecimal(s).setScale(SCALE_NUMBER, RoundingMode.DOWN).stripTrailingZeros();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return v;
    }

    public static BigDecimal parseNull(String s) {
        BigDecimal v = null;
        if (null == s || s.trim().length() == 0) return null;
        try {
            v = new BigDecimal(s).setScale(SCALE_NUMBER, RoundingMode.DOWN).stripTrailingZeros();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return v;
    }

    private static BigDecimal parse(BigDecimal v, int scale) {
        return parse(v, scale, RoundingMode.DOWN);
    }

    private static BigDecimal parse(BigDecimal v, int scale, RoundingMode roundingMode, boolean fullZero) {
        if (null == v) {
            return BigDecimal.ZERO;
        }
        v = v.setScale(scale, roundingMode);
        if (!fullZero) {
            v = new BigDecimal(v.stripTrailingZeros().toPlainString());
        }
        return v;
    }

    private static BigDecimal parse(BigDecimal v, int scale, RoundingMode roundingMode) {
        if (null == v) {
            return BigDecimal.ZERO;
        }
        v = v.setScale(scale, roundingMode);
        return v;
    }

    private static BigDecimal parse(String sv) {
        BigDecimal v = BigDecimal.ZERO;
        try {
            if (null == sv || sv.replace(" ", "").isEmpty()) {
                sv = "0";
            }
            v = new BigDecimal(sv);
        } catch (Exception e) {
            v = BigDecimal.ZERO;
        }
        return v;
    }

    private static String format(BigDecimal v, int scale, RoundingMode roundingMode, boolean fullZero) {
        if (null == v) {
            v = BigDecimal.ZERO;
        }
        if (scale > -1) {
            v = v.setScale(scale, roundingMode);
        }
        if (!fullZero) {
            v = v.stripTrailingZeros();
        }
        return v.toPlainString();
    }

    public static BigDecimal add(CalcTypeEnum calcTypeEnum, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.add(v2);
        result = result.setScale(calcTypeEnum.getScale(), RoundingMode.HALF_UP);
        return result;
    }

    public static BigDecimal subtract(CalcTypeEnum calcTypeEnum, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.subtract(v2);
        result = result.setScale(calcTypeEnum.getScale(), RoundingMode.HALF_UP);
        return result;
    }

    public static BigDecimal multiply(CalcTypeEnum calcTypeEnum, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.multiply(v2);
        result = result.setScale(calcTypeEnum.getScale(), RoundingMode.HALF_UP);
        return result;
    }

    public static BigDecimal div(CalcTypeEnum calcTypeEnum, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.divide(v2, calcTypeEnum.getScale(), RoundingMode.HALF_UP);
        return result;
    }

    /**
     * 除  ROUND_HALF_DOWN   ROUND_DOWN   ROUND_UP
     *
     * @param roundingMode 可以四舍五入  向下或向上取正
     * @param v1
     * @param v2
     * @return
     */
    public static BigDecimal div(RoundingMode roundingMode, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.divide(v2, roundingMode);
        return result;
    }

    public static BigDecimal div(Integer d1, Integer d2) {
        BigDecimal v1 = null == d1 ? BigDecimal.ZERO : BigDecimal.valueOf(d1);
        BigDecimal v2 = null == d2 ? BigDecimal.ONE : BigDecimal.valueOf(d2);
        BigDecimal result = v1.divide(v2, 2, RoundingMode.HALF_UP);
        return result;
    }

    public static BigDecimal create(CalcTypeEnum calcTypeEnum, BigDecimal v1, BigDecimal v2) {
        v1 = null == v1 ? BigDecimal.ZERO : v1;
        v2 = null == v2 ? BigDecimal.ZERO : v2;
        BigDecimal result = v1.divide(v2, calcTypeEnum.getScale(), RoundingMode.HALF_UP);
        return result;
    }

    /**
     * 判断是否是整数
     *
     * @param decimalVal
     * @return
     */
    public static boolean isIntegerValue(BigDecimal decimalVal) {
        return decimalVal.scale() <= 0 || decimalVal.stripTrailingZeros().scale() <= 0;
    }


    /**
     * 判断是否可以转换成bigdecimal
     *
     * @param str
     * @return
     */
    public static boolean isBigDecimal(String str) {
        if (str == null || str.trim().length() == 0) {
            return false;
        }
        char[] chars = str.toCharArray();
        int sz = chars.length;
        int i = (chars[0] == '-') ? 1 : 0;
        if (i == sz) {
            return false;
        }
        if (chars[i] == '.') {
            return false;
        }

        boolean radixPoint = false;
        for (; i < sz; i++) {
            if (chars[i] == '.') {
                if (radixPoint) {
                    return false;
                }
                radixPoint = true;
            } else if (!(chars[i] >= '0' && chars[i] <= '9')) {
                return false;
            }
        }
        return true;
    }

    public static BigDecimal initBigDecimal(String value) {
        return StringUtils.isBlank(value) ? BigDecimal.ZERO : new BigDecimal(value);
    }

    public static BigDecimal initBigDecimal(BigDecimal value) {
        return value == null ? BigDecimal.ZERO : value;
    }

    public static String formatBigDecimalZero(BigDecimal value, int scale, RoundingMode roundingMode) {
        if (null == value) {
            return "0";
        }
        return value.setScale(scale, roundingMode).stripTrailingZeros().toPlainString();
    }
}
