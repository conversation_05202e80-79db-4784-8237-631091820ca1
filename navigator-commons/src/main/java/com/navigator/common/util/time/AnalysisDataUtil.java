package com.navigator.common.util.time;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@Service
public class AnalysisDataUtil {

    //TODO NEO @nana util下的类 做一下分类，放入不同的package中

    public static Date tranString(String str) {
        try {
            //hard code
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = simpleDateFormat.parse(str);
            return date;
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 将Timestamp转换成String
     * @param timestamp
     * @return
     */
    public static String tranTimestampToString(Timestamp timestamp) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = df.format(timestamp);
        return str;
    }

    /**
     * 获取时间  报价  提货
     *
     * @return
     */
    public static String fullGoodsOffersTime(Date date, int hour, int minute, int second) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, hour);
        calendar.set(Calendar.MINUTE, minute);
        calendar.set(Calendar.SECOND, second);
        return DateUtil.format(calendar.getTime(), "yyyy-MM-dd HH:mm:ss");
    }

    /**
     * 判断是否在某段时间内
     *
     * @param
     */
    public static boolean belongCalendar(Timestamp beginTime, Timestamp endTime) {

        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        Calendar date = Calendar.getInstance();
        date.setTime(new Date());

        boolean before = date.before(end);
        boolean after = date.after(begin);
        return after && before;
    }

    /**
     * 包含星期
     *
     * @param tradingDay
     * @return
     */
    public static boolean belongWeekDay(String tradingDay) {
        String[] split = tradingDay.replaceAll("\\[", "").replaceAll("]", "").split(",");
        Set<Integer> set = new HashSet<>();
        for (String s : split) {
            if (!s.equalsIgnoreCase("null")) {
                Integer day = Integer.parseInt(s);
                set.add(day);
            }

        }
        Calendar calendar = Calendar.getInstance();
        int i = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        boolean contains = set.contains(i);
        return contains;
    }

    public static boolean exclusionDate(String excludeTime) {
        int row = 1;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        String[] split = excludeTime.split(",");

        for (String s : split) {
            String nowDay = format.format(new Date());
            if (nowDay.equalsIgnoreCase(s)) {
                row++;
                break;
            }
        }

        return row <= 1;

    }

    public static boolean belongTimeSlot(String timeSlot) {
        int row = 1;
        SimpleDateFormat format = new SimpleDateFormat("HH:mm:ss");
        JSONArray array = JSONUtil.parseArray(timeSlot);
        for (Object o : array) {
            JSONObject obj = (JSONObject) o;
            try {
                Date startTime = format.parse(obj.getStr("start_time"));
                Date endTime = format.parse(obj.getStr("end_time"));
                Date nowTime = format.parse(format.format(new Date()));

                Calendar date = Calendar.getInstance();
                date.setTime(nowTime);

                Calendar begin = Calendar.getInstance();
                begin.setTime(startTime);

                Calendar end = Calendar.getInstance();
                end.setTime(endTime);

                if (date.after(begin) && date.before(end)) {
                    row++;
                }
            } catch (ParseException e) {
                e.printStackTrace();
            }
        }
        return row > 1;

    }

    /**
     * 判断是否在今天之前
     */
    public static boolean beforeToday(Timestamp timestamp) {
        Date date = new Date();
        Date transDate = tranString(fullGoodsOffersTime(date, 0, 0, 0));

        Timestamp today = null;
        if(transDate != null)
            today = new Timestamp(transDate.getTime());
        else
            throw new NullPointerException();
        return timestamp.before(today);
    }

    /**
     * 两个时间进行比较
     */
    public static boolean beforeTimeStamp(Timestamp timestamp, Timestamp compare) {
        return timestamp.before(compare);
    }

    /**
     * 获取当天零点
     *
     * @return
     */
    public static Calendar getToDayZero() {
        Calendar ca = Calendar.getInstance();
        ca.setTime(new Date());
        ca.set(Calendar.HOUR_OF_DAY, 0);
        ca.set(Calendar.MINUTE, 0);
        ca.set(Calendar.SECOND, 0);
        return ca;
    }

    /**
     * 当天延顺delayDay天
     *
     * @param delayDay
     * @return
     */
    public static Calendar getToDayZeroDelay(int delayDay) {
        Calendar calendar = getToDayZero();
        calendar.add(Calendar.DATE, delayDay);
        return calendar;
    }

    /**
     * @param date
     * @return
     */
    public static String tranDate(Date date) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String str = simpleDateFormat.format(date);
        return str;
    }

}
