package com.navigator.common.util;

import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

public class MD5Util {
    private static final Logger LOGGER = LoggerFactory.getLogger(MD5Util.class);


    public static String encoderPassword(String s) throws NoSuchAlgorithmException, UnsupportedEncodingException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        Base64.Encoder base64Encoder = Base64.getMimeEncoder();
        return base64Encoder.encodeToString(md5.digest(s.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * @param plainText 需要加密的字符串
     * @return
     * @Description 字符串加密为MD5 中文加密一致通用,必须转码处理： plainText.getBytes("UTF-8")
     */
    public static String encoderByMd5(String plainText) {
        StringBuffer rlt = new StringBuffer();
        rlt.append(md5String(plainText.getBytes(StandardCharsets.UTF_8)));
        return rlt.toString();
    }


    public static String md5String(byte[] data) {
        String md5Str = "";
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] buf = md5.digest(data);
            for (byte element : buf) {
                md5Str += Byte2HexUtil.byte2Hex(element);
            }
        } catch (Exception e) {
            md5Str = null;
        }
        return md5Str;
    }

    // 不带秘钥加密
    public static String md52(String text) {
        // 加密后的字符串
        String md5str = DigestUtils.md5Hex(text);
        System.out.println("MD52加密后的字符串为:" + md5str + "\t长度：" + md5str.length());
        return md5str;
    }

    /**
     * MD5验证方法
     *
     * @param text 明文
     * @param key  密钥
     * @param md5  密文
     */
    // 根据传入的密钥进行验证
    public static boolean verify(String text, String key, String md5) throws Exception {
        String md5str = md5(text, key);
        if (md5str.equalsIgnoreCase(md5)) {
            System.out.println("MD5验证通过");
            return true;
        }
        return false;
    }

    /**
     * @param text 明文
     * @param key  密钥
     * @return 密文
     */
    // 带秘钥加密
    public static String md5(String text, String key) {
        // 加密后的字符串
        String md5str = DigestUtils.md5Hex(text + key);
        System.out.println("MD5加密后的字符串为:" + md5str);
        return md5str;
    }

    public static void main(String[] args) throws Exception {
        // uuid + SumPremium + 密钥
        //2eb3f5491f04fad9bc27687b83305cc5
        String SumPremium = "500.00";
        String text = "ydb50036EEC33B2asd34ed";
        String key = "Picc37mu63ht38mw";
        System.out.println(DigestUtils.md5Hex(text + SumPremium + key));
        System.out.println(md5(text, key).length());
    }
}
