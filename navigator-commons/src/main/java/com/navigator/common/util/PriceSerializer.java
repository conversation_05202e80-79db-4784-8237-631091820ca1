package com.navigator.common.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class PriceSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            // 保留2位小数，四舍五入
            BigDecimal number = value.setScale(2, RoundingMode.HALF_UP);
            gen.writeNumber(number.toPlainString());
        } else {
            gen.writeNumber(value);
        }

    }
}