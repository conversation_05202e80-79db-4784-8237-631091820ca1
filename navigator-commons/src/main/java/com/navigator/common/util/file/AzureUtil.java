package com.navigator.common.util.file;

import com.microsoft.azure.storage.CloudStorageAccount;
import com.microsoft.azure.storage.StorageException;
import com.microsoft.azure.storage.file.*;

import java.io.IOException;
import java.net.URISyntaxException;
import java.security.InvalidKeyException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 微软云Azure读写共享文件帮助类
 */
public class AzureUtil {

    private static String SHARE_FILE_NAME = "share/shareFile";//需要读取数据共享文件夹名称--对应云盘中你需要读取文件的文件夹名称
    private static String SHARE_NEW_FILE_NAME = "xxx";//临时存放数据共享文件夹名称--这个是copy文件至新的路径使用的，如只需要copy到原地址则不需要改参数
    private static String SHARE_NAME ="navigator";// 微软云共享云盘名称
    private static String ACCOUNT_NAME ="ldcblob";//<ACCOUNT NAME> 账号
    private static String ACCOUNT_KEY ="****************************************************************************************";// 密码
    //注意连接字符串中的xxx和yyy，分别对应Access keys中的Storage account name 和 key。
    private static String format = "DefaultEndpointsProtocol=https;AccountName=" + ACCOUNT_NAME + ";AccountKey=" + ACCOUNT_KEY + "";

    private static CloudFileShare share;

    public static void main(String[] args) throws URISyntaxException, StorageException, IOException {
        //初始化
        initAzure(SHARE_NAME);
        //遍历共享文件夹下的文件
        getFileList();
    }

    /**
     * 初始化Azure
     * 获取连接
     *
     * @param shareName
     * @throws StorageException
     */
    public static void initAzure(String shareName) {
        //CloudStorageAccount 类表示一个 Azure Storage Account，我们需要先创建它的实例，才能访问属于它的资源。
        CloudStorageAccount storageAccount = null;
        try {
            // 获得StorageAccount对象
            storageAccount = CloudStorageAccount.parse(format);
            // 由StorageAccount对象创建CloudFileClient
            CloudFileClient fileClient = storageAccount.createCloudFileClient();
            // 获取对文件共享的引用   根据传入的contashareNameinerName, 获得share实例
            share = fileClient.getShareReference(shareName);
            //不存在则创建
//            if (share.createIfNotExists()) {
//                System.out.println("New share created");
//            }
        } catch (InvalidKeyException | URISyntaxException |StorageException invalidKey) {
            // Handle the exception
            invalidKey.printStackTrace();
        }
    }

    /**
     * 遍历共享文件夹下的文件
     *
     * @throws URISyntaxException
     * @throws StorageException
     */
    public static void getFileList() throws URISyntaxException, StorageException, IOException {
        //获取对共享根目录的引用。
        CloudFileDirectory rootDir = share.getRootDirectoryReference();

        //获取对包含该文件的目录的引用
        CloudFileDirectory sampleDir = rootDir.getDirectoryReference(SHARE_FILE_NAME);

        Iterable<ListFileItem> listFileItems = sampleDir.listFilesAndDirectories();
        for (ListFileItem listFileItem : listFileItems) {
            String fileName = listFileItem.getUri().getPath().split("/")[3];
            //获取要下载的文件的引用
            CloudFile file = sampleDir.getFileReference(fileName);

            //文件存在则复制到新路径
            if (!file.exists()) {
                System.err.println(file.getUri() + " 不存在");
                break;
            }
            //拼接文件名称
            String[] split = fileName.split("\\.");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String newFileName = split[0] + "_" + sdf.format(new Date()) + "." + split[1];

            //复制文件至newFilePath,复制成功则删除原地址文件
            if (moveFile(fileName, newFileName)) {
                delFile(fileName);
            } else {
                System.err.println("复制文件失败：" + listFileItem.getUri().getPath());
            }

            //将文件的内容写入控制台。
//            System.out.println(file.downloadText());
        }
    }

    /**
     * 复制文件至新的路径
     *
     * @param fileName    文件名称
     * @param newFileName 复制到目标地址后文件名称
     */
    public static boolean moveFile(String fileName, String newFileName) {
        boolean flag = false;
        try {
            CloudFileDirectory rootDir = share.getRootDirectoryReference();
            //获取对包含该文件的目录的引用
            CloudFileDirectory sampleDir = rootDir.getDirectoryReference(SHARE_FILE_NAME);
            //获取要下载的文件的引用
            CloudFile file = sampleDir.getFileReference(fileName);
            //获取文件复制到新的地址路径  如不需要移动文件至新的路径忽略1和2两句代码即可
            CloudFileDirectory webDir = rootDir.getDirectoryReference(SHARE_NEW_FILE_NAME);
            //复制
            CloudFile copyFile = webDir.getFileReference(newFileName);
            //copyFile.startCopy(file);
            System.out.println("文件复制到目标路径："+file.getUri());//copyFile.getUri()
            flag = true;
        } catch (StorageException e) {
            e.printStackTrace();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
        return flag;
    }

    /**
     * 删除指定文件
     *
     * @param filename 文件名称
     */
    public static void delFile(String filename) {
        try {
            // 获取对共享根目录的引用。
            CloudFileDirectory rootDir = share.getRootDirectoryReference();
            // 获取对要删除的文件所在目录的引用
            CloudFileDirectory containerDir = rootDir.getDirectoryReference(SHARE_FILE_NAME);
            CloudFile file;
            file = containerDir.getFileReference(filename);
            if (file.deleteIfExists()) {
                System.out.println("文件被删除：" + filename);
            } else {
                System.err.println("文件不存在：" + filename);
            }
        } catch (StorageException e) {
            e.printStackTrace();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
    }
}

