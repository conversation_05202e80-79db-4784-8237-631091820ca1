package com.navigator.common.util;

import com.navigator.common.dto.CompareObjectDTO;
import com.navigator.common.enums.ModifySourceEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.time.DateTimeUtil;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 比较实体类前后差异返回差异字段和内容
 */
public class BeanCompareUtils<T> {


    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个list分别存obj1,obj2此属性名的值
     *
     * @param originalObject 更改前
     * @param changeObject   更改后
     * @param ignoreList     选择不比较的属性
     * @param manualList     用户手动操作的属性
     * @return 属性差异比较结果map
     */
    public static List<CompareObjectDTO> compareFields(Object originalObject, Object changeObject, List<String> ignoreList, List<String> manualList) {
        List<CompareObjectDTO> compareObjectDTOList = new ArrayList<>();
        try {
            // 只有两个对象都是同一类型的才有可比性
            if (originalObject.getClass() == changeObject.getClass()) {
                Class clazz = originalObject.getClass();
                // 获取object的属性描述
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,
                        Object.class).getPropertyDescriptors();
                // 这里就是所有的属性了
                for (PropertyDescriptor pd : pds) {
                    // 属性名
                    String name = pd.getName();
                    // 如果当前属性选择不进行比较，跳到下一次循环
                    if (ignoreList != null && ignoreList.contains(name)) {
                        continue;
                    }
                    // get方法
                    Method readMethod = pd.getReadMethod();
                    // 在obj1上调用get方法等同于获得obj1的属性值
                    Object objBefore = readMethod.invoke(originalObject);
                    // 在obj2上调用get方法等同于获得obj2的属性值
                    Object objAfter = readMethod.invoke(changeObject);
                    if (objBefore instanceof Timestamp) {
                        objBefore = new Date(((Timestamp) objBefore).getTime());
                    }
                    if (objAfter instanceof Timestamp) {
                        objAfter = new Date(((Timestamp) objAfter).getTime());
                    }
                    if (objBefore instanceof Date) {
                        objBefore = DateTimeUtil.formatDate(objBefore);
                    }
                    if (objAfter instanceof Date) {
                        objAfter = DateTimeUtil.formatDate(objAfter);
                    }
                    if (objBefore instanceof BigDecimal) {
                        objBefore = ((BigDecimal) objBefore).setScale(6, RoundingMode.HALF_UP);
                    }
                    if (objAfter instanceof BigDecimal) {
                        objAfter = ((BigDecimal) objAfter).setScale(6, RoundingMode.HALF_UP);
                    }
                    if (objBefore == null && objAfter == null) {
                        continue;
                    } else if (objBefore == null && objAfter != null) {
                        generateList(name, compareObjectDTOList, objBefore, objAfter, manualList);
                        continue;
                    }
                    // 比较这两个值是否相等
                    if (!objBefore.equals(objAfter)) {
//                        if (judge(objBefore) && judge(objAfter) && new BigDecimal(String.valueOf(objBefore)).compareTo(new BigDecimal(String.valueOf(objAfter))) == 0) {
//                            continue;
//                        }
                        generateList(name, compareObjectDTOList, objBefore, objAfter, manualList);
                    }
                }
            } else {
                throw new BusinessException(ResultCodeEnum.FAILURE);
            }
            return compareObjectDTOList;
        } catch (Exception e) {
            System.out.println(e);
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
    }

    private static boolean judge(Object object) {
        if (object instanceof String) {
            String pattern = "^\\d+(\\.\\d+)?$";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(object.toString());
            return m.matches();
        }
        return false;
    }


    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个list分别存obj1,obj2此属性名的值
     *
     * @param originalObject 更改前
     * @param changedObject  更改后
     * @param compareList    选择比较的属性
     * @param manualList     用户手动操作的属性
     * @return 属性差异比较结果map
     */
    public static List<CompareObjectDTO> compareFieldsWithNeeding(Object originalObject, Object changedObject, List<String> compareList, List<String> manualList) {
        List<CompareObjectDTO> compareObjectDTOList = new ArrayList<>();
        try {
            // 只有两个对象都是同一类型的才有可比性
            if (originalObject.getClass() == changedObject.getClass()) {
                Class clazz = originalObject.getClass();
                // 获取object的属性描述
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,
                        Object.class).getPropertyDescriptors();
                // 这里就是所有的属性了
                for (PropertyDescriptor pd : pds) {
                    // 属性名
                    String name = pd.getName();
                    // 如果当前属性选择不进行比较，跳到下一次循环
                    if (compareList == null || compareList.size() == 0) {
                        return new ArrayList<>();
                    }
                    if (!compareList.contains(name)) {
                        continue;
                    }
                    // get方法
                    Method readMethod = pd.getReadMethod();
                    // 在obj1上调用get方法等同于获得obj1的属性值
                    Object objBefore = readMethod.invoke(originalObject);
                    // 在obj2上调用get方法等同于获得obj2的属性值
                    Object objAfter = readMethod.invoke(changedObject);
                    if (objBefore instanceof Timestamp) {
                        objBefore = new Date(((Timestamp) objBefore).getTime());
                    }
                    if (objAfter instanceof Timestamp) {
                        objAfter = new Date(((Timestamp) objAfter).getTime());
                    }
                    if (objBefore == null && objAfter == null) {
                        continue;
                    } else if (objBefore == null && objAfter != null) {
                        generateList(name, compareObjectDTOList, objBefore, objAfter, manualList);
                        continue;
                    }
                    // 比较这两个值是否相等
                    if (!objBefore.equals(objAfter)) {
                        generateList(name, compareObjectDTOList, objBefore, objAfter, manualList);
                    }
                }
            } else {
                throw new BusinessException(ResultCodeEnum.FAILURE);
            }
            return compareObjectDTOList;
        } catch (Exception e) {
            System.out.println(e);
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
    }

    private static void generateList(String name, List<CompareObjectDTO> compareObjectDTOList, Object objBefore, Object objAfter, List<String> manualList) {
        CompareObjectDTO compareObjectDTO = new CompareObjectDTO();
        compareObjectDTO.setName(name);
        compareObjectDTO.setBefore(objBefore == null ? "" : String.valueOf(objBefore));
        compareObjectDTO.setAfter(objAfter == null ? "" : String.valueOf(objAfter));
        if (manualList != null && manualList.size() > 0 && manualList.contains(name)) {
            compareObjectDTO.setSource(ModifySourceEnum.USER.getValue());
        } else {
            compareObjectDTO.setSource(ModifySourceEnum.SYSTEM.getValue());
        }
        compareObjectDTO.setUpdateTime(DateTimeUtil.formatDateTimeString(new Date()));
        compareObjectDTOList.add(compareObjectDTO);
    }

//    /**
//     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个list分别存obj1,obj2此属性名的值
//     * @param obj1 进行属性比较的对象1
//     * @param obj2 进行属性比较的对象2
//     * @param ignoreList 选择不比较的属性
//     * @return 属性差异比较结果map
//     */
//    public  Map<String, Map> compareFields(Object obj1, Object obj2, List<String> ignoreList){
//        try {
//            //装返回值得
//            Map<String, Map> map = new LinkedHashMap<>();
//            // 只有两个对象都是同一类型的才有可比性
//            if (obj1.getClass() == obj2.getClass()) {
//                Class clazz = obj1.getClass();
//                // 获取object的属性描述
//                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,
//                        Object.class).getPropertyDescriptors();
//                // 这里就是所有的属性了
//                for (PropertyDescriptor pd : pds) {
//                    // 属性名
//                    String name = pd.getName();
//                    // 如果当前属性选择不进行比较，跳到下一次循环
//                    if (ignoreList != null && ignoreList.contains(name)) {
//                        // get方法
//                        Method readMethod = pd.getReadMethod();
//                        // 在obj1上调用get方法等同于获得obj1的属性值
//                        Object objBefore = readMethod.invoke(obj1);
//                        // 在obj2上调用get方法等同于获得obj2的属性值
//                        Object objAfter = readMethod.invoke(obj2);
//                        if (objBefore instanceof Timestamp) {
//                            objBefore = new Date(((Timestamp) objBefore).getTime());
//                        }
//                        if (objAfter instanceof Timestamp) {
//                            objAfter = new Date(((Timestamp) objAfter).getTime());
//                        }
//                        if (objBefore == null && objAfter == null) {
//                            continue;
//                        } else if (objBefore == null && objAfter != null) {
//                            Map m = new LinkedHashMap();
//                            m.put("before",objBefore);
//                            m.put("after",objAfter);
//                            map.put(name, m);
//                            continue;
//                        }
//                        // 比较这两个值是否相等,不等则放入map
//                        if (!objBefore.equals(objAfter)) {
//                            Map m = new LinkedHashMap();
//                            m.put("before",objBefore);
//                            m.put("after",objAfter);
//                            map.put(name, m);
//                        }
//                    }
//                }
//            }else {
//                System.out.println("对象类型不一致，不能完成对比");
//            }
//            return map;
//        } catch (Exception e) {
//            System.out.println("错误");
//            return null;
//        }
//    }

    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个list分别存obj1,obj2此属性名的值
     *
     * @param originalObject 更改前
     * @param changeObject   更改后
     * @param ignoreList     选择不比较的属性
     * @param manualList     用户手动操作的属性
     * @return 属性差异比较结果map
     */
    public static List<CompareObjectDTO> getFields(Object originalObject, Object changeObject, List<String> ignoreList, List<String> manualList) {
        List<CompareObjectDTO> compareObjectDTOList = new ArrayList<>();
        try {
            // 只有两个对象都是同一类型的才有可比性
            if (originalObject.getClass() == changeObject.getClass()) {
                Class clazz = originalObject.getClass();
                // 获取object的属性描述
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,
                        Object.class).getPropertyDescriptors();
                // 这里就是所有的属性了
                for (PropertyDescriptor pd : pds) {
                    // 属性名
                    String name = pd.getName();
                    // 如果当前属性选择不进行比较，跳到下一次循环
                    if (ignoreList != null && ignoreList.contains(name)) {
                        continue;
                    }
                    // get方法
                    Method readMethod = pd.getReadMethod();
                    // 在obj1上调用get方法等同于获得obj1的属性值
                    Object objBefore = readMethod.invoke(originalObject);
                    // 在obj2上调用get方法等同于获得obj2的属性值
                    Object objAfter = readMethod.invoke(changeObject);
                    if (objBefore instanceof Timestamp) {
                        objBefore = new Date(((Timestamp) objBefore).getTime());
                    }
                    if (objAfter instanceof Timestamp) {
                        objAfter = new Date(((Timestamp) objAfter).getTime());
                    }
                    if (objBefore instanceof Date) {
                        objBefore = DateTimeUtil.formatDate(objBefore);
                    }
                    if (objAfter instanceof Date) {
                        objAfter = DateTimeUtil.formatDate(objAfter);
                    }
                    if (objBefore instanceof BigDecimal) {
                        objBefore = ((BigDecimal) objBefore).setScale(6, RoundingMode.HALF_UP);
                    }
                    if (objAfter instanceof BigDecimal) {
                        objAfter = ((BigDecimal) objAfter).setScale(6, RoundingMode.HALF_UP);
                    }
                    generateList(name, compareObjectDTOList, objBefore, objAfter, manualList);
                }
            } else {
                return Collections.emptyList();
            }

            return compareObjectDTOList;
        } catch (Exception e) {
            System.out.println(e);
            throw new BusinessException(ResultCodeEnum.FAILURE);
        }
    }

    public static Map<String, CompareObjectDTO> mapCompareObjectDTO(List<CompareObjectDTO> compareObjectDTOList) {
        Map<String, CompareObjectDTO> mapObj = new HashMap<>();
        for (CompareObjectDTO compareObjectDTO : compareObjectDTOList) {
            mapObj.put(compareObjectDTO.getName(), compareObjectDTO);
        }
        return mapObj;
    }

    /**
     * 比较两个map的差异，目前主要用于开发测试，尽量避免使用
     * @param originalMap
     * @param targetMap
     * @param ignoreList
     * @return
     */
    @Deprecated
    public static List<CompareObjectDTO> compareMapFields(Map<String, Object> originalMap, Map<String, Object> targetMap, List<String> ignoreList) {
        List<CompareObjectDTO> list = new ArrayList<>();
        if (null == ignoreList) {
            ignoreList = new ArrayList<>();
            ignoreList.add("id");
            ignoreList.add("updatedAt");
            ignoreList.add("updatedBy");
            ignoreList.add("createdAt");
            ignoreList.add("createdBy");
            ignoreList.add("serialVersionUID");
        }

        Set<String> originalMapKeySet = originalMap.keySet();

        for (String s : targetMap.keySet()) {
            CompareObjectDTO compareObjectDTO = new CompareObjectDTO();
            if (ignoreList.contains(s)) {
                continue;
            }

            if (!originalMapKeySet.contains(s)) {
                compareObjectDTO.setName(s);
                compareObjectDTO.setAfter("多字段");
                list.add(compareObjectDTO);
                continue;
            }

            Object oo = originalMap.get(s);
            Object to = targetMap.get(s);

            String oos = String.valueOf(oo);
            String tos = String.valueOf(to);

            if (!oos.equals(tos)) {
                compareObjectDTO.setName(s);
                compareObjectDTO.setBefore(oos);
                compareObjectDTO.setAfter(tos);
                list.add(compareObjectDTO);
            }
        }
        return list;
    }

}
