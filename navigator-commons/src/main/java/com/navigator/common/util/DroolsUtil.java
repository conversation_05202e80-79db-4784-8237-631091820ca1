package com.navigator.common.util;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.ContractApproveBizInfoDTO;
import com.navigator.common.dto.DroolsRuleBizInfoDTO;
import com.navigator.common.dto.DroolsRuleDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.kie.api.KieBase;
import org.kie.api.io.Resource;
import org.kie.api.io.ResourceType;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.kie.internal.utils.KieHelper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class DroolsUtil {

    /**
     * 工作流专用
     * 根据规则文件和工作流数据进行匹配
     *
     * @param ruleName
     * @param bizParameter
     */
    public static void run(String ruleName, ContractApproveBizInfoDTO bizParameter) {
        System.out.println("drools Test start ");

        //DROOLS文件加载模式
        KieHelper helper = new KieHelper();
        Resource resource = ResourceFactory.newClassPathResource(ruleName, "UTF-8");
        helper.addResource(resource);
        KieBase kieBase = helper.build();
        KieSession kieSession = kieBase.newKieSession();

        kieSession.insert(bizParameter);

        int count = kieSession.fireAllRules();

        kieSession.dispose();
        System.out.println("drools Test end ");
    }


    /**
     * 通用，目前主要用于现货贸易合同的条款组、条款的匹配
     * 按照规则文件和通用规则数据对象进行匹配
     *
     * @param ruleFileName
     * @param ruleBizInfoDTO
     */
    public static void runFile(String ruleFileName, DroolsRuleBizInfoDTO ruleBizInfoDTO) {

        log.info("Drools Start Run with file=" + ruleFileName);
        log.info(JSON.toJSONString(ruleBizInfoDTO));

        //DROOLS文件加载模式
        KieHelper helper = new KieHelper();
        Resource resource = ResourceFactory.newClassPathResource(ruleFileName, "UTF-8");
        helper.addResource(resource);
        KieBase kieBase = helper.build();
        KieSession kieSession = kieBase.newKieSession();

        kieSession.insert(ruleBizInfoDTO);
        //kieSession.setGlobal(ruleBizInfoDTO.getMapBizData());

        int count = kieSession.fireAllRules();

        kieSession.dispose();

        log.info("Drools End Run with file=" + ruleFileName);
        // BUGFIX：case-1002824 添加审批结果日志 Author: wan 2024-12-04 Start
        log.info("Drools 匹配结果" + FastJsonUtils.getBeanToJson(ruleBizInfoDTO));
        // BUGFIX：case-1002824 添加审批结果日志 Author: wan 2024-12-04 end

    }

    /**
     * 通用，目前主要用于现货贸易合同的条款组、条款的匹配
     * 按照规则内容和通用规则数据对象进行匹配
     *
     * @param ruleContent
     * @param ruleBizInfoDTO
     */
    public static void runContent(String ruleContent, DroolsRuleBizInfoDTO ruleBizInfoDTO) {
        log.info("Drools Start Run with Content");
        log.info(ruleContent);
        log.info(JSON.toJSONString(ruleBizInfoDTO));

        //DROOLS规则内容加载模式
        KieHelper helper = new KieHelper();
        helper.addContent(ruleContent, ResourceType.DRL);
        KieBase kieBase = helper.build();
        KieSession kieSession = kieBase.newKieSession();

        kieSession.insert(ruleBizInfoDTO);

        int count = kieSession.fireAllRules();

        kieSession.dispose();

        log.info("Drools End Run with Content");
    }

    /**
     * 通用，目前主要用于现货贸易合同的条款组、条款的匹配
     * 按照规则数据列表和通用规则数据对象进行匹配
     *
     * @param ruleDataDTOList
     * @param ruleBizInfoDTO
     */
    public static void runRuleInfos(List<DroolsRuleDataDTO> ruleDataDTOList, DroolsRuleBizInfoDTO ruleBizInfoDTO) {
        log.info("Drools Start Run with RuleInfoDTO");
        log.info(JSON.toJSONString(ruleDataDTOList));
        log.info(JSON.toJSONString(ruleBizInfoDTO));

        String ruleContent = DroolsContractRuleBuilder.buildRule(ruleDataDTOList);

        //DROOLS规则内容加载模式
        KieHelper helper = new KieHelper();
        helper.addContent(ruleContent, ResourceType.DRL);
        KieBase kieBase = helper.build();
        KieSession kieSession = kieBase.newKieSession();

        kieSession.insert(ruleBizInfoDTO);

        int count = kieSession.fireAllRules();

        kieSession.dispose();

        log.info("Drools End Run with Content");
        log.info("Drools 匹配结果" + FastJsonUtils.getBeanToJson(ruleBizInfoDTO));
    }

    public static void runRuleContent(String ruleContent, ContractApproveBizInfoDTO ruleBizInfoDTO) {
        log.info("Drools Start Run with ruleContent");
        log.info(ruleContent);
        //DROOLS规则内容加载模式
        KieHelper helper = new KieHelper();
        helper.addContent(ruleContent, ResourceType.DRL);
        KieBase kieBase = helper.build();
        KieSession kieSession = kieBase.newKieSession();
        kieSession.insert(ruleBizInfoDTO);
        kieSession.fireAllRules();
        kieSession.dispose();

        log.info("Drools End Run with Content");
        log.info("Drools 匹配结果" + FastJsonUtils.getBeanToJson(ruleBizInfoDTO));
    }

    public static void main(String[] args) {
        List<DroolsRuleDataDTO> ruleDataDTOList = new ArrayList<>();
        DroolsRuleDataDTO ruleDataDTO1 = new DroolsRuleDataDTO()
                .setRuleCode("Rule_1")
                .setRuleInfo("mapBizData.get(\"deliveryType\") == 3")
                .setReferType("LOA");
        DroolsRuleDataDTO ruleDataDTO2 = new DroolsRuleDataDTO()
                .setRuleCode("Rule_3")
                .setRuleInfo("mapBizData.get(\"deliveryType\") == 1")
                .setReferType("LOA");
        DroolsRuleDataDTO ruleDataDTO3 = new DroolsRuleDataDTO()
                .setRuleCode("Rule_4")
                .setRuleInfo("mapBizData.get(\"contractType\") == 1")
                .setReferType("LOA_2");
        ruleDataDTOList.add(ruleDataDTO1);
        ruleDataDTOList.add(ruleDataDTO2);
        ruleDataDTOList.add(ruleDataDTO3);

        DroolsRuleBizInfoDTO ruleBizInfoDTO = new DroolsRuleBizInfoDTO();
        Map<String, Object> mapBizData = new HashMap<>();
        mapBizData.put("deliveryType", 1);
        mapBizData.put("contractType", 1);
        ruleBizInfoDTO.setMapBizData(mapBizData);
        runRuleInfos(ruleDataDTOList, ruleBizInfoDTO);
    }


}
