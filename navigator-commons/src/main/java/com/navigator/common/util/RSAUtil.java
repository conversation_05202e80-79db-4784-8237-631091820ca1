package com.navigator.common.util;

import lombok.SneakyThrows;

import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * RSA公钥\私钥
 *
 * <AUTHOR>
 */
public class RSAUtil {

  /**
   * 生成RSA密钥对
   *
   * @return
   * @throws NoSuchAlgorithmException
   */
  public static KeyPair generateKeyPair() throws NoSuchAlgorithmException {
    KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
    keyPairGenerator.initialize(2048);
    return keyPairGenerator.generateKeyPair();
  }

  /**
   * 将私钥转换成字符串
   *
   * @param privateKey
   * @return
   */
  public static String privateKeyToString(PrivateKey privateKey) {
    return Base64.getEncoder().encodeToString(privateKey.getEncoded());
  }

  /**
   * 将公钥转换成字符串
   *
   * @param publicKey
   * @return
   */
  public static String publicKeyToString(PublicKey publicKey) {
    return Base64.getEncoder().encodeToString(publicKey.getEncoded());
  }

  /**
   * 从字符串中恢复私钥
   *
   * @param privateKeyStr
   * @return
   */
  @SneakyThrows
  public static PrivateKey stringToPrivateKey(String privateKeyStr) {
    byte[] decodedKey = Base64.getDecoder().decode(privateKeyStr);
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    return keyFactory.generatePrivate(new PKCS8EncodedKeySpec(decodedKey));
  }

  /**
   * 从字符串中恢复公钥
   *
   * @param publicKeyStr
   * @return
   */
  @SneakyThrows
  public static PublicKey stringToPublicKey(String publicKeyStr) {
    byte[] decodedKey = Base64.getDecoder().decode(publicKeyStr);
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    return keyFactory.generatePublic(new X509EncodedKeySpec(decodedKey));
  }

  /**
   * 示例：生成密钥对并转换成字符串
   *
   * @param args
   * @throws Exception
   */
  public static void main(String[] args) throws Exception {
    KeyPair keyPair = generateKeyPair();
    String privateKeyStr = privateKeyToString(keyPair.getPrivate());
    String publicKeyStr = publicKeyToString(keyPair.getPublic());

    System.out.println("Private Key (String): " + privateKeyStr);
    System.out.println("Public Key (String): " + publicKeyStr);

    // 从字符串中恢复密钥对
    PrivateKey recoveredPrivateKey = stringToPrivateKey(privateKeyStr);
    PublicKey recoveredPublicKey = stringToPublicKey(publicKeyStr);

    // 验证恢复是否成功（这里只是简单地打印出来，实际应用中可能需要进行更严格的验证）
    System.out.println("Recovered Private Key: " + recoveredPrivateKey);
    System.out.println("Recovered Public Key: " + recoveredPublicKey);
  }
}
