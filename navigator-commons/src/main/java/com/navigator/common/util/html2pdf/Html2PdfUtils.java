package com.navigator.common.util.html2pdf;

import cn.hutool.core.io.FileUtil;
import cn.hutool.system.OsInfo;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.HtmlInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.dto.WatermarkDTO;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.WatermarkUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;

/**
 * Description: html转pdf的服务
 * Created by <PERSON><PERSON><PERSON> on 2021/11/24 19:02
 */
@Slf4j
@Component
public class Html2PdfUtils {

    @Resource
    private AzureBlobUtil azureBlobUtil;
    @Autowired
    private WatermarkUtil watermarkUtil;

    /**
     * 生成pdf
     *
     * @param htmlInfoDTO
     * @return FileBaseInfoDTO
     */
    public FileBaseInfoDTO genPdfAndImage(HtmlInfoDTO htmlInfoDTO) {
        // 将html暂存到/tmp/htmlFile
        String htmlUrl = htmlInfoDTO.getHtmlUrl();
        String htmlString = htmlInfoDTO.getHtmlContent();

        // 判断是否存在目录
        String htmlPath = new File(htmlUrl).getAbsolutePath();
        FileUtil.mkParentDirs(htmlPath);
        try {
            PrintStream printStream = new PrintStream(new FileOutputStream(htmlPath), true);
            printStream.print(htmlString);
            printStream.flush();
            printStream.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }

        // html生成pdf
        return execute(htmlUrl, "pdf", htmlInfoDTO.getModuleType(), htmlInfoDTO.getContractCode(), htmlInfoDTO.getTtCode(), htmlInfoDTO.getCompanyId());

    }
//
//
//    /**
//     * 解析html生成pdf
//     *
//     * @param pageUrl fileRecord/generate/contract/html/2021-12-08/10/DFSBMS2100028_电子合同（原件）_1bba1.html
//     * @param suffix  文件后缀
//     * @return
//     * @throws Exception
//     */
//    public FileBaseInfoDTO execute(String pageUrl, String suffix, String moduleType, String contractCode, String ttCode) {
////        FileBaseInfoDTO fileBaseInfoDTO = new FileBaseInfoDTO();
//        FileBaseInfoDTO fileBaseInfoDTO = null;
//        try {
//            // 拼接执行cmd 的指令
//            StringBuilder cmdStr = new StringBuilder();
//            StringBuilder outPutPath = new StringBuilder();
//            StringBuilder fileName = new StringBuilder();
//            if (new OsInfo().isLinux()) {
//                // 对文件名进行处理 2021-04-26-15-13-33-ef76eb82d08ab.pdf
//                fileName.append(FileUtil.getName(pageUrl).split("\\.")[0]);
//
//                if ("pdf".equals(suffix)) {
//                    cmdStr.append("wkhtmltopdf");
//                    cmdStr.append(" --margin-bottom 14 ");
////                    cmdStr.append(" --margin-bottom 16  --margin-top 12  --header-left \"LDC达孚\" --header-right \"[date] [time]\" --header-font-size 10 --header-line --header-spacing 4 --footer-spacing 4 --footer-center \"- 第 [page] 页- 共[toPage] 页\" --footer-font-size 10 ");
//                    fileName.append(".").append("pdf");
//
//                    // 输出路径 fileRecord/generate/pdf/2021-04-22/2021-04-22-15-12-12-b7026def87854.pdf
//                    String pdfPath = FilePathUtil.getPdfPath(new File(pageUrl).getParentFile().getName(), fileName.toString());
//                    if (ModuleTypeEnum.LOGIN.getModule().equalsIgnoreCase(moduleType)) {
//                        pdfPath = FilePathUtil.getLoginPdfPath(new File(pageUrl).getParentFile().getName(), fileName.toString());
//                    } else {
////                        cmdStr.append("--margin-top 14  --header-left ")
////                                .append(ttCode).append(" --header-right ").append(contractCode)
////                                .append(" --header-font-size 10 --header-line --header-spacing 4 --footer-spacing 4 --footer-center [page]/[toPage] --footer-font-size 10 ");
//                        cmdStr.append("--margin-top 14 ")
//                                .append(" --footer-spacing 4 --footer-center [page]/[toPage] ")
//                                .append("--footer-left ")
//                                .append(contractCode).append(" --footer-right ").append(ttCode).append(" --footer-font-size 9 --footer-font-name simsun ");
//                    }
//                    outPutPath.append(pdfPath);
//                    log.info("pdf转Html输出目录信息==========={}", outPutPath);
//                    // 判断是否存在目录
//                    String absoluteOutPath = new File(outPutPath.toString()).getAbsolutePath();
//                    FileUtil.mkParentDirs(absoluteOutPath);
//
//                    // 拼接指令 wkhtmltopdf html绝对路径 pdf生成路径
//                    cmdStr.append(" ").append(new File(pageUrl).getAbsolutePath()).append(" ").append(absoluteOutPath);
//
//                    // 执行指令
//                    boolean success = CmdUtil.execute(cmdStr.toString());
//                    fileBaseInfoDTO = uploadToBlob(pdfPath);
//                    // 补充文件信息
////                    fileBaseInfoDTO.setAttachName(fileName.toString())
////                            .setOriginalName(fileName.toString())
////                            .setAttachUrl(outPutPath.toString())
////                            .setAttachStuff(".pdf");
//
//                    if (!success) {
//                        throw new Exception("html转换pdf过程异常,执行指令:" + cmdStr);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("html转化pdf异常,异常原因:{}", e.getMessage());
//        }
//        return fileBaseInfoDTO;
//    }

    /**
     * 解析html生成pdf
     *
     * @param pageUrl fileRecord/generate/contract/html/2021-12-08/10/DFSBMS2100028_电子合同（原件）_1bba1.html
     * @param suffix  文件后缀
     * @return
     * @throws Exception
     */
    public FileBaseInfoDTO execute(String pageUrl, String suffix, String moduleType, String contractCode, String ttCode, Integer companyId) {
//        FileBaseInfoDTO fileBaseInfoDTO = new FileBaseInfoDTO();
        FileBaseInfoDTO fileBaseInfoDTO = null;
        try {
            // 拼接执行cmd 的指令
            StringBuilder cmdStr = new StringBuilder();
            StringBuilder outPutPath = new StringBuilder();
            StringBuilder fileName = new StringBuilder();
            if (new OsInfo().isLinux()) {
                // 对文件名进行处理 2021-04-26-15-13-33-ef76eb82d08ab.pdf
                fileName.append(FileUtil.getName(pageUrl).split("\\.")[0]);

                if ("pdf".equals(suffix)) {
                    cmdStr.append("wkhtmltopdf");
//                    cmdStr.append(" --margin-left 20.32 ");
//                    cmdStr.append(" --margin-right 20.32 ");
//                    cmdStr.append(" --margin-bottom 20.32 ");
                    cmdStr.append(" --margin-left 17 ");
                    cmdStr.append(" --margin-right 17 ");
//                    cmdStr.append(" --margin-bottom 16 ");
//                    cmdStr.append(" --margin-bottom 16  --margin-top 12  --header-left \"LDC达孚\" --header-right \"[date] [time]\" --header-font-size 10 --header-line --header-spacing 4 --footer-spacing 4 --footer-center \"- 第 [page] 页- 共[toPage] 页\" --footer-font-size 10 ");
                    fileName.append(".").append("pdf");

                    // 输出路径 fileRecord/generate/pdf/2021-04-22/2021-04-22-15-12-12-b7026def87854.pdf
                    String pdfPath = FilePathUtil.getPdfPath(new File(pageUrl).getParentFile().getName(), fileName.toString());
                    if (ModuleTypeEnum.LOGIN.getModule().equalsIgnoreCase(moduleType)) {
                        pdfPath = FilePathUtil.getLoginPdfPath(new File(pageUrl).getParentFile().getName(), fileName.toString());
                    } else {
//                        cmdStr.append("--margin-top 14  --header-left ")
//                                .append(ttCode).append(" --header-right ").append(contractCode)
//                                .append(" --header-font-size 10 --header-line --header-spacing 4 --footer-spacing 4 --footer-center [page]/[toPage] --footer-font-size 10 ");
                        cmdStr
//                                .append("--margin-top 12 ")
//                                .append("--margin-top 15.24 ")
                                .append(" --footer-spacing 4 --footer-center [page]/[toPage] ")
                                .append("--footer-left ")
                                .append(contractCode).append(" --footer-right ")
                                .append(ttCode).append(" --footer-font-size 8 --footer-font-name simsun ")
//                                .append("--disable-smart-shrinking ")
//                                .append(ttCode).append(" --footer-font-size 8 --footer-font-name GB2312 ")
                                .append("--replace \"<br>\" \"\\n\" ");
                    }
                    outPutPath.append(pdfPath);
                    log.info("pdf转Html输出目录信息==========={}", outPutPath);
                    // 判断是否存在目录
                    String absoluteOutPath = new File(outPutPath.toString()).getAbsolutePath();
                    FileUtil.mkParentDirs(absoluteOutPath);

                    // 拼接指令 wkhtmltopdf html绝对路径 pdf生成路径
                    cmdStr.append(" ").append(new File(pageUrl).getAbsolutePath()).append(" ").append(absoluteOutPath);

                    // 执行指令
                    boolean success = CmdUtil.execute(cmdStr.toString());
                    log.info("uploadToBlob start");
                    fileBaseInfoDTO = uploadToBlob(pdfPath, companyId);
                    // 补充文件信息
//                    fileBaseInfoDTO.setAttachName(fileName.toString())
//                            .setOriginalName(fileName.toString())
//                            .setAttachUrl(outPutPath.toString())
//                            .setAttachStuff(".pdf");

                    if (!success) {
                        throw new Exception("html转换pdf过程异常,执行指令:" + cmdStr);
                    }
                }
            }
        } catch (Exception e) {
            log.error("html转化pdf异常,异常原因:{}", e.getMessage());
        }
        return fileBaseInfoDTO;
    }

    /**
     * 解析html生成pdf
     *
     * @return
     * @throws Exception
     */
    public Result executeV2(String cmd) {
        StringBuilder cmdStr = new StringBuilder();
        try {
            // 拼接执行cmd 的指令
            if (new OsInfo().isLinux()) {
                // 对文件名进行处理 2021-04-26-15-13-33-ef76eb82d08ab.pdf
                cmd = !StringUtils.isBlank(cmd) ? cmd :
                        "wkhtmltopdf  --margin-bottom 16  --margin-top 12  --header-left \"LDC达孚\" --header-right \"[date][time]\" --header-font-size 10 --header-line --header-spacing 4 --footer-spacing 4 --footer-center \"- 第[page]页- 共[toPage]页\" --footer-font-size 10  /webapp/navigator/service-admin/fileRecord/generate/contract/html/2022-07-05/2233/TJIBSBMS2204886-电子合同（原件）-72446.html /webapp/navigator/service-admin/fileRecord/generate/contract/pdf/2022-07-05/2233/TJIBSBMS2204886-电子合同（原件）-72446.pdf";
                cmdStr.append(cmd);

                log.info("pdf转Html输出目录信息==========={}", cmdStr.toString());
                // 执行指令
                boolean success = CmdUtil.execute(cmdStr.toString());
                if (!success) {
                    throw new Exception("html转换pdf过程异常,执行指令:" + cmdStr);
                }
            }
        } catch (Exception e) {
            log.error("html转化pdf异常,异常原因:{}", e.getMessage());
        }
        return Result.success("成功执行wkhtmltopdf" + cmdStr.toString());
    }

    private FileBaseInfoDTO uploadToBlob(String pdfPath, Integer companyId) {
        // 将文件上传到Blob上
        File tempFile = new File(pdfPath);
        InputStream inputStream = null;
        try {
            inputStream = new FileInputStream(tempFile);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        log.info("uploadToBlob===========pdfPath:" + pdfPath + ",companyId:" + companyId);
        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.uploadByInputStream(inputStream, tempFile.getParent(), tempFile.getName(), BlobFileContextEnum.PDF.getFileType());
        log.info("fileBaseInfoDTO:{}", JSON.toJSONString(fileBaseInfoDTO));
        try {
            byte[] fileContent = Files.readAllBytes(tempFile.toPath());
            MockMultipartFile mockMultipartFile = new MockMultipartFile(tempFile.getName(), tempFile.getName(), null, fileContent);
            WatermarkDTO watermarkDTO = watermarkUtil.generateWaterMark(mockMultipartFile, companyId);
            FileBaseInfoDTO fileBaseInfoDTO1 = azureBlobUtil.uploadWhiteByInputStream(watermarkDTO.getInputStream(), tempFile.getParent(), fileBaseInfoDTO.getAttachName(), BlobFileContextEnum.PDF.getFileType());
//            log.info("fileBaseInfoDTO1:{}", JSON.toJSONString(fileBaseInfoDTO1));
        } catch (IOException e) {
            log.error("uploadToBlob error:{}", JSON.toJSONString(e));
        }

        return fileBaseInfoDTO;
    }

    // 将png和pdf资源上传至oss
//    private void upLoadResource(String filePath, String fileName, String suffix) throws IOException {
//        // 将生成的pdf和图片上传至oss,回传上传地址
//        File tempFile = new File(filePath);
//
//        // 存oss 文件服务器
//        InputStream inputStream = new FileInputStream(tempFile);
//        MultipartFile multipartFile = new MockMultipartFile(fileName, fileName, null, inputStream);
//
//        FileDTO fileDTO = new FileDTO();
//        fileDTO.setProject(OssEnum.PROJECT_ERP.getCode());
//        fileDTO.setSystem(OssEnum.SYSTEM_TIANTOUCAI.getCode());
//        if ("pdf".equals(suffix)) {
//            fileDTO.setCatalog(OssEnum.CONTRACT_PDF.getCode());
//        } else if ("png".equals(suffix)) {
//            fileDTO.setCatalog(OssEnum.CONTRACT_IMAGE.getCode());
//        }
//        fileDTO.setUseOriginName(true);
//
//        fileUploadService.upload(multipartFile, fileDTO);
//    }
}
