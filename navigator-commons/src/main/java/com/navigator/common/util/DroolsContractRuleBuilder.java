package com.navigator.common.util;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.DroolsRuleDataDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class DroolsContractRuleBuilder {

    /**
     * 单体规则生成
     * 整体规则生成
     */

    static String TAB = "\t";
    static String CRLF = "\n";

//
//    /**
//     * 组装单条规则
//     *
//     * @param ruleDataDTO
//     * @return
//     */
//    public static String buildSingleRule(DroolsRuleDataDTO ruleDataDTO) {
//        StringBuilder singleRule = new StringBuilder();
//        singleRule.append("rule ").append(ruleDataDTO.getRuleCode()).append(CRLF);
//        singleRule.append(TAB).append("when").append(CRLF);
//        singleRule.append(TAB).append(TAB).append("$r:DroolsRuleBizInfoDTO(").append(ruleDataDTO.getRuleInfo()).append(")").append(CRLF);
//        singleRule.append(TAB).append("then").append(CRLF);
//        singleRule.append(TAB).append(TAB).append("$r.getMatchRules().add(drools.getRule().getName());").append(CRLF);
//        singleRule.append(TAB).append("end").append(CRLF);
//        return singleRule.toString();
//    }



    /**
     * 组装单条规则
     *
     * @param ruleDataDTO
     * @return
     */
    public static String buildSingleRule(DroolsRuleDataDTO ruleDataDTO) {
        StringBuilder singleRule = new StringBuilder();
        singleRule.append("rule ").append(ruleDataDTO.getRuleCode()).append(CRLF);
        singleRule.append(TAB).append("when").append(CRLF);
        singleRule.append(TAB).append(TAB).append("$r:DroolsRuleBizInfoDTO(").append(ruleDataDTO.getRuleInfo()).append(")").append(CRLF);
        singleRule.append(TAB).append("then").append(CRLF);
        singleRule.append(TAB).append(TAB).append("$r.getMatchRules().add(drools.getRule().getName());").append(CRLF);
        singleRule.append(TAB).append(TAB).append("$r.getMatchRuleList().add(")
                .append("new DroolsRuleDataDTO(\"").append(ruleDataDTO.getRuleCode())
                .append("\",").append(FastJsonUtils.getBeanToJson(ruleDataDTO.getRuleInfo()))
                .append(",\"")
                .append(ruleDataDTO.getReferType())
                .append("\"));")
                .append(CRLF);
        singleRule.append(TAB).append("end").append(CRLF);
        return singleRule.toString();
    }

    /**
     * 统一结尾规则
     *
     * @return
     */
    public static String getEndingRule() {

        StringBuilder singleRule = new StringBuilder();
        singleRule.append("rule ending").append(CRLF);
        singleRule.append(TAB).append("when").append(CRLF);
        singleRule.append(TAB).append(TAB).append("$r:DroolsRuleBizInfoDTO(flag==1)").append(CRLF);
        singleRule.append(TAB).append("then").append(CRLF);
        singleRule.append(TAB).append(TAB).append("update($r);").append(CRLF);
        singleRule.append(TAB).append("end").append(CRLF);
        return singleRule.toString();
    }

    /**
     * 组装规则
     *
     * @param ruleInfoDTOList
     * @return
     */
    public static String buildRule(List<DroolsRuleDataDTO> ruleInfoDTOList) {

        log.info("DroolsContractUtil Start Build RuleContent");
        log.info(JSON.toJSONString(ruleInfoDTOList));

        StringBuilder ruleContent = new StringBuilder();

        //规则头
        ruleContent.append("import java.util.HashMap; ").append(CRLF);
        ruleContent.append("import java.util.List; ").append(CRLF);
        ruleContent.append("import com.navigator.common.dto.DroolsRuleBizInfoDTO; ").append(CRLF);
        ruleContent.append("import com.navigator.common.dto.DroolsRuleDataDTO; ").append(CRLF);
        ruleContent.append(CRLF);
        ruleContent.append("lock-on-active true ").append(CRLF);
        ruleContent.append(CRLF);
        //逐条组装单个规则语句块
        for (DroolsRuleDataDTO ruleDataDTO : ruleInfoDTOList) {
            String singleRuleContent = buildSingleRule(ruleDataDTO);
            ruleContent.append(singleRuleContent).append(CRLF);
        }
        //统一结束规则语句块
        String endingRule = getEndingRule();
        ruleContent.append(endingRule);

        log.info("DroolsContractUtil End Build RuleContent");
        log.info("ruleContent拼接1" + ruleContent.toString());
        return ruleContent.toString();
    }

}
