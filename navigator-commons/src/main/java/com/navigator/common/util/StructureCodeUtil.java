package com.navigator.common.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

@Slf4j
public class StructureCodeUtil {
    public static String numToCode(int n){
        String s = Strings.EMPTY;
        while (n > 0){
            int m = n % 26;
            if (m == 0) {
                m = 26;
            }
            s = (char)(m + 64) + s;
            n = (n - m) / 26;
        }
        return s;
    }
    public static int codeToNum(String s){
        if (StringUtils.isBlank(s)) {
            return 0;
        }
        int n = 0;
        String s1 = s.toUpperCase();
        char[] chars = s1.toCharArray();
        for (int i = s.length() - 1, j = 1; i >= 0; i--, j *= 26){
            char c =chars[i];
            if (c < 'A' || c > 'Z') return 0;
            n += ((int)c - 64) * j;
        }
        return n;
    }
}
