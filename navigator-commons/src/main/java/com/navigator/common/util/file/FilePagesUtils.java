package com.navigator.common.util.file;

import com.itextpdf.text.pdf.PdfReader;
import com.navigator.common.config.properties.AzureBlobProperties;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.implementation.bytecode.Throw;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.ooxml.POIXMLDocument;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;

import java.io.FileInputStream;
import java.io.IOException;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/12
 */
@Slf4j
public class FilePagesUtils {


    public static int filesPage(String url, String fileType) {

        switch (fileType) {
            case "pdf":
            case ".pdf":
                return countPdfPage(url);
            case "docx":
            case ".docx":
                return countWordPage(url);
            case "doc":
            case ".doc":
                return countDocPage(url);
        }

        return 0;
    }

    /**
     * 计算PDF格式文档的页数
     */
    public static Integer countPdfPage(String url) {
        Integer pageNum = 0;
        PdfReader pdfReader = null;
        try {
            pdfReader = new PdfReader(url);
            pageNum = pdfReader.getNumberOfPages();
            log.info("页数:" + pageNum);
        } catch (Exception e) {
            log.info("获取协议合同数据页数错误:" + e.getMessage());
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } finally {
            try {
                pdfReader.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return pageNum;
    }

    public static void main(String[] args) {
        String url = "https://csm4nnvgsto004.blob.core.chinacloudapi.cn/preuat/preuat/fileRecord/upload/file/magellan/2023-07-14/97b78b8d3dff417ab61e1ccc2bc0f214_TJIBSBMS2203504_电子合同（原件）_02485.pdf";
        FilePagesUtils filePagesUtils = new FilePagesUtils();
        url = filePagesUtils.urlPathAnalysis(url);
        String token = "?ss=b&sig=IlUzSHN%2Fh8W1b1%2FRwVSqqYoLM31Y28%2BhqmYY71Famn4%3D&st=2023-07-14T06%3A19%3A56Z&se=2023-07-17T06%3A19%3A56Z&sv=2019-02-02&srt=sco&sp=r&sr=sco";

        Integer page = countPdfPage(url + token);
        log.info("page{}", page);
    }

    private String urlPathAnalysis(String url) {
        int index = url.lastIndexOf("/");
        String hostUrl = getHostUrl();
        try {
            //判断路径层级
            if (index >= 0) {
                String uFileName = url.substring(index);
                String fileNameEncode = URLEncoder.encode(uFileName, "UTF-8");
                String pathDir = url.substring(0, index);
                url = pathDir + fileNameEncode;
            } else {
                String fileNameEncode = URLEncoder.encode(url, "UTF-8");
                url = hostUrl + fileNameEncode;
            }
        } catch (Exception ex) {
            log.error("FileNotFoundException", ex);
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        return url;
    }

    public String getHostUrl() {
        AzureBlobProperties azureBlobProperties = new AzureBlobProperties();
        String host = azureBlobProperties.getHost();
        String env = azureBlobProperties.getEnv();
        String containName = azureBlobProperties.getContainName();
        return host + "/" + containName + "/" + env + "/";
    }

    /**
     * 计算word格式文档的页数
     */
    public static Integer countWordPage(String url) {
        Integer pageNum = 0;
        ZipSecureFile.setMinInflateRatio(-1.0d);
        XWPFDocument docx = null;
        try {
            docx = new XWPFDocument(POIXMLDocument.openPackage(url));
            pageNum = docx.getProperties().getExtendedProperties().getUnderlyingProperties().getPages();//总页数
            log.info("word文件页数" + pageNum);
        } catch (IOException e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } finally {
            try {
                docx.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return pageNum;
    }

    public static Integer countDocPage(String url) {
        Integer pageNum = 0;
        ZipSecureFile.setMinInflateRatio(-1.0d);
        WordExtractor doc = null;
        try {
            doc = new WordExtractor(new FileInputStream(url));
            pageNum = doc.getSummaryInformation().getPageCount();//总页数
            log.info("word文件页数" + pageNum);
        } catch (IOException e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } finally {
            try {
                doc.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return pageNum;
    }
}
