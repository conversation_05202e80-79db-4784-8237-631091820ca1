package com.navigator.common.util;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.navigator.common.dto.BaiDuLocationDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Map;


@Service
@Slf4j
public class BaiduMapApiUtil {

    private static final Double PI = Math.PI;

    private static final Double PK = 180 / PI;

    private static final String MAP_URL_LAT_LNG = "http://api.map.baidu.com/geocoder/v2/?ak=d8a8Go6hFwrR89uuydEdWq6DeGEa9KEl&output=json&address=";

    private static final String MAP_URL_LOCATION = "http://api.map.baidu.com/location/ip?ak=d8a8Go6hFwrR89uuydEdWq6DeGEa9KEl&ip=#ip#&coor=bd09ll";//HTTP协议

    @Resource
    HttpClient httpClient;

    /**
     * 根据地址获取经纬度
     *
     * @param address
     * @return
     */
    public Map<String, Double> getLatAndLngByAddress(String address) {
        Map<String, Double> poiMap = null;
        String result = null;
        String url = null;
        try {
            url = MAP_URL_LAT_LNG + address;
            result = httpClient.exchange(url, null, HttpMethod.GET);
        } catch (Exception e) {
            log.error("调用百度地图API获取{}的经纬度，抛错{}", e);
        }

        if (result != null && StrUtil.isNotBlank(result) && "0".equals(JSONObject.parseObject(result).get("status") + "")) {
            String lat = result.substring(result.indexOf("\"lat\":")
                    + ("\"lat\":").length(), result.indexOf("},\"precise\""));
            String lng = result.substring(result.indexOf("\"lng\":")
                    + ("\"lng\":").length(), result.indexOf(",\"lat\""));
            poiMap = ImmutableMap.of("lat", Double.parseDouble(lat), "lng", Double.parseDouble(lng));
        }
        return poiMap;
    }

    /**
     * 计算两个地址的距离（米）
     *
     * @param address
     * @param otherAddress
     * @return
     */
    public Double getDistanceFromTwoPlaces(String address, String otherAddress) {
        Double distance = null;
        if (StrUtil.isNotBlank(address) && StrUtil.isNotBlank(otherAddress)) {
            address = address.trim();
            otherAddress = otherAddress.trim();
            if (address.equals(otherAddress)) {
                return 0.0d;
            }
            Map pointA = getLatAndLngByAddress(address);
            Map pointB = getLatAndLngByAddress(otherAddress);
            distance = getDistanceFromTwoPoints(pointA, pointB);
        }
        return distance;
    }

    /**
     * 获取两个经纬度之间的距离（单位：米）
     *
     * @param pointA
     * @param pointB
     * @return
     */
    public Double getDistanceFromTwoPoints(Map pointA, Map pointB) {
        Double distance = null;
        if (pointA != null && !pointA.isEmpty() && pointB != null && !pointB.isEmpty()) {
            double lat_a = (double) pointA.get("lat");
            double lng_a = (double) pointA.get("lng");
            double lat_b = (double) pointB.get("lat");
            double lng_b = (double) pointB.get("lng");

            if (lat_a == lat_b && lng_a == lng_b) {
                return 0.0d;
            }

            double t1 = Math.cos(lat_a / PK) * Math.cos(lng_a / PK) * Math.cos(lat_b / PK) * Math.cos(lng_b / PK);
            double t2 = Math.cos(lat_a / PK) * Math.sin(lng_a / PK) * Math.cos(lat_b / PK) * Math.sin(lng_b / PK);
            double t3 = Math.sin(lat_a / PK) * Math.sin(lat_b / PK);

            double tt = Math.acos(t1 + t2 + t3);
            distance = 6366000 * tt;
        }
        return distance;
    }

    public BaiDuLocationDTO getLocationFromIp(String ip) {
        BaiDuLocationDTO baiDuLocationDTO = null;
        String url;
        try {
            url = MAP_URL_LOCATION.replaceAll("#ip#", ip);
            String result = httpClient.exchange(url, null, HttpMethod.GET);
            baiDuLocationDTO = JSONObject.parseObject(result, BaiDuLocationDTO.class);
        } catch (Exception e) {
            log.error("调用百度地图API获取{}的经纬度，抛错{}", e);
        }


        if (baiDuLocationDTO != null && baiDuLocationDTO.getStatus() == 0) {
            return baiDuLocationDTO;
        }

        return null;
    }

    /**
     * 获取两个经纬度之间的距离（单位：米）
     *
     * @param latA
     * @param lngA
     * @param latB
     * @param lngB
     * @return
     */
    public double getDistanceFromTwoPoints(double latA, double lngA, double latB, double lngB) {
        if (latA == latB && lngA == lngB) {
            return 0.0d;
        }
        double t1 = Math.cos(latA / PK) * Math.cos(lngA / PK) * Math.cos(latB / PK) * Math.cos(lngB / PK);
        double t2 = Math.cos(latA / PK) * Math.sin(lngA / PK) * Math.cos(latB / PK) * Math.sin(lngB / PK);
        double t3 = Math.sin(latA / PK) * Math.sin(latB / PK);
        double tt = Math.acos(t1 + t2 + t3);
        return 6366000 * tt;
    }
}
