package com.navigator.common.util.http;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.TrustStrategy;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.net.ssl.*;
import java.security.GeneralSecurityException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class OkHttpsUtil {

    private static Logger logger = LogManager.getLogger(OkHttpsUtil.class);

    private final OkHttpClient client;

    //连接超时时间
    private static final int connectTimeout = 3;
    //读取超时时间
    private static final int readTimeout = 5;
    //写入超时时间
    private static final int writeTimeout = 5;

    private static OkHttpsUtil okHttpsUtil = new OkHttpsUtil();

    public OkHttpsUtil() {
        X509TrustManager trustManager;
        SSLSocketFactory sslSocketFactory;
        try {
            trustManager = trustManagerForCertificates();
            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, new TrustManager[]{trustManager}, null);
            sslSocketFactory = sslContext.getSocketFactory();
        } catch (GeneralSecurityException e) {
            throw new RuntimeException(e);
        }

        client = new OkHttpClient().newBuilder()
                .connectTimeout(connectTimeout, TimeUnit.SECONDS)
                .readTimeout(readTimeout, TimeUnit.SECONDS)
                .writeTimeout(writeTimeout, TimeUnit.SECONDS)
                .sslSocketFactory(sslSocketFactory, trustManager)
                .hostnameVerifier(new HostnameVerifier() {
                    @Override
                    public boolean verify(String s, SSLSession sslSession) {
                        if (true)
                            return true;
                        return false;
                    }
                })
                //.dispatcher(dispatcher)
                .build();
    }

    public static synchronized OkHttpsUtil getInstance() {
        return okHttpsUtil;
    }

    private X509TrustManager trustManagerForCertificates() {
        return new X509TrustManager() {
            @Override
            public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                if (chain != null && authType.equals("chain")) {
                    throw new CertificateException();
                } else {
                    int i = 1;
                }
            }

            @Override
            public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                if (chain != null && authType.equals("chain")) {
                    throw new CertificateException();
                } else {
                    int i = 1;
                }
            }

            @Override
            public X509Certificate[] getAcceptedIssuers() {
                X509Certificate[] x509Certificates = new X509Certificate[0];
                return x509Certificates;
            }
        };
    }

    public String get(String url) {
        Request request = new Request.Builder().url(url).build();
        try (Response response = client.newCall(request).execute()) {
            String respContent = response.body().string();
            logger.info("url:{} excuteHttpGet response code:{}", url, response.code());
            return respContent;
        } catch (Exception e) {
            logger.info("excuteHttpGet exception error:{}", e);
        }
        return null;
    }

    public static CloseableHttpClient createSSLClientDefault() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() {
                // 信任所有
                @Override
                public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
                    return true;
                }
            }).build();
            HostnameVerifier hostnameVerifier = NoopHostnameVerifier.INSTANCE;
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext, hostnameVerifier);
            return HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } catch (Exception e) {
            logger.error("error", e);
        }
        return HttpClients.createDefault();

    }

    /**
     * get请求 传输body参数
     *
     * @param url      请求地址
     * @param param    请求参数
     * @param headers  请求头
     * @param encoding 字符格式 UTF-8
     * @return
     * @throws Exception
     */
    public String sendJsonByGetReq(String url, String param, Map<String, Object> headers, String encoding) throws Exception {

        String body = "";

        //创建httpclient对象
        CloseableHttpClient client = createSSLClientDefault();

        HttpGetWithEntity httpGetWithEntity = new HttpGetWithEntity(url);
        HttpEntity httpEntity = new StringEntity(param, ContentType.APPLICATION_JSON);
        httpGetWithEntity.setEntity(httpEntity);

        if (!headers.isEmpty()) {
            for (String key : headers.keySet()) {
                httpGetWithEntity.addHeader(key, String.valueOf(headers.get(key)));
            }
        }

        //执行请求操作，并拿到结果（同步阻塞）
        CloseableHttpResponse response = client.execute(httpGetWithEntity);
        //获取结果实体
        HttpEntity entity = response.getEntity();
        if (entity != null) {
            //按指定编码转换结果实体为String类型
            body = EntityUtils.toString(entity, encoding);
        }
        //释放链接
        response.close();
        return body;
    }
}
