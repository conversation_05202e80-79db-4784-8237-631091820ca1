package com.navigator.common.util;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * session工具类
 *
 * <AUTHOR>
 * @Date 2018-1-16
 **/


public class SessionUtil {

    public static final String ACCOUNTID = "ACCOUNTID";
    /* *
     * 全局删除id标示
     */
    public static String GLOB_DELETE_ID_VAL = "globDeleteIdVal";

    /**
     * 获取request
     *
     * @return
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        return requestAttributes == null ? null : requestAttributes.getRequest();
    }

    /**
     * 获取session
     *
     * @return
     */
    public static HttpSession getSession() {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getSession(false);
    }

    /**
     * 获取真实路径
     *
     * @return
     */
    public static String getRealRootPath() {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getServletContext().getRealPath("/");
    }

    /**
     * 获取ip
     *
     * @return
     */
    public static String getIp() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if (servletRequestAttributes != null) {
            HttpServletRequest request = servletRequestAttributes.getRequest();
            return request.getRemoteAddr();
        }
        return null;
    }

    /**
     * 获取session中的Attribute
     *
     * @param name
     * @return
     */
    public static Object getSessionAttribute(String name) {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getSession().getAttribute(name);
    }

    /**
     * 获取session中的Attribute
     *
     * @return
     */
    public static int getCurrentAccountId() {
        int currentAccountId = -9;
        try {
            HttpServletRequest request = getRequest();
            if (null != request) {
                String accountId = request.getHeader(ACCOUNTID);
                if (StringUtils.isNotBlank(accountId)) {
                    currentAccountId = Integer.valueOf(accountId);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return currentAccountId;
    }

//    public static XkFactoryEmployeeEntity getCurrentXkemployeeInfo() {
//        XkFactoryEmployeeEntity xkFactoryEmployeeEntity=null;
//        try {
//            HttpServletRequest request = getRequest();
//            if(null!=request ){
//                Object attribute = request.getSession().getAttribute(XinKeConstant.XK_EMPLOYEEENTITY);
//                if(null!=attribute) {
//                    xkFactoryEmployeeEntity= (XkFactoryEmployeeEntity)attribute;
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return xkFactoryEmployeeEntity;
//    }


    /**
     * 设置session的Attribute
     *
     * @param name
     * @param value
     */
    public static void setSessionAttribute(String name, Object value) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            request.getSession().setAttribute(name, value);
        }
    }

    /**
     * 获取request中的Attribute
     *
     * @param name
     * @return
     */
    public static Object getRequestAttribute(String name) {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getAttribute(name);
    }

    /**
     * 设置request的Attribute
     *
     * @param name
     * @param value
     */
    public static void setRequestAttribute(String name, Object value) {
        HttpServletRequest request = getRequest();
        if (request != null) {
            request.setAttribute(name, value);
        }
    }

    /**
     * 获取上下文path
     *
     * @return
     */
    public static String getContextPath() {
        HttpServletRequest request = getRequest();
        return request == null ? null : request.getContextPath();
    }

    /**
     * 删除session中的Attribute
     *
     * @param name
     */
    public static void removeSessionAttribute(String name) {
        HttpServletRequest request = getRequest();
        if(request != null)
            request.getSession().removeAttribute(name);
    }

}
