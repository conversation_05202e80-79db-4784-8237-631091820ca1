package com.navigator.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class TemplateUtil {

    /**
     * 根据键值对填充字符串，占位符是一个大括号 {} ，如("hello {name}",{name:"小明"})
     *
     * @param template 模板
     * @param map      替换占位符信息的map集合
     * @return 拼接好的字符串
     */
    public static String renderStringByBrace(String template, Map<String, Object> map) {
        Set<Map.Entry<String, Object>> sets = map.entrySet();
        for (Map.Entry<String, Object> entry : sets) {
            if (null != entry.getValue()) {
                String regex = "\\{" + entry.getKey() + "\\}";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(template);
                template = matcher.replaceAll(entry.getValue().toString());
            }
        }
        return template;
    }

    /**
     * 根据键值对填充字符串，占位符是一个双大括号 {{}} ，如("hello {{name}}",{name:"小明"})
     *
     * @param template 模板
     * @param map      替换占位符信息的map集合
     * @return 拼接好的字符串
     */
    public static String renderStringByTwoBrace(String template, Map<String, Object> map) {
        Set<Map.Entry<String, Object>> sets = map.entrySet();
        for (Map.Entry<String, Object> entry : sets) {
            String regex = "\\{\\{" + entry.getKey() + "\\}\\}";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(template);
            template = matcher.replaceAll(entry.getValue().toString());
        }
        return template;
    }

    /**
     * 条款的富文本Html信息，去掉<></>的信息，并适当换行
     *
     * @param htmlContent 富文本信息（条款正文、质量指标等）
     * @return 处理后的纯文本信息
     */
    public static String removeParentheses(String htmlContent) {
//        Pattern pattern = Pattern.compile("\\([^\\(]*\\)");
        //1、先替换</>的内容，并加换行符
        Pattern pattern1 = Pattern.compile("</[^\\<]*>");
        Matcher matcher1 = pattern1.matcher(htmlContent);
        String content = matcher1.replaceAll("\n");
        content = content.replaceAll("\n\n\n", "\n");
        content = content.replaceAll("\n\n", "\n");
        String contentInfo = content.replaceAll("&nbsp;", "");
        //2、替换<>的内容为空
        Pattern pattern2 = Pattern.compile("<[^\\<]*>");
        Matcher matcher2 = pattern2.matcher(contentInfo);
        return matcher2.replaceAll("");
    }

    public static List<String> renderKeyVariableList(String content) {
        String regex = "\\$\\{([^}]*)!\\}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(content);
        List<String> keyList = new ArrayList<>();
        while (matcher.find()) {
            keyList.add(matcher.group());
        }
        return keyList;
    }

//    /**
//     * 提取关键字${xxx!}
//     * 例如：${出场顺序!}、${工厂编码（合同价格）!}
//     *
//     * @param content 需解析的文本
//     * @return 提取的关键字变量集合
//     */
//    public static List<String> renderKeyVariableList(String content) {
//        String regex = "\\$\\{([^}]*)!\\}";
//        Pattern pattern = Pattern.compile(regex);
//        Matcher matcher = pattern.matcher(content);
//        List<String> keyList = new ArrayList<>();
//        while (matcher.find()) {
//            keyList.add(matcher.group());
//        }
//        return keyList;
//    }

    public static void main(String[] args) {
//        String content = "hello {name}, 1 2 3 4 5 {six} 7, again {name}. ";
//        Map<String, Object> map = new HashMap<>();
//        map.put("name", "java");
//        map.put("six", "6");
//        content = TemplateUtil.renderStringByBrace(content, map);
//        System.out.println(content);
//        String content = "${出场顺序!}卖方于一定期限付款，${提货方式!},$赊销额度不变，{非变量信息}，{发货库点!},${工厂编码（合同价格）!}";
//        List<String> keyList = renderKeyVariableList(content);
//        System.out.println(keyList);
    }

    public static String getTemplate(String templeEnums, Map<String, Object> map) {

        return TemplateUtil.renderStringByBrace(templeEnums, map);
    }

}
