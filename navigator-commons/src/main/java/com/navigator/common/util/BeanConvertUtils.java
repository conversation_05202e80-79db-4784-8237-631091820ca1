package com.navigator.common.util;

import ma.glasnost.orika.MapperFacade;
import ma.glasnost.orika.MapperFactory;
import ma.glasnost.orika.impl.DefaultMapperFactory;
import ma.glasnost.orika.metadata.ClassMapBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @since 2020-05-14 14:30
 */
public abstract class BeanConvertUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(BeanConvertUtils.class);

    /**
     * 默认字段实例
     */
    private static final MapperFacade MAPPER_FACADE = new DefaultMapperFactory.Builder().build().getMapperFacade();

    /**
     * 默认字段实例集合
     */
    private static final Map<String, MapperFacade> CACHE_MAPPER_FACADE_MAP = new ConcurrentHashMap<>();

    public static <T> T convert(Class<T> clazz, Object source) {
        try {
            if (source == null) {
                return null;
            }
            T t = clazz.newInstance();
            BeanUtils.copyProperties(source, t);
            return t;
        } catch (Exception e) {
            LOGGER.error(String.format("Convert error! source=%s", source), e);
            throw new RuntimeException(e);
        }

    }

    public static <T> List<T> convert2List(Class<T> clazz, List<?> sources) {
        if (CollectionUtils.isEmpty(sources)) {
            return new ArrayList();
        }
        List<T> result = new ArrayList<>();
        for (Object o : sources) {
            result.add(convert(clazz, o));
        }
        return result;
    }


    public static <T> T copy(T target, Object source) {
        try {
            if (source == null) {
                return null;
            }
            BeanUtils.copyProperties(source, target);
            return target;
        } catch (Exception e) {
            LOGGER.error(String.format("Copy error! target=%s, source=%s", target, source), e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 映射实体
     * 优点：1.使用Orika进行实体映射框架,可以将两个不同的类型的属性进行相互转换
     * 2.Orika使用字节码技术动态生成代码,没有使用反射操作，而且内部还使用了大量的缓存机制，据说是BeanUtil性能的120倍
     * 注意：如果出现类型强制转换报错则会中断转换
     *
     * @param target 映射类对象
     * @param source 数据（对象）
     * @return 映射类对象
     */
    public static <E, T> E map(Class<E> target, T source) {
        try {
            return MAPPER_FACADE.map(source, target);
        } catch (Exception e) {
            LOGGER.error(String.format("Convert error! source=%s", source), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 映射集合（默认字段）
     *
     * @param target     映射类对象
     * @param sourceList 数据（集合）
     * @return 映射类对象
     */
    public static <E, T> List<E> mapAsList(Class<E> target, Collection<T> sourceList) {
        try {
            return MAPPER_FACADE.mapAsList(sourceList, target);
        } catch (Exception e) {
            LOGGER.error(String.format("Convert error! sourceList=%s", sourceList), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 映射实体（自定义配置）
     *
     * @param target    映射类对象
     * @param source    数据（对象）
     * @param configMap 自定义配置
     *                  例：mapperFactory.classMap(User.class, UserVo.class)
     *                  .field("name", "userName")
     *                  .field("age", "ageOne")
     *                  .byDefault().register()
     * @return 映射类对象
     */
    public static <E, T> E map(Class<E> target, T source, Map<String, String> configMap) {
        try {
            MapperFacade mapperFacade = getMapperFacade(target, source.getClass(), configMap);
            return mapperFacade.map(source, target);
        } catch (Exception e) {
            LOGGER.error(String.format("Convert error! source=%s", source), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取自定义映射
     *
     * @param toClass   映射类
     * @param dataClass 数据映射类
     * @param configMap 自定义配置
     * @return 映射类对象
     */
    public static <E, T> MapperFacade getMapperFacade(Class<E> toClass, Class<T> dataClass, Map<String, String> configMap) {
        String mapKey = dataClass.getCanonicalName() + "_" + toClass.getCanonicalName();
        MapperFacade mapperFacade = CACHE_MAPPER_FACADE_MAP.get(mapKey);
        if (Objects.isNull(mapperFacade)) {
            MapperFactory factory = new DefaultMapperFactory.Builder().build();
            ClassMapBuilder classMapBuilder = factory.classMap(dataClass, toClass);
            configMap.forEach(classMapBuilder::field);
            classMapBuilder.byDefault().register();
            mapperFacade = factory.getMapperFacade();
            CACHE_MAPPER_FACADE_MAP.put(mapKey, mapperFacade);
        }
        return mapperFacade;
    }


    /**
     * object转换为map
     *
     * @return 映射类对象
     */
    public static Map convertToMap(Object object) throws Exception {
        Class<?> clazz = object.getClass();
        HashMap<String, Object> map = new HashMap<>();
        Field[] declaredFields = clazz.getDeclaredFields();
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Method m = (Method) object.getClass().getMethod("get" + getMethodName(field.getName()));
            map.put(field.getName(), m.invoke(object));
        }
        return map;
    }

    private static String getMethodName(String fildeName) throws Exception {
        byte[] items = fildeName.getBytes();
        items[0] = (byte) ((char) items[0] - 'a' + 'A');
        return new String(items);
    }

    public static Map<String, String> getObjectFieldMap(Object object, Boolean includeAll) {
        if (null == includeAll) {
            includeAll = false;
        }
        Map<String, String> map = new HashMap<>();
        String objectName = object.getClass().getSimpleName();
        List<String> typeList = getFieldTypeList();
        for (Field field : object.getClass().getDeclaredFields()) {
            if (field.getName().equals("serialVersionUID")) {
                continue;
            }
            if (typeList.contains(field.getType().getName())) {
                map.put(field.getName(), objectName);
            } else if (field.getType().getName().contains("List")) {
                try {
                    Class<?> listElementType = null;
                    listElementType = (Class<?>) ((ParameterizedType) field.getGenericType()).getActualTypeArguments()[0];
                    String listElementTypeName = listElementType.getName();
                    //常规List
                    if (typeList.contains(listElementTypeName)) {
                        map.put(field.getName(), objectName);
                    } else {
                        if (listElementTypeName.contains("DTO")
                                || field.getType().getName().contains("VO")
                                || field.getType().getName().contains("BO")
                                || field.getType().getName().contains("Entity")) {
                            if (includeAll) {
                                Class<?> clazz = Class.forName(listElementTypeName);
                                Object subObject = clazz.newInstance();
                                Map<String, String> mapSub = getObjectFieldMap(subObject, includeAll);
                                map.putAll(mapSub);
                            } else {
                                map.put(field.getName(), objectName);
                            }
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            } else {

                try {

                    if (field.getType().getName().contains("DTO")
                            || field.getType().getName().contains("VO")
                            || field.getType().getName().contains("BO")
                            || field.getType().getName().contains("Entity")) {
                        if (includeAll) {
                            Class<?> clazz = Class.forName(field.getType().getName());
                            Object subObject = clazz.newInstance();
                            Map<String, String> mapSub = getObjectFieldMap(subObject, includeAll);
                            map.putAll(mapSub);
                        } else {
                            map.put(field.getName(), objectName);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
        }

        String parentClassName = object.getClass().getSuperclass().getName();
        if (parentClassName.contains("DTO")
                || parentClassName.contains("VO")
                || parentClassName.contains("BO")
                || parentClassName.contains("Entity")) {
            if (includeAll) {
                try {
                    Class<?> clazz = Class.forName(parentClassName);
                    Object subObject = clazz.newInstance();
                    Map<String, String> mapSub = getObjectFieldMap(subObject, includeAll);
                    map.putAll(mapSub);
                }catch (Exception e){

                }

            }
        }
        return map;
    }


    private static List<String> getFieldTypeList() {
        List<String> list = new ArrayList<>();
        list.add("java.lang.String");
        list.add("java.lang.Integer");
        list.add("int");
        list.add("java.lang.Long");
        list.add("java.lang.Double");
        list.add("java.math.BigDecimal");
        list.add("java.util.Date");
        list.add("java.sql.Date");
        list.add("java.lang.Boolean");
        list.add("boolean");
        list.add("byte");
        list.add("char");
        list.add("java.lang.Byte");
        list.add("java.sql.Timestamp");
        return list;
    }

}
