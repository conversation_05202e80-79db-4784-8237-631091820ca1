package com.navigator.common.util.file;

import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.StringUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipOutputStream;


public class ZipUtil {

    private static final Log log = LogFactory.getLog(ZipUtil.class);

    /**
     * 压缩文件
     *
     * @param srcfile File[] 需要压缩的文件列表
     * @param zipfile File 压缩后的文件
     */
    public static void zipFiles(List<File> srcfile, File zipfile) {
        byte[] buf = new byte[1024];
        try {
            // Create the ZIP file
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(zipfile));
            // Compress the files
            for (int i = 0; i < srcfile.size(); i++) {
                File file = srcfile.get(i);
                FileInputStream in = new FileInputStream(file);
                // Add ZIP entry to output stream.
                String name = file.getName();
                String fileName = "(" + (i + 1) + ")" + name.substring(name.indexOf("_") + 1);
                out.putNextEntry(new ZipEntry(fileName));
                // Transfer bytes from the file to the ZIP file
                int len;
                while ((len = in.read(buf)) > 0) {
                    out.write(buf, 0, len);
                }
                // Complete the entry
                out.closeEntry();
                in.close();
            }
            // Complete the ZIP file
            out.close();
        } catch (IOException e) {
            log.error("ZipUtil zipFiles exception:" + e);
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        }
    }

    /**
     * 解压缩
     *
     * @param zipfile File 需要解压缩的文件
     * @param descDir String 解压后的目标目录
     */
    public static void unZipFiles(File zipfile, String descDir) {

        // Open the ZIP file
        FileUploadUtil.judeDirExists(new File(descDir));
        ZipFile zf = null;
        try {
            zf = new ZipFile(zipfile);
            for (Enumeration entries = zf.entries(); entries.hasMoreElements(); ) {
                // Get the entry name
                ZipEntry entry = ((ZipEntry) entries.nextElement());
                String zipEntryName = null;
                if(null != entry)
                    zipEntryName = entry.getName();
                else
                    zipEntryName = "";
                if(!zipEntryName.contains("\\")){
                    int i;
                }
                InputStream in = zf.getInputStream(entry);
                String filePath = "";
                if(null != zipEntryName && !StringUtil.isEmpty(zipEntryName))
                    filePath = descDir + zipEntryName;
                else
                    filePath = descDir;
                // System.out.println(zipEntryName);
                OutputStream out = new FileOutputStream(filePath);
                byte[] buf1 = new byte[1024];
                int len;
                while ((len = in.read(buf1)) > 0) {
                    out.write(buf1, 0, len);
                }
                // Close the file and stream
                in.close();
                out.close();
            }
        } catch (IOException e) {
            log.error("ZipUtil unZipFiles exception:" + e);
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } finally {
            if(zf != null){
                try {
                    zf.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            zf = null;
        }
    }

    /**
     * 根据文件链接把文件下载下来并且转成字节码
     *
     */
    public byte[] getFileFromURL(String urlPath) {
        byte[] data = null;
        InputStream is = null;
        HttpURLConnection conn = null;
        try {
            URL url = new URL(urlPath);
            conn = (HttpURLConnection) url.openConnection();
            conn.setDoInput(true);
            // conn.setDoOutput(true);
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(6000);
            is = conn.getInputStream();
            if (conn.getResponseCode() == 200) {
                data = readInputStream(is);
            } else {
                data = null;
            }
        } catch (MalformedURLException e) {
            log.error("MalformedURLException", e);
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } catch (IOException e) {
            log.error("IOException", e);
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        } finally {
            try {
                if (is != null) {
                    is.close();
                }
                if(conn != null)
                    conn.disconnect();
            } catch (IOException e) {
                log.error("IOException", e);
            }
        }
        return data;
    }


    public InputStream getInputStreamFromURL(String urlPath) throws IOException {
        byte[] data = null;
        InputStream is = null;
        HttpURLConnection conn = null;
            URL url = new URL(urlPath);
            conn = (HttpURLConnection) url.openConnection();
            conn.setDoInput(true);
            // conn.setDoOutput(true);
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(6000);
            is = conn.getInputStream();
        return is;
    }


    /*public InputStream getInputStreamFromHttpsURL(String urlPath) throws Exception {
        // 创建SSLContext
        SSLContext sslContext = SSLContext.getInstance("SSL");
        TrustManager[] tm = { new MyX509TrustManager() };
        // 初始化
        sslContext.init(null, tm, new java.security.SecureRandom());
        // 获取SSLSocketFactory对象
        SSLSocketFactory ssf = sslContext.getSocketFactory();
        // url对象
        URL url = new URL(urlPath);
        // 打开连接
        HttpsURLConnection conn = (HttpsURLConnection) url.openConnection();
        *//**
         * 这一步的原因: 当访问HTTPS的网址。您可能已经安装了服务器证书到您的JRE的keystore
         * 但是服务器的名称与证书实际域名不相等。这通常发生在你使用的是非标准网上签发的证书。
         *
         * 解决方法：让JRE相信所有的证书和对系统的域名和证书域名。
         *
         * 如果少了这一步会报错:java.io.IOException: HTTPS hostname wrong: should be <localhost>
         *//*
        conn.setHostnameVerifier(new MyX509TrustManager().new TrustAnyHostnameVerifier());
        // 设置一些参数
        conn.setDoOutput(true);
        conn.setDoInput(true);
        conn.setUseCaches(false);
        // 设置当前实例使用的SSLSoctetFactory
        conn.setSSLSocketFactory(ssf);
        conn.connect();


        // 得到输入流
        InputStream is = conn.getInputStream();
        return is;
    }*/

    public byte[] readInputStream(InputStream inputStream) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length = -1;
        try {
            while ((length = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            baos.flush();
        } catch (IOException e) {
            log.error("IOException", e);
          throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        }
        byte[] data = baos.toByteArray();
        try {
            inputStream.close();
            baos.close();
        } catch (IOException e) {
            log.error("IOException", e);
            throw new BusinessException(ResultCodeEnum.DEAL_FAIL);
        }
        return data;
    }
}
