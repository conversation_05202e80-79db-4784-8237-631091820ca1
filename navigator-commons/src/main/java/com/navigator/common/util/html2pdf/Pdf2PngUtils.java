package com.navigator.common.util.html2pdf;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.Objects;

/**
 * Description: pdf转image的服务
 * Created by <PERSON><PERSON><PERSON> on 2021/11/24 19:02
 */
@Slf4j
@Service
public class Pdf2PngUtils {

    public String pdf2png(String pdfFilePath, String imagePath) throws IOException {
        log.info("## PDF FILE PATH {}", pdfFilePath);
        File file = new File(pdfFilePath);
        try (PDDocument doc = PDDocument.load(file)) {
            if (!file.exists()) {
                throw new IOException("PDF文件不存在");
            }
            PDFRenderer renderer = new PDFRenderer(doc);
            // 仅生成一张png
            String path = imagePath;
            BufferedImage image = renderer.renderImageWithDPI(0, 144);
            write(image, path);
            return path;
        } catch (IOException e) {
            e.printStackTrace();
            throw new IOException("PDF转图片失败");
        }
    }

    private void write(BufferedImage image, String path) throws IOException {
        FileUtil.mkParentDirs(Objects.requireNonNull(path));
        System.out.println("## IMAGE FILE PATH" + path);
        ImageIO.write(image, "png", new File(path));
    }
}
