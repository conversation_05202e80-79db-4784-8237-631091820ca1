package com.navigator.common.util;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.navigator.common.dto.Result;
import com.navigator.common.dto.TestInfoVO;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2020-06-15 18:00
 */
public class PageUtil {
    /**
     * 手动分页
     *
     * @param sourceList
     * @param pageSize
     * @param pageNumber
     * @return
     */
    public static <T> List<T> convertPageList(List<T> sourceList, int pageSize, int pageNumber) {
        if (pageNumber == 0) {
            pageNumber = 1;
        }
        int size = sourceList.size();
        if (size <= pageNumber) {
            return sourceList;
        }
        int pageCount = size / pageSize + (size % pageSize == 0 ? 0 : 1);
        return sourceList.subList((pageNumber - 1) * pageSize, pageNumber < pageCount ? pageNumber * pageSize : size);
    }

    /**
     * iPage转Page
     *
     * @param iPage
     * @param <T>
     * @return
     */
    public static <T> Page<T> convertPage(IPage<T> iPage) {
        Page<T> result = new Page<>();
        result.setPages(iPage.getPages());
        result.setTotal(iPage.getTotal());
        result.setSize(iPage.getSize());
        result.setCurrent(iPage.getCurrent());
        result.setRecords(iPage.getRecords());
        return result;
    }

    public static void main(String[] args) {
        List<TestInfoVO> testInfoVOList = new ArrayList<>();
        for (int i = 1; i < 100; i++) {
            testInfoVOList.add(new TestInfoVO().setUserName("nana" + i).setAddress("address" + i + 1));
        }
        System.out.println(FastJsonUtils.getBeanToJson(Result.successPage(testInfoVOList, 10, 3)));
    }
}
