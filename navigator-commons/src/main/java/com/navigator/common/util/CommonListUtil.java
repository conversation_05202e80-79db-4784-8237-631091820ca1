package com.navigator.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2020-04-22 14:40
 */
public class CommonListUtil {
    /**
     * 取两个集合的差集
     *
     * @param big
     * @param small
     * @param <T>
     * @return
     */
    public static <T> List<T> getDifferences(Collection<T> big, Collection<T> small) {
        Sets.SetView<T> differences = Sets.difference(Sets.newHashSet(big), Sets.newHashSet(small));
        return Lists.newArrayList(differences);
    }

    /**
     * 取两个集合的交集
     *
     * @param big
     * @param small
     * @param <T>
     * @return
     */
    public static <T> List<T> getIntersection(List<T> big, List<T> small) {
        Sets.SetView differences = Sets.intersection(Sets.newHashSet(big), Sets.newHashSet(small));
        return Lists.newArrayList(differences);
    }

    public static boolean isNullOrEmpty(List list) {
        return null == list || list.isEmpty();
    }

    public static boolean notNullOrEmpty(List list) {
        return null != list && list.size() > 0;
    }

    public static boolean notNullOrEmpty(Set list) {
        return null != list && list.size() > 0;
    }
}
