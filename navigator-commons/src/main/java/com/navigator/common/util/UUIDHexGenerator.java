package com.navigator.common.util;

import java.util.Random;

/**
* @description: 生成随机数工具类
*
* @author: Mr.Lq
* @date: 2020/12/24 11:46
*/
public class UUIDHexGenerator {
    private String sep = "";
//    private static final int IP;
//    static {
//        int ipadd;
//        try {
//            ipadd = toInt(InetAddress.getLocalHost().getAddress());
//        } catch (Exception e) {
//            ipadd = 0;
//        }
//        IP = ipadd;
//    }
    private static short counter = (short) 0;

    static Random random = new Random();

    private static final int JVM = (int) (System.currentTimeMillis() >>> 8);

//    public static int toInt(byte[] bytes) {
//        byte minValue = Byte.MIN_VALUE;
//        int result = 0;
//        for (int i = 0; i < 4; i++) {
//            result = (result << 8) - minValue + (int) bytes[i];
//        }
//        return result;
//    }

    /**
     * Unique in a local network
     */
//    protected int getIP() {
//        return IP;
//    }

    /**
     * Unique down to millisecond
     */
    protected short getHiTime() {
        return (short) (System.currentTimeMillis() >>> 32);
    }

    protected int getLoTime() {
        return (int) System.currentTimeMillis();
    }

    /**
     * Unique across JVMs on this machine (unless they load this class in the
     * same quater second - very unlikely)
     */
    protected int getJVM() {
        return JVM;
    }

    protected String format(int intval) {
        String formatted = Integer.toHexString(intval);
        StringBuffer buf = new StringBuffer("00000000");
        buf.replace(8 - formatted.length(), 8, formatted);
        return buf.toString();
    }

    protected String format(short shortval) {
        String formatted = Integer.toHexString(shortval);
        StringBuffer buf = new StringBuffer("0000");
        buf.replace(4 - formatted.length(), 4, formatted);
        return buf.toString();
    }

//    public String generate() {
//        return new StringBuilder(36).append(format(getIP())).append(sep)
//                .append(format(getJVM())).append(sep)
//                .append(format(getHiTime())).append(sep)
//                .append(format(getLoTime())).append(sep)
//                .append(format(getCount())).toString();
//    }

    /**
     * Unique in a millisecond for this JVM instance (unless there are >
     * Short.MAX_VALUE instances created in a millisecond)
     */
    protected short getCount() {
        synchronized (UUIDHexGenerator.class) {
            if (counter < 0)
                counter = 0;
            return counter++;
        }
    }

//    public static void main(String[] args) {
//        UUIDHexGenerator uuidHexGenerator = new UUIDHexGenerator();
//        String generate = uuidHexGenerator.generate();
//        System.out.println(generate);
//        int ip2 = uuidHexGenerator.getIP();
//        System.out.println(ip2);
//    }

    /**
     * 生成位大小写加数字随机数
     * @param num
     */
    public static String randomCoding (int num) {
        String val = "";
        for (int i = 0; i < 4; i++) {//定义随机数位数
            // 输出字母还是数字
            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            // 字符串
            if ("char".equalsIgnoreCase(charOrNum)) {
                // 取得大写字母还是小写字母
                int choice = random.nextInt(2) % 2 == 0 ? 65 : 97;
                val += (char) (choice + random.nextInt(26));
            } else if ("num".equalsIgnoreCase(charOrNum)) { // 数字
                val += String.valueOf(random.nextInt(10));
            }
        }
        return val;
    }
}
