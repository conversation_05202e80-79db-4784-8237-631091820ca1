package com.navigator.common.util;

import cn.hutool.extra.template.Template;
import cn.hutool.extra.template.TemplateConfig;
import cn.hutool.extra.template.TemplateEngine;
import cn.hutool.extra.template.TemplateUtil;
import com.navigator.common.enums.ContentTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TemplateRenderUtil {

    private static TemplateEngine templateEngine;

    static {
        templateEngine = TemplateUtil.createEngine(new TemplateConfig());
    }

    //渲染模板
    public static String templateRender(Map map, String templateName) {
        if (null != map) {
            Template template = templateEngine.getTemplate(templateName);
            return template.render(map);
        }
        return templateName;
    }

    /**
     * 拼接模板消息的公用方法：企业微信，邮件，站内信
     *
     * @param title           标题
     * @param contentTemplate 消息内容模板
     * @param dataMap         key是占位符，value是替换占位符的内容
     * @param contentType     消息内容类型
     * @return Map<String, Object>
     */
    public static Map<String, Object> assemblyMessageInfo(String title, String contentTemplate, Map<String, Object> dataMap, String contentType) {

        String content;
        Map<String, Object> map = new HashMap<>();

        //1、拼接标题
        if (StringUtils.isNotBlank(title)) {
            title = templateRender(dataMap, title);
            map.put("title", title);
        }

        //2、拼接内容content
        if (ContentTypeEnum.FTL.getValue().equals(contentType)) {
            //如果contentType的类型是ftl,模板表里content存的是ftl的文件路径
            content = TemplateRenderFtlUtil.templateRender(dataMap, contentTemplate);
        } else {
            //如果contentType的类型不是ftl,模板表里的content存的是需要替换${}占位符的模板内容
            content = TemplateRenderUtil.templateRender(dataMap, contentTemplate);
        }
        //3、content是存到task表中，sendContent是要发送的内容，这里同一规范方便调用
        map.put("content", content);
        map.put("sendContent", content);

        return map;
    }


    public static Map<String, Object> gatherAssemblyMessageInfo(String title, String contentTemplate, List<Map<String, Object>> dataMaps) {

        StringBuilder content = new StringBuilder();
        Map<String, Object> map = new HashMap<>();

        if (StringUtils.isNotBlank(title)) {
            map.put("title", title);
        }
        //如果contentType的类型不是ftl,模板表里的content存的是需要替换${}占位符的模板内容
        for (Map<String, Object> dataMap : dataMaps) {
            content.append(TemplateRenderUtil.templateRender(dataMap, contentTemplate));
        }
        //3、content是存到task表中，sendContent是要发送的内容，这里同一规范方便调用
        map.put("content", content.toString());
        map.put("sendContent", content.toString());

        return map;
    }

//    public static void main(String[] args) {
////        templateEngine = TemplateUtil.createEngine(new TemplateConfig("template"));
//
//
//        String content = "${attachName}您好！\n" +
//                "欢迎来到${attachUrl}\n" +
//                "again:======${messageFormDTO.appId!\"nana\"}" +
//                "客户信息:======${messageFormDTO.userInfoVO[1].userName!\"nana\"}";
//        String template = "${attachName}您好！\n" +
//                "欢迎来到${attachUrl}\n" +
//                "密码:======${messageFormDTO.appId!\"nana\"}" +
//                "<#assign num=0/> "+
//
//                "<#if messageFormDTO.userInfoVOList[2].userName==\"nanahou1\" && (attachUrl==\"www.baidu.com\" || id==9)>"+
//                  "\n${num+1}<#assign num=num+1/> 、E101 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//                "\nE101 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//
//                "<#elseif messageFormDTO.infoList?seq_contains(\"nanahou1\") && attachUrl==\"www.baidu.com\">"+
//                "\n${num+1}<#assign num=num+1/>、E202 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//                "\nE202 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//
//                "<#elseif !['tj','yz','zjg']?seq_contains(attachName) && attachUrl==\"www.baidu.com\">"+
//                "\n${num+1}<#assign num=num+1/>、E404 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//                "\nE202 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//
//                "<#else>"+
//                "\n${num+1}<#assign num=num+1/>、E203 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//                "\nE203 客户3姓名(显示/隐藏):======${messageFormDTO.userInfoVOList[1].userName!}" +
//                "              </#if>"+
//
//                "<#list messageFormDTO.userInfoVOList as userInfoVO>\n" +
//                "客户信息-姓名:======${userInfoVO.userName!\"nana\"}，" +
//                "地址:======${userInfoVO.address!\"地址暂无\"}" +
//                "</#list>";
//
//        Map<String, String> map = new HashMap<>();
//        MessageFormDTO messageFormDTOV2 =  new MessageFormDTO().setAppId(10000).setAppKey("appKey456").setMobiles("18756114545")
//                .setUserInfoVOList(
//                        Arrays.asList(new TestInfoVO().setUserName("小明").setAddress("上海"),
//                                new TestInfoVO().setUserName("小花").setAddress("杭州"),
//                                new TestInfoVO().setUserName("nanahou")))
//                .setInfoList(Arrays.asList("nanahou","qiang"));
//        TestFileBaseInfoDTO fileBaseInfoDTO = new TestFileBaseInfoDTO()
//                .setAttachName("zjg1")
//                .setId(10)
//                .setAttachStuff(".pdf")
//                .setAttachUrl("www.baidu.com")
////                .setMessageFormDTO(FastJsonUtils.getBeanToJson(messageFormDTOV2));
//                .setMessageFormDTO(messageFormDTOV2);
//        //数据对象->map
//        Map<String, Object> beanToMap = BeanUtil.beanToMap(fileBaseInfoDTO);
////        beanToMap.put("messageFormDTO",FastJsonUtils.getJsonToMap(fileBaseInfoDTO.getMessageFormDTO()));
//        System.out.println("beanToMap：------------"+beanToMap);
//
////        String dbContentResult = TemplateRenderFtlUtil.templateRender(beanToMap, "message.ftl");
//        //dataMap+模板 渲染填充
//        String dbContentResult = templateRender(beanToMap, template);
//        System.out.println("fromString" + dbContentResult.toLowerCase());
//
//    }

//
//    public static void main(String[] args) {
//        String content =
//                "<#if messageFormDTO??&&messageFormDTO.templateId==11&&attachName==\"YZ\"&&depositAmount == 0><div class=\"fill row start\">\n" +
//                "          <div class=\"term-it" +
//                "" +
//                "" +
//                "em-bold\"><span class=\"bold\">质量指标：</span></div>\n" +
//                "          <div class=\"fill\">\n" +
//                "            <div class=\"term-row\">2、粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合江苏中海粮油工业有限公司企业标准(标准编号：Q/321182 56032488X 005）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>\n" +
//                "          </div>\n" +
//                "        </div>\n" +
//                "<#elseif messageFormDTO??&&messageFormDTO.templateId==11&&attachName==\"ZS\" ><div class=\"fill row start\">\n" +
//                "          <div class=\"term-item-bold\"><span class=\"bold\">质量指标：</span></div>\n" +
//                "          <div class=\"fill\">\n" +
//                "            <div class=\"term-row\">3、粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合舟山中海粮油工业有限公司企业标准(标准编号：Q/ZHLY 01）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>\n" +
//                "          </div>\n" +
//                "        </div>\n" +
//                "<#elseif messageFormDTO??&&messageFormDTO.templateId==11&&attachName==\"ZS2\" ><div class=\"fill row start\">\n" +
//                "          <div class=\"term-item-bold\"><span class=\"bold\">质量指标：</span></div>\n" +
//                "          <div class=\"fill\">\n" +
//                "            <div class=\"term-row\">4、粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合舟山中海粮油工业有限公司企业标准(标准编号：Q/ZHLY 01）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>\n" +
//                "          </div>\n" +
//                "        </div>\n" +
//                "<#elseif attachName==\"TJ\" ><div class=\"fill row start\">\n" +
//                "          <div class=\"term-item-bold\"><span class=\"bold\">质量指标：</span></div>\n" +
//                "          <div class=\"fill\">\n" +
//                "            <div class=\"term-row\">5、粗蛋白${eg!}±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合22企业标准(标准编号：Q/LDCTJ 10）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>\n" +
//                "          </div>\n" +
//                "        </div>\n" +
//                "<#else><div class=\"fill row start\">\n" +
//                "          <div class=\"term-item-bold\"><span class=\"bold\">质量指标：</span></div>\n" +
//                "          <div class=\"fill\">\n" +
//                "            <div class=\"term-row\">6、粗蛋白${eg!} ±0.5%，水分≤13%,尿素酶活性≤0.3 U/g，氢氧化钾蛋白质溶解度≥70%，其他指标符合11企业标准(标准编号：Q/320582 LDC1）及卫生指标标准（标准编号：GB13078）。以上货物原材料为转基因大豆。</div>\n" +
//                "          </div>\n" +
//                "        </div>\n" +
//                "</#if>";
//        TestFileBaseInfoDTO fileBaseInfoDTO = new TestFileBaseInfoDTO().setAttachName("ZS2").setEg("100含量")
//                .setDepositAmount(new BigDecimal(0)).setMessageFormDTO(new MessageFormDTO().setTemplateId(11));
//        Map<String, Object> beanToMap = BeanUtil.beanToMap(fileBaseInfoDTO);
//
//        System.out.println("beanToMap：------------" + beanToMap);
////        //dataMap+模板 渲染填充
//        String dbContentResult = templateRender(beanToMap, content);
//        System.out.println("fromString" + dbContentResult.toLowerCase());
//    }

    public static void main(String[] args) {
        String bizDataJson = "{\"ad\":\"上海\",\"ads\":\"上海\",\"ag\":\"25\",\"buyer\":\"路易达孚测试用户B\",\"bzjzj\":\"0\",\"dd\":\"指定码头船板/车板交货（“指定地点”）\",\"dg\":\"自提\",\"djj\":\"\",\"dmr\":\"0\",\"doc\":\"2022年06月10日\",\"ds\":\"江苏省张家港保税区宝岛路1号\",\"eg\":\"42%\",\"ema\":\"<EMAIL>；<EMAIL>\",\"ewm\":\"https://csm4vnvgsto001.blob.core.chinacloudapi.cn/test/test/upload/magellan/barcode/1a03db3cd2644cdabd6661b6eec05c42_二维码.png?ss=b&sig=20lIDmlzvyUSHy9xMjBPOBOGZ6FCDis1Qbhua7eJMKI%3D&st=2022-06-10T03%3A01%3A23Z&se=2022-06-10T16%3A00%3A00Z&sv=2019-02-02&srt=sco&sp=r&sr=sco\",\"fox\":\"路易达孚测试用户B\",\"gqbz\":\"企标值\",\"hy\":\"2022年09日\",\"hyj\":\"2209\",\"jcj\":0.000000,\"jhgc\":\"路易达孚（张家港）饲料蛋白有限公司\",\"jzfk\":\"2022年06月11日\",\"kh\":\"中国银行宜山路支行\",\"khfk\":\"中国银行上海市中银大厦支行\",\"khmc\":\"路易达孚（天津）国际贸易有限公司\",\"kjh\":\"TJIBSBMS2200479\",\"kjr\":\"2022年06月10日\",\"kszj\":\"0.000000\",\"kszjz\":\"0E-12\",\"mads\":\"天津自贸试验区（东疆保税港区）重庆道以南、呼伦贝尔路以西铭海中心3号楼-5、6-704\",\"mbo\":\"13166212334；\",\"me\":\"路易达孚（张家港）饲料蛋白有限公司\",\"mema\":\"<EMAIL>\",\"mes\":5,\"mfox\":\"王莉;庄超男;杨玲铃\",\"mmbo\":\"15386134298\",\"mr\":\"5%\",\"mt\":99.000000,\"na\":\"路易达孚测试用户B\",\"no\":\"TJIBSBMS2200479\",\"noy\":\"TJIBSBMS2200479\",\"nprt\":90.090000000000,\"os\":1,\"pe\":\"M-S-送货-目的港过磅\",\"pm\":\"赊销\",\"po\":\"2022年06月10日至2022年06月30日\",\"pr\":3000.00,\"prt\":297000.000000,\"prx\":\"单价包含\",\"py\":\"无\",\"seller\":\"路易达孚（张家港）饲料蛋白有限公司\",\"sxlx\":\"0.000000\",\"sxrq\":\"5\",\"templateCondition\":{\"actionType\":2,\"addedDepositRate\":0,\"contractType\":1,\"deliveryFactoryCode\":\"ZJG\",\"deliveryType\":1,\"depositAmount\":14850.000000,\"modifyList\":[\"goodsId\",\"goodsPackageId\",\"packageWeight\",\"shipWarehouseId\"],\"paymentType\":1,\"priceEndType\":2},\"ttxr\":\"2022年06月10日\",\"txm\":\"https://csm4vnvgsto001.blob.core.chinacloudapi.cn/test/test/upload/magellan/barcode/fa71ca22065f4835945c84c229e7613f_条形码.png?ss=b&sig=20lIDmlzvyUSHy9xMjBPOBOGZ6FCDis1Qbhua7eJMKI%3D&st=2022-06-10T03%3A01%3A23Z&se=2022-06-10T16%3A00%3A00Z&sv=2019-02-02&srt=sco&sp=r&sr=sco\",\"ty\":\"一口价合同\",\"vr\":\"豆粕\",\"xyb\":\"001\",\"ydoc\":\"2022年06月10日\",\"yf\":0.000000,\"yhy\":\"2209\",\"yjhgc\":\"路易达孚（张家港）饲料蛋白有限公司\",\"ypr\":3000.00,\"yyf\":0.000000,\"zh\":\"66666666\",\"zhfk\":\"457281554845\",\"zqf\":\"0.000000\",\"zqfz\":\"0E-12\"}";
        Map<String, Object> beanToMap = FastJsonUtils.getJsonToMap(bizDataJson);
        String templateCondition = "{\"actionType\":2,\"addedDepositRate\":0,\"contractType\":1,\"deliveryFactoryCode\":\"ZJG\",\"deliveryType\":1,\"depositAmount\":14850.000000,\"modifyList\":[\"goodsId\",\"goodsPackageId\",\"packageWeight\",\"shipWarehouseId\"],\"paymentType\":1,\"priceEndType\":2}";
        beanToMap.put("templateCondition", FastJsonUtils.getJsonToMap(templateCondition));
        System.out.println("beanToMap=======================" + beanToMap);
        String templateInfo = TemplateRenderFtlUtil.templateRender(beanToMap, "test.ftl");
        System.out.println("templateInfo:=====================" + templateInfo);
    }

}
