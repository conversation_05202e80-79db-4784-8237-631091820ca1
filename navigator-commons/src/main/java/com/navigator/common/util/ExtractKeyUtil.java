package com.navigator.common.util;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Author: wang tao
 * @Date: 2021/3/20
 * @Time: 17:36
 * @Desception:
 */
public class ExtractKeyUtil {

    /**
     * 使用正则表达式提取中括号中的内容:按{}
     *
     * @param msg 要提取的字符串
     * @return List
     */
    public static List<String> extractMessageByBrace(String msg) {

        List<String> list = new ArrayList<String>();
        String express = "(\\{\\w+\\})";
        Pattern p = Pattern.compile(express);
        Matcher m = p.matcher(msg);
        while (m.find()) {
            list.add(m.group().substring(1, m.group().length() - 1));
        }
        return list;
    }

    /**
     * 使用正则表达式提取中括号中的内容:按[]
     *
     * @param msg 要提取的字符串
     * @return List
     */
    public static List<String> extractMessageByBrackets(String msg) {

        List<String> list = new ArrayList<String>();
        String express = "(\\[[^\\]]*\\])";
        Pattern p = Pattern.compile(express);
        Matcher m = p.matcher(msg);
        while (m.find()) {
            list.add(m.group().substring(1, m.group().length() - 1));
        }
        return list;
    }

    public static void main(String[] args) {
        String template = "专家{expertName},您好！请于{dateTime}前往\"{address}\"参与评标，口令密码:{password}。如有疑问请致电:{phone}";
        System.out.println(extractMessageByBrace(template));
    }
}
