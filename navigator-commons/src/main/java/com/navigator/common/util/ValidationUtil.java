package com.navigator.common.util;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.HashSet;
import java.util.Set;

/**
 * 校验工具类
 *
 * <AUTHOR>
 */
public class ValidationUtil {

    private static Validator validator;

    static {
        ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
        validator = validatorFactory.getValidator();
    }

    /**
     * 校验器
     *
     * @return
     */
    public static Validator getValidator() {
        return validator;
    }

    /**
     * 校验规则
     *
     * @param t
     * @return
     */
    public static Set<String> validate(Object t) {
        Set<ConstraintViolation<Object>> set = validator.validate(t);
        Set<String> messageSet = new HashSet<>();
        for (ConstraintViolation<Object> item : set) {
            messageSet.add(item.getMessage());
        }
        return messageSet;
    }
}