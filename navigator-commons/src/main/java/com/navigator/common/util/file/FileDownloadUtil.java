package com.navigator.common.util.file;

import com.navigator.common.constant.FileConstant;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.*;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;

/**
 * <AUTHOR>
 * @since 2020-07-09 14:10
 */
public class FileDownloadUtil {
    private static final Logger logger = LoggerFactory.getLogger(FileDownloadUtil.class);

    /**
     * 文件下载
     *
     * @param path     服务器文件相对路径
     * @param response 响应
     * @return 响应下载结果
     */
    public static HttpServletResponse download(String path, HttpServletResponse response) {
        InputStream fis = null;
        try {
            // path是指欲下载的文件的路径。
            if (StringUtils.isBlank(path)) {
                throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
            }
            File file = new File(path);
            if (!file.exists()) {
                throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
            }
            // 取得文件名。
            String fileName = file.getName();
            fileName = fileName.substring(fileName.indexOf("_") + 1);
            // 以流的形式下载文件。
            fis = new BufferedInputStream(new FileInputStream(path));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);

            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.addHeader("Content-Length", "" + file.length());
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
            logger.info("文件下载路径：" + path);
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            try {
                if(fis != null)
                    fis.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            fis = null;
        }
        return response;
    }

    public static byte[] downLoadFileBytesByUrl(String fileUrl) {
        try {
            byte[] fileBytes = loadFileByteFromURL(fileUrl);
            if (fileBytes != null) {
                System.out.println("fileBytes length:" + fileBytes.length);
            } else {
                System.out.println("fileBytes == null");
            }
            return fileBytes;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static boolean downLoadFileByUrl(String fileUrl, String filePath, String fileName) {
        FileOutputStream outputStream = null;
        try {
            byte[] fileBytes = loadFileByteFromURL(fileUrl);
            if (fileBytes != null) {
                System.out.println("fileBytes length:" + fileBytes.length);
            } else {
                System.out.println("fileBytes == null");
            }
            String newOath = filePath;
            File file = new File(newOath);
            judeDirExists(file);
            outputStream = new FileOutputStream(file + fileName);
            outputStream.write(fileBytes);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            if (null != outputStream) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void judeDirExists(File file) {
        if (!file.exists()) {
            file.mkdirs();
        }
    }

    /**
     * 从url获取文件内容的字节数组
     *
     * @param fileUrl
     * @return
     */
    public static byte[] loadFileByteFromURL(String fileUrl) {

        if (fileUrl.startsWith("http://")) {
            return httpConverBytes(fileUrl);
        } else if (fileUrl.startsWith("https://")) {
            return httpsConverBytes(fileUrl);
        } else {
            return null;
        }

    }

    /**
     * @param fileUrl
     * @return
     * @MethodName httpConverBytes
     * @Description http路径文件内容获取
     */
    public static byte[] httpConverBytes(String fileUrl) {
        BufferedInputStream in = null;
        ByteArrayOutputStream out = null;
        URLConnection conn = null;

        try {
            URL url = new URL(fileUrl);
            conn = url.openConnection();

            in = new BufferedInputStream(conn.getInputStream());

            out = new ByteArrayOutputStream(1024);
            byte[] temp = new byte[1024];
            int size = 0;
            while ((size = in.read(temp)) != -1) {
                out.write(temp, 0, size);
            }
            byte[] content = out.toByteArray();
            return content;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != in)
                    in.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            try {
                if(null != out)
                    out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * @param fileUrl
     * @return
     * @MethodName httpsConverBytes
     * @Description https路径文件内容获取
     */
    private static byte[] httpsConverBytes(String fileUrl) {
        BufferedInputStream inStream = null;
        ByteArrayOutputStream outStream = null;

        try {

            TrustManager[] tm = {new TrustAnyTrustManager()};
            //solve sonar
            String agreement = "SSL";
            SSLContext sc = SSLContext.getInstance(agreement, "SunJSSE");
            sc.init(null, tm, new java.security.SecureRandom());
            URL console = new URL(fileUrl);

            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setRequestMethod("GET");
            conn.connect();

            inStream = new BufferedInputStream(conn.getInputStream());
            outStream = new ByteArrayOutputStream();

            byte[] buffer = new byte[1024];
            int len = 0;
            while ((len = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, len);
            }

            byte[] content = outStream.toByteArray();
            return content;

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != inStream) {
                try {
                    inStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (null != outStream) {
                try {
                    outStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    /**
     * 信任证书的管理器
     *
     * <AUTHOR>
     */
    private static class TrustAnyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            if (chain != null && authType.equals("chain")) {
                throw new CertificateException();
            } else {
                int i = 1;
            }
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
            if (chain != null && authType.equals("chain")) {
                throw new CertificateException();
            } else {
                int i = 1;
            }
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            if (true)
                return true;
            return false;
        }
    }

    public static String getExtensionName(String filename) {
        if (filename != null && filename.length() > 0) {
            int dot = filename.lastIndexOf('.');
            if (dot > -1 && dot < filename.length() - 1) {
                return filename.substring(dot + 1);
            }
        }
        return filename;
    }


    public static void main(String[] args) {
        String fileUrl = "https://wesign-prod-r.signit.vip:10443/WSID_LINK_00000180a8d380f54a1eb5bdb07d0001/download-file?token=9de8292b0400423fb876e67f7b7414fd";
        String filePath = FileConstant.FILE_UPLOAD;
        System.out.println(downLoadFileByUrl(fileUrl, filePath, "nana6.pdf"));
    }


}
