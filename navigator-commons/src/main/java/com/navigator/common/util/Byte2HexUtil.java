/**
 * Copyright (c) 2011-2014, hubin (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License. You may obtain a copy of
 * the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
 * License for the specific language governing permissions and limitations under
 * the License.
 */
package com.navigator.common.util;

import java.io.UnsupportedEncodingException;
import java.util.Formatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 16进制字串转换工具类
 *
 * <AUTHOR>
 * @since 2020-07-29 20:20
 */
public class Byte2HexUtil {

    /**
     * 字节转换为 16 进制字符串
     *
     * @param b 字节
     * @return
     */
    public static String byte2Hex(byte b) {
        String hex = Integer.toHexString(b);
        if (hex.length() > 2) {
            hex = hex.substring(hex.length() - 2);
        }
        while (hex.length() < 2) {
            hex = "0" + hex;
        }
        return hex;
    }

    /**
     * 字节数组转换为 16 进制字符串
     *
     * @param bytes 字节数组
     * @return
     */
    public static String byte2Hex(byte[] bytes) {
        Formatter formatter = new Formatter();
        for (byte b : bytes) {
            formatter.format("%02x", b);
        }
        String hash = formatter.toString();
        formatter.close();
        return hash;
    }

    /**
     * 字符串转换成为16进制(无需Unicode编码)
     *
     * @param str
     * @return
     */
    public static String str2HexStr(String str) {
        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (int i = 0; i < bs.length; i++) {
            bit = (bs[i] & 0x0f0) >> 4;
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;
            sb.append(chars[bit]);
            // sb.append(' ');
        }
        return sb.toString().trim();
    }

    /**
     * 16进制直接转换成为字符串(无需Unicode解码)
     *
     * @return 对应的字符串
     * <AUTHOR>
     */
    public static String hexStr2Str(String hex) {
        String hexStr = "";
        String str = "0123456789ABCDEF"; //16进制能用到的所有字符 0-15
        for (int i = 0; i < hex.length(); i++) {
            String s = hex.substring(i, i + 1);
            if (s.equals("a") || s.equals("b") || s.equals("c") || s.equals("d") || s.equals("e") || s.equals("f")) {
                s = s.toUpperCase().substring(0, 1);
            }
            hexStr += s;
        }

        char[] hexs = hexStr.toCharArray();//toCharArray() 方法将字符串转换为字符数组。
        int length = (hexStr.length() / 2);//1个byte数值 -> 两个16进制字符
        byte[] bytes = new byte[length];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            int position = i * 2;//两个16进制字符 -> 1个byte数值
            n = str.indexOf(hexs[position]) * 16;
            n += str.indexOf(hexs[position + 1]);
            // 保持二进制补码的一致性 因为byte类型字符是8bit的  而int为32bit 会自动补齐高位1  所以与上0xFF之后可以保持高位一致性
            //当byte要转化为int的时候，高的24位必然会补1，这样，其二进制补码其实已经不一致了，&0xff可以将高的24位置为0，低8位保持原样，这样做的目的就是为了保证二进制数据的一致性。
            bytes[i] = (byte) (n & 0xff);
        }
        String name = "";
        try {
            name = new String(bytes, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return name;
    }
//    /**
//     * 16进制转字符串（中英）
//     *
//     * @param hexStr
//     * @return
//     */
//    public static String hexStr2Str(String hexStr) {
//        String str = "0123456789ABCDEF";
//        char[] hexs = hexStr.toCharArray();
//        byte[] bytes = new byte[hexStr.length() / 2];
//        int n;
//        for (int i = 0; i < bytes.length; i++) {
//            n = str.indexOf(hexs[2 * i]) * 16;
//            n += str.indexOf(hexs[2 * i + 1]);
//            bytes[i] = (byte) (n & 0xff);
//        }
//        return new String(bytes);
//    }

    /**
     * 把汉字转成UNICODE
     */
    private static String strToUnicode(String s) {
        String str = "";
        for (int i = 0; i < s.length(); i++) {
            int ch = (int) s.charAt(i);
            if (ch > 255)
                str += "\\u" + Integer.toHexString(ch);
            else
                str += "\\" + Integer.toHexString(ch);
        }

        return str;
    }

    /**
     * 将字符编码转换成US-ASCII码
     */
    public static String toASCII(String str) throws UnsupportedEncodingException {
        String US_ASCII = "US-ASCII";
        if (str != null) {
            // 用默认字符编码解码字符串。
            byte[] bs = str.getBytes();
            // 用新的字符编码生成字符串
            return new String(bs);
        } else {
            return "";
        }
    }


    /**
     * Unicode转 汉字字符串
     *
     * @param str \u6728
     * @return '木' 26408
     */
    public static String unicodeToString(String str) {

        Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
        Matcher matcher = pattern.matcher(str);
        char ch;
        while (matcher.find()) {
            // group 6728
            String group = matcher.group(2);
            // ch:'木' 26408
            ch = (char) Integer.parseInt(group, 16);
            // group1 \u6728
            String group1 = matcher.group(1);
            str = str.replace(group1, ch + "");
        }
        return str;
    }

    public static void main(String[] args) throws Exception {

//        String unicode = "\\u672c\\u5de5\\u5177\\u53ef\\u4ee5\\u628a\\u4e2d\\u6587\\u3001\\u82f1\\u6587\\u5b57\\u6bcd\\u3001\\u6807\\u70b9\\u7b26\\u53f7\\u3001\\u7279\\u6b8a\\u7279\\u53f7\\u7b49\\u5b57\\u7b26\\u4e32\\u8f6c\\u6362\\u6210\\u4ee5\\u53cd\\u659c\\u6760u(\\u)\\u5f00\\u5934\\u768416\\u8fdb\\u5236Unicode\\u7f16\\u7801\\uff0c\\u53cd\\u4e4b\\u8fd8<><!-- TP002\\u65b0\\u6210\\u4ea4\\u8ba2\\u5355\\u6a21\\u677f -->\n" +
//                "<!DOCTYPE html>\n" +
//                "<html lang=\"en\">\n" +
//                "<head>\n" +
//                "  <meta charset=\"UTF-8\">\n" +
//                "  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">\n" +
//                "  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
//                "  <title>TP002\\u65b0\\u6210\\u4ea4\\u8ba2\\u5355\\u6a21\\u7248</title>\n" +
//                "</head>\n" +
//                "<body>\n" +
//                "  <div class=\"temp-contract-content\">\n" +
//                "    <div class=\"t-left code-image\">\n" +
//                "      <img src=\"https://ldcblob.blob.core.chinacloudapi.cn/navigator/dev/upload/magellan/barcode/c748de5e968b45aca09700de20e0f729_\\u4e8c\\u7ef4\\u7801.png\" alt=\"\\u6761\\u5f62\\u7801\">\n" +
//                "    </div>\n" +
//                "</body>\n" +
//                "</html>";
//        String unicode = "<!-- TP002\\u26032\\u25104\\u20132\\u35746\\u21333\\u27169\\u26495 -->Ú<!DOCTYPE html>Ú<html lang=\"en\">Ú<head>Ú  <meta charset=\"UTF-8\">Ú  <meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge\">Ú  <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">Ú  <title>TP002\\u26032\\u25104\\u20132\\u35746\\u21333\\u27169\\u29256</title>Ú    <style>Ú     div,p,span,br,table,tr,td,th,tbody,thead,tfoot {page-break-inside: avoid !important;}Ú.temp-contract-content{Ú        width: 95%;Ú        margin: 0 auto;Ú        line-height: 2em;Ú        box-sizing: border-box;Ú        padding:0;Ú        font-family: 'SimSun';Ú      }Ú      .temp-contract-content .term-num{Ú        width: 35px;Ú        text-align: center;Ú      }Ú      .temp-contract-content .bold {Ú        font-weight: bold;Ú      }Ú      .temp-contract-content .fill {Ú        -webkit-box-flex: 1;Ú        -webkit-flex:1;Ú        flex:1Ú      }Ú      .temp-contract-content .term-row{Ú        /* padding-left:2em */Ú      }Ú      .temp-contract-content .padding-em{Ú        padding-left: 2em;Ú      }Ú      .temp-contract-content .column {Ú        display: -webkit-box;Ú        display: flex;Ú        align-items: center;Ú        flex-direction: column;Ú      }Ú      .temp-contract-content .row {Ú        display: -webkit-box;Ú        display: flex;Ú        flex-direction: row;Ú      }Ú      .temp-contract-content .start {Ú        align-items: flex-start;Ú      }Ú      .temp-contract-content .center {Ú        -webkit-box-pack: center;Ú        justify-content: center;Ú      }Ú      .temp-contract-content .between {Ú        -webkit-box-pack: justify;Ú        justify-content: space-between;Ú        text-align: left;Ú      }Ú      .temp-contract-content .t-left{Ú        float: left;Ú      }Ú      .temp-contract-content .t-right{Ú        float: right;Ú      }Ú      .temp-contract-content .clear{Ú        clear: both;Ú      }Ú      .temp-contract-content .absolute-center{Ú        position:absolute;Ú        left:0px;Ú        right:0px;Ú        text-align: center;Ú      }Ú      .temp-contract-content .relative{Ú        position: relative;Ú      }Ú      .temp-contract-content .text-align{Ú        text-align: center;Ú      }Ú      .temp-contract-content .t-sign{Ú        width:180px;Ú        position: absolute;Ú        top:-20px;Ú        left:50pxÚ      }Ú      .temp-contract-content .t-hidden{Ú        visibility: hidden;Ú      }Ú      .temp-contract-content .term-item-bold{Ú        width:90px;Ú        padding-right:10px;Ú      }Ú      .temp-contract-content .term-content{Ú        width:100%;Ú        display: inline-block;Ú      }Ú      .temp-contract-content .sub-title{Ú        line-height: 0.8em;Ú      }Ú      .temp-contract-content .term-table{Ú        width:90%;Ú        margin:0 auto;Ú        border: 1px solid #000;ÚÚ        border-collapse: collapse;Ú      }Ú      .temp-contract-content .term-table td , .temp-contract-content .term-table tr{Ú        text-align: center;Ú        border: 1px solid #000ÚÚ      }Ú      .temp-contract-content .term-table td{Ú        height: 32px;Ú      }Ú      .temp-contract-content .code-image{Ú        height: 60px;Ú      }Ú      .temp-contract-content .code-image img{Ú        height:100%Ú      }ÚÚ  </style>Ú</head>Ú<body>Ú  <div class=\"temp-contract-content\">Ú    <div class=\"t-left code-image\">¢\u0002\u0002\u0002\u0002\u0002\u0003Æ\u0096Ör\u00077&3Ò&\u0087GG\u00073¢òöÆF6&Æö\"æ&Æö\"æ6÷&Ræ6\u0086\u0096æ\u00166Æ÷VF\u0017\u0006\u0092æ6âöæ\u0017f\u0096v\u0017F÷\"öFWb÷W\u0006Æö\u0016BöÖ\u0016vVÆÆ\u0016âö&\u0017&6öFRö3sC\u0086FSVS\u0093c\u0086#CV\u00166\u0013\u0003\u0093s\u0003\u0006FS#\u0006S\u0006cs#\u0095õÇS#\u0003\u0013\u0003\u0085ÇS3#S\u0003\u0005ÇS3\u0003s#\u0012ç\u0006ær\"\u0006\u0016ÇCÒ%ÇS#cCcUÇS#CC\u0013\u0085ÇS3\u0003s#\u0012#ê    </div>Ú    <div class=\"t-right code-image\">¢\u0002\u0002\u0002\u0002\u0002\u0003Æ\u0096Ör\u00077&3Ò&\u0087GG\u00073¢òöÆF6&Æö\"æ&Æö\"æ6÷&Ræ6\u0086\u0096æ\u00166Æ÷VF\u0017\u0006\u0092æ6âöæ\u0017f\u0096v\u0017F÷\"öFWb÷W\u0006Æö\u0016BöÖ\u0016vVÆÆ\u0016âö&\u0017&6öFRö#&\u0016\u0013\u0083C\u0083\u0093SCFcFF&&\u0013\u0083\u0093s\u0003C#6#s\u0003\u00936c\u0006\u0015õÇS#cCcUÇS#CC\u0013\u0085ÇS3\u0003s#\u0012ç\u0006ær\"\u0006\u0016ÇCÒ%ÇS#\u0003\u0013\u0003\u0085ÇS3#S\u0003\u0005ÇS3\u0003s#\u0012#ê    </div>Ú    <div class=\"clear row\">Ú      <div class=\"code-image\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003Æ\u0096Ör\u00077&3Ò&\u0087GG\u00073¢òöÆF6&Æö\"æ&Æö\"æ6÷&Ræ6\u0086\u0096æ\u00166Æ÷VF\u0017\u0006\u0092æ6âöæ\u0017f\u0096v\u0017F÷\"÷\u0007&öB÷W\u0006Æö\u0016BôÄD2ÔÄôtòç\u0006ær\"\u0006\u0016ÇCÒ&Æövò#ê      </div>Ú         <div class=\"text-align bold term-content template-title\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097cåÇS3\u0083\u0013CEÇS#\u0013\u0083\u0003eÇS3SsCeÇS#\u0013333ÂöF\u0097cê        <div class=\"sub-title\">ÿ\b\\u36865\\u36135-\\u19968\\u21475\\u20215\\u21512\\u21516 -\\u36170\\u38144ÿ\t</div>¢\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B\u00066ÆV\u0017\"#ê      <div class=\"term-content padding-em\">\\u31614\\u32422\\u26085\\u26399ÿ\u001A2022\\u2418005\\u2637621\\u26085</div>¢\u0002\u0002\u0002\u0003ÂöF\u0097câ\n" +
//                "¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B\u00066ÆV\u0017\"#ê      <div class=\"term-content padding-em\">\\u35746\\u21333\\u21495ÿ\u001ATJIBSBMS2203942</div>¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003Æ'\"\u0002óí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B\u00066ÆV\u0017\"#ê      <div class=\"term-content padding-em\">\\u26412\\u38144\\u21806\\u35746\\u21333\\u20381\\u25454\\u21452\\u26041\\u201102022\\u2418005\\u2637621\\u26085\\u31614\\u35746\\u30340\\u35910\\u31893\\u38144\\u21806\\u26694\\u26550\\u21512\\u21516ÿ\b\\u21512\\u21516\\u21495\\u20026TJIBSBMS2203942ÿ\tÿ\b \u001C\\u26694\\u26550\\u21512\\u21516 \u001Dÿ\t\\u30830\\u314350\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0003Æ'\"\u0002óê      <div class=\"term-content padding-em\">\\u21452\\u26041\\u21516\\u24847\\u26412\\u38144\\u21806\\u35746\\u21333\\u30340\\u26465\\u27454\\u21644\\u26465\\u20214\\u22914\\u19979ÿ\u001A</div>¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B#í¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖçVÒ#ã\u0013\u0013ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ6öçFVçB#í¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ\u0007&÷r\u00077F\u0017'B#ê          <div class=\"term-item-bold\"><span class=\"bold\">\\u36135\\u29289ÿ\u001A</span></div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ#ê            <div class=\"term-row\">\\u35910\\u31893ÿ\b\\u20197\\u19979\\u31216 \u001C\\u36135\\u29289 \u001Dÿ\t</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê        </div>Ú      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">20\u0001</div>Ú      <div class=\"term-content\">Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3s3#UÇS3s3#\u007Fñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#ã\u0013CRÃ\u0003\u0003\u0005ÇS#\u0013SCOðÅÇS#\u0003\u0083\u0003\u0015ÇS3Ssc\u0083UÇS3\u00033C\u0005ÇS#\u00833#%ÇS3\u0003s\u0003\u0015ÇS3s3#\u007FðÅÇS#\u0003\u0013\u0093uÇS#3CSEÇS3\u0083Cc\u0095ÇS#\u0003\u00133%ÇS3c\u00133UÇS3s3#UÇS3s3#uÇS#\u0003\u0003#eÇS#\u0003\u00933OðÅÇS3\u0003\u0003\u0003\u0015ÇS#\u001333EÇS#c\u0003C\u0015ÇS3c\u0083s5ÇS#S3#\u0013#ÂöF\u0097cê          </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B#í¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖçVÒ#ã3\u0013ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ6öçFVçB#í¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ\u0007&÷r\u00077F\u0017'B#ê          <div class=\"term-item-bold\"><span class=\"bold\">\\u36136\\u37327\\u25351\\u26631ÿ\u001A</span></div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ#ê            <div class=\"term-row\">\\u31895\\u34507\\u3033342% ±0.5%ÿ\f\\u27700\\u20998\"d13%,\\u23615\\u32032\\u37238\\u27963\\u24615\"d0.3 U/gÿ\f\\u27682\\u27687\\u21270\\u38078\\u34507\\u30333\\u36136\\u28342\\u35299\\u24230\"e70%ÿ\f\\u20854\\u20182\\u25351\\u26631\\u31526\\u21512\\u20225\\u19994\\u26631\\u20934(\\u26631\\u20934\\u32534\\u21495ÿ\u001AQ/320582 LDC1ÿ\t\\u21450\\u21355\\u29983\\u25351\\u26631\\u26631\\u20934ÿ\b\\u26631\\u20934\\u32534\\u21495ÿ\u001AGB13078ÿ\t0\u0002\\u20197\\u19978\\u36135\\u29289\\u21407\\u26448\\u26009\\u20026\\u36716\\u22522\\u22240\\u22823\\u359100\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê        </div>¢\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097câ\u0002\n" +
//                "¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B#í¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖçVÒ#ãC\u0013ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ6öçFVçB#í¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ\u0007&÷r\u00077F\u0017'B#ê          <div class=\"term-item-bold\"><span class=\"bold\">\\u21253\\u35013\\u21450\\u26631\\u35782\\u35201\\u27714ÿ\u001A</span></div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ#ê            <div class=\"term-row\">240\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#\u0003\u0013\u0013\u0005ÇS#\u0003\u0083suÇS#sC#UÇS3\u0083\u0013CEÇS#\u0013\u0083\u0003eÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS#c\u0013\u0003/ðÅÇS#C#\u0013%ÇS#\u0003\u0003\u0003UÇS#cc\u0083EÇS3c\u0093\u0083\u0015ÇS#3C3%ÇS###c\u0095ÇS#3Cs\u0085ÇS#c3suÇS#\u0003\u0083S\u0015ÇS#\u0003\u0083\u0093%ÇS\u0013\u0093\u0093\u0093EÇS3cs\u0013eÇS##S#%ÇS###C\u0005ÇS#\u0093\u0093\u00835ÇS#\u0093#\u0083\u0095ÇS#3C35ÇS#\u0003\u0083C\u0005ÇS3\u0013cC\u0095ÇS#\u0093s\u0003%ÇS#\u0013CS\u0005ÇS#cc3\u0015ÇS3Ss\u0083%ÇS3\u0013cC\u0095ÇS#\u0093s\u0003%ÇS3\u00033C\u0005ÇS3S#\u0003\u0015ÇS#ss\u0013OðÅÇS#3SCUÇS#cC\u0013%ÇS#\u00133#uÇS3SsS\u0085ÇS3\u0093\u000335ÇS\u0013\u0093\u0093s\u0095ÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3c\u0083#uÇS3C\u0083\u0093%ÇS#c\u0013#eÇS3\u0003\u00833\u0005ÇS\u0013\u0093\u0093\u0083\u0085ÇS#\u0013S\u0013%ÇS#s\u0083c\u0015ÇS3\u00033C\u0005ÇS3cs\u0013eÇS##S#%ÇS###C\u0005ÇS#cc3\u0015ÇS3Ss\u0083#%ÇS##3\u0013%ÇS#\u0003\u00133%ÇS3c\u00133UÇS##3#\u0005ÇS#\u0083\u0083SuÇS#CC\u00035ÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3S\u0003\u00135ÇS3cs\u0013\u0002õÇS3S\u0003\u00135ÇS3333uÇS#\u0013S\u0013\u008FðÅÇS3\u0003\u0003\u0003\u0015ÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#S\u0013\u0093\u0015ÇS3C\u0083\u0093%ÇS#3SCUÇS3cs\u0013eÇS##S#%ÇS###C\u0005ÇS#\u0093\u0093\u00835ÇS#\u0093#\u0083\u0095ÇS#3C35ÇS#\u0003\u0083C\u0005ÇS3\u00033C\u0005ÇS3\u0003C\u0013uÇS3\u0013cC\u0095ÇS#\u0013cCEÇS#S\u0013\u0093\u0015ÇS3C\u0083\u0093#*            </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê        </div>Ú      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">70\u0001</div>Ú      <div class=\"term-content\">Ú         <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS#\u0003#\u0013UÇS#cc\u0083Oñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#\u0013335ÇS#\u0003#\u0013UÇS#\u0003\u0003#eÇS#\u0003\u0013SEÇS#sccUÇS#C\u0003cUÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÅÇS#\u0013SCuÇS3\u0013#CeÇS#\u0003#\u0013RÅÇS#\u0013335ÇS#\u0003#\u0013UÇS#\u0013#S5ÇS#\u0013SCuÇS#S\u0093SUÇS3\u0013\u0083\u00935ÇS3C\u0093\u0013uÇS3c\u0013C\u00833\u0002ã\u0003\u0005ÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÅÇS#\u00833\u0083%ÇS#c3\u0093\u0095ÇS3c\u0013S3C\u0002ã\u0003\u0005ÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÅÇS3\u0093cC\u0005ÇS#\u0083#\u0003\u0015ÇS3c\u0013S3\u0083\u0002ã\u0003\u0005ÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÅÇS#3CS\u0085ÇS3Ss\u0083UÇS#S#C\u0005ÇS#\u0003#\u0013S#\"ã\u0003\u0005ÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÅÇS#S\u0013c5ÇS3#C\u00935ÇS3c\u0013S3\u0012ã\u0003\u0005ÇS#\u0003\u0083\u00032õÇS#\u0013SCOðÃÂöF\u0097cê          </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">80\u0001</div>Ú      <div class=\"term-content\">Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS#\u0003\u00133%ÇS3c\u00133UÇS#c\u0013\u0003%ÇS3\u00833\u0083\u0082õÇS##3#\u0005ÇS#\u0083\u0083S\u007Fñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#\u0003\u00133%ÇS3c\u00133UÇS##3#\u0005ÇS#\u0083\u0083S\u007Fñ¥ÇS#S3S\u0015ÇS#3CS\u0005ÇS3\u0003s#\u0015ÇS##\u00833eÇS3333uÇS#cC\u0093RõÇS3cs\u0013\u0005ÇS#cC\u0093UÇS#\u0003\u00133%ÇS3c\u00133_ð\u0082\u0001ÅÇS#S3S\u0015ÇS#3CS\u0005ÇS##3#\u0005ÇS#\u0083\u0083Sr\u0001ßð\u0093\"\u0003ÂöF\u0097cê            <div class=\"term-row\">\\u22320\\u22336ÿ\u001Aÿ\b \u001C\\u25351\\u23450\\u22320\\u28857 \u001Dÿ\t0\u0002 </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#\u0003\u00133%ÇS3c\u00133UÇS#c3\u0093\u0095ÇS3\u0083C\u0083\u000Fñ£#\u0003#%ÇS#C\u0013\u0083\u0003\u0003UÇS#c3sc#%ÇS#c\u0003\u0083UÇS33#cs#\u0003#%ÇS#C\u0013\u0083\u0003\u0003UÇS#c3sc#uÇS#c\u0003\u0083_ð\u0085ÇS#\u0013#S5ÇS#\u0013SCuÇS3c#\u0013UÇS#sC\u0093\u0005ÇS#\u0003\u0003\u0003EÇS#c\u0003\u0083_ð\u0093ÂöF\u0097cê          </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">90\u0001</div>Ú      <div class=\"term-content\">Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS#\u0003\u0013\u0083EÇS#sCSOñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#C#\u0013%ÇS#\u0003\u0013\u0013\u0005ÇS#sS\u0093\u0095ÇS#S#\u0003\u0095ÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS#SSS%ÇS3c\u00133UÇS#\u0013S\u0013\u0085ÇS3\u00033C\u0003\u0013\u0005ÇS#\u0003\u0003\u0013\u0005ÇS33#S\u0085ÇS#\u0083\u0093\u0083%ÇS#c\u0003\u0083UÇS#\u0003\u0083c\u0095ÇS#\u0013S#\u0015ÇS#\u001333EÇS#c\u0003C\u0015ÇS#\u0003\u0083C\u0005ÇS3\u0093\u0003c\u0095ÇS#S\u0093\u00035ÇS#\u0003\u0013\u0083EÇS3S\u0083\u00135ÇS#S#\u0003\u0095ÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3\u00033C\u0005ÇS3c\u00133UÇS#sCSOðÅÇS#\u0003\u0013\u0093uÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS#\u0003\u00133%ÇS#SS\u0003\u0095ÇS#\u0003\u0093s5ÇS3SssuÇS3\u00033C\u0005ÇS#\u0003\u0093\u0083eÇS#\u0003\u0083SUÇS#c\u0003\u0083UÇS#c3\u0093\u0095ÇS3c#\u0013UÇS3\u0013c3\u0093#ÂöF\u0097cê            <div class=\"term-row\">\\u36135\\u27454\\u30340\\u26368\\u32456\\u32467\\u31639\\u39035\\u20381\\u25454\\u21512\\u21516\\u20215\\u266840\u0001\\u25152\\u26377\\u24050\\u20132\\u36135\\u30340\\u36135\\u29289\\u37325\\u37327\\u36827\\u348920\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#c\u0003\u0083\u0005ÇS3Sss\u0005ÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS###C\u0005ÇS#\u00033\u0003\u0095ÇS#\u0013C\u0003uÇS###C\u0005ÇS#cC\u0013\u0005ÇS33\u0003#\u0015ÇS#S3S5ÇS#\u0093\u00033\u0015ÇS#cC\u0013%ÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS3S#c\u0085ÇS#3CS\u0005ÇS#\u0013S#\u0015ÇS#\u001333EÇS#c\u0003C\u0015ÇS#\u0003\u0013\u0083EÇS#sCSOðÅÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#sS\u0093\u0095ÇS#C3\u0013\u0005ÇS#c3\u0093\u0095ÇS\u0013\u0093\u0093c\u0085ÇS##\u0083#_ðÅÇS#\u001333EÇS#c\u0003C\u0015ÇS#c3suÇS#cC3UÇS#S3S5ÇS#\u0093\u00033\u0015ÇS#3CSEÇS3\u0083Cc\u0095ÇS#C3\u0013\u0005ÇS3S\u0083#5ÇS##\u0083#UÇS#S\u0093c\u0085ÇS#\u0003\u0013\u0093r\u0001ÅÇS#C3\u0013\u0005ÇS#c3\u0093\u0095ÇS#S\u0093\u00035ÇS#\u0003\u0013\u0083EÇS#sCSEÇS3\u0093\u000332£\u0002ã\u0003RRõÇS##\u0083#R\u0001ÕÇS3\u00033C\u0005ÇS#sc\u0003EÇS#\u00033c5ÇS#\u0013S#\u0015ÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#S\u0093\u0013\u0005ÇS#\u0013Cc%ÇS###C\u0005ÇS#\u0003\u0013\u0083EÇS#sCSEÇS#C3\u0013\u0005ÇS3S\u0083#5ÇS3#s\u0083\u0005ÇS3c\u0093s5ÇS#\u0013Cc5ÇS3\u00033C\u0005ÇS#\u0013\u000335ÇS#Cc\u0083uÇS#SC3\u0095ÇS##\u008333#ÂöF\u0097cê          </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">100\u0001</div>Ú      <div class=\"term-content\">Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS#3cS5ÇS3#C#%ÇS#\u0003CCUÇS3SssuÇS3s3#\u009Fñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#â\u0083\u0012\u0092\u0005ÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#C#\u0013%ÇS#\u0003\u0013\u0013\u0003#\u0003#%ÇS#C\u0013\u0083\u0003\u0003UÇS#c3sc#5ÇS#c\u0003\u0083UÇS#\u0013\u0003c\u0092\u0085ÇS##\u0093\u0013EÇS3c\u00933UÇS#s\u0083c\u0015ÇS#3CS\u0005ÇS33C\u0013\u0005ÇS#\u0003SS\u0015ÇS#c\u0003\u0083UÇS#S\u0013\u0013\u0005ÇS#\u0003#C\u0015ÇS#Cc\u0083uÇS#c\u0003\u0083RÅÇS#\u0013\u0003\u0013uÇS3\u0093\u00033EÇS#C3\u0013\u0005ÇS33#cuÇS#\u0003\u0083SEÇS#\u0013S\u0013\u0085ÇS3\u00033C\u0005ÇS3\u0013S3%ÇS\u0013\u0093\u0093c\u0085ÇS#\u0003\u0003\u0013\u0005ÇS#C\u00033uÇS#\u00033\u0013eÇS#c\u0003\u0083R\u0095ÇS#\u0013S#\u0015ÇS#\u001333EÇS#c\u0003C\u0015ÇS#S\u0093\u00035ÇS#\u0003\u0013\u0083EÇS#\u0003\u0083C\u0005ÇS3\u0093\u0003c\u0095ÇS3c\u00133UÇS#sCSEÇS3\u00033C\u0003RUÇS#\u00033\u0013eÇS#\u0003\u0003#eÇS#3cS5ÇS3#C#%ÇS#\u0003CCUÇS3SssuÇS3s3#\u0093%ÇS##3\u0013%ÇS#3cS5ÇS3#C#%ÇS#c3\u0093\u0095ÇS3\u00833\u0083\u008FðÅÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS##\u0093\u0083uÇS3#CSeÇS#\u0003CCUÇS#S3CUÇS#3cS5ÇS3#C#%ÇS#\u0003CCUÇS3SssuÇS3s3#\u0095ÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u00033\u0003%ÇS#\u0003\u0013\u0013\u0005ÇS#cC\u0013\u0005ÇS#\u0003\u0013\u0083EÇS#sCSEÇS3s\u0003\u0093eÇS#\u0003\u0093\u0093\u0085ÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3c\u00133UÇS#\u0003SC\u0005ÇS3\u00033C\u0003RS%ÇS#3cS5ÇS3#C#%ÇS#\u0003CCUÇS3SssuÇS3s3#\u0095ÇS##\u0093\u0013EÇS#c3suÇS\u0013\u0093\u0093\u0083\u0015ÇS3c#s_ðÅÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#C#\u0013%ÇS##3\u0013%ÇS#SS\u0003\u0095ÇS#\u0013\u0003C\u0005ÇS#\u001333EÇS#c\u0003C\u0015ÇS3c\u0083c\u0015ÇS#\u0013\u0013S%ÇS#3cS5ÇS3#C#%ÇS#\u0003CCUÇS3SssuÇS3s3#\u0095ÇS3c\u0083\u0093\u0005ÇS3\u0003c\u00935ÇS#\u0013S\u0013\u0085ÇS3\u00033C\u0005ÇS\u0013\u0093\u0093c\u0085ÇS#\u0003\u0003\u0013\u0005ÇS#C\u00033uÇS#\u00033\u0013eÇS#c\u0003\u0083UÇS#\u0003#\u0093EÇS\u0013\u0093\u0093\u0083\u0015ÇS3c##\u0095ÇS3c\u0083\u0003uÇS\u0013\u0093\u0093suÇS#\u0003\u0003\u0013\u0005ÇS33#S\u0085ÇS#\u0083\u0093\u0083%ÇS#c\u0003\u0083UÇS#\u0003\u0083c\u0095ÇS3C\u0093\u0013uÇSC\u0003s\u0083C%ÇS#\u0003CCUÇS3SssuÇS3s3#\u0095ÇS##3\u0013%ÇS#c3c\u0085ÇS#\u0013S\u0013\u0085ÇS\u0013\u0093\u0093c\u0085ÇS3\u0013S\u0003\u0085ÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS3c\u00133UÇS#sCSEÇS#\u0003\u0003\u00135ÇS#\u0003\u0083\u0003UÇS#S#c\u0093#ÂöF\u0097cê            <div class=\"term-row\">(2) \\u22312\\u21512\\u21516\\u31614\\u35746\\u21518\\u33267\\u20080\\u26041\\u25552\\u36135\\u21069ÿ\f\\u22914\\u24066\\u22330\\u20215\\u26684\\u27599\\u19979\\u36300\\u36229\\u36807\\u21512\\u21516\\u20215\\u26684\\u30340ÿ\f\\u21334\\u26041\\u26377\\u26435\\u23601\\u26410\\u25552\\u36135\\u37096\\u20998\\u21521\\u20080\\u26041\\u36861\\u21152\\u21516\\u31561\\u27604\\u20363\\u30340\\u23653\\u32422\\u20445\\u35777\\u373290\u0002\\u20080\\u26041\\u24212\\u22312\\u25509\\u21040\\u21334\\u26041\\u36890\\u30693\\u21518\\u30340\\u19968\\u20010\\u24037\\u20316\\u26085\\u20869\\u20294\\u19981\\u36229\\u36807\\u19977\\u20010\\u33258\\u28982\\u26085\\u20869\\u21521\\u21334\\u26041\\u25903\\u20184\\u36861\\u21152\\u37096\\u20998\\u30340\\u23653\\u32422\\u20445\\u35777\\u373290\u0002ÿ\b\\u24066\\u22330\\u20215\\u26684\\u20197\\u21334\\u26041\\u24403\\u26085\\u25253\\u20215\\u20026\\u20934ÿ\t0\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê        </div>¢\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'&÷r\u00077F\u0017'B#í¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖçVÒ#ã\u0013\u0013\u0013ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ6öçFVçB#í¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ\u0007&÷r\u00077F\u0017'B#ê          <div class=\"term-item-bold\"><span class=\"bold\">\\u37325\\u373270\u0001\\u36136\\u37327\\u26816\\u39564ÿ\u001A</span></div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò&f\u0096ÆÂ#ê            <div class=\"term-row\">M-S-\\u36865\\u36135-\\u25972\\u21253-0.15%\\u20080\\u26041</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cê        </div>Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB\u0007BÖ\u0086\u0096FFVâ#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS3s3#UÇS3s3#s\u0015ÇS3c\u00133eÇS3s3#uÇS#c\u0083\u0013eÇS3\u0093ScOñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS3c\u00133eÇS3s3#uÇS#\u0003\u0013\u0093uÇS#\u001333EÇS#c\u0003C\u0015ÇS#C\u00033uÇS#\u00133s\u0085ÇS#\u0013c\u0093uÇS3c\u00133eÇS#c\u0083\u0013eÇS3\u0093ScEÇS#\u0003\u0003#eÇS#\u0003\u00933C%ÇS##\u0093\u0013EÇS#c3suÇS3c\u00133eÇS3s3#uÇS#C3#%ÇS3SsS\u008FðÅÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#C#\u0013%ÇS##3\u0013%ÇS#S\u0093\u0013\u0005ÇS3c\u00133UÇS#\u0013S\u0013\u0082\u00032\u0005ÇS#\u0003\u0003\u0013\u0005ÇS#C\u00033uÇS#\u00033\u0013eÇS#c\u0003\u0083UÇS#\u0003\u0083c\u0095ÇS#\u0003\u0003s\u0005ÇS3\u0083sSEÇS#SSS%ÇS#\u0003\u0093\u0083oðÅÇS3c\u0093#eÇS#c3\u0093\u0095ÇS3S#s\u0005ÇS#\u0003\u0003#eÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS3\u0013S#eÇS#\u0013S\u0013%ÇS#cC\u0013%ÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS3S#\u0003\u0015ÇS#ss\u0013C%ÇS#cC\u0013\u0005ÇS3#Cc5ÇS#\u001333EÇS#c\u0003C\u0015ÇS#\u0003\u0003s\u0005ÇS3\u0083sSEÇS3\u0003\u00833\u0005ÇS3SsC\u008FðÅÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS\u0013\u0093\u0093\u0083\u0015ÇS#CCs\u0015ÇS#\u00033S\u0015ÇS#\u0093\u0093\u0093%ÇS#S\u0013\u0013\u0005ÇS##s\u0083\u0085ÇS3#c#%ÇS#c3suÇS3c\u00133eÇS3s3#uÇS#C3#%ÇS3SsS\u0085ÇS3\u00033C\u0005ÇS3c\u00133UÇS#\u0093#\u0083\u009FðÅÇS#\u0013SC%ÇS#\u0013\u0003\u0013uÇS3S#s\u0005ÇS#\u0003\u0003#eÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇSC\u0003ccEÇS3SsC\u0085ÇS3S\u0083\u00135ÇS3s\u0003\u0093eÇS#\u0003\u0093\u0093\u0085ÇS3c\u00133eÇS3s3#uÇS3\u0013S#eÇS#\u0013S\u0013%ÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS#cc3\u0015ÇS#\u0003\u00933OñµÇS#3SCUÇS3c\u00133eÇS3s3#uÇS#c3suÇS#C3#%ÇS3SsS\u0085ÇS3\u00033C\u0005ÇS3c\u00133UÇS#\u0093#\u0083\u009FðÅÇS3\u0003\u0003\u0003\u0015ÇS#\u0003\u0003\u0083\u0003\u0015ÇS#\u001333EÇS#\u0013CS%ÇS#c\u0003C\u0015ÇS#\u0003\u0083C\u0095ÇS#\u0013S\u0013eÇS#S#suÇS#ccs\u0095ÇS#\u0003\u00133%ÇS#\u0013CS%ÇS#c\u0003C\u0015ÇS3SsC\u0085ÇS#\u0013C\u0083uÇS3\u00033C\u0005ÇS#c\u0083\u0013eÇS3\u0093ScEÇS#cC#eÇS#cS\u0003\u000Fð\u0085ÇS3c\u0083\u0093\u0005ÇS3c\u0083\u0003uÇS###c\u0095ÇS#3Cs\u0082\u00044ä\u00152\u0005ÇS3SsC\u0085ÇS3SssuÇS3\u00033C\u0005ÇS#c\u0083\u0013eÇS3\u0093ScEÇS#cC#eÇS#cS\u0003\u000FðÅÇS#\u0003\u0013\u0093uÇS#\u001333EÇS#c\u0003C\u0015ÇS#SSS%ÇS#\u00033s\u0095ÇS3\u00033C\u0005ÇS#\u0003#\u0013\u0005ÇS3S\u0003\u0003\u0095ÇS#cC#eÇS#cS\u0003\u0005ÇS#\u0013S\u0013uÇS#\u0013335ÇS#\u0003\u0003\u00135ÇS3c\u0083s5ÇS#S3#\u001Fð\u0095ÇS#c\u0083\u0013eÇS3\u0093ScOðÅÇS#C\u0013\u0083%ÇS#\u0003\u0013\u0093uÇS#sC\u0093%ÇS#\u0003\u0003#eÇS#c3c\u0085ÇS3#CSeÇS#c\u0083\u0013eÇS3\u0093ScEÇS3#CcuÇS#cS#C%ÇS##\u0093\u0013EÇS#c\u0083\u0013eÇS3\u0093ScEÇS#\u0013S\u0013%ÇS#cc\u0083OðÅÇS#c\u0083\u0013eÇS3\u0093ScEÇS3c\u0013S5ÇS3\u0003\u0003\u0003\u0015ÇS#\u0003\u0003\u0083\u0005ÇS#c\u0003C\u0015ÇS#S#\u0013UÇS#S#\u0083_ñµÇS##\u0093\u0013EÇS#c\u0083\u0013eÇS3\u0093ScEÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u0013S\u0013%ÇS#cc\u0083OðÅÇS#\u001333EÇS#c\u0003C\u0015ÇS#S#\u0013UÇS#S#\u0083UÇS#c\u0083\u0013eÇS3\u0093ScEÇS3c\u0013S?ðÅÇS#\u001333EÇS#c\u0003C\u0015ÇS#\u0013C\u0083uÇS3c\u0083s5ÇS#S3#\u0015ÇS#\u0003\u0083\u00135ÇS3c\u0013S5ÇS#c3SeÇS#SCC%ÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u0013S\u0013%ÇS#cc\u0083EÇS3c\u00133UÇS#\u0093#\u0083\u0095ÇS#S\u0013\u0013\u0005ÇS3\u0083CsuÇS#\u0003#\u0013UÇS#S\u0013\u0013\u0005ÇS#\u0003\u0083SEÇS#\u0003\u0013\u0083%ÇS#\u0013S\u0013%ÇS#\u0093s\u0003%ÇS#c\u0003C\u0015ÇS#cc\u0093oðÅÇS#\u0003#\u0093EÇS\u0013\u0093\u0093\u0083\u0015ÇS#S#\u0013UÇS#S#\u0083UÇS#\u0003\u0083SEÇS#3C#uÇS#\u0003\u0003C\u0015ÇS#\u0013\u0013S5ÇS#\u0013cCEÇS3c\u00133\u0015ÇS#\u0003#\u0013\u0093#ÂöF\u0097cê            <div class=\"term-row\">\\u22914\\u30830\\u35748\\u36136\\u37327\\u25110\\u25968\\u37327\\u36229\\u20986\\u21512\\u21516\\u35268\\u23450\\u38480\\u20540ÿ\f\\u20080\\u26041\\u19981\\u24471\\u21333\\u26041\\u38754\\u36864\\u361350\u0002\\u21334\\u26041\\u36180\\u20607\\u35745\\u31639\\u22914\\u19979ÿ\u001A</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#ã\u0012â\u0005ÇS#ss\u0003\u0005ÇS#\u0003\u0093\u0093\u008Fñ¯ð\u0085ÇS3\u0003\u00833\u0005ÇS3SsC\u0085ÇS#\u0003SC\u0002\u0002Ò\u0003\u00132_ð\u009DuÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u0013S\u0013%ÇS#cc\u0083EÇS#\u0013c\u0093uÇS3s3#UÇS3s3#}uÇS#\u001333EÇS#c\u0003C\u0015ÇS#C\u00033uÇS#\u00133s\u0085ÇS#\u0003\u00133%ÇS3c\u00133UÇS#\u0003#\u0013S#ÂöF\u0097cê            <div class=\"term-row\">2. \\u34507\\u30333ÿ\u001Aÿ\b\\u21512\\u21516\\u35268\\u23450\\u34507\\u30333\\u21547\\u37327 - \\u30830\\u35748\\u20540ÿ\t×\\u19981\\u21512\\u26684\\u21697\\u37325\\u37327×\\u21334\\u26041\\u24037\\u21378\\u20132\\u36135\\u20215/\\u21512\\u21516\\u35268\\u23450\\u34507\\u30333\\u373270\u0002</div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#ã2â\u0005ÇS3\u0013\u0083\u0093UÇS3#C#\u0005ÇS3#S\u0003\u000Fñ¯ð\u0085ÇS3\u0003\u00833\u0005ÇS3SsC\u0085ÇS#\u0003SC\u0002\u0002\u00012\u000Fð\u009DuÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u0013S\u0013%ÇS#cc\u0083EÇS#\u0013c\u0093uÇS3s3#UÇS3s3#}uÇS#\u001333EÇS#c\u0003C\u0015ÇS#C\u00033uÇS#\u00133s\u0085ÇS#\u0003\u00133%ÇS3c\u00133UÇS#\u0003#\u0013S#ÂöF\u0097cê            <br />¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS#3CS\u0005ÇS#\u0003\u0003C\u001Fñ¢\u0005ÇS#\u001333EÇS#c\u0003C\u0015ÇS#C\u00033uÇS#\u00133s\u0085ÇS#\u0003\u00133%ÇS3c\u00133UÇS#\u0003#\u0013UÇS#S3S\u0012\u0004U\u0085r\u0005ÇS#C\u00033uÇS#\u00133s\u0085ÇS#\u0003#\u0013UÇS#cc\u0083OðÅÇS\u0013\u0093\u0093\u0083\u0015ÇS#\u0013SCuÇS3c\u0083\u0013eÇS3c\u0013S3\u0015ÇS3c#\u0013UÇS#\u0013S\u0013EÇS3c\u0013S3\u0015ÇS3c\u0083cUÇS3c\u00133UÇS3c\u0013S3\u0015ÇS3\u0093cC\u0005ÇS#\u0083#\u0003\u0015ÇS3c\u0013S5ÇS3\u0013Sc\u0015ÇS3c\u0013S5ÇS#\u0093\u0093\u0093##ÂöF\u0097cê          </div>¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÂöF\u0097cí¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003Æ'\"\u0002óê      </div>Ú    </div>Ú    <div class=\"row start\">Ú      <div class=\"term-num\">120\u0001</div>Ú      <div class=\"term-content\">Ú        <div class=\"fill row start\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&ÒÖ\u0097FVÒÖ&öÆB#ãÇ7\u0006\u0016â\u00066Æ\u001773Ò&&öÆB#åÇS3c\u0083\u0093\u0005ÇS3\u0003c\u0093?ñ£Â÷7\u0006\u0016ããÂöF\u0097cê          <div class=\"fill\">¢\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0002\u0003ÆF\u0097b\u00066Æ\u001773Ò'FW&Ò×&÷r#åÇS3\u0083S\u0003\u0005ÇS#cC\u0013%ÇS#\u0013S\u0013%ÇS#\u0013S\u0013eÇS#\u0003\u0003\u00135ÇS#c3suÇS#\u00933\u0003UÇ";
//        String html = "  <div class=\\\"clear row\\\">\\r\\n 编码转换测试文本  <div class=\\\"code-image\\\">\\n        <img src=\\\"https://ldcblob.blob.core.chinacloudapi.cn/navigator/prod/upload/LDC-LOGO.png\\\" alt=\\\"logo\\\">\\n      </div>\\r\\n         <div class=\\\"text-align bold term-content template-title\\\">\\n        <div>销售订单</div>\\n        <div class=\\\"sub-title\\\">（送货-一口价合同 -赊销）</div>\\n      </div>\\r\\n    </div>\\r\\n    <div class=\\\"row start clear\\\">\\n      <div class=\\\"term-content padding-em\\\">签约日期：2022年05月21日</div>\\n    </div> \\r\\n    <div class=\\\"row start clear\\\">\\n      <div class=\\\"term-content padding-em\\\">订单号：TJIBSBMS2203942</div>\\n    </div>\\r\\n    <br />\\r\\n    <div class=\\\"row start clear\\\">\\n      <div class=\\\"term-content padding-em\\\">本销售订单依据双方于2022年05月21日签订的豆粕销售框架合同（合同号为TJIBSBMS2203942）（“框架合同”）确立。</div>\\n      <br />\\n      <div class=\\\"term-content padding-em\\\">双方同意本销售订单的条款和条件如下：</div>\\n    </div>\\r\\n    <div class=\\\"row start\\\">\\r\\n      <div class=\\\"term-num\\\">1、</div>\\r\\n      <div class=\\\"term-content\\\">\\r\\n        <div class=\\\"fill row start\\\">\\n          <div class=\\\"term-item-bold\\\"><span class=\\\"bold\\\">货物：</span></div>\\n          <div class=\\\"fill\\\">\\n            <div class=\\\"term-row\\\">豆粕（以下称“货物”）</div>";
//        String unicode = toASCII(html);
//        System.out.println("unicode编码" + unicode);
//        String c16 = str2HexStr(html);
//        System.out.println("十六进制" + c16);
        String c16 = "3c212d2d2054503030325c7532363033325c7532353130345c7532303133325c7533353734365c7532313333335c7532373136395c753236343935202d2d3eda3c21444f43545950452068746d6c3eda3c68746d6c206c616e673d22656e223eda3c686561643eda20203c6d65746120636861727365743d225554462d38223eda20203c6d65746120687474702d65717569763d22582d55412d436f6d70617469626c652220636f6e74656e743d2249453d65646765223eda20203c6d657461206e616d653d2276696577706f72742220636f6e74656e743d2277696474683d6465766963652d77696474682c20696e697469616c2d7363616c653d312e30223eda20203c7469746c653e54503030325c7532363033325c7532353130345c7532303133325c7533353734365c7532313333335c7532373136395c7532393235363c2f7469746c653eda202020203c73";
        String hexCon = hexStr2Str(c16);
        System.out.println("十六进制解码：" + hexCon);
        System.out.println("Unicode解码" + unicodeToString(hexCon));
    }


}
