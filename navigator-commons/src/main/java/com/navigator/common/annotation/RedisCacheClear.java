package com.navigator.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2021-03-09 17:32
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RedisCacheClear {
    String name();

    /**
     * Specify the key by expression script, optional. If not specified,
     * use all parameters of the target method and keyConvertor to generate one.
     *
     * @return an expression script which specifies key
     */
    String key();

    /**
     *
     * Expression script used for conditioning the cache operation, the operation is vetoed when evaluation result is false.
     * Evaluation occurs after real method invocation so we can refer <code>#result</code> in script.
     */
    String condition() default "";
}

