package com.navigator.common.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ApproveInfo {
    //TODO NEO
    @AliasFor(annotation = Component.class)
    String description() default "";

    @AliasFor(annotation = Component.class)
    int index() default 0;
}
