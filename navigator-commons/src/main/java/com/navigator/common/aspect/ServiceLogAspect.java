package com.navigator.common.aspect;

import com.alibaba.fastjson.JSON;
import com.navigator.common.annotation.ServiceLog;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021年8月30日
 */
@Aspect
@Component
public class ServiceLogAspect {

    private final Logger logger = LoggerFactory.getLogger(ServiceLogAspect.class);

    @Around("@within(com.navigator.common.annotation.ServiceLog)||@annotation(com.navigator.common.annotation.ServiceLog)")
    public Object doServiceLog(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //
        Object target = joinPoint.getTarget();

        String methodName = signature.getName();

        Class[] parameterTypes = signature.getMethod().getParameterTypes();

        Method method = target.getClass().getMethod(methodName, parameterTypes);
        if (null == method) {
            return joinPoint.proceed();
        }
        if (!method.isAnnotationPresent(ServiceLog.class)) {
            return joinPoint.proceed();
        }
        String methodParams = getMethodParams(joinPoint);
        logger.info("开始请求方法:[{}] 参数:[{}]", methodName, methodParams);
        long start = System.currentTimeMillis();
        Object result = joinPoint.proceed();
        long end = System.currentTimeMillis();
        logger.info("结束请求方法:[{}] 参数:[{}] 返回结果:[{}] 耗时:[{}]毫秒", methodName, methodParams, result.toString(), end - start);
        return result;
    }

    private String getMethodParams(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        StringBuilder sb = new StringBuilder();
        if (args == null || args.length <= 0) {
            return sb.toString();
        }

        for (Object arg : args) {
            String paramStr = JSON.toJSONString(arg);
            sb.append(paramStr).append(",");
        }
        return sb.deleteCharAt(sb.length() - 1).toString();
    }
}
