package com.navigator.common.aspect;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CreateCache;
import com.navigator.common.annotation.RedisCacheClear;
import com.navigator.common.exception.BusinessException;
import io.lettuce.core.RedisClient;
import io.lettuce.core.RedisFuture;
import io.lettuce.core.api.StatefulRedisConnection;
import io.lettuce.core.api.async.RedisAsyncCommands;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2021-03-09 17:34
 */
@Slf4j
@Aspect
@Component
@ConditionalOnClass(Cache.class)
public class RedisCacheAspect {
    @CreateCache
    private Cache<Serializable, Serializable> cache;

    @Around(value = "@annotation(redisCacheClear)")
    public Object aroundAspect(ProceedingJoinPoint joinPoint, RedisCacheClear redisCacheClear) {
        Object result = null;
        String name = redisCacheClear.name();
        String key = redisCacheClear.key();
        String cacheName = name + key;
        try {
            RedisClient redisClient = this.cache.unwrap(RedisClient.class);
            StatefulRedisConnection<String, String> connect = redisClient.connect();
            RedisAsyncCommands<String, String> redisAsyncCommands = connect.async();
            RedisFuture<List<String>> keys = redisAsyncCommands.keys(cacheName);
            List<String> keyList = keys.get(10, TimeUnit.SECONDS);
            if (!CollectionUtils.isEmpty(keyList)) {
                keyList.forEach(redisAsyncCommands::del);
            }
            result = joinPoint.proceed();
        } catch (InterruptedException e) {
            log.error("缓存清除异常", e);
            // Restore interrupted state...
            Thread.currentThread().interrupt();
        } catch (Throwable throwable) {
            log.error("缓存清除异常", throwable);
            throw new BusinessException(throwable);
        }
        return result;
    }
}
