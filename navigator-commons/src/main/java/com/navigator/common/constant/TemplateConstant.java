package com.navigator.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-03 17:39
 */
public interface TemplateConstant {
    Integer DEFAULT_TEMPLATE_ID = 397;
    List<String> defaultDeliveryFactoryList = Arrays.asList("ZJG", "YZ", "ZS", "ZZY", "DG", "TJ", "TJIB");
    /**
     * 保证金使用规则描述（option1/option2）
     */
    String DePOSIT_USE_RULE_RATIO_OPTION2_SALE = "每次买方支付合同货款时，经卖方书面同意已支付的履约保证金可全部或部分用于冲抵相应货款，前提是冲抵货款后剩余的履约保证金金额不应低于根据相关约定应维持的履约保证金金额。";
    String DePOSIT_USE_RULE_RATIO_OPTION2_PURCHASE = "每次买方支付合同货款时，双方可协商一致将已支付的履约保证金全部或部分用于冲抵相应货款。";
    String DePOSIT_USE_RULE_LAST_OPTION1 = "履约保证金在最后一笔合同货款中充抵。";
    /**
     * 以上期限文本(交货截止日期 < 合同签约日期,拆分）
     */
    String ABOVE_DEADLINE_INFO = "以上期限不具有约束力，仅供参考。卖方应当应买方要求在合理期限内尽速交货，交货期限以合同实际执行为准。";

    String PRICE_DEADLINE_DATE = "买方须在#priceEndTime#前作价完毕，确认最终定价。";
    String PRICE_DEADLINE_TEXT = "买方须在提货后#priceEndTime#个自然日内作价完毕，确认最终定价。";

    String ALL_DESC = "全部";
    String ALL_CODE_SYMBOL = ",All,";
    String ALL_ID_SYMBOL = ",0,";

    /**
     * 履约保证金文本特殊处理（当日卖方基差报价${lytext!}元以上）
     * 豆油-销售-基差（付款条款变更，取300）
     * lytext
     */
    Integer SBO_SALE_DIFF_DEPOSIT_AMOUNT = 300;
    Integer SBM_DEPOSIT_AMOUNT = 100;
    // BUGFIX：Case-1003305-数字合同模板,品名模板触发条件提交报错 Author: nana 2025-06-24
    /**
     * SPU信息
     */
    String TEMPLATE_RULE_SPU = "spuName";
}
