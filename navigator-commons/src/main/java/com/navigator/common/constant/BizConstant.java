package com.navigator.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 业务常量
 * </p>
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
public interface BizConstant {
    /**
     * 普通用户转月次数
     */
    int NORMAL_CUSTOMER_TRANSFER_COUNT = 1;

    /**
     * 转月白名单次数
     */
    int VIP_CUSTOMER_TRANSFER_COUNT = 4;

    /**
     * 普通用户反点价次数
     */
    int NORMAL_CUSTOMER_REVERSE_COUNT = 0;

    /**
     * 反点价白名单次数
     */
    int VIP_CUSTOMER_REVERSE_COUNT = 1;

    String DEFAULT_BIZ_OPERATION_LOG_TEMPLATE = "T_DefaultBizOperationLog";

    String T_CONTRACT_COMMON_OP = "T_CONTRACT_COMMON_OP";

    String M_OPERATION_LOG = "M_OPERATION_LOG";

    String DEFAULT_INMAIL_MESSAGE_TEMPLATE = "T_DefaultInmailMessage";

    int DEFAULT_PAY_DAYS = 1;

    /**
     * 系统管理员的角色id
     */
    List<Integer> SYSTEM_ROLE_LIST = Arrays.asList(1, 2, 100, 101, 102, 103);
//    List<Integer> SYSTEM_ROLE_LIST = Arrays.asList(-1);

    String DROOLS_RULE_FOLDER = "rules/";
    String ALL_CONTRACT_RULES_FILE_NAME = "ALL_CONTRACT_RULES.drl";
}
