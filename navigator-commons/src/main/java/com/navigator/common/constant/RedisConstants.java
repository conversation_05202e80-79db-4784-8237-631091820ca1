package com.navigator.common.constant;

/**
 * <p>
 * Redis路径常量池
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-24
 */
public interface RedisConstants {
    /**
     * 豆粕销售合同当前最大编号
     */
    String MAX_NAV_SBM_SALES_CONTRACT_SEQUENCE = "sbm:sales:contract:sequence";

    /**
     * 豆粕采购合同当前最大编号
     */
    String MAX_NAV_SBM_PURCHASE_CONTRACT_SEQUENCE = "sbm:purchase:contract:sequence";

    /**
     * LKG豆粕销售合同当前最大编号
     */
    String MAX_LKG_SBM_SALES_CONTRACT_SEQUENCE = "lkg:sbm:sales:contract:sequence";

    /**
     * LKG豆粕采购合同当前最大编号
     */
    String MAX_LKG_SBM_PURCHASE_CONTRACT_SEQUENCE = "lkg:sbm:purchase:contract:sequence";

    /**
     * 拆分豆粕销售合同编号
     */
    String MODIFY_SBM_SALES_CONTRACT_CODE = "sbm:sales:contract:modify:code:";

    /**
     * 销售合同定价状态
     */
    String MODIFY_SBM_SALES_PRICE_STATUS = "sbm:sales:contract:modify:code:priceStatus:";

    /**
     * 拆分豆粕采购合同编号
     */
    String MODIFY_SBM_PURCHASE_CONTRACT_CODE = "sbm:purchase:contract:modify:code:";

    /**
     * 豆粕销售TT
     */
    String MAX_SBM_SALES_TT_CODE = "sbm:sales:TT:code";

    /**
     * 豆粕采购TT
     */
    String MAX_SBM_PURCHASE_TT_CODE = "sbm:purchase:TT:code";


    /** *********************豆油 *********************************/

    /**
     * 豆油销售合同当前最大编号
     */
    String MAX_NAV_SBO_SALES_CONTRACT_SEQUENCE = "sbo:sales:contract:sequence";

    /**
     * 豆油采购合同当前最大编号
     */
    String MAX_NAV_SBO_PURCHASE_CONTRACT_SEQUENCE = "sbo:purchase:contract:sequence";

    /**
     * lkg豆油销售合同当前最大编号
     */
    String MAX_LKG_SBO_SALES_CONTRACT_SEQUENCE = "lkg:sbo:sales:contract:sequence";

    /**
     * lkg豆油采购合同当前最大编号
     */
    String MAX_LKG_SBO_PURCHASE_CONTRACT_SEQUENCE = "lkg:sbo:purchase:contract:sequence";

    /**
     * 豆油销售TT
     */
    String MAX_SBO_SALES_TT_CODE = "sbo:sales:TT:code";

    /**
     * 豆油采购TT
     */
    String MAX_SBO_PURCHASE_TT_CODE = "sbo:purchase:TT:code";

    /**
     * 结构化定价合同编号
     */
    String MAX_STRUCTURE_CONTRACT_CODE = "structure:contract:code";

    static String getKey(String sequenceCode) {
        String name = "";
        switch (sequenceCode) {
            case "MAX_NAV_SBM_SALES_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_NAV_SBM_SALES_CONTRACT_SEQUENCE;
                break;
            case "MAX_NAV_SBM_PURCHASE_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_NAV_SBM_PURCHASE_CONTRACT_SEQUENCE;
                break;
            case "MAX_LKG_SBM_SALES_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_LKG_SBM_SALES_CONTRACT_SEQUENCE;
                break;
            case "MAX_LKG_SBM_PURCHASE_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_LKG_SBM_PURCHASE_CONTRACT_SEQUENCE;
                break;
            case "MAX_NAV_SBO_SALES_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_NAV_SBO_SALES_CONTRACT_SEQUENCE;
                break;
            case "MAX_NAV_SBO_PURCHASE_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_NAV_SBO_PURCHASE_CONTRACT_SEQUENCE;
                break;
            case "MAX_LKG_SBO_SALES_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_LKG_SBO_SALES_CONTRACT_SEQUENCE;
                break;
            case "MAX_LKG_SBO_PURCHASE_CONTRACT_SEQUENCE":
                name = RedisConstants.MAX_LKG_SBO_PURCHASE_CONTRACT_SEQUENCE;
                break;
        }
        return name;
    }

    /** *********************用户验证码 *********************************/
    /**
     * 注册验证码
     */
    String USER_REGISTER_CODE = "user_register_code";
    /**
     * 注册验证码有效期
     */
    int USER_CODE_TIME = 60 * 5;
    /**
     * 验证码发送
     */
    String USER_REGISTER_CODE_SEND = "user_register_code_send";
    /**
     * 验证码发送有效期
     */
    int USER_CODE_TIME_SEND = 60 * 1;
    /**
     * 注册验证码短信-模版ID
     */
    Integer SMS_REGISTER_CODE_TEMPLATE_ID = 2015;


    /**
     * 用户重置密码验证码
     */
    String C_USER_RESET_PASSWORD_CODE = "c_user_reset_password_code";
    /**
     * 用户重置密码验证码发送
     */
    String C_USER_RESET_PASSWORD_CODE_SEND = "c_user_reset_password_code_send";


    /**
     * 麦哲伦用户登录密码验证码
     */
    String M_USER_LOGIN_PASSWORD_CODE = "m_user_login_password_code";
    /**
     * 麦哲伦用户登录密码验证码发送
     */
    String M_USER_LOGIN_PASSWORD_CODE_SEND = "m_user_login_password_code_send";

    /**
     * 哥伦布用户登录密码验证码
     */
    String C_USER_LOGIN_PASSWORD_CODE = "c_user_login_password_code";
    /**
     * 哥伦布用户登录密码验证码发送
     */
    String C_USER_LOGIN_PASSWORD_CODE_SEND = "c_user_login_password_code_send";


    /**
     * 麦哲伦AAD登录密码验证码
     */
    String M_AAD_LOGIN_PASSWORD_CODE = "m_aad_login_password_code";
    /**
     * 麦哲伦AAD登录密码验证码发送
     */
    String M_AAD_LOGIN_PASSWORD_CODE_SEND = "m_aad_login_password_code_send";

    /**
     * 品类缓存
     */
    String IMPORT_EMPLOY_CACHE_CATEGORY = "import_employ_cache_category";
    /**
     * 工厂缓存
     */
    String IMPORT_EMPLOY_CACHE_FACTORY = "import_employ_cache_factory";
    /**
     * ********************商品
     *********************************/
    String GOODS_CATEGORY_INFO = "goods:category:info";

    String CONTRACT_CONFIRM_COMPLETE_CODE = "contract:confirm:complete:code:";

    String CONTRACT_CLOSE_TAIL_TIMES = "contract:closeTailTimes:";

    String CONTRACT_SAVE_TT_TIMES = "contract:saveTTTimes:";

    String CONTRACT_SAVE_TT_SUBMIT = "contract:saveTTSubmit:";

    String CONTRACT_SAVE_TT_SAVE = "contract:saveTTSave:";

    String CONTRACT_INVALID_TT_SAVE = "contract:invalidTTSave:";

    // BUGFIX：case-1003134 航海家为提货量为负数 Author: Mr 2025-04-17
    String CONTRACT_EXECUTE_UPDATE = "contract:executeUpdate:";

}
