package com.navigator.common.constant;

import java.util.Arrays;
import java.util.List;

public interface ContractSignConstant {

    String WHEN_ERROR_CONTRACT_TYPE = "未定类型合同";
    String WHEN_ERROR_SIGN_DATE = "合同约定的日期";
    String WHEN_ERROR_DELIVERY_MODE = "自提（默认）";
    String WHEN_ERROR_PAYMENT_TYPE = "预付（默认）";
    String WHEN_ERROR_DELIVERY_DATE = "合同约定的提货周期";
    String WHEN_ERROR_DEPOSIT_PAY_END_DATE = "合同签订后一天内";
    String WRITE_OFF_TIME = "仓单注销周期确实";
    String WHEN_ERROR_WEIGHT_CHECK = "默认重量验收标准";
    String WHEN_ERROR_PACKAGE_WEIGHT = "默认包装重量标准";
    String WHEN_OIL_PACKAGE_WEIGHT = "散装";
    String WHEN_ERROR_BANK_NAME = "开户行信息缺失";
    String WHEN_ERROR_BANK_ACCOUNT_NO = "银行账户信息缺失";
    String WHEN_ERROR_BANK_ACCOUNT_NAME = "收款开户名称缺失";

    String WHEN_ERROR_WARESHOUSE = "交货工厂信息缺失";
    String WHEN_ERROR_DELIVERY_ADDRESS = "库点交货地址缺失";
    String WHEN_ERROR_DELIVERY_POINT = "库点交货地点信息缺失";
    String WHEN_ERROR_PROTOCOL_NO = "无框架协议";
    String WHEN_ERROR_PROTOCOL_START_DATE = "无框架协议";
    List<String> DESTINATION_LKG_CODE_LIST = Arrays.asList("SBO00008", "SBM00002");

    List<Integer> HIGH_DEPOSIT_RATE_NORMAL = Arrays.asList(new Integer[]{5, 10, 15});
    Integer HIGH_DEPOSIT_RATE_5 = 5;

    Integer HIGH_DEPOSIT_RATE_7 = 7;

    Integer HIGH_DEPOSIT_RATE_10 = 10;

    Integer HIGH_DEPOSIT_RATE_15 = 15;

    Integer HIGH_DEPOSIT_RATE_20 = 20;


}
