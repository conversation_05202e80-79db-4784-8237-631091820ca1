package com.navigator.common.fallback;

import com.google.common.reflect.AbstractInvocationHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@AllArgsConstructor
public class FallbackInvocationHandler extends AbstractInvocationHandler {
    private Object target;
    private Throwable cause;

    @Override
    protected Object handleInvocation(Object proxy, Method method, Object[] args) throws Throwable {
        Parameter[] parameters = method.getParameters();
        String parameterContent = IntStream.range(0, parameters.length)
                .boxed()
                .map(index -> parameters[index].getName().concat("=").concat(String.valueOf(args[index])))
                .collect(Collectors.joining(","));
        log.error("{} fallback >>> {}", method.getName(), parameterContent, this.cause);
        return method.invoke(this.target, args);
    }
}
