package com.navigator.common.exception;

import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BusinessException.class)
    public Result businessException(BusinessException e) {
        log.error("业务异常:{}", e.getMessage(), e);
        int code = e.getCode() == 0 ? ResultCodeEnum.BIZ_ERROR.getCode() : e.getCode();
        return Result.failure(code, e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MyActivityException.class)
    public Result activityException(MyActivityException e) {
        log.error("工作流异常:{}", e.getMessage(), e);
        // e.getMessage  -- > 系统处理异常，请联系系统管理员
        return Result.failure(ResultCodeEnum.SYSTEM_FLOW_FAILURE, e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(RuntimeException.class)
    public Result runtimeException(RuntimeException e) {
        log.error("系统运行异常:{}", e.getMessage(), e);
        // e.getMessage  -- > 系统运行异常，请联系系统管理员
        return Result.failure(ResultCodeEnum.SYSTEM_RUNING_FAILURE,  e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(Exception.class)
    public Result exception(Exception e) {
        log.error("系统异常:{}", e.getMessage(), e);
        // e.getMessage  -- > 系统处理异常，请联系系统管理员
        return Result.failure(ResultCodeEnum.SYSTEM_DEAL_FAILURE, e.getMessage());
    }

    @ExceptionHandler(value = {MethodArgumentNotValidException.class})
    public Result handleVaildException(MethodArgumentNotValidException e) {
        log.error("数据校验出现问题{}，异常类型：{}", e.getMessage(), e.getClass());
        BindingResult bindingResult = e.getBindingResult();

        Map<String, String> errorMap = new HashMap<>();
        bindingResult.getFieldErrors().forEach((fieldError) -> {
            // 错误字段 、 错误提示
            errorMap.put(fieldError.getField(), fieldError.getDefaultMessage());
        });
        Result<Object> errorResult = new Result<>();
        errorResult.setData(errorMap);
        errorResult.setCode(ResultCodeEnum.PARAMS_MISS.getCode());
        FieldError fieldError = bindingResult.getFieldError();
        if (fieldError != null)
            errorResult.setMessage(fieldError.getDefaultMessage());
        return errorResult;
    }

    // BUGFIX：case-1003183 生产环境中抛了大量异常， 严重影响troubleshoot Author: Mr 2025-04-29 Start

    /**
     * 处理缺少必需参数的异常
     *
     * @param e       异常对象
     * @param request HttpServletRequest 对象
     * @return Result 对象
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public Result handleMissingServletRequestParameterException(MissingServletRequestParameterException e, HttpServletRequest request) {
        String paramName = e.getParameterName();
        String requestURI = request.getRequestURI();

        // 获取Controller方法信息
        String handlerInfo = "未知";
        Object handler = request.getAttribute(HandlerMapping.BEST_MATCHING_HANDLER_ATTRIBUTE);
        if (handler instanceof HandlerMethod) {
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            String controllerName = handlerMethod.getBeanType().getSimpleName();
            String methodName = handlerMethod.getMethod().getName();
            handlerInfo = controllerName + "#" + methodName;
        }

        // 使用WARN级别而不是ERROR，因为这是客户端错误而不是服务器错误
        log.warn("请求缺少必需参数: {}, 接口路径: {}, Controller方法: {}", paramName, requestURI, handlerInfo);
        return Result.failure(ResultCodeEnum.PARAMS_MISS.getCode(),
                String.format("接口 [%s] 缺少必需参数: %s", requestURI, paramName));
    }
    // BUGFIX：case-1003183 生产环境中抛了大量异常， 严重影响troubleshoot Author: Mr 2025-04-29 End
}
