package com.navigator.common.exception;

import com.navigator.common.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class MyActivityException extends RuntimeException {
    private int code;
    private String msg;

    public MyActivityException(ResultCodeEnum resultCode) {
        // 调用getMessage()方法时返回
        super(resultCode.getMsg());

        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
    }

    public MyActivityException(ResultCodeEnum resultCode, String addMsg) {
        // 调用getMessage()方法时返回
        super(resultCode.getMsg());

        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg() + ":" + addMsg;
    }

    public MyActivityException(Throwable cause) {
        super(cause);
    }

    public MyActivityException(String message) {
        super(message);
    }
}
