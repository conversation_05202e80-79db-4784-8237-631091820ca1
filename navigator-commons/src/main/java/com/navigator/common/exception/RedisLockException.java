package com.navigator.common.exception;

import com.navigator.common.enums.ErrorCodeEnum;
import com.navigator.common.enums.ResultCodeEnum;

public class RedisLockException extends BusinessException {

    public RedisLockException() {
        super();
    }

    public RedisLockException(Throwable cause) {
        super(cause);
    }

    public RedisLockException(ResultCodeEnum resultCodeEnum) {
        super(resultCodeEnum.getCode(), resultCodeEnum.getMsg());
    }

    public RedisLockException(String message) {
        super(message);
    }

    public RedisLockException(String message, Throwable cause) {
        super(message, cause);
    }

    public RedisLockException(int code, String message) {
        super(code, message);
    }

    public RedisLockException(int code, String msgFormat, Object... args) {
        super(code, msgFormat, args);
    }

    public RedisLockException(ErrorCodeEnum codeEnum, Object... args) {
        super(codeEnum, args);
    }
}
