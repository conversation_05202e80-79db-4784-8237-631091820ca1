package com.navigator.common.exception;

import com.navigator.common.enums.ErrorCodeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;


@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessException extends RuntimeException {
    private int code;
    private String msg;

    public BusinessException() {
    }

    public BusinessException(ResultCodeEnum resultCode) {
        // 调用getMessage()方法时返回
        super(resultCode.getMsg());

        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg();
    }

    public BusinessException(ErrorCodeEnum errorCode) {
        // 调用getMessage()方法时返回
        super(errorCode.msg());

        this.code = errorCode.code();
        this.msg = errorCode.msg();
    }

    public BusinessException(Integer code, String msg) {
        // 调用getMessage()方法时返回
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    public BusinessException(ResultCodeEnum resultCode, String addMsg) {
        // 调用getMessage()方法时返回
        super(resultCode.getMsg() + ":" + addMsg);

        this.code = resultCode.getCode();
        this.msg = resultCode.getMsg() + ":" + addMsg;
    }

    public BusinessException(Throwable cause) {
        super(cause);
    }

    public BusinessException(String message) {
        super(message);
    }

    public BusinessException(String message, Throwable cause) {
        super(message, cause);
    }

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(int code, String msgFormat, Object... args) {
        super(String.format(msgFormat, args));
        this.code = code;
    }

    public BusinessException(ErrorCodeEnum codeEnum, Object... args) {
        super(String.format(codeEnum.msg(), args));
        this.code = codeEnum.code();
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
