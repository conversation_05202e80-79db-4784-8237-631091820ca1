package com.navigator.common.exception;

import com.navigator.common.enums.ErrorCodeEnum;
import com.navigator.common.enums.ResultCodeEnum;

public class PermissionException extends BusinessException {

    public PermissionException() {
        super();
    }

    public PermissionException(Throwable cause) {
        super(cause);
    }

    public PermissionException(ResultCodeEnum resultCodeEnum) {
        super(resultCodeEnum.getCode(), resultCodeEnum.getMsg());
    }

    public PermissionException(String message) {
        super(message);
    }

    public PermissionException(String message, Throwable cause) {
        super(message, cause);
    }

    public PermissionException(int code, String message) {
        super(code, message);
    }

    public PermissionException(int code, String msgFormat, Object... args) {
        super(code, msgFormat, args);
    }

    public PermissionException(ErrorCodeEnum codeEnum, Object... args) {
        super(codeEnum, args);
    }
}
