package com.navigator.common.exception;

import cn.hutool.json.JSONUtil;
import com.microsoft.azure.storage.StorageException;
import com.navigator.common.dto.ExceptionInfoDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ErrorCodeEnum;
import com.navigator.common.enums.ResultCodeEnum;
import io.jsonwebtoken.ExpiredJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 全局的的异常拦截器
 */
@Slf4j
public abstract class BaseGlobalExceptionHandler {

    /**
     * 业务异常.
     *
     * @param e the e
     * @return the wrapper
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BusinessException.class)
    public Result businessException(BusinessException e) {
        ExceptionInfoDTO exceptionInfo = JSONUtil.toBean(e.getMessage(), ExceptionInfoDTO.class);
        return Result.failure(exceptionInfo.getStatus(), exceptionInfo.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(BusinessException.class)
    public Result storageException(StorageException e) {
        ExceptionInfoDTO exceptionInfo = JSONUtil.toBean(e.getMessage(), ExceptionInfoDTO.class);
        return Result.failure(exceptionInfo.getStatus(), exceptionInfo.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MyActivityException.class)
    public Result activityException(MyActivityException e) {
        log.error("工作流异常:{}", e.getMessage(), e);
        return Result.failure(e.getCode(), e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(RedisLockException.class)
    public Result redisLockException(RedisLockException e) {
        ExceptionInfoDTO exceptionInfo = JSONUtil.toBean(e.getMessage(), ExceptionInfoDTO.class);
        return Result.failure(exceptionInfo.getStatus(), exceptionInfo.getMessage());
    }

    /**
     * 参数非法异常.
     *
     * @param e the e
     * @return the Result
     */
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(IllegalArgumentException.class)
    public Result illegalArgumentException(IllegalArgumentException e) {
        log.error("参数非法异常={}", e.getMessage(), e);
        return Result.failure(ErrorCodeEnum.**********);
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(ExpiredJwtException.class)
    public Result expiredJwtException(ExpiredJwtException e) {
        log.error("认证异常={}", e.getMessage(), e);
        return Result.failure(ResultCodeEnum.USER_LOGGED_IN_FAILURE, e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(PermissionException.class)
    public Result permissionException(PermissionException e) {
        log.error("权限异常={}", e.getMessage(), e);
        return Result.failure(e.getCode(), e.getMessage());
    }

    /**
     * 全局异常.
     *
     * @param e the e
     * @return the wrapper
     */
    @ResponseBody
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result exception(Exception e) {
        log.error("全局异常={}", e.getMessage(), e);
        return Result.failure(e.getMessage());
    }

    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result methodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error("参数异常={}", e.getMessage(), e);
        FieldError fieldError = e.getBindingResult().getFieldError();
        if(fieldError != null)
            return Result.failure(fieldError.getDefaultMessage());
        else
            return Result.failure();
    }
}
