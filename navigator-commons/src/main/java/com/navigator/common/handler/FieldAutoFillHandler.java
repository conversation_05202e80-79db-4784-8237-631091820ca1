package com.navigator.common.handler;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.IsDeletedEnum;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * todo 统一将Date -> LocalDateTime
 * 实体字段自动填充处理类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class FieldAutoFillHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Date now = new Date();
        if (this.getFieldValByName("createdAt", metaObject) == null) {
            this.setInsertFieldValByName("createdAt", now, metaObject);
        }
        if (this.getFieldValByName("updatedAt", metaObject) == null) {
            this.setInsertFieldValByName("updatedAt", now, metaObject);
        }
        if (this.getFieldValByName("isDeleted", metaObject) == null) {
            this.setInsertFieldValByName("isDeleted", IsDeletedEnum.NOT_DELETED.getValue(), metaObject);
        }
        if (this.getFieldValByName("status", metaObject) == null) {
            this.setInsertFieldValByName("status", DisableStatusEnum.ENABLE.getValue(), metaObject);
        }
        if (this.getFieldValByName("version", metaObject) == null) {
            this.setInsertFieldValByName("version", 0, metaObject);
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Date now = DateTimeUtil.now();
        try {
            this.setUpdateFieldValByName("updatedAt", now, metaObject);
        } catch (Exception e) {
            log.error("MetaObject update updatedAt Exception", e);
        }
    }
}
