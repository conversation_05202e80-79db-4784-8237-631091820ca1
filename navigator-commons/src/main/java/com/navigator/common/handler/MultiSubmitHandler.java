package com.navigator.common.handler;

import com.navigator.common.annotation.MultiSubmit;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 使用redis分布式锁(setnx)解决
 * hash值 对象实例相同，默认的hash值相等(配合lombok的@Data对hashcode的重写)，通过hash值作为锁值校验入参(不要随便重写request hash()方法)
 * hash值超时时间（默认 5 s）
 */
@Aspect
@Component
@Slf4j
public class MultiSubmitHandler {

    @Autowired
    private RedisUtil redisUtil;

    @Pointcut("@annotation(com.navigator.common.annotation.MultiSubmit)")
    public void pointcut() {
    }

    /**
     * 防止重复提交的请求,请求之前的逻辑
     * 对 用户id + 请求参数的hashcode 加锁
     *
     * @param joinPoint 获取请求Request
     */
    @Before(value = "pointcut() && @annotation(multiSubmit)")
    public void before(JoinPoint joinPoint, MultiSubmit multiSubmit) {
        long start = System.currentTimeMillis();
        Object[] objects = joinPoint.getArgs();

        if (objects.length == 1) {
            String key = "REPEAT_LOCK:" + objects[0].hashCode();
            if (redisUtil.setNx(key, multiSubmit.timeout())) {
                log.info("submitting repeat check time : " + (System.currentTimeMillis() - start) + "ms, key:" + key);
            } else {
                log.error("submitting repeat: " + joinPoint.toLongString());
                throw new BusinessException(ResultCodeEnum.REPEATED_SUBMIT);
            }
        }
    }
}

