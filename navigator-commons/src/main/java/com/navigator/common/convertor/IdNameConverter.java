package com.navigator.common.convertor;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.navigator.common.redis.RedisUtil;
import com.navigator.common.util.SpringContextUtil;
import com.navigator.common.util.StringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 结果数据转换器
 *
 * @Author: lincl
 * @Date: 2024/7/23 16:11
 */
@Slf4j
public class IdNameConverter {

    /**
     * 最多缓存多少
     */
    private final static int MAX_SIZE = 10000;

    /**
     * 本地缓存
     */
    private static Map<IdNameType, Map<String, String>> CACHE_MAP = new ConcurrentHashMap<>(100);

    /**
     * 是否启用本地缓存
     */
    private static boolean LOCAL_CACHE = false;

    /**
     * redis
     */
    private static RedisUtil redisUtil;

    /**
     * 初始化
     */
    public static void init() {
        // 初始化获取bean
        if (redisUtil == null) {
            redisUtil = SpringContextUtil.getBean(RedisUtil.class);
        }
    }

    /**
     * 获取缓存map
     *
     * @param idNameType
     * @return
     */
    public static Map<String, String> getCacheMap(IdNameType idNameType) {
        // 本地缓存
        Map<String, String> map;
        if (LOCAL_CACHE) {
            map = CACHE_MAP.get(idNameType);
            if (map == null) {
                map = new ConcurrentHashMap<>(1024);
                CACHE_MAP.put(idNameType, map);
            }
        } else {
            map = new HashMap<>(16);
        }
        return map;
    }

    /**
     * 获取名称
     *
     * @param idNameType
     * @param id
     * @return
     */
    public static String getName(IdNameType idNameType, String id) {
        return IdNameConverter.getName(idNameType, StrUtil.split(id, ','));
    }

    /**
     * 获取名称
     *
     * @param idNameType
     * @param idList
     * @return
     */
    @SneakyThrows
    public static String getName(IdNameType idNameType, List<String> idList) {
        // 初始化
        init();
        // 转换结果
        List<String> result = new ArrayList<>(idList.size());
        // 本地缓存
        Map<String, String> map = getCacheMap(idNameType);
        // 查找数据
        for (String id : idList) {
            String name = map.get(id);
            if (StrUtil.isBlank(name)) {
                name = redisUtil.getItemOfMap(idNameType.getCachePrefix(), id);
                if (StrUtil.isNotBlank(name)) {
                    // 超出容量，同时为了避免数据过时，直接清理集合
                    if (map.size() >= MAX_SIZE) {
                        map.clear();
                    }
                    map.put(id, name);
                }
            }
            if (StrUtil.isBlank(name)) {
                name = id;
            }
            result.add(name);
        }
        return StrUtil.join(",", result);
    }

    /**
     * 更新缓存
     *
     * @param idNameType 数据类型
     * @param list       数据集合
     */
    public static void setName(IdNameType idNameType, List<Tree<Integer>> list) {
        // 初始化
        init();
        // 本地缓存
        Map<String, String> map = getCacheMap(idNameType);
        for (Tree item : list) {
            if (item.getId() != null && item.getName() != null) {
                map.put(item.getId().toString(), item.getName().toString());
            }
        }
        // 更新缓存
        redisUtil.delete(idNameType.getCachePrefix());
        redisUtil.setMap(idNameType.getCachePrefix(), map);
        log.info("“ {} ” 缓存设置成功！共 {} 个数据！", idNameType.getType(), list.size());
    }

    /**
     * 更新名称
     *
     * @param idNameType 数据类型
     * @param id         id
     * @param name       名称
     */
    public static void setName(IdNameType idNameType, String id, String name) {
        IdNameConverter.setName(idNameType, id, name, true);
    }

    /**
     * 更新名称
     *
     * @param idNameType 数据类型
     * @param id         id
     * @param name       名称
     * @param publish    是否发布
     */
    public static void setName(IdNameType idNameType, String id, String name, boolean publish) {
        if (StrUtil.isBlank(name)) {
            return;
        }
        // 初始化
        init();
        // 本地缓存
        Map<String, String> map = getCacheMap(idNameType);
        map.put(id, name);
        // 更新缓存
        redisUtil.setItemOfMap(idNameType.getCachePrefix(), id, name);
        // 发布
//        if (publish) {
//            IdName idName = new IdName();
//            idName.setDataType(dataType);
//            idName.setId(id);
//            idName.setName(name);
//            RabbitMsgDTO rabbitMsgDTO = new RabbitMsgDTO(IdUtil.sortUUID(), RabbitMsgDTO.Type.ID_NAME, idName);
//            RabbitUtil.publishMsg(rabbitMsgDTO);
//        }
    }

    /**
     * 删除名称
     *
     * @param idNameType 数据类型
     * @param ids        ids
     */
    public static void delName(IdNameType idNameType, String... ids) {
        // 初始化
        init();
        redisUtil.delItemOfMap(idNameType.getCachePrefix(), Arrays.asList(ids));

    }

    /**
     * 单个对象转换
     *
     * @param idNameType 数据类型
     * @param obj        数据对象
     * @param <T>
     * @return
     */
    public static <T> T toName(IdNameType idNameType, T obj) {
        return IdNameConverter.toName(idNameType, obj, null);
    }

    /**
     * 集合转换
     *
     * @param list 数据集合
     * @param <T>
     * @return
     */
    public static <T> List<T> toName(IdNameType idNameType, List<T> list) {
        return IdNameConverter.toName(idNameType, list, null);
    }

    /**
     * 分页对象转换
     *
     * @param page
     * @return
     */
    public static IPage toName(IdNameType idNameType, IPage page) {
        return IdNameConverter.toName(idNameType, page, null);
    }

    /**
     * 单个对象转换
     *
     * @param idNameType 数据类型
     * @param obj        数据对象
     * @param fieldNames 转换字段名称
     * @param <T>
     * @return
     */
    public static <T> T toName(IdNameType idNameType, T obj, String[] fieldNames) {
        if (obj == null) {
            return null;
        }

        // 默认转换字段
        if (StringUtil.isEmpty(fieldNames)) {
            fieldNames = idNameType.getFieldNames();
        }

        if (StringUtil.isNotEmpty(fieldNames)) {
            for (int i = 0; i < fieldNames.length; i++) {
                String fieldName = fieldNames[i];
                String labelField;
                // 存储名称的字段名
                if (fieldName.endsWith("Id") || fieldName.endsWith("Ids")) {
                    labelField = fieldName.replace("Ids", "Names").replace("Id", "Name");
                } else {
                    labelField = fieldName + "Name";
                }
                try {
                    //Map对象
                    if (obj instanceof Map) {
                        if (((Map) obj).get(fieldName) != null) {
                            String userId = ((Map) obj).get(fieldName) + "";
                            String nickName = IdNameConverter.getName(idNameType, userId);
                            ((Map) obj).put(labelField, nickName);
                        }
                    } else {
                        Class clz = obj.getClass();
                        if (ReflectUtil.hasField(clz, fieldName)) {
                            Object fieldValue = ReflectUtil.getFieldValue(obj, fieldName);
                            String id = null;
                            if (fieldValue instanceof List || fieldValue instanceof Set) {
                                id = StrUtil.join(",", fieldValue);
                            } else {
                                id = String.valueOf(fieldValue);
                            }
                            if (StrUtil.isNotBlank(id)) {
                                String nickName = IdNameConverter.getName(idNameType, id);
                                if (ReflectUtil.hasField(clz, labelField)) {
                                    // 定义了额外的字段来存储名称
                                    ReflectUtil.setFieldValue(obj, labelField, nickName);
                                } else {
                                    ReflectUtil.setFieldValue(obj, fieldName, nickName);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return obj;
    }

    /**
     * 集合转换
     *
     * @param list       数据集合
     * @param fieldNames 字段名称
     * @param <T>
     * @return
     */
    public static <T> List<T> toName(IdNameType idNameType, List<T> list, String[] fieldNames) {
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                IdNameConverter.toName(idNameType, list.get(i), fieldNames);
            }
        }
        return list;
    }

    /**
     * 分页对象转换
     *
     * @param page
     * @param fieldNames 字段名称
     * @return
     */
    public static IPage toName(IdNameType idNameType, IPage page, String[] fieldNames) {
        if (page.getRecords() != null && page.getRecords().size() > 0) {
            IdNameConverter.toName(idNameType, page.getRecords(), fieldNames);
        }
        return page;
    }

    /**
     * 设置缓存
     *
     * @param idNameType
     * @param list
     * @param <T>
     */
    public static <T> void setCache(IdNameType idNameType, List<T> list) {
        IdNameConverter.setCache(idNameType, list, "id", "name");
    }

    /**
     * 设置缓存
     *
     * @param idNameType
     * @param list
     * @param idFieldName
     * @param nameFieldName
     * @param <T>
     */
    public static <T> void setCache(IdNameType idNameType, List<T> list, String idFieldName, String nameFieldName) {
        if (StringUtil.isBlank(idFieldName)) {
            idFieldName = "id";
        }
        if (StringUtil.isBlank(nameFieldName)) {
            idFieldName = "name";
        }
        List<Tree<Integer>> result = new ArrayList<>();
        for (T item : list) {
            Integer id = (Integer) ReflectUtil.getFieldValue(item, idFieldName);
            String name = String.valueOf(ReflectUtil.getFieldValue(item, nameFieldName));
            result.add(new Tree().setId(id).setName(name));
        }
        IdNameConverter.setName(idNameType, result);
    }
}
