package com.navigator.common.convertor;

/**
 * 数据类型枚举类
 *
 * <AUTHOR>
 */
public enum IdNameType {

    /**
     * 用户：ID-昵称
     */
    user_id_name("用户：ID-昵称", "createdBy", "updatedBy", "uploadBy", "auditBy"),
    /**
     * 品类：ID-名称
     */
    category_id_name("品类：ID-名称", "categoryId"),
    /**
     * 品类：ID-名称
     */
    category_id_serialNo("品类：ID-主编码", "serialNo"),
    /**
     * 品类：主编码-名称
     */
    category_serialNo_name("品类：主编码-名称", "category1", "category2", "category3"),
    /**
     * 规格：ID-名称
     */
    attribute_id_name("规格：ID-名称", "attributeId"),
    /**
     * 规格值：ID-名称
     */
    attributeValue_id_name("规格值：ID-名称", "attributeValueId"),
    /**
     * SPU：ID-名称
     */
    spu_id_name("SPU：ID-名称", "spuId"),
    /**
     * SKU：ID-名称
     */
    sku_id_name("SKU：ID-名称", "skuId", "goodsId"),
    ;

    /**
     * 类型
     */
    private String type;

    /**
     * 默认转换字段
     */
    private String[] fieldNames;

    IdNameType(String type, String... fieldNames) {
        this.type = type;
        this.fieldNames = fieldNames;
    }

    /**
     * 获取数据类型
     *
     * @return
     */
    public String getType() {
        return type;
    }

    /**
     * 获取默认转换字段
     *
     * @return
     */
    public String[] getFieldNames() {
        return fieldNames;
    }

    /**
     * 获取缓存前缀
     *
     * @return
     */
    public String getCachePrefix() {
        return "ID_NAME:" + this.name();
    }
}
