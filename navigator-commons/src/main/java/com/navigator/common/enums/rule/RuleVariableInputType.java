package com.navigator.common.enums.rule;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-15 16:34
 **/
@AllArgsConstructor
@Getter
public enum RuleVariableInputType {
    SELECT("select", "选择框"),
    INPUT("input", "输入框"),
    BOOLEAN("boolean", "布尔值（是否）"),
    ;
    private String value;
    private String desc;

    public static RuleVariableInputType getByValue(String value) {
        return Arrays.stream(values())
                .filter(variableInputType -> value.equals(variableInputType.getValue()))
                .findFirst()
                .orElse(SELECT);
    }
}
