package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/16
 */

@Getter
@AllArgsConstructor
public enum ExchangeEnum {
    /**
     * 仓单交易所
     */

    DCE("DCE", "大连商品交易所"),
    ZCE("ZCE", "郑州商品交易所"),
    ;
    private String value;
    private String desc;


    public static ExchangeEnum getByValue(String value) {
        for (ExchangeEnum exchangeEnum : ExchangeEnum.values()) {
            if (exchangeEnum.getValue().equals(value)) {
                return exchangeEnum;
            }
        }
        return ExchangeEnum.DCE;
    }

    public static String getDescByValue(String value) {
        for (ExchangeEnum exchangeEnum : ExchangeEnum.values()) {
            if (exchangeEnum.getValue().equals(value)) {
                return exchangeEnum.getDesc();
            }
        }
        return ExchangeEnum.DCE.getDesc();
    }
}
