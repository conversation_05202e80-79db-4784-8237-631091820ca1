package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum IsDeletedEnum {
    /**
     * 是否已删除
     */
    NOT_DELETED(0, "未删除"),
    DELETED(1, "删除");

    private Integer value;
    private String desc;

    public static IsDeletedEnum getByValue(Integer code) {
        return Arrays.stream(values())
                .filter(deletedEnum -> Objects.equals(code, deletedEnum.value))
                .findFirst()
                .orElse(NOT_DELETED);
    }
}
