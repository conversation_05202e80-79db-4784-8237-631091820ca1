package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2024-08-16 11:05
 **/
@AllArgsConstructor
@Getter
public enum SyncSystemEnum {
    /**
     * 账套同步的系统
     */
    LINKINAGE("LKG", 1, "Linkinage系统"),

    ATLAS("ATLAS", 2, "Atlas系统");

    String value;
    Integer code;
    String desc;

    public static SyncSystemEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(syncSystemEnum -> value.equals(syncSystemEnum.getValue()))
                .findFirst()
                .orElse(LINKINAGE);
    }

    public static SyncSystemEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(syncSystemEnum -> code.equals(syncSystemEnum.getCode()))
                .findFirst()
                .orElse(LINKINAGE);
    }

    public static String getDescByValue(String value) {
        return getByValue(value).getDesc();
    }

    public static String getDescByCode(Integer code) {
        return getByCode(code).getDesc();
    }


}
