package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RuleModuleTypeEnum {
    /**
     * 模块状态
     */
    DEFAULT("default", "默认"),
    LOA_APPROVAL("LOA_APPROVAL", "LOA审批"),
    CONTRACT_SIGN("CONTRACT_SIGN", "协议出具"),

    ;

    private String module;
    private String desc;

    public static RuleModuleTypeEnum getModule(String module) {
        return Arrays.stream(values())
                .filter(moduleType -> StringUtils.equals(module, moduleType.getModule()))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static String getDescByValue(String bizModule) {
        return getModule(bizModule).getDesc();
    }
}
