package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum DisableStatusEnum {
    /**
     * 有效状态
     */
    ENABLE(1, "启用"),
    DISABLE(0, "禁用");

    private Integer value;
    private String desc;

    public static DisableStatusEnum valueOf(Integer value) {
        return Arrays.stream(values())
                .filter(statusEnum -> Objects.equals(value, statusEnum.value))
                .findFirst()
                .orElse(ENABLE);
    }

    public static String getDescByValue(Integer value) {
        return valueOf(value).getDesc();
    }

    public static BasicOperateEnum getOperateByStatus(Integer status) {
        return ENABLE.getValue().equals(status) ? BasicOperateEnum.UPDATE_STATUS_ON : BasicOperateEnum.UPDATE_STATUS_OFF;
    }

}
