package com.navigator.common.enums.rule;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-11 18:42
 **/
@AllArgsConstructor
@Getter
public enum RuleVariableBizCodeEnum {
    /**
     * 规则条件变量动态取值
     */
    CATEGORY1("category1", "一级品类"),
    CATEGORY2("category2", "二级品类"),
    CATEGORY3("category3", "三级品种"),
    CATEGORY_ID("categoryId", "二级品种"),
    DELIVERY_FACTORY_CODE("deliveryFactoryCode", "交货工厂编码"),
    SPEC_ID("specId", "货品规格"),
    STRUCTURE_TYPE("structureType", "结构化定价类型"),

    //================== 质量指标规则 ====================
    WAREHOUSE("warehouse", "发货库点"),
    CUSTOMER("customer", "专属客户"),
    ENTERPRISE("enterprise", "集团客户"),
    COMPANY_CODE("companyCode", "公司主体"),
    ;
    private String value;
    private String desc;

    public static RuleVariableBizCodeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(ruleVariableBizCodeEnum -> value.equals(ruleVariableBizCodeEnum.getValue()))
                .findFirst()
                .orElse(DELIVERY_FACTORY_CODE);
    }
}
