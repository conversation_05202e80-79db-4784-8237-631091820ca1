package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum ResultCodeEnum {
    /**
     * 返回值枚举
     */

    OK(0, "处理成功"),
    WEBSOCKET_SUCCESS(2, "websocket连接成功"),
    /**
     * 系统异常
     */
    FAILURE(1, "系统异常"),
    SYSTEM_FLOW_FAILURE(408, "工作流异常，请联系系统管理员!"),
    SYSTEM_RUNING_FAILURE(505, "系统运行异常，请联系系统管理员!"),
    SYSTEM_DEAL_FAILURE(403, "系统处理异常，请联系系统管理员"),
    // http状态
    UNAUTHORIZED(401, "Unauthorized-未授权"),
    SYSTEM_INNER_ERROR(403, "系统繁忙，请稍后重试"),
    SYSTEM_NO_MESSAGE_ERROR(404, "No message available"),

    PARAM_ERROR(502, "参数非法"),
    FAILED_DEL_OWN(503, "不能删除自己"),
    FAILED_USER_ALREADY_EXIST(504, "该用户已存在"),
    INTERNAL_SERVER_ERROR(505, "服务器内部错误"),
    BIZ_ERROR(506, "业务异常"),
    RECORD_NOT_EXIST(507, "记录不存在"),
    REMOTE_SERVICE_ERROR(508, "远程服务调用失败"),
    RESULT_HTTP_CODE_ERROR(509, "HTTP响应码错误!"),
    RESULT_JSON_ERROR(510, "json解析错误!"),
    RESULT_IO_ERROR(511, "网络IO错误!"),
    REPEATED_SUBMIT(512, "禁止重复提交,请稍等片刻!"),

    WECHAT_USER_PERMISSION(48001, "该公众号没有获取用户信息权限！"),

    PARAMS_MISS(50001, "缺少接口中必填参数"),
    RECORD_REPEAT(50002, "重复数据"),

    /**
     * 用户模块错误：20001-29999
     */
    //用户登录
    USER_NOT_LOGGED_IN(100001, "用户登录失效"),
    USER_FORBIDDEN(100001, "用户被禁用"),
    USER_LOGIN_ERROR(100002, "账号不存在或密码错误"),
    USER_HAS_EXISTED(100003, "用户已存在"),
    USER_NOT_REGISTER(100004, "用户未注册"),
    USER_LOGGED_IN_FAILURE(100005, "用户登录失效,请重新登录"),
    USER_AUTH_FAILURE(100006, "认证失败"),
    USER_NOT_EXIST(100007, "用户不存在"),
    USER_USERNAME_EXIST(100008, "用户名已存在"),
    USER_PASSWORD_ERROR(100009, "用户密码错误"),
    USER_PASSWORD_INITIAL(100010, "不能使用初始密码"),
    USER_PASSWORD_SAME(100011, "密码相同"),
    USER_PHONE_EXIST(100012, "手机号已存在"),
    USER_LOGGED_FAILURE(100005, "用户验证失败"),
    // 用户管理
    USER_CREATE_FAILED(100011, "用户新增失败!"),
    USER_UPDATE_FAILED(100012, "用户修改失败!"),
    USER_DELETE_FAILED(100013, "用户删除失败!"),
    FORCED_CHANGE_PASSWORD(100014, "请修改密码"),
    PLEASE_CHANGE_PASSWORD(100015, "建议您修改密码"),
    USER_PASSWORD_EXPIRED(1000016, "密码过期,请修改密码"),
    USER_NOT_ADMIN(1000017, "拥有系统管理员角色才可进行该操作"),
    USER_MAIN_ADMIN(1000018, "此用户为主管理员，不可取消"),
    //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 start
    USER_CUSTOMER_FORBIDDEN(1000019, "该主体已被禁用，请重新登录或联系管理员"),
    //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 end
    //用户权限
    PERMISSION_NO_ACCESS(100101, "无访问权限"),
    //部门异常信息
    DEPT_NAME_NOT_EXIST(100201, "部门名称不能为空"),
    DEPT_NOT_FIND(100202, "部门不存在"),
    DEPT_UPDATE_FAILED(100203, "部门修改失败"),
    DEPT_CREATE_FAILED(100204, "部门创建失败"),
    DEPT_DELETE_FAILED(100205, "部门删除失败"),
    DEPT_NOT_EXIST_SUBDEPTS(100206, "该部门不存在子部门"),
    DEPT_SUB_LESSTRAN_PARENT(100207, "子部门等级要比父部门等级小一级"),
    DEPT_NOT_IN_SAME_COMPANY(100207, "父部门和子部门不在同一公司"),
    DEPT_LEADER_NOT_FIND(100208, "部门领导不存在"),
    DEPT_SUB_NOT_SAME_WITH_PARENT(100209, "该部门与其父部门不能为同一部门"),
    DEPT_LEADER_NOT_SELECT(100210, "部门领导未选择"),
    //公司
    COMP_NAME_NOT_EXIST(100301, "公司名称不能为空"),
    COMP_NOT_FIND(100302, "公司不存在"),
    COMP_UPDATE_FAILED(100303, "公司修改失败"),
    COMP_CREATE_FAILED(100304, "公司创建失败"),
    COMP_DELETED_FAILED(100305, "公司删除失败"),
    //角色
    ROLE_CREATE_FAILED(100401, "角色新增失败!"),
    ROLE_UPDATE_FAILED(100402, "角色修改失败!"),
    ROLE_DELETE_FAILED(100403, "角色删除失败!"),
    POWER_CREATE_FAILED(100404, "权限新增失败!"),
    POWER_UPDATE_FAILED(100405, "权限修改失败!"),
    POWER_DELETE_FAILED(100406, "权限删除失败!"),
    USER_ROLE_CREATE_FAILED(100407, "用户添加角色失败!"),
    ROLE_POWER_CREATE_FAILED(100408, "角色添加权限失败!"),
    USER_ROLE_DELETE_FAILED(100409, "用户删除角色失败!"),
    ROLE_POWER_DELETE_FAILED(100410, "角色删除权限失败!"),
    ROLE_NAME_HAS_EXISTED(100411, "角色名已存在"),
    ROLE_NOT_EXISTED(100412, "角色不存在"),
    POWER_NAME_HAS_EXISTED(100413, "权限名已存在"),
    POWER_NOT_EXISTED(100414, "权限不存在"),
    MENU_NAME_HAS_EXISTED(100415, "菜单名已存在"),
    MENU_NOT_EXISTED(100416, "菜单不存在"),
    MENU_CREATE_FAILED(100417, "菜单新增失败!"),
    MENU_UPDATE_FAILED(100418, "菜单修改失败!"),
    MENU_DELETE_FAILED(100419, "菜单删除失败!"),
    MENU_EMPUTY(100500, "菜单为空!"),
    ROLE_EMPUTY(100501, "角色不能为空!"),
    NOT_SAME_ROLE_DEF(100502, "复制失败，仅可复制相同角色的权限"),
    NOT_SAME_COLUMBUS_ROLE_DEF(100503, "复制失败，仅可复制相同角色的权限"),

    EMPLOY_PARAM_ERROR(100420, "账号数据错误!"),
    EMPLOY_FORBIDDEN(100421, "账号被禁用,请联系管理员"),
    EMPLOY_NOT_ONLY_ONE(100422, "手机绑定账号不唯一,请联系管理员!"),
    EMAIL_ERROR(100423, "已存在相同邮箱用户,请修改"),
    EMAIL_PHONE_ERROR(100424, "已存在相同邮箱,手机号用户,请修改"),
    PHONE_NOT_ONLY_ONE(100425, "已存在相同手机号用户,请修改"),
    NAME_CODE_ERROR(100426, "已存在相同名称和编号角色,请修改"),
    EMPLOY_NOT_EXISTED(100427, "账号不存在"),
    LKG_ERROR(100428, "已存在相同微软或LKG编号,请修改"),

    /**
     * 用户标签模块
     */
    FAILED_TAG_NAME_EXIST(100501, "标签已存在"),
    FAILED_TAG_BE_USED(100502, "标签被使用，不可删除"),
    TAG_CREATE_FAILED(100503, "标签创建失败"),
    TAG_DELETE_FAILED(100504, "标签删除失败"),
    /**
     * 分类
     */
    CATEGORY_NAME_EXIST(100601, "分类已存在"),
    CATEGORY_CREATE_FAILED(100602, "分类创建失败"),
    CATEGORY_UPDATE_FAILED(100603, "分类修改失败"),
    CATEGORY_DELETE_FAILED(100604, "分类删除失败"),

    /**
     * 账号
     */
    PHONE_NOT_SAME(100701, "手机号不一致"),
    VERIFY_CODE_NOT(100702, "验证码过期,请重新获取"),
    VERIFY_CODE_ERROR1(100702, "验证码错误"),
    PASSWORD_THE_SAME(100703, "新密码与原密码不能一样"),
    C_EMPLOY_CUSTOMER_BINDING(100704, "用户已和该主体绑定,请勿重复绑定"),
    /**
     * 消息模块
     */
    MESSAGE_SMS_SEND_FAIL(150000, "短信发送失败"),
    MESSAGE_ERROR_OR_EXPIRED(150001, "验证码不正确或已过期"),
    PHONE_ERROR(150002, "手机号码格式非法或不能为空"),
    VERIFY_CODE_ERROR(150003, "验证码错误"),
    VERIFY_CODE_REPEATED(150004, "请勿重复发送验证码,请60秒以后再试"),

    /**
     * 文件
     */
    FILE_SERVER_ERROR(160004, "文件服务器连接失败"),
    FILE_UPLOAD_FAIL(160000, "文件上传失败"),
    DOWNLOAD_FAILED(160001, "下载文件不存在!"),
    FILE_EMPTY(160002, "上传文件为空"),
    DELETE_FAIL(160003, "删除文件失败"),
    DEAL_FAIL(160005, "文件处理失败"),

    SELECT_TRADE_DAY(170001, "请选择交易日日期～"),
    /**
     * 工作流
     */
    PROCKEY_EMPTY(200001, "流程标识不能为空"),
    USER_ID_EMPTY(200002, "启动人或审批人不能为空"),
    TASK_ID_EMPTY(200003, "任务ID不能为空"),
    RESULT_EMPTY(200004, "审批结果不能为空"),
    BUSINESS_KEY_EMPTY(200005, "关联业务CODE不能为空"),
    APPROVE_RULE_VALUE_EMPTY(200006, "审批规则值不能为空"),
    BIZ_MODULE_EMPTY(200007, "业务模块不能为空"),
    BIZ_ID_EMPTY(200008, "业务ID不能为空"),

    NO_PERMISSION(210001, "无权限"),
    NO_MODEL(210002, "未找到当前流程"),
    NO_TASK(210003, "任务不存在"),

    REQUEST_ACTIVITI_ERROR(220001, "请求工作流引擎失败"),
    ABNORMAL_OPERATION(220002, "审批操作异常"),
    PROCESS_INSTANCE_EXIST(220003, "流程已存在，不能重复发起"),
    CAN_NOT_REJECT(220004, "当前流程不可驳回"),
    START_APPROVAL_ERROR(220005, "发起审批失败"),

    /**
     * TT 300000
     */
    APPROVE_FAIL(300001, "审批失败"),
    TT_IS_NOT_EXIST(300002, "TT不存在"),
    TT_APPROVAL_NOT_COMPLETED(300003, "TT未审批完成"),
    NOT_COMPLETED(300004, "信息填写不完整,请填写完整后再提交"),
    SUBMIT_FAIL(300005, "提交失败"),
    TT_FAIL(300006, "保存失败"),
    PAYCONDITION_NOT_COMPLETED(300010, "请检查付款代码状态,业务类型,数据是否存在"),
    GOODS_NOT_COMPLETED(300008, "请检查货品配置是否正确"),
    QUALITY_NOT_COMPLETED(300009, "请检查发货库点配置质量指标配置是否正确"),
    RISK_RESIDUAL_NOT_EXIST(300011, "无法获取该客户RR limit数据，TT无法提交，请检查客户主数据!"),
    RISK_RESIDUAL_NOT_GET(300012, "无法获取该客户状态，TT无法提交，请检查客户主数据!"),
    RISK_RESIDUAL_CUSTOMER_NO_TRADE(300013, "Trade Status: no trade,该客户不允许成交!"),
    RISK_RESIDUAL_USAGE_OFF(300014, "该客户RR limit已超限，请在CRIS系统申请提额后提交！"),
    RISK_RESIDUAL_USAGE_OVER(300015, "该客户RR limit已超过平台最大限额，请联系风控部门提额后提交!"),
    SAVE_TT_FAIL(300007, "创建TT失败"),
    TT_MODIFY_NOT_EQUAL(300011, "合同信息发生改变，请重新对合同发起变更"),
    TT_CREDIT_PAYMENT(300012, "请检查客户的赊销预付款配置是否正确"),
    TT_DELIVERY(300013, "请检查交提货方式配置是否正确"),
    TT_PACKAGE_WEIGHT(300014, "请检查袋皮扣重配置是否正确"),
    TT_WEIGHT(300015, "请检查重量验收配置是否正确"),
    TT_DEPOSIT_RATE(300016, "请检客户履约保证金配置是否正确"),
    TT_PRICE_LOA(300016, "请检基差价或者蛋白价超出，请填写备注说明"),
    TT_INVOICE(300016, "请检查客户配置发票信息是否正确"),
    TT_WAREHOUSE(300017, "请检查库点配置信息是否有效，库点是否关联账套"),
    TT_ACCOUNT(300018, "请检客户账户配置是否有效"),
    // BUGFIX：case-1003051 履约保证金释放方式为空 Author: Mr 2025-03-18
    TT_DEPOSIT_RELEASE_TYPE(300019, "请检查履约保证金释放方式是否正确"),
    TT_DOMAIN_CODE(300020, "请检查期货合约和期货月份是否正确"),

    /**
     * 合同 400000
     */
    CONTRACT_IS_NOT_EXIST(400001, "合同不存在"),
    CONTRACT_STATUS_EXCEPTION(400002, "合同状态异常"),
    CLOSING_PRICE_NOT_EXIST(400003, "未查询到收盘价"),
    CONTRACT_IS_NOT_EFFECTIVE(400004, "合同未生效"),
    CONTRACT_IS_NOT_SPLITTING(400004, "合同状态不允许拆分"),
    CONTRACT_IS_NOT_MODIFICATION(400004, "合同未在修改中"),
    CONTRACT_CONFIRMED_ALL(400005, "合同已全部定价"),
    CONTRACT_NOT_CONFIRMED_ALL(400006, "合同未全部定价"),
    CONTRACT_TYPE_NOT_SUPPORT_MODIFY(400007, "合同类型不支持修改"),
    CONTRACT_TYPE_NOT_SUPPORT_CLOSED(400007, "合同类型不支持关闭"),
    CONTRACT_DELIVERY_NUM_EXCEPTION(400007, "该合同可提量不等于合同关闭量，无法进行合同关闭！"),
    CONTRACT_TYPE_NOT_SUPPORT_WRITEOFF(400007, "合同类型不支持注销"),
    CONTRACT_NUM_EXCEPTION(400008, "合同数量异常"),
    CONTRACT_TOTAL_PRICE_NUM_EXCEPTION(400008, "合同定价单数量异常"),
    CONTRACT_APPLY_NOT_DEAL_EXCEPTION(400009, "合同有申请待成交的申请单"),
    CONTRACT_BUY_BACK_TYPE_EXCEPTION(400010, "合同回购的类型异常"),
    CONTRACT_BUY_BACK_NUM_EXCEPTION(400010, "回购数量为0"),
    CONTRACT_BUY_BACK_PRICE_NUM_EXCEPTION(400010, "该合同未定价量小于合同回购量，无法进行合同回购！"),
    CONTRACT_BUY_BACK_DELIVERY_NUM_EXCEPTION(400010, "该合同可提量小于合同回购量，无法进行合同回购！"),
    CONTRACT_WASH_OUT_TYPE_EXCEPTION(400011, "解约定赔的类型异常"),
    CONTRACT_WASH_OUT_NUM_EXCEPTION(400011, "解约定赔的数量异常"),
    CONTRACT_WASH_OUT_EXCEPTION(400012, "解约定赔异常"),
    CONTRACT_CLOSED_NUM_EXCEPTION(400013, "合同关闭的数量异常"),
    CONTRACT_PRICE_EXCEPTION(400014, "合同价格信息异常"),
    CONTRACT_EXIST_INVALID_APPLY(400015, "合同已存在作废申请"),
    CONTRACT_BILL_NUM_EXCEPTION(400016, "合同已开单，不可进行系统操作"),
    CONTRACT_WEIGHT_TOLERANCE_NUM_EXCEPTION(400017, "关闭异常！合同溢短装量大于关闭的数量"),
    CONTRACT_STRUCTURE_NUM_EXCEPTION(4000018, "结构化定价中,可操作量不足"),
    ZAN_DING_JIA_CANNOT(4000019, "暂定价合同不可解约定赔"),
    ALLOCATE_EXIST(4000020, "存在待审核分配单,不可解约定赔"),
    WASHOUT_ERROE_1(4000021, "定价量=0且已提货量>0,不可解约定赔"),
    WASHOUT_ERROE_2(4000022, "定价量<总量,不可解约定赔"),
    WASHOUT_ERROE_3(4000023, "定价量=总量且未操作定价完毕,不可解约定赔"),
    WASHOUT_ERROE_4(4000024, "合同正在头寸处理分配中，不可解约定赔"),
    WASHOUT_ERROE_5(4000025, "合同未定价量小于解约定赔量，无法进行解约定赔！"),
    WASHOUT_ERROE_6(4000025, "合同可提量小于解约定赔量，无法进行解约定赔！"),
    CONTRACT_NUM_INSUFFICIENT(4000026, "可操作量不足"),
    CONTRACT_EQUITY_LIST_NOT_NULL(4000027, "合同权益变更列表不能为空"),
    CONTRACT_EQUITY_NOT_APPROVE(4000028, "合同权益变更存在未审批任务"),
    CONTRACT_EQUITY_CHANGE_TIMES_ERROR(4000029, "合同权益变更次数异常"),
    CONTRACT_EQUITY_CHANGE_BASIS_NOT_APPROVE(4000030, "基差合同在头寸处理分配待审核状态中，提交失败"),
    CONTRACT_EQUITY_CHANGE_PRICE_NOT_APPROVE(4000031, "一口价合同反点价在待挂单、待成交状态中，提交失败"),
    CONTRACT_EQUITY_CHANGE_TRANSFER_NUM_ERROR(4000032, "基差合同对应的期货合约的可转月量小于需要权益变更的合同量，提交失败"),
    CONTRACT_NOT_CLOSE_TAIL_NUM(4000033, "不可关闭尾量"),
    CONTRACT_NOT_CLOSE_TAIL_NUM2(4000034, "合同未关闭尾量"),
    CONTRACT_NOT_SUPPORT_SAVE_TT(4000035, "一个合同仅支持保存一个变更TT！"),
    CONTRACT_NOT_SUPPORT_WRITE_OFF_STATUS(4000035, "已注销状态不可注销！"),
    CONTRACT_WRITE_OFF_NUM_LESS_WRITE_NUM(4000036, "注销数量超出可注销数量！"),
    CONTRACT_WITH_DRAW_NUM_MORE_DELIVERY_NUM(4000037, "撤回数量超出可提货数量！"),
    CONTRACT_WRITE_OFF_WITH_DRAW_SIGN_STATUS(4000038, "待回签和待确认合规不可撤回！"),
    WARRANT_CONTRACT_INVALID_WARRANTNUM(4000039, "已住销量>0,不可作废！"),
    WARRANT_CONTRACT_INVALID_BUYBACKNUM(4000040, "已回购>0,不可作废！"),
    CONTRACT_WRITE_OFF_NUM_LESS_WARRANT_NUM(4000041, "注销数量超出仓单持有量！"),
    WARRANT_CONTRACT_CLOSE_SIGN(4000042, "仓单合同关闭存在注销新合同协议未完成！"),
    WARRANT_CONTRACT_CLOSE_NUM(4000043, "仓单合同关闭存在可注销量>0！"),
    CONTRACT_TYPE_NOT_SUPPORT_PRICE(400044, "合同类型不支持定价"),
    WARRANT_CONTRACT_INVALID_ATLAS(400045, "该合同可提量不等于合同作废量，无法进行合同作废"),
    WARRANT_CONTRACT_WITHDRAW_ATLAS(400046, "   该注销合同可提量不等于注销撤回量，无法撤回！"),
    WARRANT_CONTRACT_INVALID_PRICE(400047, "请完成相应盘面处理"),
    WARRANT_CONTRACT_INVALID_PURCHASE(400048, "采购仓单合同作废,对应的仓单已经分配或者转让产生销售合同是不能进行作废的！"),
    WARRANT_CONTRACT_INVALID_STATUS(400049, "原仓单合同未生效或者注销修改中时应不支持注销撤回"),


    /**
     * 期货（点价/定价）模块 500000
     */
    PRICE_APPLY_CAN_NOT_CHANGE(500001, "不可改撤单"),
    APPLY_NUM_MORE_THAN_CAN_PRICE_NUM(500002, "申请点价量大于可点量"),
    TRANSFER_MONTH_HAS_EXIST(500003, "转月申请已存在，请勿重复提交"),
    APPLY_NUM_MORE_THAN_CAN_REVERSE_PRICING_NUM(500004, "申请反点价量大于可反点价量"),
    PRICE_APPLY_NOT_EXIST(500005, "申请单不存在"),
    CAN_NOT_PRICE(500006, "当前时间不可操作"),
    MODIFY_PRICE_APPLY_ONLY_(500007, "只能改单或撤单一次"),
    MODIFY_PRICE_APPLY_FAILED(500007, "改单或撤单失败"),
    CAN_NOT_TRANSFER_MONTH(500008, "不可转入当前合约"),
    CONFIRMED_PRICE_FAILED(500009, "合同定价失败"),
    CONFIRMED_ALREADY_PRICE(500009, "该合同已定过价，无法再次定价。"),
    DEAL_HAND_NUM_MORE_THAN_APPLY_HAND_NUM(500010, "成交手数大于申请手数"),
    SURPLUS_NUM_LESS_THAN_TEN(500011, "剩余量小于10吨，需全部提交"),
    APPLY_NUM_MUST_MULTIPLE_OF_TEN(500012, "申请量大于10，并且剩余量大于等于10"),
    DEAL_HAND_NUM_MUST_THAN_ZERO(500013, "成交手数必须大于0"),
    TRANSACTION_PRICE_MORE_THAN_APPLY_PRICE(500014, "成交价格不能大于申请价格"),
    TRANSFER_MONTH_APPLY_NOT_COMPLETE_ALLOCATE(500015, "转月申请单未完全分配完"),
    TOTAL_AMOUNT_ALLOCATED_GREATER_THAN_REMAINING_DISTRIBUTABLE_AMOUNT(500016, "本次分配量总量大于剩余可分配数量"),
    TRANSFER_MONTH_TIMES_ERROR(500017, "转月次数数据异常"),
    REVERSE_PRICE_TIMES_IS_NOT_ENOUGH(500018, "反点价次数不足"),
    TRANSFER_TIMES_IS_NOT_ENOUGH(500019, "转月次数不足"),
    TIME_LIMIT_DOMAIN_PRICE_UPLOAD(500020, "请在以下时间段内上传收盘价"),
    PRICE_ALLOCATE_NOT_EXIST(500021, "分配单不存在"),
    ADD_PRICE_APPLY_BE_DEFEATED(500022, "申请单新增失败"),
    DEAL_HAND_NUM_MORE_THAN_APPLY_NUM(500010, "成交数量大于申请单数量"),
    PRICE_DEAL_NOT_EXIST(500024, "成交单不存在"),
    PRICE_DEAL_DE_DEFEATED(500025, "成交失败"),
    REVERSE_PRICE_DATE_ERROR(500026, "该合同开始交货日期小于30天"),
    NOT_UPDATE_DATA(500027, "未修改数据"),
    NO_CHANGE(500028, "数据未变化"),
    POSITION_ERROR(500029, "申请单不存在"),
    UPLOAD_NULL(500030, "上传数据为空"),
    PRICE_ALLOCATE_ALREADY_OPERATION(500031, "分配单以操作"),
    PRICE_CONTRACT_EQUITY(500032, "合同权益审核中!!!"),
    APPLY_DEAL_CONTRARY_NUM(500032, "已无撤回次数!"),
    APPLY_CONTRARY_ALL_EXCEPTION(500032, "请先撤回全部(转月/反点价/点价)单后再撤回部分(转月/反点价/点价)单!"),
    NUMBER_CANNOT_CONTRARY(500032, "可操作量不足无法撤回"),
    NUMBER_STRUCTURE_NOT_CONTRARY(500033, "结构化定价单不可撤回"),
    APPLY_CANCEL_CONTRACT_ALREADY_DISPOSE(500032, "已操作合同无法撤回"),
    NOT_REPETITIVE_OPERATION(500034, "请勿重复操作!"),
    WAIT_CONFIRMATION(500035, "等待挂单确认"),

    /**
     * 协议 500000
     */
    CONTRACT_SIGN_IS_NOT_EXIST(500001, "协议不存在"),
    CONTRACT_SIGN_STATUS_EXCEPTION(500002, "协议状态异常"),
    CONTRACT_SIGN_HAS_PROVIDE(500003, "合同已出具，不可重复操作！"),
    CONTRACT_SIGN_HAS_CHECKED(500004, "合同已审核，不可重复操作！"),
    CONTRACT_SIGN_HAS_CONFIRMED(500005, "合同已确认合规，不可重复操作！"),
    CONTRACT_SIGN_SEND_MAIL(500006, "协议已发送邮件，不可重复操作！"),
    CONTRACT_SIGN_HAS_SPONSOR(500007, "合同已发起签章,请勿重复操作"),
    CONTRACT_SIGN_HAS_SPONSOR_NOT_CONTRARY(500008, "合同已发起签章,无法撤回"),
    CONTRACT_SIGN_SPLIT_NOT_CONTRARY(500009, "已拆分不给撤回"),
    CONTRACT_NOT_APPLY(500010, "未查询到(点价/转月)单"),

    /**
     * 合同模版
     */
    TEMPLATE_IS_NOT_EXIST(600002, "合同模版不存在"),
    TEMPLATE_DUPLICATE(600003, "合同模版编码或条件重复"),
    TEMPLATE_MATCH_NOT_EXIST(600003, "匹配对应模板异常（部分转月或反点价）"),
    TEMPLATE_GROUP_CODE_DUPLICATE(600101, "条款组编码重复"),
    TEMPLATE_GROUP_NOT_EXIT(600102, "条款组不存在"),
    TEMPLATE_ITEM_NOT_EXIT(600103, "条款不存在"),
    TEMPLATE_ITEM_CODE_DUPLICATE(600102, "条款组编码重复"),
    TEMPLATE_MATCH_NOT_EXIT(600104, "无匹配的合同模板"),
    TEMPLATE_MARK_MAIN_VERSION(600105, "合同模版状态已禁用"),
    TEMPLATE_COPY_DUPLICATE(600106, "合同模版重复"),
    TEMPLATE_GROUP_EXIST_NOT_MAIN_VERSION(600107, "存在非正式版本的条款组"),
    TEMPLATE_ITEM_EXIST_NOT_MAIN_VERSION(600108, "存在非正式版本的条款"),
    TEMPLATE_NOT_MAIN_VERSION(600109, "该模板为非正式版本，不可导出！"),

    /**
     * 质量指标
     */
    QUALITY_NOT_EXIST(600201, "质量指标不存在"),
    QUALITY_DUPLICATE(600202, "质量指标重复"),

    /**
     * 客户
     */
    CUSTOMER_IS_NOT_EXIST(700002, "客户不存在"),
    CUSTOMER_IS_NOT_WHITE(700003, "客户不是白名单客户"),
    CUSTOMER_PAYMENT_TYPE_HAS_CHANGE(700004, "客户主数据配置，付款方式已更新为：预付"),
    CUSTOMER_PAYMENT_CREDIT_HAS_CHANGE(700005, "客户主数据配置，付款方式已更新为"),
    CUSTOMER_DEPOSIT_RATE_EXIST(700106, "客户履约保证金已存在"),
    CUSTOMER_RESIDUAL_RISK_NOT_EXIST(700007, "无法获取该客户RR limit数据，TT无法提交，请检查客户主数据"),


    FACTORY_SHORT_NAME_REPEAT(700001, "工厂简称重复！"),
    FACTORY_NOT_EXIT(700002, "工厂信息不存在"),
    FACTORY_WAREHOUSE_NOT_EXIT(700003, "发货库点信息不存在"),
    FACTORY_WAREHOUSE_LKG_CODE_EXIT(700004, "发货库点编码重复"),
    DELIVERY_TYPE_LKG_CODE_EXIST(700005, "交提货方式编码重复~"),
    CONFIG_LKG_CODE_EXIST(700006, "配置的集团编码重复~"),
    CONFIG_LKG_NAME_EXIST(700007, "配置的集团名称重复~"),
    CUSTOMER_IS_STRUCTURE(700008, "该客户未开通结构化定价~"),
    CUSTOMER_NOT_CONTACT(700009, "请配置客户通知人信息后出具~"),
    WAREHOUSE_CODE_REPEAT(700010, "库点编码重复，请重新填写"),
    WAREHOUSE_NAME_REPEAT(700011, "库点名称重复，请重新填写"),

    SYSTEM_RULE_EXTRA_PRICE_UNIQUE(700003, "基差基准价配置,同一品类、合约、工厂及交货月份只能配置一条有效数据！"),
    SYSTEM_RULE_YYQ_UNIQUE(700004, "易企签只能有一条配置！"),
    INVOICE_TYPE_REPEAT(700005, "发票类型重复，请检查数据！"),
    CUSTOMER_STATUS_ERROR(700006, "客户状态异常!"),
    CUSTOMER_NOT_EXISTS(700010, "客户配置不存在!"),
    SAME_INVOICE(700011, "存在同主体配置，请重新选择LDC主体"),
    CUSTOMER_PROPERTY_EXIST(700012, "属性已存在请勿重复添加"),
    SYSTEM_RULE_PROTEIN_PRICE_UNIQUE(700013, "蛋白价差,同一品类、规格、工厂及交货月份只能配置一条有效数据！"),

    /**
     * LKG对接 800000
     */
    GET_LKG_CONTRACT_EXCEPTION(800001, "获取LKG合同信息异常"),
    GET_ATLAS_CONTRACT_EXCEPTION(800002, "获取ATLAS合同信息异常"),

    /**
     * 配置
     */
    SYSTEM_RULE_EXIST(900000, "配置已存在！"),
    SYSTEM_RULE_ERROR(900001, "配置不存在！"),
    SETTING_ERROR(900002, "数值设置错误"),
    SETTING_MAX_ERROR(900003, "输入的上限阈值不能等于或者低于下限阈值"),
    SETTING_MIN_ERROR(900004, "输入的下限阈值不能等于或者高于上限阈值"),


    /**
     * 同步相关操作
     */
    OCCUPY_TT(1000001, "当前有用户操作这个合同，请放弃操作并等候，下次操作前请确认合同最新的数据和状态"),
    NO_OP_TT(1000002, "没有可以操作的TT"),


    /**
     *
     */
    NOT_BLANK(1100001, "结构化名称不能为空"),
    NAME_REPEAT(1100002, "该结构化名称已存在，请检查"),
    NOT_EXIT(1100003, "该结构化类型不存在，请检查"),

    /**
     * 公告
     */
    STATUS_ERROR(1200000, "该状态禁止切换，请检查"),
    SAME_ERROR(1200001, "无信息更新,无法提交"),

    /**
     * PaymentTerm
     */
    REPEAT_LKG_CODE(1300001, "已存在LKG代码，请检查"),
    REPEAT_ATLAS_CODE(1300002, "已存在ATLAS代码，请检查"),
    REPEAT_PAYMENT_CODE(1300003, "已存在PaymentTerm代码，请检查"),
    PAY_CONDITION_IS_NOT_ENABLE(1300004, "请检查付款代码状态,业务类型,数据是否存在"),
    PAY_CONDITION_IS_NOT_EXIST(1300005, "PaymentTerm代码不存在，请检查"),
    PAY_CONDITION_IS_NOT_BUCODE(1300006, "PaymentTerm代码业务类型不对，请检查"),

    COMPANY_NAME_EXISTING(1400001, "主体名称已存在"),
    COMPANY_SHOR_NAME_EXISTING(1400002, "主体简称已存在"),
    COMPANY_LKG_CODE_EXISTING(1400003, "LKG编码已存在"),
    COMPANY_STSTUS_DISABLE(1400004, "此公司Columbus系统服务已关闭"),
    COMPANY_CUSTOMER_STSTUS_DISABLE(1400005, "该手机号已被禁用，请联系管理员"),

    /**
     * Delivery
     */
    DELIVERY_WAREHOUSE_CODE_REPEAT(1400001, "提货库点添加失败:库点编码重复"),
    DELIVERY_WAREHOUSE_LDC_REPEAT(1400002, "提货库点添加失败:LDC库重复"),
    DELIVERY_APPLY_NOT_EXIST(1400003, "提货申请单不存在"),
    DELIVERY_APPLY_NUM_ERROR(1400004, "提货申请单数量不足"),
    DELIVERY_APPLY_PLATE_NUMBER_ERROR(1400005, "提货申请单车船号异常"),
    DELIVERY_APPLY_DRIVER_NAME_ERROR(1400006, "提货申请单司机/船长姓名异常"),
    DELIVERY_APPLY_WAREHOUSE_TYPE_ERROR(1400007, "提货申请单库点类型异常"),
    DELIVERY_APPLY_DRIVER_ID_NUMBER_ERROR(1400008, "提货申请单司机/船长身份证号异常"),
    DELIVERY_APPLY_ONBOARD_PHONE_ERROR(1400009, "提货申请单司机/船长手机号异常"),
    DELIVERY_APPLY_IS_SHARE_ERROR(1400010, "提货申请单是否拼车/船异常"),
    DELIVERY_APPLY_DELIVERY_WAREHOUSE_ERROR(1400011, "提货申请单提货库点异常"),
    DELIVERY_APPLY_INVALID_ERROR(1400012, "提货申请已开单，不允许作废"),
    DELIVERY_APPLY_CANCEL_ERROR(1400013, "提货申请单已审核，不允许撤回"),
    DELIVERY_APPLY_AUDIT_ERROR(1400014, "提货申请单审核异常"),
    DELIVERY_APPLY_CANCEL_ONLY_ONCE(1400015, "提货申请单审核通过，只能申请作废一次"),
    CONTRACT_APPLY_DELIVERY_NUM_EXCEPTION(1400016, "合同正在提货申请，不允许操作"),
    DELIVERY_APPLY_CONTRACT_NOT_EXIST(1400017, "提货申请单未分配合同，不允许提交此申请单"),
    CONTRACT_CLOSE_TAIL_NUM_TIMES(1400018, "一个合同仅可关闭一次"),
    CONTRACT_TAIL_CLOSE_NUM_EXCEPTION(1400019, "合同已经尾量关闭，不允许操作"),
    // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-06-17 Start
    DELIVERY_APPLY_STATUS_ERROR(1400020, "提货申请单状态异常"),
    // BUGFIX：case-1002657 哥伦布提交提货委托申请， 麦哲伦出现两条一样申请号的申请单 Author: Mr 2024-06-17 End
    DELIVERY_APPLY_NUM_MORE_THAN_CAN_APPLY(1400021, "提货申请量大于库点可提量，请核实修改后再次提交"),
    DELIVERY_APPLY_OPEN_QUANTITY_EXCEPTION(1400022, "获取提货申请OpenQuantity异常"),
    DELIVERY_APPLY_NUM_ALLOCATE(1400023, "请分配合同"),
    DELIVERY_APPLY_NUM_NOT_MATCH_ALLOCATE(1400024, "提货申请数量与分配数量不一致"),
    DELIVERY_APPLY_SITE_EXCEPTION(1400025, "帐套错误，请重新选择卖方主体或交货工厂"),
    DELIVERY_APPLY_PRE_ALLOCATE_EXCEPTION(1400026, "预分配逻辑返回结果报错"),
    DELIVERY_APPLY_VEHICLE_TYPE_ERROR(1400027, "提货申请单车辆类型异常"),
    DELIVERY_APPLY_STATION_ERROR(1400028, "提货申请单车站异常"),
    DELIVERY_APPLY_DRAFT_DEPTH_ERROR(1400029, "提货申请单核定干舷异常"),

    MENU_USER_COLLECT_NOT_EXIST(1500001, "用户收藏夹菜单信息不存在"),
    MENU_USER_HAS_COLLECTED(1500002, "用户已收藏过该菜单"),
    MENU_USER_COLLECT_NAME_SAME(1500003, "收藏夹名称重复，请重新填写！"),

    /**
     * 仓单
     */
    WARRANT_NOT_EXIST(1600001, "仓单不存在"),
    WARRANT_NOT_DRAFT(1600002, "仓单状态非草稿，不允许提交"),
    WARRANT_NOT_ENOUGH(1600003, "持有量不足"),
    WARRANT_NOT_ALLOCATE(1600004, "仓单状态非已生效，不允许分配/转让"),
    WARRANT_NOT_CANCEL(1600005, "仓单状态非已生效，不允许注销"),
    WARRANT_NOT_EXIST_DATA(1600006, "未查询到仓单数据"),
    WARRANT_NOT_EDIT(1600007, "不允许编辑"),

    SITE_NOT_EXIST(1700001, "账套不存在!"),
    SITE_EXIST(1700002, "帐套信息重复，请重新填写！"),
    SITE_CATEGORY3_EXIST_IN_PROCESS_CONTRACT(1700003, "有open的合同，不可移除品种！"),
    SITE_EXIST_IN_PROCESS_CONTRACT(1700004, "有open的合同，不可禁用当前账套！"),

    /**
     * KingStar 对接
     */
    KINGSTAR_REQUEST_PARAM_ERROR(1800001, "请求参数缺失或格式错误"),
    KINGSTAR_REQUEST_DATA_NOT_EXIST(1800002, "无效的申请编号或数据不存在"),
    KINGSTAR_REQUEST_STATUS_ERROR(1800003, "当前业务状态不允许此操作"),
    KINGSTAR_REQUEST_TIMEOUT(1800102, "系统内部处理超时"),
    KINGSTAR_REQUEST_DEPENDENCY_ERROR(1800103, "第三方依赖服务不可用"),
    KINGSTAR_REQUEST_DUPLICATE(1800201, "请求重复提交"),
    KINGSTAR_REQUEST_UNKNOWN_ERROR(1800999, "未知系统错误"),

    /**
     * ATLAS对接
     */
    SYNC_CUCKOO_CONTRACT_EXCEPTION(1500001, "同步CUCKOO合同异常"),
    SYNC_ATLAS_CONTRACT_EXCEPTION(1500002, "同步ATLAS合同异常"),
    SYNC_CONTRACT_RECORD_IS_NOT_EXIST(1500003, "合同的同步记录不存在"),
    ATLAS_REQUEST_TIMEOUT(1500004, "ATLAS服务请求超时，请稍后再试"),
    HTTP_REQUEST_TIMEOUT(1500005, "服务请求超时，请稍后再试"),

    COMMON_CONFIG_GROUP_NULL(1600001, "配置不存在!"),
    ;

    private int code;
    private String msg;


    ResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer code() {
        return this.code;
    }

    public String msg() {
        return this.msg;
    }

    public static String getMsg(String name) {
        for (ResultCodeEnum item : ResultCodeEnum.values()) {
            if (item.name().equals(name)) {
                return item.msg;
            }
        }
        return name;
    }

    public static Integer getCode(String name) {
        for (ResultCodeEnum item : ResultCodeEnum.values()) {
            if (item.name().equals(name)) {
                return item.code;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name();
    }

    public static ResultCodeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(resultCode -> Objects.equals(code, resultCode.code))
                .findFirst()
                .orElse(OK);
    }

    public static ResultCodeEnum getByMsg(String msg) {
        return Arrays.stream(values())
                .filter(resultCode -> Objects.equals(msg, resultCode.msg))
                .findFirst()
                .orElse(FAILURE);
    }
}
