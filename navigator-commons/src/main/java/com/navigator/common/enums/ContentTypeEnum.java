package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/3/20 13:58
 */
@Getter
@AllArgsConstructor
public enum ContentTypeEnum {
    /**
     * 模版内容类型
     */
    DEFAULT("default", "默认"),
    FTL("ftl", "content模板内容的ftl文件"),
    NORMAL("normal", "content");
    private String value;
    private String desc;

    public static ContentTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(contentTypeEnum -> StringUtils.equals(value, contentTypeEnum.getValue()))
                .findFirst()
                .orElse(DEFAULT);
    }
}
