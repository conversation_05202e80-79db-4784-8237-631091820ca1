package com.navigator.common.enums;

/**
 * 序列号生成
 *
 * @Author: lincl
 * @Date: 2018/12/3 11:02
 */
public enum SequenceEnum {
    /**
     * NAV品种ID
     */
    NAV_ID("N.", "NAV品种ID"),
    /**
     * NAV SKU ID
     */
    NAV_SKU_ID("SKU.", "NAV SKU ID"),
    /**
     * NAV SPU ID
     */
    NAV_SPU_ID("SPU.", "NAV SPU ID"),
    /**
     * 仓单注册号
     */
    WARRANT_CODE("", "仓单注册号"),
    /**
     * 仓单销售合同号
     */
    WARRANT_SALES_CONTRACT_CODE("", "仓单销售合同号"),
    /**
     * 仓单销售子合同号
     */
    WARRANT_SALES_CHILD_CONTRACT_CODE("", "仓单销售子合同号"),
    /**
     * 仓单采购合同号
     */
    WARRANT_PURCHASE_CONTRACT_CODE("", "仓单采购合同号"),
    /**
     * 仓单采购子合同号
     */
    WARRANT_PURCHASE_CHILD_CONTRACT_CODE("", "仓单采购子合同号"),
    /**
     * 仓单合同TT编号
     */
    WARRANT_CONTRACT_TT_CODE("", "仓单合同TT编号"),
    /**
     * 仓单合同协议编号
     */
    WARRANT_CONTRACT_SIGN_CODE("", "仓单合同协议编号"),
    /**
     * 仓单合同-提货权
     */
    WARRANT_CONTRACT_CARGO_RIGHTS_CODE("", "仓单合同-提货权"),
    /**
     * 现货销售合同编号
     */
    SPOT_SALES_CONTRACT_CODE("", "仓单合同-提货权"),
    /**
     * 现货销售子合同编号
     */
    SPOT_SALES_CHILD_CONTRACT_CODE("", "现货销售子合同编号"),
    /**
     * 现货采购合同编号
     */
    SPOT_PURCHASE_CONTRACT_CODE("", "现货采购合同编号"),
    /**
     * 现货采购子合同编号
     */
    SPOT_PURCHASE_CHILD_CONTRACT_CODE("", "现货采购子合同编号"),
    /**
     * 子TT编号
     */
    CHILD_TT_CODE("", "子TT编号");;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 名称
     */
    private String name;

    SequenceEnum(String prefix, String name) {
        this.prefix = prefix;
        this.name = name;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取字典值
     *
     * @return
     */
    public String getValue() {
        return this.name().toLowerCase();
    }

    /**
     * 根据字典值获取枚举类
     *
     * @return
     */
    public static SequenceEnum getByValue(String value) {
        for (SequenceEnum item : SequenceEnum.values()) {
            if (item.getValue().equalsIgnoreCase(value)) {
                return item;
            }
        }
        return null;
    }
}
