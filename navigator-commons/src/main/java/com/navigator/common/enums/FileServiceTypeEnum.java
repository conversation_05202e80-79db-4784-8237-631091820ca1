package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021-11-30 11:12
 */
@Getter
@AllArgsConstructor
public enum FileServiceTypeEnum {
    /**
     * 文件存储资源类型
     */
    LOCAL_SERVER(1, "本地服务器"),
    DMS(2, "达孚-DMS"),
    OSS(3, "OSS"),
    BLOB(4, "BLOB"),
    ;

    private Integer value;
    private String desc;

    public static FileServiceTypeEnum getByValue(Integer code) {
        return Arrays.stream(values())
                .filter(fileServiceTypeEnum -> Objects.equals(code, fileServiceTypeEnum.value))
                .findFirst()
                .orElse(LOCAL_SERVER);
    }

}
