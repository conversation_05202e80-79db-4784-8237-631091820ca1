package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020-10-30 14:47
 */
@Getter
@AllArgsConstructor
public enum RegisterEnum {
    /**
     * 是否已注册
     */

    UN_REGISTER(0, "未注册"),
    HAS_REGISTER(1, "已注册"),
    ;

    private int value;
    private String desc;

    public static RegisterEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(registerEnum -> Objects.equals(code, registerEnum.value))
                .findFirst()
                .orElse(UN_REGISTER);
    }

    //TODO NEO 新E标？无用的代码要删掉
}
