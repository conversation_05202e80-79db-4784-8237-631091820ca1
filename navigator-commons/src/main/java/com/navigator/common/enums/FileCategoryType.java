package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2020-05-27 17:12
 */
@Getter
@AllArgsConstructor
public enum FileCategoryType {

    /**
     * 文件路径类型
     */
    DEFAULT(-1, "默认", "系统", 0),
    PDF(10, "pdf", "PDF", 0),
    HTML(11, "html", "HTML", 0),
    WORD(12, "word", "WORD", 0),
    ZIP(13, "zip", "ZIP", 0),

    /**
     * 附件文件类型
     */
    CONTRACT_TEMPLATE_HTML_ORIGINAL(1000, "电子合同模版内容", "系统组装", 0),

    //    CONTRACT_PDF_ORIGINAL(1001, "电子合同（原件）", "LDC出具", 0),
//    CONTRACT_PDF_SIGNATURE_LDC(1002, "电子合同（LDC签章）", "LDC出具", 0),
//    CONTRACT_PDF_SIGNATURE_CUSTOMER(1003, "电子合同（客户签章）", "客户出具", 0),

    CONTRACT_PDF_ORIGINAL(1001, "contract-original", "LDC出具", 0),
    CONTRACT_PDF_SIGNATURE_LDC(1002, "contract-ldc", "LDC出具", 0),
    CONTRACT_PDF_SIGNATURE_CUSTOMER(1003, "contract-customer", "客户出具", 0),
    WHITE_PDF(1004, "white-contract", "空白协议", 0),


    CANCEL_CONTRACT(3001, "取消声明合同", "合同作废", 0),
    THIRD_PROTOCOL_CONTRACT(3002, "三方协议合同", "非同一集团主体变化", 0),


    LOGIN_PDF(2001, "登录协议", " ", 0),

    DELIVERY_APPLY(4001, "提货申请", " ", 0),

    ;


    private int code;
    private String msg;
    private String source;
    private int sort;

    public static FileCategoryType valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(fileCategoryType -> Objects.equals(code, fileCategoryType.code))
                .findFirst()
                .orElse(DEFAULT);
    }

    /**
     * 电子合同Pdf所有文件
     *
     * @return
     */
    public static List<Integer> getAllContractPdfType() {
        return Arrays.asList(CONTRACT_PDF_ORIGINAL.getCode(), CONTRACT_PDF_SIGNATURE_LDC.getCode(), CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
    }

    /**
     * 电子合同客户签署合同
     *
     * @return
     */
    public static List<Integer> getCustomerContractPdfType() {
        return Arrays.asList(CONTRACT_PDF_SIGNATURE_CUSTOMER.getCode());
    }


    /**
     * 电子合同Pdf所有文件
     *
     * @return
     */
    public static List<Integer> getContractSelfFileType() {
        return Arrays.asList(CANCEL_CONTRACT.getCode(), THIRD_PROTOCOL_CONTRACT.getCode());
    }

}
