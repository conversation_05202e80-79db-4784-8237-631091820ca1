package com.navigator.common.enums;


/**
 * The class Error code enums.
 */
public enum ErrorCodeEnum {
    /**
     * Gl 99990100 error code enums.
     */
    GL99990100(9999100, "参数异常"),
    /**
     * Gl 99990401 error code enums.
     */
    GL99990401(99990401, "无访问权限"),
    /**
     * Gl 000500 error code enums.
     */
    GL99990500(500, "未知异常"),
    /**
     * Gl 000403 error code enums.
     */
    GL99990403(9999403, "无权访问"),
    /**
     * Gl 000404 error code enums.
     */
    GL9999404(9999404, "找不到指定资源"),
    /**
     * Gl 99990001 error code enums.
     */
    GL99990001(99990001, "注解使用错误"),
    /**
     * Gl 99990002 error code enums.
     */
    GL99990002(99990002, "微服务不在线,或者网络超时"),
    /**
     * Uac 10010001 error code enums.
     */
    //1001 用户中心
    UAC10010001(10010001, "会话超时,请刷新页面重试"),
    /**
     * Uac 10010002 error code enums.
     */
    UAC10010002(10010002, "TOKEN解析失败"),
    /**
     * Uac 10010003 error code enums.
     */
    UAC10010003(10010003, "操作频率过快, 您的帐号已被冻结"),
    /**
     * Uac 10011001 error code enums.
     */
    UAC10011001(10011001, "用户Id不能为空"),
    /**
     * Uac 10011002 error code enums.
     */
    UAC10011002(10011002, "找不到用户,loginName=%s"),
    /**
     * Uac 10011003 error code enums.
     */
    UAC10011003(10011003, "找不到用户,userId=%s"),
    /**
     * Uac 10011004 error code enums.
     */
    UAC10011004(10011004, "找不到用户,email=%s"),
    /**
     * Uac 10011006 error code enums.
     */
    UAC10011006(10012006, "手机号不能为空"),
    /**
     * Uac 10011007 error code enums.
     */
    UAC10011007(10011007, "登录名不能为空"),
    /**
     * Uac 10011008 error code enums.
     */
    UAC10011008(10011008, "新密码不能为空"),
    /**
     * Uac 10011009 error code enums.
     */
    UAC10011009(10011009, "确认密码不能为空"),
    /**
     * Uac 10011010 error code enums.
     */
    UAC10011010(10011010, "两次密码不一致"),
    /**
     * Uac 10011011 error code enums.
     */
    UAC10011011(10011011, "用户不存在, userId=%s"),
    /**
     * Uac 10011012 error code enums.
     */
    UAC10011012(10011012, "登录名已存在"),
    /**
     * Uac 10011013 error code enums.
     */
    UAC10011013(10011013, "手机号已存在"),
    /**
     * Uac 10011014 error code enums.
     */
    UAC10011014(10011014, "密码不能为空"),
    /**
     * Uac 10011016 error code enums.
     */
    UAC10011016(10011016, "用户名或密码错误"),
    /**
     * Uac 10011017 error code enums.
     */
    UAC10011017(10011017, "验证类型错误"),
    /**
     * Uac 10011018 error code enums.
     */
    UAC10011018(10011018, "邮箱不能为空"),
    /**
     * Uac 10011019 error code enums.
     */
    UAC10011019(10011019, "邮箱已存在"),
    /**
     * Uac 10011020 error code enums.
     */
    UAC10011020(10011020, "短信模板不能为空"),
    /**
     * Uac 10011021 error code enums.
     */
    UAC10011021(10011021, "发送短信验证码对象转换为json字符串失败"),
    /**
     * Uac 10011022 error code enums.
     */
    UAC10011022(10011022, "发送短信验证码失败"),
    /**
     * Uac 10011023 error code enums.
     */
    UAC10011023(10011023, "越权操作"),
    /**
     * Uac 10011024 error code enums.
     */
    UAC10011024(10011024, "找不到绑定的用户, userId=%"),
    /**
     * Uac 10011025 error code enums.
     */
    UAC10011025(10011025, "用户已存在, loginName=%"),
    /**
     * Uac 10011026 error code enums.
     */
    UAC10011026(10011026, "更新用户失败, userId=%"),
    /**
     * Uac 10011027 error code enums.
     */
    UAC10011027(10011027, "找不到用户,phone=%s"),
    /**
     * Uac 10011028 error code enums.
     */
    UAC10011028(10011028, "链接已失效"),
    /**
     * Uac 10011029 error code enums.
     */
    UAC10011029(10011029, "重置密码失败"),
    /**
     * Uac 10011030 error code enums.
     */
    UAC10011030(10011030, "激活失败, 链接已过期"),
    /**
     * Uac 10011031 error code enums.
     */
    UAC10011031(10011031, "验证码超时, 请重新发送验证码"),
    /**
     * Uac 10011032 error code enums.
     */
    UAC10011032(10011032, "邮箱不存在, loginName=%s,email=%s"),
    /**
     * Uac 10011033 error code enums.
     */
    UAC10011033(10011033, "清空该用户常用菜单失败"),
    /**
     * Uac 10011034 error code enums.
     */
    UAC10011034(10011034, "不允许操作admin用户"),
    /**
     * Uac 10011035 error code enums.
     */
    UAC10011035(10011035, "原始密码输入错误"),
    /**
     * Uac 10011036 error code enums.
     */
    UAC10011036(10011036, "新密码和原始密码不能相同"),
    /**
     * Uac 10011037 error code enums.
     */
    UAC10011037(10011037, "修改用户失败,userId=%s"),
    /**
     * Uac 10011038 error code enums.
     */
    UAC10011038(10011038, "激活用户失败,userId=%s"),
    /**
     * Uac 10011039 error code enums.
     */
    UAC10011039(10011039, "验证token失败"),
    /**
     * Uac 10011040 error code enums.
     */
    UAC10011040(10011040, "解析header失败"),
    /**
     * Uac 10011041 error code enums.
     */
    UAC10011041(10011041, "页面已过期,请重新登录"),
    /**
     * Uac 10011042 error code enums.
     */
    UAC10011042(10011042, "Cookie转码异常"),
    /**
     * Uac 10012001 error code enums.
     */
    UAC10012001(10012001, "角色ID不能为空"),
    /**
     * Uac 10012002 error code enums.
     */
    UAC10012002(10012002, "拥有的角色不允许禁用"),
    /**
     * Uac 10012003 error code enums.
     */
    UAC10012003(10012003, "系统角色不能删除"),
    /**
     * Uac 10012004 error code enums.
     */
    UAC10012004(10012004, "超级角色Id不能为空"),

    /**
     * Uac 10012005 error code enums.
     */
    UAC10012005(10012005, "找不到角色信息,roleId=%s"),
    /**
     * Uac 10012006 error code enums.
     */
    UAC10012006(10012006, "删除角色失败, roleId=%s"),
    /**
     * Uac 10012007 error code enums.
     */
    UAC10012007(10012007, "批量删除角色失败, roleId=%s"),
    /**
     * Uac 10012008 error code enums.
     */
    UAC10012008(10012008, "找不到绑定的角色, roleId=%s"),


    /**
     * Uac 10013001 error code enums.
     */
    UAC10013001(10013001, "父菜单不存在,menuId=%s"),
    /**
     * Uac 10013002 error code enums.
     */
    UAC10013002(10013002, "更新上级菜单失败,menuId=%s"),
    /**
     * Uac 10013003 error code enums.
     */
    UAC10013003(10013003, "菜单不存在,menuId=%s"),
    /**
     * Uac 10013004 error code enums.
     */
    UAC10013004(10013004, "启用菜单失败,menuId=%s"),
    /**
     * Uac 10013005 error code enums.
     */
    UAC10013005(10013005, "禁用菜单失败,menuId=%s"),
    /**
     * Uac 10013006 error code enums.
     */
    UAC10013006(10013006, "更新菜单状态失败,menuId=%s"),
    /**
     * Uac 10013007 error code enums.
     */
    UAC10013007(10013007, "根菜单不能禁用"),
    /**
     * Uac 10013008 error code enums.
     */
    UAC10013008(10013008, "删除菜单失败, menuId=%s"),
    /**
     * Uac 10013009 error code enums.
     */
    UAC10013009(10013009, "请先分配菜单"),
    /**
     * Uac 10013010 error code enums.
     */
    UAC10013010(10013010, "选择菜单不是根目录,menuId=%s"),


    /**
     * Uac 10014001 error code enums.
     */
    UAC10014001(10014001, "找不到权限信息, actionId=%s"),
    /**
     * Uac 10014002 error code enums.
     */
    UAC10014002(10014002, "删除失败, actionId=%s"),
    /**
     * Uac 10014003 error code enums.
     */
    UAC10014003(10014003, "保存权限信息失败"),
    /**
     * Uac 10015001 error code enums.
     */
    UAC10015001(10015001, "找不到组织信息,groupId=%s"),
    /**
     * Uac 10015002 error code enums.
     */
    UAC10015002(10015002, "组织状态不存在"),
    /**
     * Uac 10015003 error code enums.
     */
    UAC10015003(10015003, "操作越权, 启用子节点, 必须先启用父节点"),
    /**
     * Uac 10015004 error code enums.
     */
    UAC10015004(10015004, "找不到组织信息,groupId=%s"),
    /**
     * Uac 10015006 error code enums.
     */
    UAC10015006(10015006, "更新组织信息失败,groupId=%s"),
    /**
     * Uac 10015007 error code enums.
     */
    UAC10015007(10015007, "该组织下还存在子节点，不能将其删除, Pid=%s"),
    /**
     * Uac 10015008 error code enums.
     */
    UAC10015008(10015008, "该组织下绑定的用户，不能将其删除, groupId=%s"),
    /**
     * Uac 10015009 error code enums.
     */
    UAC10015009(10015009, "找不到上级组织, groupId=%s"),
    /**
     * Mdc 10021001 error code enums.
     */
    OPC10040004(10040004, "发送短信失败"),
    /**
     * Opc 10040005 error code enums.
     */
    OPC10040005(10040005, "生成邮件消息体失败"),
    /**
     * Opc 10040006 error code enums.
     */
    OPC10040006(10040006, "获取模板信息失败"),
    /**
     * Opc 10040007 error code enums.
     */
    OPC10040007(10040007, "更新附件失败, id=%s"),
    /**
     * Opc 10040008 error code enums.
     */
    OPC10040008(10040008, "找不到该附件信息, id=%s"),
    /**
     * Opc 10040009 error code enums.
     */
    OPC10040009(10040009, "上传图片失败"),
    /**
     * Tpc 10050001 error code enums.
     */
    OPC10040010(10040010, "文件名不能为空"),
    /**
     * Opc 10040011 error code enums.
     */
    OPC10040011(10040011, "今日流量已用尽, 请明天再试"),
    /**
     * Tpc 10050001 error code enums.
     */
// 1005 任务中心
    TPC10050001(10050001, "消息的消费Topic不能为空"),
    /**
     * Tpc 10050002 error code enums.
     */
    TPC10050002(10050002, "根据消息key查找的消息为空"),
    /**
     * Tpc 10050003 error code enums.
     */
    TPC10050003(10050003, "删除消息失败,messageKey=%s"),
    /**
     * Tpc 10050004 error code enums.
     */
    TPC10050004(10050004, "消息中心接口异常,message=%s, messageKey=%s"),
    /**
     * Tpc 10050005 error code enums.
     */
    TPC10050005(10050005, "目标接口参数不能为空"),
    /**
     * Tpc 10050006 error code enums.
     */
    TPC10050006(10050006, "根据任务Id查找的消息为空"),

    /**
     * Tpc 10050007 error code enums.
     */
    TPC10050007(10050007, "消息数据不能为空"),
    /**
     * Tpc 10050008 error code enums.
     */
    TPC10050008(10050008, "消息体不能为空,messageKey=%s"),
    /**
     * Tpc 10050009 error code enums.
     */
    TPC10050009(10050009, "消息KEY不能为空"),
    /**
     * Tpc 100500010 error code enums.
     */
    TPC100500010(10050010, "Topic=%s, 无消费者订阅"),
    /**
     * Tpc 100500011 error code enums.
     */
    TPC100500011(10050011, "Mq编码转换异常, MessageKey=%s"),
    /**
     * Tpc 100500012 error code enums.
     */
    TPC100500012(10050012, "发送MQ失败, MessageKey=%s"),
    /**
     * Tpc 100500013 error code enums.
     */
    TPC100500013(10050013, "延迟级别错误, Topic=%s, MessageKey=%s"),
    /**
     * Tpc 100500014 error code enums.
     */
    TPC100500014(10050014, "MQ重试三次,仍然发送失败, Topic=%s, MessageKey=%s"),
    /**
     * Tpc 100500015 error code enums.
     */
    TPC100500015(10050015, "消息PID不能为空, messageKey=%s"),
    ;
    private int code;
    private String msg;

    /**
     * Msg string.
     *
     * @return the string
     */
    public String msg() {
        return msg;
    }

    /**
     * Code int.
     *
     * @return the int
     */
    public int code() {
        return code;
    }

    ErrorCodeEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    /**
     * Gets enums.
     *
     * @param code the code
     * @return the enums
     */
    public static ErrorCodeEnum getEnum(int code) {
        for (ErrorCodeEnum ele : ErrorCodeEnum.values()) {
            if (ele.code() == code) {
                return ele;
            }
        }
        return null;
    }
}
