package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 20:36
 */
@Getter
@AllArgsConstructor
public enum RuleReferTypeEnum {
    /**
     * 模版层级类型
     */
    LOA_A("LOA_A", " LOA-A签汇签", RuleModuleTypeEnum.LOA_APPROVAL),
    LOA_B("LOA_B", " LOA-B签汇签", RuleModuleTypeEnum.LOA_APPROVAL),
    LOA_C_SINGLE("LOA_C_SINGLE", " LOA-C签单签", RuleModuleTypeEnum.LOA_APPROVAL),
    LOA_C_EXCHANGE("LOA_C_EXCHANGE", "LOA-C签汇签", RuleModuleTypeEnum.LOA_APPROVAL),

    SIGN_PAPER_RULE("SIGN_PAPER_RULE", "正本规则（超期超限）", RuleModuleTypeEnum.CONTRACT_SIGN),

    ;

    private String value;
    private String desc;
    private RuleModuleTypeEnum ruleModuleTypeEnum;

    public static RuleReferTypeEnum getByValue(String value) {
        for (RuleReferTypeEnum en : RuleReferTypeEnum.values()) {
            if (value.equals(en.getValue())) {
                return en;
            }
        }
        return RuleReferTypeEnum.LOA_A;
    }

    public static RuleModuleTypeEnum getRuleModuleTypeByValue(String value) {
        for (RuleReferTypeEnum en : RuleReferTypeEnum.values()) {
            if (value.equals(en.getValue())) {
                return en.getRuleModuleTypeEnum();
            }
        }
        return RuleReferTypeEnum.LOA_A.getRuleModuleTypeEnum();
    }

    public static String getDescByValue(String value) {
        return getByValue(value).getDesc();
    }

    public static List<String> getLoaAllTypeList() {
        return Arrays.asList(LOA_A.getValue(), LOA_B.getValue(), LOA_C_SINGLE.getValue(), LOA_C_EXCHANGE.getValue());
    }
}
