package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum StandardType {
    /**
     * 有效状态
     */
    NATIONAL_STANDARD(0, "国标"),
    COMPANY_STANDARD(1, "企标");

    private Integer value;
    private String desc;

    public static StandardType valueOf(Integer value) {
        return Arrays.stream(values())
                .filter(statusEnum -> Objects.equals(value, statusEnum.value))
                .findFirst()
                .orElse(NATIONAL_STANDARD);
    }

    public static String getDescByValue(Integer value) {
        return valueOf(value).getDesc();
    }


}
