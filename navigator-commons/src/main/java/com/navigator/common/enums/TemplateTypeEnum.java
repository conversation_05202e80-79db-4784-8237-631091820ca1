package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 20:36
 */
@Getter
@AllArgsConstructor
public enum TemplateTypeEnum {
    /**
     * 模版层级类型
     */
    ORIGIN_TEMPLATE(1, "条款管理"),
    M_TERMS_TEMPLATE(2, "条款组管理"),
    SALES_CONTRACT_TEMPLATE(3, "模版管理"),
    LOG_TEMPLATE(4, "日志模版"),
    LOGIN_TEMPLATE(8, "登录模版"),
    TEMPLATE_CHECK_RULE(6, "模板出具标识规则"),
    ;

    private Integer value;
    private String desc;

    public static TemplateTypeEnum getByValue(int value) {
        for (TemplateTypeEnum en : TemplateTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return TemplateTypeEnum.ORIGIN_TEMPLATE;
    }

    public static String getDescByValue(int value) {
        return getByValue(value).getDesc();
    }

    public static List<Integer> getTemplateTypeList() {
        return Arrays.asList(ORIGIN_TEMPLATE.getValue(), M_TERMS_TEMPLATE.getValue(), SALES_CONTRACT_TEMPLATE.getValue());
    }
}
