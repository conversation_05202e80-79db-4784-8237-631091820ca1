package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum BusinessCodeEnum {
    /**
     * 业务操作场景编码
     */
    DEFAULT(0, "default", "默认"),

    /**
     * TT（销售合同-新增）
     */
    TT_SALE_CONTRACT_ADD(1001, "tt_sale_contract_add", "TT-销售合同-保存"),
    TT_SALE_CONTRACT_MODIFY(1002, "tt_sale_contract_modify", "TT-销售合同-修改"),
    TT_SALE_CONTRACT_COMMIT(1003, "tt_sale_contract_commit", "TT-销售合同-提交"),
    //TT-审批
    TT_SALE_CONTRACT_APPROVAL_A_PASS(1004, "tt_sale_contract_approval", "TT-销售合同-审批"),
    TT_SALE_CONTRACT_WITHDRAW(1005, "tt_sale_contract_withdraw", "TT-销售合同-撤回"),
    TT_SALE_CONTRACT_CANCEL(1006, "tt_sale_contract_cancel", "TT-销售合同-作废"),

    /**
     * 销售合同
     */
    SALE_CONTRACT_ISSUE(1101, "sale_contract_issue", "销售合同-出具"),
    SALE_CONTRACT_CHECK_PASS(1102, "sale_contract_check_pass", "销售合同-审核通过"),
    SALE_CONTRACT_CHECK_REJECT(1103, "sale_contract_check_reject", "销售合同-审核驳回"),

    ;

    private int value;
    private String desc;
    private String msg;

    public static BusinessCodeEnum getEnumByValue(Integer value) {
        return Arrays.stream(values())
                .filter(moduleType -> value == moduleType.getValue())
                .findFirst()
                .orElse(DEFAULT);
    }

    public static List<Integer> getTtOperaionType() {
        return Arrays.asList(BusinessCodeEnum.TT_SALE_CONTRACT_ADD.getValue());
    }
}
