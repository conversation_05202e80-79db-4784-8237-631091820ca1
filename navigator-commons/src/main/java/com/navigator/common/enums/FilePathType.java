package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2021-12-09 10:45
 */
@Getter
@AllArgsConstructor
public enum FilePathType {
    /**
     * 文件路径类型
     */
    PDF("pdf", "PDF"),
    HTML("html", "HTML"),
    WORD("word", "WORD"),
    ZIP("zip", "ZIP"),
    CSV("csv", "CSV");
    private String value;
    private String desc;

    public static FilePathType getByValue(String value) {
        return Arrays.stream(values())
                .filter(filePathType -> Objects.equals(value, filePathType.value))
                .findFirst()
                .orElse(PDF);
    }

}
