package com.navigator.common.enums;

import lombok.Getter;

@Getter
public enum SystemUserEnum {
    SYSTEM(-9, "-9", "系统管理员"),
    SYSTEM_COLUMBUS(-99, "-99", "系统管理员"),
    ADMIN_SUPER(1, "1", "系统超级管理员"),
    ADMIN_BIZ(2, "2", "业务管理员管理员"),
    ADMIN_DEV(3, "3", "业务管理员管理员"),
    reserve1(4, "4", "预留系统用户"),
    reserve2(5, "5", "预留系统用户"),
    reserve3(6, "6", "预留系统用户"),
    reserve4(7, "7", "预留系统用户"),
    UT_TESTER(8, "8", "UT"),
    ADMIN_EMPTY(9, "9", "系统用户");

    private int employId;
    private String userId;
    private String userName;

    SystemUserEnum(int employId, String userId, String userName) {
        this.employId = employId;
        this.userId = userId;
        this.userName = userName;
    }
}
