package com.navigator.common.enums;

import lombok.Getter;

/**
 * <p>
 * 计算类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Getter
public enum CalcTypeEnum {
    /**
     * 计算类型
     */
    PRICE("单价", 6),
    COUNT("数量", 3),
    AVE_PRICE("加权平均价", 2),
    ;

    String description;
    int scale;

    CalcTypeEnum(String description, int scale) {
        this.description = description;
        this.scale = scale;
    }
}
