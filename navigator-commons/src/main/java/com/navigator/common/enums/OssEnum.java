package com.navigator.common.enums;


import lombok.Getter;

@Getter
public enum OssEnum {

    /**
     * 系统
     * name为文件路径 慎重更改
     */
    SYSTEM_XINKE(1, "XINKE"),
    SYSTEM_TIANTOUCAI(2, "TIANTOUCAI"),

    /**
     * 项目
     */
    PROJECT_AITM(101, "AITM"),
    PROJECT_TRADE(102, "trade"),
    PROJECT_ERP(103, "erp"),
    PROJECT_WX_TRADE(104, "wx-trade"),

    /**
     * 功能
     */
    CATALOG_CATEGORY(201, "category"),
    CATALOG_ACCOUNT(202, "account"),
    CATALOG_PORK_OFFER(203, "porkOffer"),
    ;
    int code;
    String name;

    OssEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }


    /**
     * 通过value取枚举
     *
     * @param code
     * @return
     */
    public static OssEnum getCode(int code) {
        for (OssEnum enums : OssEnum.values()) {
            if (enums.getCode() == code) {
                return enums;
            }
        }
        return null;
    }

    /**
     * 通过code获取name
     *
     * @param code
     * @return
     */
    public static String getName(int code) {
        for (OssEnum enums : OssEnum.values()) {
            if (enums.getCode() == code) {
                return enums.name;
            }
        }
        return "";
    }
}
