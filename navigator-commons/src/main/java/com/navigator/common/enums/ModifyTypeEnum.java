package com.navigator.common.enums;

import lombok.Getter;

@Getter
public enum ModifyTypeEnum {
    /**
     * 变更来源
     */
    REVISE(1, "修改"),
    SPLIT(2, "拆分"),
    ;

    private Integer value;
    private String desc;

    ModifyTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static ModifyTypeEnum getByValue(Integer value) {
        if (null == value) {
            return ModifyTypeEnum.REVISE;
        }
        for (ModifyTypeEnum en : ModifyTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return ModifyTypeEnum.REVISE;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }


}
