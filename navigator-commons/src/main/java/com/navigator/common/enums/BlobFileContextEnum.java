package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum BlobFileContextEnum {

    /**
     * 文件格式
     */
    JPEG("image/jpeg", "jpeg格式", "jpeg"),
    JPG("image/jpg", "jpg格式", "jpg"),
    PNG("image/png", "png格式", "png"),
    PDF("application/pdf", "pdf格式", "pdf"),
    ZIP("application/x-zip-compressed", "zip格式", "zip"),
    CSV("text/csv", "csv格式", "csv"),

    ;

    private String fileType;
    private String desc;
    private String typeInfo;

    /**
     * 获取所有预览类型
     *
     * @return 结果-预览类型
     */
    public static List<String> getAllViewTypeList() {
        return Arrays.asList(JPEG.getTypeInfo(), JPG.getTypeInfo(), PNG.getTypeInfo(), PDF.getTypeInfo());
    }

    public static BlobFileContextEnum getByTypeInfo(String typeInfo) {
        return Arrays.stream(values())
                .filter(blobFileContextEnum -> typeInfo.equals(blobFileContextEnum.getTypeInfo()))
                .findFirst()
                .orElse(PDF);
    }
}
