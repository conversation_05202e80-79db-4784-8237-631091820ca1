package com.navigator.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-08-29 18:02
 **/
@Getter
@AllArgsConstructor
public enum BasicOperateEnum {
    NEW(1, "新增", "New"),
    UPDATE(2, "修改", "UPDATE"),
    UPDATE_STATUS_ON(3, "启用", "UPDATE_STATUS_ON"),
    UPDATE_STATUS_OFF(4, "禁用", "UPDATE_STATUS_OFF"),
    COPY_TEMPLATE(5, "复制模板", "COPY_TEMPLATE"),
    RELATION_TEMPLATE_ITEM(6, "模板-条款绑定关系-绑定", "RELATION_TEMPLATE_ITEM"),
    RELATION_TEMPLATE_ITEM_REMOVE(7, "模板-条款绑定关系-解除", "RELATION_TEMPLATE_ITEM_REMOVE"),
    MARK_MAIN_VERSION(8, "标记为正式版本", "MARK_MAIN_VERSION"),
    JSON_IMPORT(9, "脚本导入", "JSON_IMPORT"),
    JSON_EXPORT(10, "脚本导出", "JSON_EXPORT"),
//    EXCEL_EXPORT(9, "Excel导出", "EXCEL_EXPORT"),
    ;
    private Integer value;
    private String desc;
    private String memo;

    public static BasicOperateEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(basicOperateEnum -> value.equals(basicOperateEnum.getValue()))
                .findFirst()
                .orElse(NEW);
    }

    public static List<Integer> needRecordDetailInfoType() {
        return Arrays.asList(NEW.getValue(), UPDATE.getValue(), COPY_TEMPLATE.getValue(), MARK_MAIN_VERSION.getValue(), JSON_IMPORT.getValue());
    }
    public static List<Integer> getMainVersionType() {
        return Arrays.asList(MARK_MAIN_VERSION.getValue(), JSON_IMPORT.getValue(), JSON_EXPORT.getValue());
    }


}
