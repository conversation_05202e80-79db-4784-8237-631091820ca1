package com.navigator.common.config.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "azure.blob")
@Data
@ToString
@RefreshScope
public class AzureBlobProperties {
    private String accountName;
    private String accountKey;
    private String containName;
    private String closeContainName;
    private String env;
    private String host;
    private String sas;
}
