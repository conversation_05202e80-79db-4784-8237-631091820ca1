package com.navigator.common.config.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConfigurationProperties(prefix = "navigator.common")
@Data
@ToString
@RefreshScope
public class CommonProperties {
    private List<Integer> systemRoleList;
    private List<Integer> columbusRoleList;
    private Integer beginEmployId;
    private Integer columbusBeginEmployId;
    private Boolean captcha;
    private List<String> customerNameList;
    private Boolean customerStatus;
}
