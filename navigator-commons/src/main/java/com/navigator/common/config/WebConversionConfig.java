package com.navigator.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.support.GenericConversionService;
import org.springframework.web.bind.support.ConfigurableWebBindingInitializer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;

import javax.annotation.PostConstruct;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 * Web 配置类 - 负责注册自定义的类型转换器，解决 OpenFeign 调用时区偏差问题
 * <p>
 * BUGFIX：case-1003020 基差合同期货价格取用数据不同 Author: Mr 2025-03-07
 * </p>
 *
 * <p>
 * 【背景问题 - OpenFeign 调用导致的时间偏差】
 * - 现象：当服务端通过 OpenFeign 调用接口时，传输的时间为 "Wed Mar 06 20:00:00 CST 2025"，
 * 但接收到返回值时，时间变为 "Wed Mar 07 10:00:00 CST 2025"，存在 14 小时的偏差。
 * - 原因：Date 类型解析时会受到时区影响：
 * 1. 解析请求参数时，默认使用美国 CST（Central Standard Time, UTC-6）时区解析时间。
 * 2. 但在解析返回值时，使用的是中国 CST（China Standard Time, UTC+8）时区，导致 14 小时偏差。
 * - 解决方案：在服务端配置自定义的字符串到 Date 的转换器，确保时间解析的一致性。
 * </p>
 *
 * <p>
 * 【本类作用 - 统一字符串到 Date 的转换规则】
 * - 通过 Spring 的 Converter 机制，注册自定义转换器 StringToDateConverter。
 * - 采用固定格式 "EEE MMM dd HH:mm:ss 'CST' yyyy"（如 "Wed Mar 06 20:00:00 CST 2025"）。
 * - 使用 Locale.US 解析，确保时间解析规则与调用方一致，避免时区偏差。
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-07
 */
@Configuration
public class WebConversionConfig {

    @Autowired
    private RequestMappingHandlerAdapter handlerAdapter;

    /**
     * 注册自定义的字符串到日期转换器
     */
    @PostConstruct
    public void registerCustomConverters() {
        ConfigurableWebBindingInitializer initializer =
                (ConfigurableWebBindingInitializer) handlerAdapter.getWebBindingInitializer();
        if (initializer != null && initializer.getConversionService() != null) {
            GenericConversionService conversionService =
                    (GenericConversionService) initializer.getConversionService();
            conversionService.addConverter(String.class, Date.class, new StringToDateConverter());
        }
    }

    /**
     * 自定义转换器：将字符串格式的日期转换为 Date 类型
     */
    static class StringToDateConverter implements Converter<String, Date> {
        private static final String DATE_PATTERN = "EEE MMM dd HH:mm:ss 'CST' yyyy";
        private static final Locale LOCALE = Locale.US;
        private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat(DATE_PATTERN, LOCALE);

        @Override
        public Date convert(String source) {
            try {
                return DATE_FORMAT.parse(source);
            } catch (ParseException e) {
                throw new IllegalArgumentException("Invalid date format: " + source, e);
            }
        }
    }
}
