package com.navigator.common.config.properties;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "oss")
@Data
@ToString
public class OssProperties {
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String endpoint;
    private String urlPrefix;
    private String callbackUrl;
    private String catalog;
    private String bucketNameErp;
    private String urlPrefixErp;

}
