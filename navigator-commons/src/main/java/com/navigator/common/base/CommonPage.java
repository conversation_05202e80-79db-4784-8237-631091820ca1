package com.navigator.common.base;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class CommonPage<T> implements Serializable {
    private Integer pageNum;
    private Integer pageSize;
    private Integer totalPage;
    private Long total;
    private List<T> list;

    public CommonPage() {
    }

    /**
     * @description:   分页列表 ：将原来的分页list转成VO
     * @param:  list 新的list
     * @param:  commonPage 原来的分页对象
     * @return:
     *
     * @author: Mr.Lq
     * @date: 2020/12/17 14:11
     */
    public static <T> CommonPage<T> convert(List<T> newList , CommonPage oldCommonPage) {
        CommonPage<T> result = new CommonPage<T>();
        result.setTotalPage(oldCommonPage.getTotalPage());
        result.setPageNum(oldCommonPage.getPageNum());
        result.setPageSize(oldCommonPage.getPageSize());
        result.setTotal(oldCommonPage.getTotal());
        result.setList(newList);
        return result;
    }
    /**
     * 将PageHelper分页后的list转为分页信息
     */
    public static <T> CommonPage<T> convert(List<T> list) {
        CommonPage<T> result = new CommonPage<T>();
        PageInfo<T> pageInfo = new PageInfo<T>(list);
        result.setTotalPage(pageInfo.getPages());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setList(pageInfo.getList());
        return result;
    }

    /**
     * 将SpringData分页后的list转为分页信息
     */
    public static <T> CommonPage<T> convert(Page<T> pageInfo) {
        CommonPage<T> result = new CommonPage<T>();
        result.setTotalPage(pageInfo.getPages());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setList(pageInfo.getResult());
        return result;
    }

    /**
     * 将SpringData分页后的list转为分页信息
     */
    public static <T> CommonPage<T> convert(Page<T> pageInfo,List<T> list) {
        CommonPage<T> result = new CommonPage<T>();
        result.setTotalPage(pageInfo.getPages());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setTotal(pageInfo.getTotal());
        result.setList(list);
        return result;
    }


}
