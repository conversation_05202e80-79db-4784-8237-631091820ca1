package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 16:07
 **/
@AllArgsConstructor
@Getter
public enum BuCodeEnum {
    /**
     * 业务类型
     */
    ST("ST", "现货"),
    //    FT("FT", "期货"),
    WT("WT", "仓单");

    String value;
    String desc;

    public static BuCodeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(buCodeEnum -> StringUtils.equals(value, buCodeEnum.getValue()))
                .findFirst()
                .orElse(ST);
    }

    public static String getDescByValue(String value) {
        return Arrays.stream(values())
                .filter(buCodeEnum -> StringUtils.equals(value, buCodeEnum.getValue()))
                .findFirst()
                .orElse(ST).getDesc();
    }

    /**
     * 解析（,ST,WT,）
     *
     * @param value
     * @return
     */
    public static String getDescByValueList(String value) {
        List<String> buCodeList = getBuCodeList(value);
        return getBuNameInfo(buCodeList);
    }

    /**
     * 解析（,ST,WT,）
     *
     * @param buCode
     * @return
     */
    public static List<String> getBuCodeList(String buCode) {
        return Arrays.stream(buCode
                .substring(1, buCode.length() - 1)
                .split(",")).collect(Collectors.toList());
    }

    public String getBuCodeInfo(List<String> buCodeList) {
        String buCodeInfo = "";
        for (String buCode : buCodeList) {
            if ("All".equals(buCode)) {
                buCodeInfo += "全部";
            } else {
                buCodeInfo += BuCodeEnum.getDescByValue(buCode);
            }
        }
        return buCodeInfo;
    }


    public static String getBuNameInfo(List<String> buCodeList) {
        List<String> buNameList = new ArrayList<>();
        for (String buCode : buCodeList) {
            if ("All".equals(buCode)) {
                buNameList.add("全部");
            } else {
                buNameList.add(BuCodeEnum.getDescByValue(buCode));
            }
        }
        return StringUtils.join(buNameList, ",");
    }

    //便利枚举
    public static List<String> getBuCodeList() {
        List<String> buCodeList = new ArrayList<>();
        for (BuCodeEnum buCodeEnum : BuCodeEnum.values()) {
            buCodeList.add(buCodeEnum.getValue());
        }
        return buCodeList;
    }
}
