package com.navigator.bisiness.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
public enum ContractTradeTypeEnum {
    /**
     * tradeType
     */
    UNKNOWN(-1, TTTypeEnum.UNKNOWN, 1, "未知"),
    NEW(101, TTTypeEnum.NEW, 1, "新增合同"),
    REVISE_NORMAL(102, TTTypeEnum.REVISE, 1, "修改合同"),
    //修改合同(客户主体变化）：默认走新增的数字合同-模板出具，不需要配置模板
    REVISE_CHANGE_CUSTOMER(103, TTTypeEnum.REVISE, 2, "修改合同(客户主体变化）"),
    SPLIT_NORMAL(104, TTTypeEnum.SPLIT, 1, "拆分合同"),
    SPLIT_CHANGE_CUSTOMER(105, TTTypeEnum.SPLIT, 2, "拆分合同(客户主体变化）"),
    PRICE(111, TTTypeEnum.PRICE, 1, "合同点价"),
    FIXED(112, TTTypeEnum.FIXED, 1, "合同暂定价定价"),
    PRICE_RESULT(113, TTTypeEnum.PRICE, 2, "合同点价结果录入"),
    TRANSFER_PART(121, TTTypeEnum.TRANSFER, 1, "合同转月（部分）"),
    TRANSFER_ALL(122, TTTypeEnum.TRANSFER, 2, "合同转月（全部）"),
    TRANSFER_RESULT(123, TTTypeEnum.TRANSFER, 3, "合同转月结果录入"),
    REVERSE_PRICE_PART(131, TTTypeEnum.REVERSE_PRICE, 1, "合同反点价（部分）"),
    REVERSE_PRICE_ALL(132, TTTypeEnum.REVERSE_PRICE, 2, "合同反点价（全部）"),
    STRUCTURE_PRICE(141, TTTypeEnum.STRUCTURE_PRICE, 1, "新增结构化定价合同"),
    BUYBACK(151, TTTypeEnum.BUYBACK, 1, "合同回购"),
    WASHOUT(161, TTTypeEnum.WASHOUT, 1, "解约定赔"),
    PUT_BACK(171, TTTypeEnum.PUT_BACK, 1, "合同回售"),
    CLOSED(191, TTTypeEnum.CLOSED, 1, "合同关闭"),
    INVALID(181, TTTypeEnum.INVALID, 1, "合同作废"),
    CONTRACT_CANCEL(182, TTTypeEnum.CONTRACT_CANCEL, 1, "合同取消"),
    // 为兼容工作流
    EQUITY_CHANGE(201, TTTypeEnum.EQUITY_CHANGE, 1, "权益变更"),
    ALLOCATE(202, TTTypeEnum.ALLOCATE, 1, "分配"),
    ASSIGN(203, TTTypeEnum.ASSIGN, 1, "转让"),
    WARRANT_PURCHASE(204, TTTypeEnum.NEW, 1, "新增仓单采购"),
    WRITE_OFF_A(205, TTTypeEnum.WRITE_OFF, 1, "注销（不修改货品和含税单价）"),
    WRITE_OFF_B(206, TTTypeEnum.WRITE_OFF, 1, "注销不修改提货方且修改（货品或含税单价）"),
    WRITE_OFF_C(207, TTTypeEnum.WRITE_OFF, 1, "注销同时修改提货方，修改（货品或含税单价）"),
    WRITE_OFF_OM_A(208, TTTypeEnum.WRITE_OFF, 1, "豆二注销（不修改提货方）"),
    WRITE_OFF_OM_B(209, TTTypeEnum.WRITE_OFF, 1, "豆二注销（修改提货方）"),
    WARRANT_WITHDRAW(210, TTTypeEnum.WARRANT_WITHDRAW, 1, "仓单注销撤回"),
    ;

    int value;
    TTTypeEnum contractBisinessType;
    /**
     * 相同TTType的不同情况
     * 默认为1
     * 修改(客户主体变化）、拆分(客户主体变SalesContractAddTTDTO化）、转月（全部）、反点价（全部）为2
     */
    int tradeCondition;
    String desc;

    ContractTradeTypeEnum(int value, TTTypeEnum contractBisinessType, int tradeCondition, String desc) {
        this.value = value;
        this.contractBisinessType = contractBisinessType;
        this.tradeCondition = tradeCondition;
        this.desc = desc;
    }


    public TTTypeEnum getTTType() {
        return contractBisinessType;
    }

    public static ContractTradeTypeEnum getByValue(Integer value) {
        if (null == value) return UNKNOWN;
        for (ContractTradeTypeEnum en : ContractTradeTypeEnum.values()) {
            if (en.getValue() == value) {
                return en;
            }
        }
        return ContractTradeTypeEnum.UNKNOWN;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static ContractTradeTypeEnum getByTTType(int tttype, int condition) {
        ContractTradeTypeEnum rtn = ContractTradeTypeEnum.UNKNOWN;
        TTTypeEnum ttTypeEnum = TTTypeEnum.getByType(tttype);
        for (ContractTradeTypeEnum en : ContractTradeTypeEnum.values()) {
            if (en.getContractBisinessType() == ttTypeEnum) {
                rtn = en;
                if (en.getTradeCondition() == condition) {
                    return en;
                }
            }
        }
        return rtn;
    }

    public static List<ContractTradeTypeEnum> getByTTType(int tttype) {
        List<ContractTradeTypeEnum> list = new ArrayList<>();
        for (ContractTradeTypeEnum en : ContractTradeTypeEnum.values()) {
            if (en.getContractBisinessType().getType() == tttype) {
                list.add(en);
            }
        }
        return list;
    }


    public static TTTypeEnum getTTType(int value) {
        return getByValue(value).getTTType();
    }

    public static String getTradeTypeInfo(List<Integer> salesTypeList) {
        List<String> tradeTypeNameList = new ArrayList<>();
        for (Integer tradeType : salesTypeList) {
            if (0 == tradeType) {
                tradeTypeNameList.add("全部");
            } else {
                tradeTypeNameList.add(ContractTradeTypeEnum.getDescByValue(tradeType));
            }
        }
        return StringUtils.join(tradeTypeNameList, ",");
    }

    /**
     * 数字合同-新增产生合同的操作类型
     *
     * @return
     */
    public static List<Integer> getNewTemplateProtocol() {
        return Arrays.asList(NEW.getValue(), REVISE_CHANGE_CUSTOMER.getValue(), SPLIT_NORMAL.getValue(), SPLIT_CHANGE_CUSTOMER.getValue(),
                TRANSFER_PART.getValue(), REVERSE_PRICE_PART.getValue(), REVERSE_PRICE_ALL.getValue(), STRUCTURE_PRICE.getValue(), BUYBACK.getValue());
    }

    public static List<Integer> getNeedCustomerProtocolTradeList() {
        return Arrays.asList(ContractTradeTypeEnum.NEW.getValue(), ContractTradeTypeEnum.REVISE_NORMAL.getValue(),
                ContractTradeTypeEnum.REVISE_CHANGE_CUSTOMER.getValue(), ContractTradeTypeEnum.TRANSFER_PART.getValue(),
                ContractTradeTypeEnum.REVERSE_PRICE_PART.getValue(), ContractTradeTypeEnum.REVERSE_PRICE_ALL.getValue(),
                ContractTradeTypeEnum.BUYBACK.getValue(), ContractTradeTypeEnum.PUT_BACK.getValue(),
                ContractTradeTypeEnum.ALLOCATE.getValue(), ContractTradeTypeEnum.ASSIGN.getValue());
    }

    /**
     * 需要数字合同-拼接合同模板的操作类型
     * (部分转月、全部反点价、部分反点价)
     */
    public static List<Integer> needTemplateBind() {
        return Arrays.asList(TRANSFER_PART.getValue(), REVERSE_PRICE_PART.getValue(), REVERSE_PRICE_ALL.getValue());
    }

    /**
     * 不需要出具合同模板的类型
     *
     * @return
     */
    public static List<Integer> getNoHuskyTemplateTradeList() {
        return Arrays.asList(ALLOCATE.getValue(), WRITE_OFF_A.getValue(), WRITE_OFF_B.getValue(), WRITE_OFF_OM_A.getValue(), WRITE_OFF_OM_B.getValue());
    }
}
