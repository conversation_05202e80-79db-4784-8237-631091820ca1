package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2024-11-19 16:07
 **/
@AllArgsConstructor
@Getter
public enum DepositPaymentType {
    /**
     * 交割保证金付款方式
     */
    LETTER_OF_GUARANTEE(1, "保函"),
    CASH(2, "现金");

    Integer value;
    String desc;

    public static DepositPaymentType getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(depositPaymentType -> value.equals(depositPaymentType.getValue()))
                .findFirst()
                .orElse(LETTER_OF_GUARANTEE);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
