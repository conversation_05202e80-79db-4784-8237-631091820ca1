package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 15:59
 **/
@AllArgsConstructor
@Getter
public enum ProtocolTypeEnum {
    CONTRACT("CONTRACT", 0, "大合同"),
    ORDER("ORDER", 1, "订单"),
    AGREEMENT("AGREEMENT", 2, "补充协议"),
    ;
    String value;
    Integer code;
    String desc;

    public static ProtocolTypeEnum getByValue(String value) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> value.equals(protocolTypeEnum.getValue()))
                .findFirst()
                .orElse(AGREEMENT);
    }

    public static String getDescByValue(String value) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> value.equals(protocolTypeEnum.getValue()))
                .findFirst()
                .orElse(AGREEMENT)
                .getDesc();
    }

    public static String getDescByCode(Integer code) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> code.equals(protocolTypeEnum.getCode()))
                .findFirst()
                .orElse(CONTRACT)
                .getDesc();
    }

    public static ProtocolTypeEnum getByCode(Integer code) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> code.equals(protocolTypeEnum.getCode()))
                .findFirst()
                .orElse(AGREEMENT);
    }

    public static String getProtocolTypeInfo(List<String> protocolTypeList) {
        List<String> protocolTypeNameList = new ArrayList<>();
        for (String protocolType : protocolTypeList) {
            if ("All".equals(protocolType)) {
                protocolTypeNameList.add("全部");
            } else {
                protocolTypeNameList.add(ProtocolTypeEnum.getDescByValue(protocolType));
            }
        }
        return StringUtils.join(protocolTypeNameList, ",");
    }

}
