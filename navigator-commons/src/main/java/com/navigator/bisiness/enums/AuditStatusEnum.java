package com.navigator.bisiness.enums;

import lombok.Getter;

/**
 * 申请单审核状态
 *
 * <AUTHOR>
 * @date 2022/1/5 11:57
 */
@Getter
public enum AuditStatusEnum {

    /**
     * 审核状态
     */
    AUDIT(1, "审核中"),
    PASS(2, "通过"),
    REJECT(3, "驳回"),

    ;

    int value;
    String description;

    AuditStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public static AuditStatusEnum getByValue(int value) {
        for (AuditStatusEnum en : AuditStatusEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return AuditStatusEnum.AUDIT;
    }

}
