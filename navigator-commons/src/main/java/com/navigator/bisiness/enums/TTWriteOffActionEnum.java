package com.navigator.bisiness.enums;

import lombok.Getter;

/**
 * 仓单注销时tt的动作
 *  1 修改TT 2 销售提货合同新增TT 3 仓单采购合同新增TT
 */
@Getter
public enum TTWriteOffActionEnum {

    REVISE(1, "修改TT"),
    DELIVERY_ADD(2, "提货合同新增TT"),
    PURCHASE_ADD(3, "仓单采购合同新增TT"),
    TRADE_ADD(4, "仓单贸易合同新增TT"),
    ;

    final Integer value;
    final String description;

    TTWriteOffActionEnum(Integer value, String description) {
        this.value = value;
        this.description = description;
    }


}
