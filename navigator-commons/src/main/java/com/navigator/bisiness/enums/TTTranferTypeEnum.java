package com.navigator.bisiness.enums;

import lombok.Getter;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/25 16:23
 */

@Getter
public enum TTTranferTypeEnum {

    //TODO NEO NANA  1、命名问题  2、PriceType？

    TRANSFER_MONTH(1, "转月"),
    PART_TRANSFER_MONTH(2, "部分转月"),
    REVERSE_PRICING(3, "反点价"),
    PART_REVERSE_PRICING(4, "部分反点价"),
    ;

    Integer value;
    String desc;

    TTTranferTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static TTTranferTypeEnum getByValue(Integer value) {
        for (TTTranferTypeEnum en : TTTranferTypeEnum.values()) {
            if (value.equals(en.getValue())) {
                return en;
            }
        }
        return TTTranferTypeEnum.TRANSFER_MONTH;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
