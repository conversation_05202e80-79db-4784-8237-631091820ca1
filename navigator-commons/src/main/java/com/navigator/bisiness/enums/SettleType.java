package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2024-11-19 16:07
 **/
@AllArgsConstructor
@Getter
public enum SettleType {
    /**
     * 结算方式
     */
    EXCHANGE(1, "交易所结算"),
    //    FT("FT", "期货"),
    AUTO_SELF(2, "自行结算");

    Integer value;
    String desc;

    public static SettleType getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(settleType -> value.equals(settleType.getValue()))
                .findFirst()
                .orElse(EXCHANGE);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
