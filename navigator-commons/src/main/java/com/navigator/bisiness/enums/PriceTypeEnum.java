package com.navigator.bisiness.enums;

import lombok.Getter;

/**
 * 点价类型（点价/转月/反点价）
 *
 * <AUTHOR>
 * @date 2022/1/5 11:43
 */
@Getter
public enum PriceTypeEnum {

    PRICING(1, "点价"),
    TRANSFER_MONTH(2, "转月"),
    REVERSE_PRICING(3, "反点价"),
    CONFIRM_PRICED(4, "定价"),
    STRUCTURE_PRICING(5, "结构化定价"),
    STRATEGIC_ARBITRAGE(6, "套利"),
    ;

    int value;
    String desc;

    PriceTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static PriceTypeEnum getByValue(int value) {
        for (PriceTypeEnum en : PriceTypeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return PriceTypeEnum.PRICING;
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }
}
