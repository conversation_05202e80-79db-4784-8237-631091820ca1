package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum TTTypeEnum {

    /**
     * TT类型
     * {@link ContractTradeTypeEnum}
     */
    UNKNOWN(0, "UNKNOWN", "未知"),
    NEW(1, "ADD", "合同新增"),
    REVISE(2, "REVISE", "合同修改"),
    SPLIT(3, "SPLIT", "合同拆分"),
    TRANSFER(4, "TRANSFER", "合同转月"),
    REVERSE_PRICE(5, "REVERSE_PRICE", "合同反点价"),
    PRICE(6, "PRICE", "合同点价"),
    STRUCTURE_PRICE(7, "STRUCTURE_PRICE", "合同结构化定价"),
    BUYBACK(8, "BUYBACK", "合同回购"),
    WASHOUT(9, "WASHOUT", "合同解约定赔"),
    PUT_BACK(10, "PUT_BACK", "合同回售"),
    CLOSED(11, "CLOSED", "合同关闭"),
    FIXED(12, "FIXED", "合同暂定价定价"),
    INVALID(13, "INVALID", "合同作废"),
    EQUITY_CHANGE(14, "EQUITY_CHANGE", "权益变更"),
    ALLOCATE(15, "ALLOCATE", "分配"),
    ASSIGN(16, "ASSIGN", "转让"),
    WRITE_OFF(18, "WRITE_OFF", "注销"),
    WRITE_OFF_OM(19, "WRITE_OFF_OM", "豆二注销"),
    CONTRACT_CANCEL(20, "CONTRACT_CANCEL", "合同取消"),
    WARRANT_WITHDRAW(21, "WARRANT_WITHDRAW", "仓单撤回"),
    ;

    private Integer type;
    private String code;
    private String desc;

    public static TTTypeEnum getByType(Integer type) {
        for (TTTypeEnum ttTypeEnum : TTTypeEnum.values()) {
            if (ttTypeEnum.getType().equals(type)) {
                return ttTypeEnum;
            }
        }
        return UNKNOWN;
    }

    public static String getDescByValue(Integer type) {
        return getByType(type).getDesc();
    }

    public static String getCodeByValue(Integer type) {
        return getByType(type).getCode();
    }

    /**
     * TT数据存储在dbt_tt_add表中的TT类型
     * 【新增、回购、解约定赔、关闭、作废】
     * @return
     */
    public static List<Integer> getTtAddTypeList() {
        return Arrays.asList(TTTypeEnum.NEW.getType(), TTTypeEnum.BUYBACK.getType(), TTTypeEnum.WASHOUT.getType(), TTTypeEnum.CLOSED.getType(), TTTypeEnum.INVALID.getType(),TTTypeEnum.ALLOCATE.getType(),TTTypeEnum.ASSIGN.getType());
    }

    /**
     * 获取需要从合同取值的字段
     * [点价、转月、反点价、暂定价定价、结构化定价]
     * @return
     */
    public static List<Integer> needContractInfoTtTypeList() {
        return Arrays.asList(TTTypeEnum.TRANSFER.getType(), TTTypeEnum.REVERSE_PRICE.getType(), TTTypeEnum.PRICE.getType(), TTTypeEnum.STRUCTURE_PRICE.getType(), TTTypeEnum.FIXED.getType());
    }

    /**
     * 仓单合同出具（除了点价、全部转月，解约定赔、关闭、作废），加载条件为大合同属性
     * @return
     */
    public static List<Integer> needContractToWarrantTemplate(){
        return Arrays.asList(TTTypeEnum.NEW.getType(),TTTypeEnum.TRANSFER.getType(),TTTypeEnum.BUYBACK.getType(),TTTypeEnum.ALLOCATE.getType(),
                TTTypeEnum.ASSIGN.getType(),TTTypeEnum.WRITE_OFF.getType(),TTTypeEnum.WRITE_OFF_OM.getType());
    }
}
