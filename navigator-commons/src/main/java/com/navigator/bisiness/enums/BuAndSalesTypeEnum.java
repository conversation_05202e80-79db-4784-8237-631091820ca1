package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum BuAndSalesTypeEnum {
    ST_PURCHASE("ST_1", "现货采购"),
    ST_SALES("ST_2", "现货销售"),
    WT_PURCHASE("WT_1", "仓单采购"),
    WT_SALES("WT_2", "仓单销售");

    String value;
    String desc;

    public static BuAndSalesTypeEnum getByValue(String value) {
        for (BuAndSalesTypeEnum buAndSalesTypeEnum : BuAndSalesTypeEnum.values()) {
            if (buAndSalesTypeEnum.getValue().equals(value)) {
                return buAndSalesTypeEnum;
            }
        }
        return null;
    }

}
