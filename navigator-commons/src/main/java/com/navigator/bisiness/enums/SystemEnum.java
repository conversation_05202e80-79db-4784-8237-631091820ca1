package com.navigator.bisiness.enums;

import lombok.Getter;

/**
 * <p>
 * 系统 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-14
 */
@Getter
public enum SystemEnum {
    /**
     * 系统类型
     */
    MAGELLAN(1, "magellan", -9001, "麦哲伦", "MGL"),
    COLUMBUS(2, "columbus", -9002, "哥伦布", "CLB"),
    PIGE<PERSON>(3, "pigeon", -9003, "PIGEON(LNG对接)", ""),
    SPARROW(4, "sparrow", -9004, "SPARROW(三方对接)", ""),
    ACTIVITI(5, "activiti", -9005, "ACTIVITI（工作流）", ""),
    JOB(6, "job", -9006, "定时处理器", ""),
    MDM(10, "mdm", -9010, "MDM主数据", ""),
    LKG(11, "linkinage", -9011, "Linkinage", ""),
    YQQ(12, "signit", -9012, "易企签", ""),
    CUCKOO(13, "CUCKOO", -9013, "CUCKOO(ATLAS对接)", ""),
    ATLAS(14, "ATLAS_CN", -9014, "atlas", ""),
    ;
    Integer value;
    String name;
    int employId;
    String description;
    String shortName;

    SystemEnum(int value, String name, int employId, String description, String shortName) {
        this.value = value;
        this.name = name;
        this.employId = employId;
        this.description = description;
        this.shortName = shortName;
    }

    public static SystemEnum getByValue(Integer value) {
        if (null == value) {
            return MAGELLAN;
        }
        for (SystemEnum en : SystemEnum.values()) {
            if (en.getValue().equals(value)) {
                return en;
            }
        }
        return MAGELLAN;
    }

    /**
     * 是否等于某个值
     *
     * @param value
     * @return
     */
    public boolean equals(Integer value) {
        return this.getValue().equals(value);
    }
}
