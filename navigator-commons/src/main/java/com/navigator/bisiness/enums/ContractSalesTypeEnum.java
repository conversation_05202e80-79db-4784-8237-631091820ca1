package com.navigator.bisiness.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 合同销售类型 枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Getter
public enum ContractSalesTypeEnum {
    /**
     * 合同销售类型
     */
    PURCHASE(1, "采购", "P"),
    SALES(2, "销售", "S");
    int value;
    String description;
    String directCode;

    ContractSalesTypeEnum(int value, String description, String directCode) {
        this.value = value;
        this.description = description;
        this.directCode = directCode;
    }

    public static ContractSalesTypeEnum getByValue(Integer value) {
        if (null == value) return ContractSalesTypeEnum.PURCHASE;
        for (ContractSalesTypeEnum contractPurchaseTypeEnum : ContractSalesTypeEnum.values()) {
            if (contractPurchaseTypeEnum.getValue() == value) {
                return contractPurchaseTypeEnum;
            }
        }
        return ContractSalesTypeEnum.PURCHASE;
    }

    public static ContractSalesTypeEnum getByDesc(String desc) {
        if (StringUtils.isBlank(desc)){
            return ContractSalesTypeEnum.SALES;
        }
        for (ContractSalesTypeEnum contractPurchaseTypeEnum : ContractSalesTypeEnum.values()) {
            if (contractPurchaseTypeEnum.getDesc().equals(desc)) {
                return contractPurchaseTypeEnum;
            }
        }
        return ContractSalesTypeEnum.SALES;
    }

    public static String getDescByValue(Integer salesType) {
        return getByValue(salesType).getDescription();
    }

    public String getDesc() {
        return this.description;
    }

    public static String getSalesTypeInfo(List<Integer> salesTypeList) {
        List<String> salesTypeNameList = new ArrayList<>();
        for (Integer salesType : salesTypeList) {
            if (0 == salesType) {
                salesTypeNameList.add("全部");
            } else {
                salesTypeNameList.add(ContractSalesTypeEnum.getDescByValue(salesType));
            }
        }
        return StringUtils.join(salesTypeNameList, ",");
    }
}
