package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 15:30
 **/
@AllArgsConstructor
@Getter
public enum LogicRelationEnum {
    AND("&&", "且"),
    OR("||", "或"),
    ;
    String code;
    String description;

    public static LogicRelationEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(logicRelationEnum -> StringUtils.equals(code, logicRelationEnum.getCode()))
                .findFirst()
                .orElse(AND);
    }
}
