package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContractNatureEnum {

    /**
     * 合同形式
     * {@link ContractTradeTypeEnum}
     */
    SPOT_TRADE(1, "SPOT_TRADE", "现货贸易合同"),
    WAREHOUSE_TRADE(2, "WAREHOUSE_TRADE", "仓单贸易合同"),
    WAREHOUSE_DELIVERY(3, "WAREHOUSE_DELIVERY", "仓单提货合同"),
    WAREHOUSE_CARGO_RIGHTS(4, "WAREHOUSE_CARGO_RIGHTS", "仓单提货密码/仓单货权合同"),
    ;

    private Integer value;
    private String code;
    private String desc;

    public static ContractNatureEnum getValue(Integer value) {
        for (ContractNatureEnum contractNatureEnum : ContractNatureEnum.values()) {
            if (contractNatureEnum.getValue().equals(value)) {
                return contractNatureEnum;
            }
        }
        return SPOT_TRADE;
    }

    public static String getDescByValue(Integer value) {
        return getValue(value).getDesc();
    }

    public static String getCodeByValue(Integer value) {
        return getValue(value).getCode();
    }

}
