package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-05 11:54
 **/
@AllArgsConstructor
@Getter
public enum PatternRelationEnum {
    EQUALS("==", "等于"),
    NOT_EQUALS("!=", "不等于"),
    GREATER(">", "大于"),
    GREATER_EQUALS(">=", "大于等于"),
    LESS("<", "小于"),
    LESS_EQUALS("<=", "小于等于"),
    CONTAINS("contains", "包含"),
    NOT_CONTAINS("not contains", "不包含"),

    ;
    String code;
    String description;

    public static PatternRelationEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(patternRelationEnum -> StringUtils.equals(code, patternRelationEnum.getCode()))
                .findFirst()
                .orElse(EQUALS);
    }

    public static String getDescByCode(String code) {
        return getByCode(code).getDescription();
    }

    public static List<String> getContainRelation() {
        return Arrays.asList(CONTAINS.getCode(), NOT_CONTAINS.getCode());
    }

}
