package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-03 15:59
 **/
@AllArgsConstructor
@Getter
public enum ValueTypeEnum {
    INTEGER(1, "Integer", "整数"),
    STRING(2, "String", "字符串"),
    ;
    Integer value;
    String code;
    String description;

    public static ValueTypeEnum getByValue(Integer value) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> value.equals(protocolTypeEnum.getValue()))
                .findFirst()
                .orElse(INTEGER);
    }

    public static ValueTypeEnum getByCode(String code) {
        return Arrays.stream(values())
                .filter(protocolTypeEnum -> code.equals(protocolTypeEnum.getCode()))
                .findFirst()
                .orElse(INTEGER);
    }
}
