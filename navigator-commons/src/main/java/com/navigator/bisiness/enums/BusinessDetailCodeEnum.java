package com.navigator.bisiness.enums;

import lombok.Getter;

@Getter
public enum BusinessDetailCodeEnum {

    DEFAULT("10001", "未知操作"),
    C_EMPLOY_ROLE_EDIT("10002", "哥伦布用户角色修改"),
    EMPLOY_ROLE_EDIT("20001", "系统用户角色修改"),
    UPDATE_CONTRACT_TEMPLATE("20010", "修改合同模板属性"),


    ;

    String value;
    String description;

    BusinessDetailCodeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static BusinessDetailCodeEnum getByValue(String value) {
        for (BusinessDetailCodeEnum en : BusinessDetailCodeEnum.values()) {
            if (value == en.getValue()) {
                return en;
            }
        }
        return DEFAULT;
    }
}
