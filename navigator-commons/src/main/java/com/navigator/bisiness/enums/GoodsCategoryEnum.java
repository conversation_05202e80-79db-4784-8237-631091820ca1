package com.navigator.bisiness.enums;

import com.navigator.common.convertor.IdNameConverter;
import com.navigator.common.convertor.IdNameType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/11/29 10:20
 */

@Getter
@AllArgsConstructor
public enum GoodsCategoryEnum {
    //TODO NEO 最终的值要跟LDC确认，与Linkinage保持一致
    OSM(10, 0, "油脂豆粕", "SMO", 0, "02", ""),
    OSM_MEAL(11, 10, "豆粕", "SBM", 1, "02", "M"),
    OSM_OIL(12, 10, "豆油", "SBO", 2, "03", "Y"),
    GRAIN(20, 0, "谷物", "CROP", 0, "0", ""),
    OSM_CORN(21, 20, "玉米", "CORN", 0, "0", "BP"),
    GRAIN_SORGO(22, 20, "高粱", "BCO", 0, "0", "R"),

    SPECIAL_OIL(25, 10, "特种油脂", "SO", 2, "03", "P"),
    SPECIFIC_PROTEIN(26, 10, "特种蛋白", "SP", 0, "0", "OI"),
    GRAIN_CORN(27, 10, "副产品", "BP", 2, "03", "BP"),
    SOYBEAN2(28, 10, "豆二", "SBNB", 2, "03", "B"),

    SUGAR(30, 0, "糖", "SUG", 0, "0", "U"),
    COTTON(40, 0, "棉花", "COT", 0, "0", "T"),
    COFFEE(50, 0, "咖啡", "COF", 0, "0", "F");

    private Integer value;
    private Integer parentValue;
    private String desc;
    private String code;
    private Integer lkgTransSequenc;
    private String lkgCommodityClass;
    private String lkgFutureSymbol;


    /**
     * 获取所有业务平台列表
     *
     * @return
     */
    public static List<GoodsCategoryEnum> getPlatformList() {
        List<GoodsCategoryEnum> list = new ArrayList<>();
        for (GoodsCategoryEnum en : GoodsCategoryEnum.values()) {
            if (en.parentValue > 0) {
                list.add(en);
            }
        }

        return list;
    }

    public static List<GoodsCategoryEnum> getGoodsCategoryList() {
        List<GoodsCategoryEnum> list = new ArrayList<>();

        //TODO NEO

        return list;
    }

    public static List<GoodsCategoryEnum> getGoodsCategoryList(String categoryName) {
        List<GoodsCategoryEnum> list = new ArrayList<>();

        //TODO NEO
        //获取所有分类，包括子分类

        return list;
    }

    public static GoodsCategoryEnum getGoodsCategory(String categoryName) {

        for (GoodsCategoryEnum en : GoodsCategoryEnum.values()) {
            if (categoryName == en.getDesc()) {
                return en;
            }
        }
        //默认豆粕
        return GoodsCategoryEnum.OSM_MEAL;
    }

    public static Integer getValueByCode(String categoryCode) {
        for (GoodsCategoryEnum en : GoodsCategoryEnum.values()) {
            if (categoryCode.equals(en.getCode())) {
                return en.getValue();
            }
        }
        //默认豆粕
        return GoodsCategoryEnum.OSM_MEAL.getValue();
    }

    public static GoodsCategoryEnum getByValue(Integer categoryValue) {
        //
        //默认豆粕
        return Arrays.stream(values())
                .filter(goodsCategoryEnum -> Objects.equals(categoryValue, goodsCategoryEnum.getValue()))
                .findFirst()
                .orElse(OSM_MEAL);
    }

    public static GoodsCategoryEnum getByCode(String categoryCode) {
        //默认豆粕
        return Arrays.stream(values())
                .filter(goodsCategoryEnum -> Objects.equals(categoryCode, goodsCategoryEnum.getCode()))
                .findFirst()
                .orElse(OSM_MEAL);
    }

    public static GoodsCategoryEnum getByDesc(String desc) {
        //默认豆粕
        return Arrays.stream(values())
                .filter(goodsCategoryEnum -> Objects.equals(desc, goodsCategoryEnum.getDesc()))
                .findFirst()
                .orElse(OSM_MEAL);
    }

    public static Integer getValueByDesc(String desc) {
        //默认豆粕
        return getByDesc(desc.trim()).getValue();
    }

    public static GoodsCategoryEnum getByLkgFutureSymbol(String lkgFutureSymbol) {
        //默认豆粕
        return Arrays.stream(values())
                .filter(goodsCategoryEnum -> Objects.equals(lkgFutureSymbol, goodsCategoryEnum.getLkgFutureSymbol()))
                .findFirst()
                .orElse(OSM_MEAL);
    }

    public static String getDescByValue(Integer value) {
        return getByValue(value).getDesc();
    }

    public static String getDesc(Integer categoryValue) {
        return IdNameConverter.getName(IdNameType.category_id_name, categoryValue.toString());
    }

    public static String getCategoryInfo(List<Integer> categoryValueList) {
        List<String> categoryNameList = new ArrayList<>();
        for (Integer categoryValue : categoryValueList) {
            if (0 == categoryValue) {
                categoryNameList.add("全部");
            } else {
                categoryNameList.add(GoodsCategoryEnum.getDescByValue(categoryValue));
            }
        }
        return StringUtils.join(categoryNameList, ",");
    }
}
