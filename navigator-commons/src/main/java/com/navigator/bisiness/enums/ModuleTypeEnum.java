package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
public enum ModuleTypeEnum {
    /**
     * 模块状态
     */
    DEFAULT("default", "默认"),
    USER("user", "用户模块"),
    ROLE("role", "角色模块"),
    POWER("power", "权限模块"),
    MENU("menu", "菜单模块"),
    USER_TAG("user_tag", "标签模块"),
    USER_ROLE("user_role", "用户角色模块"),
    MENU_POWER("menu_power", "菜单权限模块"),
    DEPARTMENT("department", "部门模块"),

    TRADE_TICKET("TT", "TT模块"),
    CONTRACT("contract", "contract模块"),
    PRICE("price", "点价模块"),
    CONTRACT_SIGN("contract_sign", "协议模块"),
    LOGIN("login", "登录模块"),
    MSG("msg","信息模块"),
    MLOG("mlog","magellan日志模块"),
    CLOG("clog","columbus日志模块"),
    CUSTOMER("CUSTOMER","客户模块"),
    DELIVERY("delivery","提货模块"),
    CONTRACT_EQUITY("contract_equity","权益变更模块"),

    LOA_APPROVAL("loa_approval","LOA审批"),

    ;

    private String module;
    private String desc;

    public static ModuleTypeEnum getModule(String module) {
        return Arrays.stream(values())
                .filter(moduleType -> StringUtils.equals(module, moduleType.getModule()))
                .findFirst()
                .orElse(DEFAULT);
    }

    public static List<String> getIgnoreLotOperaionType() {
        return Arrays.asList(ModuleTypeEnum.USER.getModule());
    }

    public static String getDescByValue(String bizModule) {
        return getModule(bizModule).getDesc();
    }
}
