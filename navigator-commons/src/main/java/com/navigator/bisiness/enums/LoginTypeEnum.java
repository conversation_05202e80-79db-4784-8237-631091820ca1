package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录方式枚举
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-30
 */
@Getter
@AllArgsConstructor
public enum LoginTypeEnum {

    /**
     * 邮箱密码登录
     */
    EMAIL_PASSWORD(1, "邮箱密码"),

    /**
     * 手机验证码登录
     */
    PHONE_CODE(2, "手机验证码"),

    /**
     * AAD登录
     */
    AAD_LOGIN(3, "AAD登录");

    /**
     * 登录方式代码
     */
    private final Integer code;

    /**
     * 登录方式描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 登录方式代码
     * @return 对应的枚举，如果不存在则返回null
     */
    public static LoginTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (LoginTypeEnum loginType : values()) {
            if (loginType.getCode().equals(code)) {
                return loginType;
            }
        }
        return null;
    }

    /**
     * 根据代码获取描述
     *
     * @param code 登录方式代码
     * @return 对应的描述，如果不存在则返回"未知"
     */
    public static String getDescriptionByCode(Integer code) {
        LoginTypeEnum loginType = getByCode(code);
        return loginType != null ? loginType.getDescription() : "未知";
    }

}
