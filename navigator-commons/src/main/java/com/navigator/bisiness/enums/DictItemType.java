package com.navigator.bisiness.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-06 14:00
 **/
@AllArgsConstructor
@Getter
public enum DictItemType {
    SIGN_TYPE("signType", "合同终止方式"),
    DESTINATION_CODE("destinationCode", "目的港/目的地"),
    layout("layOut", "布局样式"),
    ENTERPRISE_CODE("enterpriseCode", "特殊模板集团客户"),
    ;
    String value;
    String desc;

    public static DictItemType getByValue(String value) {
        return Arrays.stream(values())
                .filter(dictItemType -> StringUtils.equals(value, dictItemType.getValue()))
                .findFirst()
                .orElse(SIGN_TYPE);
    }

    public static String getDescByValue(String value) {
        return Arrays.stream(values())
                .filter(dictItemType -> StringUtils.equals(value, dictItemType.getValue()))
                .findFirst()
                .orElse(SIGN_TYPE).getDesc();
    }

}
