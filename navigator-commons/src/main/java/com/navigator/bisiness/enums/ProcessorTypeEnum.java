package com.navigator.bisiness.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ProcessorTypeEnum {
    /**
     * 豆粕
     * TT/合同/协议-处理类型
     */
    SBM_S_ADD("SBM_S_TT_ADD", "SBM_S_CONTRACT_ADD", "SBM_S_SIGN_ADD","PROC_DEF_SC_ADD", "豆粕销售合同新增"),
    SBM_S_REVISE("SBM_S_TT_REVISE", "SBM_S_CONTRACT_REVISE", "SBM_S_SIGN_REVISE","PROC_DEF_SC_MODIFY", "豆粕销售合同拆分"),
    SBM_S_SPLIT("SBM_S_TT_SPLIT", "SBM_S_CONTRACT_SPLIT", "SBM_S_SIGN_SPLIT", "PROC_DEF_SC_MODIFY","豆粕销售合同修改"),
    SBM_S_TRANSFER("SBM_S_TT_TRANSFER", "SBM_S_CONTRACT_TRANSFER", "SBM_S_SIGN_TRANSFER", "PROC_DEF_SC_PRICE","豆粕销售合同转月"),
    SBM_S_PRICE("SBM_S_TT_PRICE", "SBM_S_CONTRACT_PRICE", "SBM_S_SIGN_PRICE", "PROC_DEF_SC_PRICE","豆粕销售合同点价"),
    SBM_S_FIXED("SBM_S_TT_FIXED", "SBM_S_CONTRACT_FIXED", "SBM_S_SIGN_FIXED", "PROC_DEF_SC_FIXED","豆粕销售合同暂定价定价"),
    SBM_S_REVERSE_PRICE("SBM_S_TT_REVERSE_PRICE", "SBM_S_CONTRACT_REVERSE_PRICE", "SBM_S_SIGN_REVERSE_PRICE","PROC_DEF_SC_PRICE", "豆粕销售合同反点价"),
    SBM_S_STRUCTURE_PRICE("SBM_S_TT_STRUCTURE_PRICE", "SBM_S_CONTRACT_STRUCTURE_PRICE", "SBM_S_SIGN_STRUCTURE_PRICE", "PROC_DEF_SC_STRUCTURE_PRICE","豆粕销售合同结构化定价"),
    SBM_S_WASHOUT("SBM_S_TT_WASHOUT", "SBM_S_CONTRACT_WASHOUT", "SBM_S_SIGN_WASHOUT", "PROC_DEF_SC_WASHOUT","豆粕销售解约定赔"),
    SBM_S_CLOSED("SBM_S_TT_CLOSED", "SBM_S_CONTRACT_CLOSED", "SBM_S_SIGN_CLOSED", "PROC_DEF_SC_CLOSED","豆粕销售合同关闭"),
    SBM_S_INVALID("SBM_S_TT_INVALID", "SBM_S_CONTRACT_INVALID", "SBM_S_SIGN_INVALID", "PROC_DEF_SC_CLOSED","豆粕销售合同作废"),
    SBM_S_EQUITY("SBM_S_TT_EQUITY", "SBM_S_CONTRACT_EQUITY", "SBM_S_SIGN_EQUITY", "PROC_DEF_SC_EQUITY","豆粕销售权益变更审批"),


    SBM_P_BUYBACK("SBM_P_TT_BUYBACK", "SBM_P_CONTRACT_BUYBACK", "SBM_P_SIGN_BUYBACK", "PROC_DEF_SC_BUYBACK","豆粕销售合同回购"),
    SBM_P_ADD("SBM_P_TT_ADD", "SBM_P_CONTRACT_ADD", "SBM_P_SIGN_ADD", "PROC_DEF_PC_ADD","豆粕采购合同新增"),
    SBM_P_REVISE("SBM_P_TT_REVISE", "SBM_P_CONTRACT_REVISE", "SBM_P_SIGN_REVISE","PROC_DEF_PC_MODIFY", "豆粕采购合同拆分"),
    SBM_P_SPLIT("SBM_P_TT_SPLIT", "SBM_P_CONTRACT_SPLIT", "SBM_P_SIGN_SPLIT", "PROC_DEF_PC_MODIFY","豆粕采购合同修改"),
    SBM_P_TRANSFER("SBM_P_TT_TRANSFER", "SBM_P_CONTRACT_TRANSFER", "SBM_P_SIGN_TRANSFER", "PROC_DEF_PC_PRICE","豆粕采购合同转月"),
    SBM_P_PRICE("SBM_P_TT_PRICE", "SBM_P_CONTRACT_PRICE", "SBM_P_SIGN_PRICE", "PROC_DEF_PC_PRICE","豆粕采购合同点价"),
    SBM_P_FIXED("SBM_P_TT_FIXED", "SBM_P_CONTRACT_FIXED", "SBM_P_SIGN_FIXED", "PROC_DEF_PC_FIXED","豆粕采购合同暂定价定价"),
    SBM_P_REVERSE_PRICE("SBM_P_TT_REVERSE_PRICE", "SBM_P_CONTRACT_REVERSE_PRICE", "SBM_P_SIGN_REVERSE_PRICE","PROC_DEF_PC_PRICE", "豆粕采购合同反点价"),
    SBM_P_STRUCTURE_PRICE("SBM_P_TT_STRUCTURE_PRICE", "SBM_P_CONTRACT_STRUCTURE_PRICE", "SBM_P_SIGN_STRUCTURE_PRICE", "PROC_DEF_PC_STRUCTURE_PRICE","豆粕采购合同结构化定价"),
    SBM_P_WASHOUT("SBM_P_TT_WASHOUT", "SBM_P_CONTRACT_WASHOUT", "SBM_P_SIGN_WASHOUT", "PROC_DEF_PC_WASHOUT","豆粕采购解约定赔"),
    SBM_P_CLOSED("SBM_P_TT_CLOSED", "SBM_P_CONTRACT_CLOSED", "SBM_P_SIGN_CLOSED", "PROC_DEF_PC_CLOSED","豆粕采购合同关闭"),
    SBM_P_INVALID("SBM_P_TT_INVALID", "SBM_P_CONTRACT_CINVALID", "SBM_P_SIGN_INVALID", "PROC_DEF_PC_CLOSED","豆粕采购合同作废"),


    /**
     * 豆油
     * TT/合同/协议-处理类型
     */
    SBO_S_ADD("SBO_S_TT_ADD", "SBO_S_CONTRACT_ADD", "SBO_S_SIGN_ADD","PROC_SBO_DEF_SC_ADD", "豆油销售合同新增"),
    SBO_S_REVISE("SBO_S_TT_REVISE", "SBO_S_CONTRACT_REVISE", "SBO_S_SIGN_REVISE","PROC_SBO_DEF_SC_MODIFY", "豆油销售合同拆分"),
    SBO_S_SPLIT("SBO_S_TT_SPLIT", "SBO_S_CONTRACT_SPLIT", "SBO_S_SIGN_SPLIT", "PROC_SBO_DEF_SC_MODIFY","豆油销售合同修改"),
    SBO_S_TRANSFER("SBO_S_TT_TRANSFER", "SBO_S_CONTRACT_TRANSFER", "SBO_S_SIGN_TRANSFER", "PROC_SBO_DEF_SC_PRICE","豆油销售合同转月"),
    SBO_S_PRICE("SBO_S_TT_PRICE", "SBO_S_CONTRACT_PRICE", "SBO_S_SIGN_PRICE", "PROC_SBO_DEF_SC_PRICE","豆油销售合同点价"),
    SBO_S_FIXED("SBO_S_TT_FIXED", "SBO_S_CONTRACT_FIXED", "SBO_S_SIGN_FIXED", "PROC_SBO_DEF_SC_FIXED","豆油销售合同暂定价定价"),
    SBO_S_REVERSE_PRICE("SBO_S_TT_REVERSE_PRICE", "SBO_S_CONTRACT_REVERSE_PRICE", "SBO_S_SIGN_REVERSE_PRICE","PROC_SBO_DEF_SC_PRICE", "豆油销售合同反点价"),
    SBO_S_STRUCTURE_PRICE("SBO_S_TT_STRUCTURE_PRICE", "SBO_S_CONTRACT_STRUCTURE_PRICE", "SBO_S_SIGN_STRUCTURE_PRICE", "PROC_SBO_DEF_SC_STRUCTURE_PRICE","豆油销售合同结构化定价"),
    SBO_S_WASHOUT("SBO_S_TT_WASHOUT", "SBO_S_CONTRACT_WASHOUT", "SBO_S_SIGN_WASHOUT", "PROC_SBO_DEF_SC_WASHOUT","豆油销售解约定赔"),
    SBO_S_CLOSED("SBO_S_TT_CLOSED", "SBO_S_CONTRACT_CLOSED", "SBO_S_SIGN_CLOSED", "PROC_SBO_DEF_SC_CLOSED","豆油销售合同关闭"),
    SBO_S_INVALID("SBO_S_TT_INVALID", "SBO_S_CONTRACT_INVALID", "SBO_S_SIGN_INVALID", "PROC_SBO_DEF_SC_CLOSED","豆油销售合同作废"),
    SBO_S_EQUITY("SBO_S_TT_EQUITY", "SBO_S_CONTRACT_EQUITY", "SBO_S_SIGN_INVALID", "PROC_SBO_DEF_SC_EQUITY","豆油销售权益变更审批"),


    SBO_P_BUYBACK("SBO_P_TT_BUYBACK", "SBO_P_CONTRACT_BUYBACK", "SBO_P_SIGN_BUYBACK", "PROC_SBO_DEF_SC_BUYBACK","豆油销售合同回购"),
    SBO_P_ADD("SBO_P_TT_ADD", "SBO_P_CONTRACT_ADD", "SBO_P_SIGN_ADD", "PROC_SBO_DEF_PC_ADD","豆油采购合同新增"),
    SBO_P_REVISE("SBO_P_TT_REVISE", "SBO_P_CONTRACT_REVISE", "SBO_P_SIGN_REVISE","PROC_SBO_DEF_PC_MODIFY", "豆油采购合同拆分"),
    SBO_P_SPLIT("SBO_P_TT_SPLIT", "SBO_P_CONTRACT_SPLIT", "SBO_P_SIGN_SPLIT", "PROC_SBO_DEF_PC_MODIFY","豆油采购合同修改"),
    SBO_P_TRANSFER("SBO_P_TT_TRANSFER", "SBO_P_CONTRACT_TRANSFER", "SBO_P_SIGN_TRANSFER", "PROC_SBO_DEF_PC_PRICE","豆油采购合同转月"),
    SBO_P_PRICE("SBO_P_TT_PRICE", "SBO_P_CONTRACT_PRICE", "SBO_P_SIGN_PRICE", "PROC_SBO_DEF_PC_PRICE","豆油采购合同点价"),
    SBO_P_FIXED("SBO_P_TT_FIXED", "SBO_P_CONTRACT_FIXED", "SBO_P_SIGN_FIXED", "PROC_SBO_DEF_PC_FIXED","豆油采购合同暂定价定价"),
    SBO_P_REVERSE_PRICE("SBO_P_TT_REVERSE_PRICE", "SBO_P_CONTRACT_REVERSE_PRICE", "SBO_P_SIGN_REVERSE_PRICE","PROC_SBO_DEF_PC_PRICE", "豆油采购合同反点价"),
    SBO_P_STRUCTURE_PRICE("SBO_P_TT_STRUCTURE_PRICE", "SBO_P_CONTRACT_STRUCTURE_PRICE", "SBO_P_SIGN_STRUCTURE_PRICE", "PROC_SBO_DEF_PC_STRUCTURE_PRICE","豆油采购合同结构化定价"),
    SBO_P_WASHOUT("SBO_P_TT_WASHOUT", "SBO_P_CONTRACT_WASHOUT", "SBO_P_SIGN_WASHOUT", "PROC_SBO_DEF_PC_WASHOUT","豆油采购解约定赔"),
    SBO_P_CLOSED("SBO_P_TT_CLOSED", "SBO_P_CONTRACT_CLOSED", "SBO_P_SIGN_CLOSED", "PROC_SBO_DEF_PC_CLOSED","豆油采购合同关闭"),
    SBO_P_INVALID("SBO_P_TT_INVALID", "SBO_P_CONTRACT_INVALID", "SBO_P_SIGN_INVALID", "PROC_SBO_DEF_PC_CLOSED","豆油采购合同作废"),




    ;

    private String ttValue;
    private String contractValue;
    private String contractSignValue;
    private String processKey; // 流程定义的Key，据此找到流程定义
    private String desc;


    public static ProcessorTypeEnum getByTTValue(String ttValue) {
        for (ProcessorTypeEnum typeEnum : ProcessorTypeEnum.values()) {
            if (typeEnum.ttValue.equalsIgnoreCase(ttValue)) {
                return typeEnum;
            }
        }
        return SBM_S_ADD;
    }

    public static ProcessorTypeEnum getByContractValue(String contractValue) {
        for (ProcessorTypeEnum typeEnum : ProcessorTypeEnum.values()) {
            if (typeEnum.contractValue.equalsIgnoreCase(contractValue)) {
                return typeEnum;
            }
        }
        return SBM_S_ADD;
    }

    public static ProcessorTypeEnum getByContractSignValue(String contractSignValue) {
        for (ProcessorTypeEnum typeEnum : ProcessorTypeEnum.values()) {
            if (typeEnum.contractSignValue.equalsIgnoreCase(contractSignValue)) {
                return typeEnum;
            }
        }
        return SBM_S_ADD;
    }

    /**
     * 获取第n个"_"的字符串
     * @param processorTypeEnum
     * @param index
     * @return
     */
    public static String getStringByIndex(ProcessorTypeEnum processorTypeEnum,int index) {
        String ttValue = processorTypeEnum.getTtValue();
        int i = 0;
        int num = 0;
        while (num < index) {
            i = ttValue.indexOf("_",i+1);
            num++;
        }
        if (index != 0) {
            i = i + 1;
        }
        return ttValue.substring(i);
    }
}
