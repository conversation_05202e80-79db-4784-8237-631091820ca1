[{"buCode": "All,", "canModify": 1, "categoryId": "12,11,", "code": "Clause_0401", "companyCode": "All,", "content": "<p style=\"text-indent: 28pt;\">本协议自双方盖章后生效，双方互不承担违约责任，并承诺今后不会就原合同项下任何内容向对方提出索赔或其它仲裁、诉讼请求。</p><p style=\"text-indent: 28pt;\">本协议一式两份，双方各执一份，均具有同等法律效力。</p>", "contractActionType": "", "createdAt": "2023-09-14 15:40:08", "createdBy": "admin1", "customerCode": "All,", "customerName": "All,", "enterpriseCode": null, "enterpriseName": null, "id": 492, "isFixed": 0, "mainVersionStatus": 1, "memo": "", "name": "豆油&豆粕-销售&采购-合同关闭-合同中部-无提货", "needNum": 0, "protocolType": "All,", "ruleCode": "d4876cf9-2857-404c-be71-a19407f8ed0a", "ruleInfo": " mapBizData.get(\"signType\") == 0 ", "salesType": "1,2,", "sort": 10000, "status": 1, "templateGroupCode": "ClauseGroup_0231", "templateRule": {"conditionInfo": "合同终止方式等于补充协议;", "conditionVariable": "signType", "conditionVariableList": null, "createdAt": "2023-09-14 15:40:50", "id": 570, "isDeleted": 0, "memo": null, "referCode": "Clause_0401", "referType": 1, "ruleCode": "d4876cf9-2857-404c-be71-a19407f8ed0a", "ruleDetailEntityList": [{"conditionValue": "0", "conditionValueInfo": "补充协议", "conditionVariable": "signType", "conditionVariableId": 134, "createdAt": "2023-09-16 16:37:32", "id": 2767, "isDeleted": 0, "level": 1, "logicRelation": "", "memo": null, "patternRelation": "==", "referCode": "Clause_0401", "referType": 1, "ruleCode": "d4876cf9-2857-404c-be71-a19407f8ed0a", "ruleDesc": "合同终止方式等于补充协议", "ruleInfo": "mapBizData.get(\"signType\") == 0", "sort": 1, "updatedAt": "2023-09-16 16:37:32"}], "ruleInfo": " mapBizData.get(\"signType\") == 0 ", "updatedAt": "2023-09-14 15:40:50"}, "title": "", "updatedAt": "2023-12-08 10:55:40", "updatedBy": "admin3", "version": "20231208105540"}, {"buCode": "All,", "canModify": 1, "categoryId": "12,11,", "code": "Clause_0400", "companyCode": "All,", "content": "<p style=\"text-indent: 28pt;\">本协议自双方盖章后生效，就部分解除货物买卖，双方互不承担违约责任，并承诺今后不会就原合同项下部分解除数量的任何内容向对方提出索赔或其它仲裁、诉讼请求。</p><p style=\"text-indent: 28pt;\">本协议一式两份，双方各执一份，均具有同等法律效力。</p>", "contractActionType": "", "createdAt": "2023-09-14 15:37:00", "createdBy": "admin1", "customerCode": "All,", "customerName": "All,", "enterpriseCode": null, "enterpriseName": null, "id": 491, "isFixed": 0, "mainVersionStatus": 1, "memo": "", "name": "豆油&豆粕-销售--合同关闭-合同中部-有提货", "needNum": 0, "protocolType": "All,", "ruleCode": "d8500563-775f-4341-a4c6-1d09222aca51", "ruleInfo": " mapBizData.get(\"signType\") == 1 ", "salesType": "2,", "sort": 10000, "status": 1, "templateGroupCode": "ClauseGroup_0231", "templateRule": {"conditionInfo": "合同终止方式等于尾量协议;", "conditionVariable": "signType", "conditionVariableList": null, "createdAt": "2023-09-14 15:37:00", "id": 567, "isDeleted": 0, "memo": null, "referCode": "Clause_0400", "referType": 1, "ruleCode": "d8500563-775f-4341-a4c6-1d09222aca51", "ruleDetailEntityList": [{"conditionValue": "1", "conditionValueInfo": "尾量协议", "conditionVariable": "signType", "conditionVariableId": 134, "createdAt": "2023-09-16 16:36:17", "id": 2765, "isDeleted": 0, "level": 1, "logicRelation": "", "memo": null, "patternRelation": "==", "referCode": "Clause_0400", "referType": 1, "ruleCode": "d8500563-775f-4341-a4c6-1d09222aca51", "ruleDesc": "合同终止方式等于尾量协议", "ruleInfo": "mapBizData.get(\"signType\") == 1", "sort": 1, "updatedAt": "2023-09-16 16:36:17"}], "ruleInfo": " mapBizData.get(\"signType\") == 1 ", "updatedAt": "2023-09-14 15:37:00"}, "title": "", "updatedAt": "2023-12-08 10:55:40", "updatedBy": "admin3", "version": "20231208105540"}]