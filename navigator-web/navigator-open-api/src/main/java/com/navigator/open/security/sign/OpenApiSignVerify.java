package com.navigator.open.security.sign;

import cn.hutool.core.util.ReUtil;
import com.navigator.common.util.RSAUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.Signature;
import java.util.Base64;

/**
 * 签名验证
 *
 * <AUTHOR>
 */
@Slf4j
public class OpenApiSignVerify {

    /**
     * 公钥
     */
    private PublicKey publicKey;

    public OpenApiSignVerify(String publicKeyStr) {
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("签名证书信息");
        log.info("publicKey={}", publicKeyStr);
        log.info(OpenApiSignConstant.DIVIDER_END);
        this.publicKey = RSAUtil.stringToPublicKey(publicKeyStr);
    }

    /**
     * 签名验证
     *
     * @param method
     * @param url
     * @param data
     * @param authorization
     * @return
     */
    @SneakyThrows
    public Boolean verifySign(String method, String url, String data, String authorization) {
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("凭证信息");
        log.info(authorization);
        log.info(OpenApiSignConstant.DIVIDER_START);
        String timestamp = ReUtil.get("timestamp=\\\"([^\\\"]+)\\\"", authorization, 1);
        String nonceStr = ReUtil.get("nonceStr=\\\"([^\\\"]+)\\\"", authorization, 1);
        String signedStr = ReUtil.get("signedStr=\\\"([^\\\"]+)\\\"", authorization, 1);
        OpenApiSignDTO openApiSignDTO = new OpenApiSignDTO(method, url, Long.parseLong(timestamp), nonceStr, data);
        String signStr = openApiSignDTO.getSignStr();
        byte[] signedBytes = Base64.getDecoder().decode(signedStr);
        byte[] hash = MessageDigest.getInstance("SHA-256").digest(signStr.getBytes(StandardCharsets.UTF_8));
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initVerify(publicKey);
        signature.update(hash);
        return signature.verify(signedBytes);
    }

    public static void main(String[] args) {
        String authorization = "OPENAPI-SHA256-RSA2048 appId=\"123\" certCode=\"12345678\" timestamp=\"1728001152603\" nonceStr=\"TFHEQ2NAL4UQ8KNDW1Z1N4FZWLZ0DROL\" signedStr=\"SrIWWhxnN/jqZUgIEfa6cxTj2e5+acqYaOB4YrCpQAhBrHziuKkTrx5mVSuXhWNvw43zpl4YhGOUPB+nrXV3ObKjwyNCmfHdK3Me5j5VIgzryX92i+3B2CxaLkoX9FIIDJyPYBUznKatrJwciL1subt4ScdyuADxIofsELg1N3jg84xjQZo3K0LEtR0XY4/JxmJltL5E1fv57nS4RiJQFmRyoSWKYw/OD9yZ0Dni+qM4w/tlJbuOfNrS98OUQpCedTNZF42GUsGdBcf5tVvDIOtFFFgAf1j8Zz2YNwBRrdgTH+6zZk6mqitP6BvI8DuqM99ui9Ao1GDwCjsnFdh50A==\"";
        String appId = ReUtil.get("appId=\\\"([^\\\"]+)\\\"", authorization, 1);
        String certCode = ReUtil.get("certCode=\\\"([^\\\"]+)\\\"", authorization, 1);
        // TODO：这里根据mchId,certNo获取商户公钥
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgcl4+BveAxrAUTpw7l7tY7O23VP+vDd87/6CHME8cE00P192s0Mu61M3Gp2Sz983l+NvPc9+o7sEuleW9zUeP8m5YDoxZHfyYrNguiW39omkF9fLuLF+Iv9WBT7v675MaE8diAPm48rZ3IMXX2pCevFQ+pWApaP41HNuycuCsREdKO5P/p8A0XvGEcYfK60bTo1sExRrCCR7Wve/2dtSQ2RAawdBX/KZW9Pejlcdu809916gIb5coo9YTfLlDmP4zn5WwVdWW/5ifd1E7ADCsDgbv+u2Sn4WnYCwWAKC3vnwO7smmcebUV8tBZp5gUQ0LjM47YfuavSUPkvFsDof8wIDAQAB";
        OpenApiSignVerify openApiSignVerify = new OpenApiSignVerify(publicKey);
        Boolean result = openApiSignVerify.verifySign("POST", "/v3/refund/domestic/refunds/123123123123", "test", authorization);
        log.info(result.toString());
    }
}
