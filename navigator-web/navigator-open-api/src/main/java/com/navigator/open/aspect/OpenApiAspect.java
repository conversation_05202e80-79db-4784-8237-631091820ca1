package com.navigator.open.aspect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONUtil;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.RequestUtil;
import com.navigator.common.util.StringUtil;
import com.navigator.open.constant.OpenApiCode;
import com.navigator.open.security.sign.OpenApiSignVerify;
import com.navigator.open.facade.OpenApiLogFacade;
import com.navigator.open.facade.OpenAppFacade;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.pojo.entity.OpenAppEntity;
import com.navigator.open.pojo.qo.OpenAppQO;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * 日志切面
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Slf4j
public class OpenApiAspect {

    @Resource
    private OpenAppFacade openAppFacade;

    @Resource
    private OpenApiLogFacade openApiLogFacade;

    /**
     * 配置切入点
     */
    @Pointcut("@annotation(com.navigator.open.annotation.OpenApi)")
    public void logPointcut() {
        // 该方法无方法体,主要为了让同类中其他方法使用此切入点
    }

    /**
     * 配置环绕通知,使用在方法logPointcut()上注册的切入点
     *
     * @param joinPoint join point for advice
     */
    @Around("logPointcut()")
    public Object logAround(ProceedingJoinPoint joinPoint) throws Throwable {
        Date now = new Date();
        // 执行
        Object result = joinPoint.proceed();
        // 执行后保存日志
        this.process(OpenApiCode.HTTP_OK, joinPoint, now, JSONUtil.toJsonStr(result));
        return result;
    }

    /**
     * 配置异常通知
     *
     * @param joinPoint join point for advice
     * @param e         exception
     */
    @AfterThrowing(pointcut = "logPointcut()", throwing = "e")
    public void logAfterThrowing(JoinPoint joinPoint, Throwable e) {
        // 保存异常日志
        if (e instanceof BusinessException) {
            this.process(((BusinessException) e).getCode(), joinPoint, new Date(), ((BusinessException) e).getMsg());
        } else {
            this.process(OpenApiCode.HTTP_INTERNAL_ERROR, joinPoint, new Date(), e.getMessage());
        }

    }

    /**
     * 处理
     *
     * @param code
     * @param joinPoint
     * @param requestTime
     * @param responseData
     */
    private void process(int code, JoinPoint joinPoint, Date requestTime, String responseData) {
        String requestMethod = null;
        String requestUrl = null;
        String requestData = null;
        String appId = null;
        String msg = null;
        try {
            HttpServletRequest request = RequestUtil.getRequest();
            requestMethod = request.getMethod();
            log.info("请求方法：{}", requestMethod);
            requestUrl = request.getRequestURI();
            log.info("请求地址：{}", requestUrl);
            // 请求参数
            Object[] argValues = joinPoint.getArgs();
            requestData = JSONUtil.toJsonStr(argValues[0]);
            log.info("请求参数：{}", requestData);
            String authorization = request.getHeader("Authorization");
            if (StringUtil.isNullBlank(authorization)) {
                throw new BusinessException(OpenApiCode.SIGN_VERIFY_PARAM_ERROR, "参数错误：缺少认证信息 Authorization");
            }
            if (!authorization.startsWith("OPENAPI-SHA256-RSA2048")) {
                throw new BusinessException(OpenApiCode.SIGN_VERIFY_PARAM_ERROR, "参数错误：目前仅支持OPENAPI-SHA256-RSA2048认证类型");
            }
            // 验证签名
            appId = ReUtil.get("appId=\\\"([^\\\"]+)\\\"", authorization, 1);
            if (StringUtil.isNullBlank(appId)) {
                throw new BusinessException(OpenApiCode.SIGN_VERIFY_PARAM_ERROR, "参数错误：缺少appId");
            }
            String certCode = ReUtil.get("certCode=\\\"([^\\\"]+)\\\"", authorization, 1);
            if (StringUtil.isNullBlank(certCode)) {
                throw new BusinessException(OpenApiCode.SIGN_VERIFY_PARAM_ERROR, "参数错误：缺少certCode");
            }
            List<OpenAppEntity> openAppList = openAppFacade.queryOpenAppList(new OpenAppQO().setId(Integer.parseInt(appId)).setCertCode(certCode));
            if (CollUtil.isEmpty(openAppList)) {
                throw new BusinessException(OpenApiCode.BUS_PARAM_ERROR, "找不到应用信息");
            }
            OpenApiSignVerify openApiSignVerify = new OpenApiSignVerify(openAppList.get(0).getPublicKey());
            Boolean result = openApiSignVerify.verifySign(requestMethod, requestUrl, requestData, authorization);
            if (!result) {
                throw new BusinessException(OpenApiCode.SIGN_VERIFY_FAIL, "签名验证失败");
            }
        } catch (Exception e) {
            msg = e.getMessage();
            if (e instanceof BusinessException) {
                code = ((BusinessException) e).getCode();
            } else {
                code = OpenApiCode.HTTP_INTERNAL_ERROR;
            }
            throw e;
        } finally {
            // 记录日志
            OpenApiLogAddDTO openApiLogAddDTO = new OpenApiLogAddDTO();
            openApiLogAddDTO.setOpenAppId(Integer.parseInt(appId));
            openApiLogAddDTO.setRequestMethod(requestMethod);
            openApiLogAddDTO.setRequestUrl(requestUrl);
            openApiLogAddDTO.setRequestData(requestData);
            openApiLogAddDTO.setResponseData(responseData);
            openApiLogAddDTO.setRequestTime(requestTime);
            openApiLogAddDTO.setResponseTime(new Date());
            openApiLogAddDTO.setCode(code);
            openApiLogAddDTO.setMsg(msg);
            openApiLogFacade.addOpenApiLog(openApiLogAddDTO);
        }
    }
}
