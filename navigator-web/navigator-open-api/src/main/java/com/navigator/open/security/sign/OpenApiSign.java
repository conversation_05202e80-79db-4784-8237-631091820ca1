package com.navigator.open.security.sign;

import cn.hutool.core.util.RandomUtil;
import com.navigator.common.util.RSAUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.Signature;
import java.util.Base64;

/**
 * 签名
 *
 * <AUTHOR>
 */
@Slf4j
public class OpenApiSign {

    /**
     * APP ID
     */
    private String appId;

    /**
     * 证书编号
     */
    private String certCode;

    /**
     * 私钥
     */
    private PrivateKey privateKey;

    public OpenApiSign(String appId, String certCode, String privateKeyStr) {
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("签名证书信息");
        log.info("appId={}", appId);
        log.info("certCode={}", certCode);
        log.info("privateKey={}", privateKeyStr);
        log.info(OpenApiSignConstant.DIVIDER_END);
        this.appId = appId;
        this.certCode = certCode;
        this.privateKey = RSAUtil.stringToPrivateKey(privateKeyStr);
    }

    /**
     * 获取签名字符串
     *
     * @param openApiSignDTO
     * @return
     */
    @SneakyThrows
    private String getSignedStr(OpenApiSignDTO openApiSignDTO) {
        byte[] hash = MessageDigest.getInstance("SHA-256").digest(openApiSignDTO.getSignStr().getBytes(StandardCharsets.UTF_8));
        Signature signature = Signature.getInstance("SHA256withRSA");
        signature.initSign(privateKey);
        signature.update(hash);
        byte[] signedBytes = signature.sign();
        String signedStr = Base64.getEncoder().encodeToString(signedBytes);
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("签名后字符串");
        log.info(signedStr);
        log.info(OpenApiSignConstant.DIVIDER_END);
        return signedStr;
    }

    /**
     * 获取凭证
     *
     * @param openApiSignDTO
     * @return
     */
    public String getAuthorization(OpenApiSignDTO openApiSignDTO) {
        String signedStr = this.getSignedStr(openApiSignDTO);
        String authorization = String.format("OPENAPI-SHA256-RSA2048 appId=\"%s\" certCode=\"%s\" timestamp=\"%s\" nonceStr=\"%s\" signedStr=\"%s\"",
                appId, certCode, openApiSignDTO.getTimestamp(), openApiSignDTO.getNonceStr(), signedStr);
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("凭证信息");
        log.info(authorization);
        log.info(OpenApiSignConstant.DIVIDER_END);
        return authorization;
    }

    public static void main(String[] args) {
        String privateKey = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCByXj4G94DGsBROnDuXu1js7bdU/68N3zv/oIcwTxwTTQ/X3azQy7rUzcanZLP3zeX4289z36juwS6V5b3NR4/yblgOjFkd/Jis2C6Jbf2iaQX18u4sX4i/1YFPu/rvkxoTx2IA+bjytncgxdfakJ68VD6lYClo/jUc27Jy4KxER0o7k/+nwDRe8YRxh8rrRtOjWwTFGsIJHta97/Z21JDZEBrB0Ff8plb096OVx27zT33XqAhvlyij1hN8uUOY/jOflbBV1Zb/mJ93UTsAMKwOBu/67ZKfhadgLBYAoLe+fA7uyaZx5tRXy0FmnmBRDQuMzjth+5q9JQ+S8WwOh/zAgMBAAECggEAErjrmG78sdn0EEOr8tcCy7UrFfZ98szWWxCN2HQPseuRM+sjhmyUoiHY+x/sA8yhimmmrHX4QMpDvlXQU0z4Vh9VfIoogePDtXVG6NN0HD5yy3W3z6bbGVVYJV2U4p8LCqkPC4X0kru6fdLzVjJP8MhK9DsI2dwzkwIr4zRWYBBuqiOOjP3NoY1WliHvG8VxMld8C02ZKVSAhactMoZbExSxv26CTbV0vMRdJm2C2X5tRu9zywK0dfNZTlzbksJeC0erDjZW+itrM4Itl7DFuYL00ABnMEylKwXv3msVGdMrnDfDqlJ8ugwxua6ycUTwqGjbbEULFwiOqFiSBaJ58QKBgQD6rst3qbuFYIiwrvtw/dFtsEmH/taTyKBhxUOFebWg3YaQGx1QrOYPCGGjhyJXsztmjLAqfbIcbBMWtkVgAiZwdU9B3NUpL4QRyFh33UH+je9zioIrV27/svobr3CBH+S9Jm5T1jN4u6zHR0BoMIYIVgveYntZfPu1xP7dxA7svQKBgQCEijb5Bw4x97hRyQanF7Fo2D+13KHNT+9H1KR/3fdKka9qjfiEAB7xDkIIem8sojuMbTUFFxBTsQw5v17U1NbiJnJV4lvNFeI0k5RClO+vi+z15eB+T9WW2Ac7xvsVmPLu+GWvPBLjXnxoubyvITWymsC7ExQuWqy7koEacggCbwKBgQCP03PXnyGAv8cNIAgRZv8eoNM/XJ3+QlCKZ+R6kz77Ib5Ptc7W96wzGcrzSNgSWzoMd1Gnv2BOYE9/a2pYUEN/qD6l+i6up51tmnXvp5lylDq7Hs/aUBrWR/ipo7m3y/Wv9Hm+ThpP/dV+rEmuvZBY/g1nNTeyOzwrqO/ViuRJPQKBgBiHf+8MuDrVLt6sBrgr8WCHDRt6TtOxpIY1u5ASo27Akad1yPWdFBzm15+8ERFAoSa3G2Xqw9HJZYrWeRWIopBOL0Dn2LeXAjtayMidydJCYkOMHMUjv1Lgfk444EU5/lPt0tvgkVpVJU84eMwsqER60YqN2GFP1MIONiiPzB2vAoGBAPAVaDC8/pPEVn4u4AfKPtjI7RboK6e/Q/3jhFRrom3gctTh/nIslzynKx9pB90Un2USd7evj71ejmfjz/XZMoyvoaMlOxN6OW1zNV1sOU66zBBNkIxFqPKOTILjQoD8hsDzbYs5+sJ+MZrPhgj2uGYZmJ7vcKgjA4A+Vn2LtKvT";
        OpenApiSignDTO openApiSignDTO = new OpenApiSignDTO("POST", "/openApi/queryOpenApiList", System.currentTimeMillis(), RandomUtil.randomString(32).toUpperCase(), "{\"name\":\"\",\"id\":0}");
        OpenApiSign openApiSign = new OpenApiSign("1", "12345678", privateKey);
        String authorization = openApiSign.getAuthorization(openApiSignDTO);
        log.info(authorization);
    }
}
