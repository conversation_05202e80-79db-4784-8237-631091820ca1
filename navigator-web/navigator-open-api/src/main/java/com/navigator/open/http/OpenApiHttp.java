package com.navigator.open.http;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.navigator.common.exception.BusinessException;
import com.navigator.open.constant.OpenApiCode;
import com.navigator.open.facade.OpenApiLogFacade;
import com.navigator.open.pojo.dto.OpenApiLogAddDTO;
import com.navigator.open.security.sign.OpenApiSign;
import com.navigator.open.security.sign.OpenApiSignDTO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 发起请求工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class OpenApiHttp {

    @Resource
    private OpenApiLogFacade openApiLogFacade;

    /**
     * APP ID
     */
    private String appId;

    /**
     * 证书编号
     */
    private String certCode;

    /**
     * 私钥
     */
    private String privateKey;

    public OpenApiHttp(String appId, String certCode, String privateKey) {
        this.appId = appId;
        this.certCode = certCode;
        this.privateKey = privateKey;
    }

    /**
     * 发起请求
     *
     * @param url
     * @param data
     * @return
     */
    public String doPost(String url, Object data) {
        // 请求接口
        String requestMethod = null;
        String requestUrl = null;
        Date requestTime = new Date();
        String requestData = data == null ? null : JSONUtil.toJsonStr(data);
        String responseData = null;
        String appId = null;
        int code = 200;
        String msg = null;
        try {
            Method method = Method.POST;
            HttpRequest httpRequest = HttpUtil.createRequest(method, url);
            // 请求头
            OpenApiSignDTO openApiSignDTO = new OpenApiSignDTO(method.name(), url, System.currentTimeMillis(), RandomUtil.randomString(32).toUpperCase(), requestData);
            OpenApiSign openApiSign = new OpenApiSign(this.appId, this.certCode, privateKey);
            String authorization = openApiSign.getAuthorization(openApiSignDTO);
            Map<String, String> headerMap = new HashMap<>(16);
            headerMap.put("authorization", authorization);
            httpRequest.headerMap(headerMap, true);
            httpRequest.body(requestData);
            httpRequest.header("Content-type", "application/json;charset=UTF-8");
            httpRequest.timeout(10000);
            responseData = httpRequest.execute().body();
        } catch (IORuntimeException e) {
            code = OpenApiCode.HTTP_CLIENT_TIMEOUT;
            msg = "请求超时";
        } catch (Exception e) {
            msg = e.getMessage();
            if (e instanceof BusinessException) {
                code = ((BusinessException) e).getCode();
            } else {
                code = OpenApiCode.HTTP_INTERNAL_ERROR;
            }
            throw e;
        } finally {
            // 记录日志
            OpenApiLogAddDTO openApiLogAddDTO = new OpenApiLogAddDTO();
            openApiLogAddDTO.setOpenAppId(Integer.parseInt(appId));
            openApiLogAddDTO.setRequestMethod(requestMethod);
            openApiLogAddDTO.setRequestUrl(requestUrl);
            openApiLogAddDTO.setRequestData(requestData);
            openApiLogAddDTO.setResponseData(responseData);
            openApiLogAddDTO.setRequestTime(requestTime);
            openApiLogAddDTO.setResponseTime(new Date());
            openApiLogAddDTO.setCode(code);
            openApiLogAddDTO.setMsg(msg);
            openApiLogFacade.addOpenApiLog(openApiLogAddDTO);
        }
        return responseData;
    }
}
