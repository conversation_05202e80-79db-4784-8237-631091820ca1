package com.navigator.open.security.sign;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

/**
 * 请求对象
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Accessors(chain = true)
public class OpenApiSignDTO {

    /**
     * HTTP请求方法
     */
    private String method;

    /**
     * URL
     */
    private String url;

    /**
     * 请求时间戳
     */
    private Long timestamp;

    /**
     * 请求随机串
     */
    private String nonceStr;

    /**
     * 请求报文主体
     */
    private String data;

    /**
     * 构造方法
     *
     * @param method
     * @param url
     * @param timestamp
     * @param nonceStr
     * @param data
     */
    public OpenApiSignDTO(String method, String url, Long timestamp, String nonceStr, String data) {
        this.method = method;
        this.url = url;
        this.timestamp = timestamp;
        this.nonceStr = nonceStr;
        this.data = data;
    }

    /**
     * 获取待签名字符串
     *
     * @return
     */
    public String getSignStr() {
        String signStr = String.format("%s\n%s\n%s\n%s\n%s\n", this.getMethod().toUpperCase(), this.getUrl(), this.getTimestamp(), this.getNonceStr(), this.getData());
        log.info(OpenApiSignConstant.DIVIDER_START);
        log.info("待签名字符串");
        log.info(signStr);
        log.info(OpenApiSignConstant.DIVIDER_END);
        return signStr;
    }
}
