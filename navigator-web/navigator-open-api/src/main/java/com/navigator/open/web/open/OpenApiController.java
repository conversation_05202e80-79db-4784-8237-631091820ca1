package com.navigator.open.web.open;

import com.navigator.common.dto.Result;
import com.navigator.open.annotation.OpenApi;
import com.navigator.open.facade.OpenApiFacade;
import com.navigator.open.pojo.entity.OpenApiEntity;
import com.navigator.open.pojo.qo.OpenApiQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * Open API Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-30
 */
@RestController
@RequestMapping("/open/api")
public class OpenApiController {

    @Resource
    private OpenApiFacade openApiFacade;

    /**
     * 根据条件：获取Open API列表
     *
     * @param condition
     * @return
     */
    @OpenApi
    @PostMapping("/queryOpenApiList")
    Result<List<OpenApiEntity>> queryOpenApiList(@RequestBody OpenApiQO condition) {
        return Result.success(openApiFacade.queryOpenApiList(condition));
    }

    /**
     * 根据ID：获取Open API
     *
     * @param id
     * @return
     */
    @GetMapping("/getOpenApiById")
    Result<OpenApiEntity> getOpenApiById(@RequestParam(value = "id") Integer id) {
        return Result.success(openApiFacade.getOpenApiById(id));
    }
}
