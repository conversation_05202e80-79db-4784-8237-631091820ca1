package com.navigator.open.constant;

import cn.hutool.http.HttpStatus;

/**
 * 响应编码常量
 *
 * <AUTHOR>
 */
public class OpenApiCode extends HttpStatus {

    //    验签相关

    /**
     * 验签失败
     */
    public static final int SIGN_VERIFY_FAIL = 400;
    /**
     * 验签参数错误
     */
    public static final int SIGN_VERIFY_PARAM_ERROR = 410;
    /**
     * 验签时间戳错误
     */
    public static final int SIGN_VERIFY_TIMESTAMP_ERROR = 411;

    //    参数相关

    /**
     * 业务参数错误
     */
    public static final int BUS_PARAM_ERROR = 510;

//    业务范围	1000
//
//    合同处理	2000
//
//    头寸处理	3000
//
//    提货处理	4000
//
//    财务处理	5000
//
//    其他	9000


}
