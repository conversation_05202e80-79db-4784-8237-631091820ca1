package com.navigator.open;

import com.yomahub.tlog.core.enhance.bytes.AspectLogEnhance;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication(scanBasePackages = "com.navigator", exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade"})
@EnableTransactionManagement
@EnableAspectJAutoProxy
public class OpenApiNavigatorApplication {
    static {
        //进行日志增强，自动判断日志框架
        AspectLogEnhance.enhance();
    }
    public static void main(String[] args) {
        SpringApplication.run(OpenApiNavigatorApplication.class, args);
    }

}
