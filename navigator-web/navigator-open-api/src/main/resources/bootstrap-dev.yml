spring:
  cloud:
    nacos:
      username: nacos
      password: nacos
      discovery:
        server-addr: http://192.168.1.31:8848
        namespace: navigator_cloud_dev
      config:
        server-addr: http://192.168.1.31:8848
        file-extension: yaml
        namespace: navigator_cloud_dev
        group: WEB_GROUP
        shared-configs:
          - data-id: navigator-commons.yaml
            group: DEFAULT_GROUP
            refresh: true
