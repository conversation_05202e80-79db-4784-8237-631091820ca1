FROM openjdk:11.0
RUN mkdir /config
COPY  navigator-web/navigator-columbus-web/src/main/resources/bootstrap-dev.yml /config
COPY  navigator-web/navigator-columbus-web/src/main/resources//bootstrap.yml /config
RUN rm -rf /etc/localtime && ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo "Asia/Shanghai" > /etc/timezone
COPY deploy/navigator-columbus-web/*.jar /navigator-columbus-web-1.0-SNAPSHOT.jar
CMD java  -jar /navigator-columbus-web-1.0-SNAPSHOT.jar
