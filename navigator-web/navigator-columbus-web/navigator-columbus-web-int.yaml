apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-columbus-web-int
  namespace: int
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-columbus-web-int
  template:
    metadata:
      labels:
        app: ldc-navigator-columbus-web-int
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-columbus-web-int:#{Build.BuildId}#
        name: ldc-navigator-columbus-web-int
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "int"
---
apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-columbus-web-int
  namespace: int
spec:
  type: ClusterIP
  ports:
  - port: 8080
    protocol: TCP
#    targetPort: 8080
  selector:
    app: ldc-navigator-columbus-web-int