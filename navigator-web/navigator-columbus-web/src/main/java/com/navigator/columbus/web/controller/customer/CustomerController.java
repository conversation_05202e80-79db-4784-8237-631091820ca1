package com.navigator.columbus.web.controller.customer;

import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 14:26`
 */
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private CEmployFacade employFacade;

    /**
     * 根据客户id查询 客户是否使用系统,是否使用易企签,是否实名  易企签通用配置是否启用
     *
     * @param customerId
     * @return
     */
    @GetMapping(value = "/customerSignParameter")
    public Result customerSignParameter(@RequestParam("customerId") Integer customerId){

        CEmployEntity employEntity = employFacade.getEmployById(customerId);

        return customerFacade.customerSignParameter(employEntity.getCustomerId());
    }

    @PostMapping("/queryCustomerSupplierAll")
    public Result queryCustomerSupplierAll(@RequestBody QueryDTO<CustomerDTO> queryDTO) {
        return customerFacade.queryCustomerSupplierAll(queryDTO);
    }
}
