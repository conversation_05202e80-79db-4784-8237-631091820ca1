package com.navigator.columbus.web.config;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 16:19
 **/
@RestController
@RequestMapping("/site")
public class SiteController {
    @Resource
    private SiteFacade siteFacade;

    /**
     * 根据账套编码查询账套详情
     *
     * @param siteCode
     * @return
     */
    @GetMapping("/getSiteDetailByCode")
    public Result<SiteEntity> getSiteByCode(@RequestParam(value = "siteCode") String siteCode) {
        return Result.success(siteFacade.getSiteDetailByCode(siteCode));
    }

    /**
     * 获取账套信息
     *
     * @param companyId
     * @param category2
     * @param status
     * @return
     */
    @GetMapping("/getSiteList")
    public Result<List<SiteEntity>> getSiteList(@RequestParam(value = "companyId", required = false) Integer companyId,
                                                @RequestParam(value = "category2", required = false) Integer category2,
                                                @RequestParam(value = "syncSystem", required = false) String syncSystem,
                                                @RequestParam(value = "status", required = false) Integer status) {
        return Result.success(siteFacade.getSiteList(companyId, category2, syncSystem, status));
    }

    /**
     * 获取工厂编码
     *
     * @param syncSystem
     * @return
     */
    @GetMapping("/queryFactoryCodeBySyncSystem")
    public Result<Set<String>> queryFactoryCodeBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(siteFacade.queryFactoryCodeBySyncSystem(syncSystem));
    }


}
