package com.navigator.columbus.web.config;

import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/systemRule")
public class SystemRuleController {

    @Autowired
    private SystemRuleFacade systemRuleFacade;

    /**
     * 查询系统配置
     *
     * @return List<SystemRuleVO>
     */
    @PostMapping("/getSystemRule")
    public Result getSystemRule(@RequestBody SystemRuleDTO systemRuleDTO) {
        List<SystemRuleVO> systemRuleVOList = systemRuleFacade.getSystemRule(systemRuleDTO);
        return Result.success(systemRuleVOList);
    }

    /**
     * 查询所有配置信息
     *
     * @param systemRuleDTO
     * @return
     */
    @PostMapping("/querySystemRuleDetail")
    public Result querySystemRuleDetail(@RequestBody SystemRuleDTO systemRuleDTO) {
        return Result.success(systemRuleFacade.querySystemRuleDetail(systemRuleDTO));
    }
}
