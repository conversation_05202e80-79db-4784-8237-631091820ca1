package com.navigator.columbus.web.security.filter;

import com.navigator.security.authentication.TokenManager;
import com.navigator.security.filter.TokenAuthenticationFilter;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description: 访问过滤器
 * Created by <PERSON><PERSON>ong on 2021/11/3 13:46
 */

public class ColumbusTokenAuthenticationFilter extends TokenAuthenticationFilter {

    private TokenManager tokenManager;

    public ColumbusTokenAuthenticationFilter(AuthenticationManager authManager, TokenManager tokenManager) {
        super(authManager);
        this.tokenManager = tokenManager;
    }

    @Override
    public UsernamePasswordAuthenticationToken getAuthentication(HttpServletRequest request, HttpServletResponse res) {
        // token置于header里
        String token = request.getHeader("token");
        if (token != null && !"".equals(token.trim())) {
            String phone = tokenManager.getPhoneByJwtToken(request);
            String employId = tokenManager.getUserIdFromToken(request);
            tokenManager.verifyColumbusForbidden(employId, res);
            if (tokenManager.verifyNeedRefresh(request)) {
                res.setHeader("JWT-RefreshStatus", "1");
            } else {
                res.setHeader("JWT-RefreshStatus", "0");
            }
            res.setHeader("Access-Control-Expose-Headers", "JWT-RefreshStatus");
            return backAuthentication(phone, employId, token);
        }
        return null;
    }


    @Override
    public Boolean verifyStatus(HttpServletRequest request) {
        String employId = tokenManager.getUserIdFromToken(request);
        return tokenManager.verifyStatus(employId) == 1;
    }

}
