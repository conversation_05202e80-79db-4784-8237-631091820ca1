package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.columbus.CMenuFacade;
import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/menu")
@Deprecated
public class MenuController {

    @Autowired
    private CMenuFacade menuFacade;

    @PostMapping("/getMenusByEmploy")
    public Result getMenusByEmploy(@RequestBody CMenuDTO menuDTO) {
        return menuFacade.getMenusByEmploy(menuDTO);
    }


    /**
     *
     *
     * @return
     */
    @PostMapping("/getMenuByRoleId")
    public Result getMenuByRoleId(@RequestBody CRoleDTO roleDTO) {
        return menuFacade.getMenuByRoleId(roleDTO);
    }

    /**
     *
     *
     * @return
     */
    @PostMapping("/saveRoleMenu")
    public Result saveRoleMenu(@RequestBody CMenuDTO roleDTO) {
        return menuFacade.saveRoleMenu(roleDTO);
    }
}
