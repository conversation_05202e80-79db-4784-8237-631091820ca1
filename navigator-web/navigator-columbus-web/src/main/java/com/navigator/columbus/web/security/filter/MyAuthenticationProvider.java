package com.navigator.columbus.web.security.filter;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.columbus.web.security.entity.UserDetail;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.redis.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Slf4j
public class MyAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonProperties commonProperties;
    @Resource
    private CEmployCustomerFacade cEmployCustomerFacade;

    //自定义密码验证
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if (authentication == null)
            return null;
        String credentials = (String) authentication.getCredentials();
        if (null == credentials)
            return null;
        if (!credentials.contains("\\")) {
            int i;
        }
        LoginDTO loginDTO = JSON.parseObject(credentials, LoginDTO.class);
        //todo 登录验证功能后续释放
        String key = RedisConstants.C_USER_LOGIN_PASSWORD_CODE + loginDTO.getPhone();
        String code = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(code)) {
            log.error("========> login  failed ,验证码已过期,请重新获取");
            throw new BadCredentialsException("验证码已过期,请重新获取");
        }
        if (!code.equalsIgnoreCase(loginDTO.getCaptcha())
                /*&& !timeCode.equalsIgnoreCase(loginDTO.getCaptcha())*/
                && (commonProperties.getCaptcha() == null
                || commonProperties.getCaptcha())) {
            log.error("========> login  failed ,验证码错误");
            throw new BadCredentialsException("验证码错误");
        }
        Result result = cEmployFacade.getEmployByPhone(loginDTO.getPhone().trim(), null);
        List<CEmployEntity> employEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), CEmployEntity.class);
        if (CollectionUtils.isEmpty(employEntityList)) {
            log.error("========> login  failed ,用户不存在");
            throw new BadCredentialsException("用户不存在");
        }
        List<CEmployEntity> employEntities = employEntityList.stream().filter(i -> i.getStatus() == 1).collect(Collectors.toList());
        if (employEntities.size() == 0) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            throw new BadCredentialsException("账号被禁用,请联系管理员");
        }
        List<CEmployCustomerVO> cEmployCustomerVOS = cEmployCustomerFacade.queryCEmployCustomerVOByCEmployId(employEntities.get(0).getId());
        if (cEmployCustomerVOS.isEmpty()) {
            log.error("========> login  failed ,账号被禁用,请联系管理员");
            throw new BadCredentialsException("账号被禁用,请联系管理员");
        }
        if (employEntities.size() > 1) {
            log.error("========> login  failed ,手机绑定账号不唯一,请联系管理员");
            throw new BadCredentialsException("手机绑定账号不唯一,请联系管理员");
        }
        CEmployEntity employEntity = employEntities.get(0);
        UserDetail userDetail = new UserDetail(employEntity, getUserAuthority(1L));
        if (authentication.getCredentials() == null) {
            log.error("========> login  failed ,凭证为空");
            throw new BadCredentialsException("凭证为空");
        }
        UsernamePasswordAuthenticationToken authenticationResult = new UsernamePasswordAuthenticationToken(userDetail, authentication.getCredentials(), userDetail.getAuthorities());
        authenticationResult.setDetails(authentication.getDetails());
        return authenticationResult;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return true;
    }

    public List<GrantedAuthority> getUserAuthority(Long userId) {
        return AuthorityUtils.commaSeparatedStringToAuthorityList("");
    }

}
 