package com.navigator.columbus.web.controller.goods;


import com.navigator.common.dto.Result;
import com.navigator.goods.facade.AttributeFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 规格表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@RestController
@RequestMapping("/attribute")
public class AttributeController {
    @Resource
    private AttributeFacade attributeFacade;

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param attributeId 规格ID
     * @return 规格列表
     */
    @GetMapping("/getAttributeValueList")
    public Result getAttributeValueList(@RequestParam("attributeId") Integer attributeId) {
        return Result.success(attributeFacade.getAttributeValueList(attributeId));
    }

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param categoryId 分类ID
     * @return 规格列表
     */
    @GetMapping("/getSpecListByCategoryId")
    public Result getSpecListByCategoryId(@RequestParam("categoryId") Integer categoryId) {
        return Result.success(attributeFacade.getSpecListByCategoryId(categoryId));
    }

}
