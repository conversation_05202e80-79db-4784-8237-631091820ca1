package com.navigator.columbus.web.security.config;

import com.navigator.columbus.web.security.filter.ColumbusTokenAuthenticationFilter;
import com.navigator.columbus.web.security.filter.TokenLoginFilter;
import com.navigator.columbus.web.service.LoginLogRecordService;
import com.navigator.security.authentication.TokenLogoutHandler;
import com.navigator.security.authentication.TokenManager;
import com.navigator.security.authentication.UnauthorizedEntryPoint;
import com.navigator.security.permission.MyDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Description: Security配置类
 * Created by YuYong on 2021/10/27 15:28
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {
    private TokenManager tokenManager;

    @Autowired
    private LoginLogRecordService loginLogRecordService;

    @Value("${security.validate}")
    private Integer securityValidate;

    @Autowired
    public SecurityConfig(TokenManager tokenManager) {
        this.tokenManager = tokenManager;
    }

    /**
     * 配置设置
     *
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.exceptionHandling()
                .authenticationEntryPoint(new UnauthorizedEntryPoint()).accessDeniedHandler(new MyDeniedHandler())
                .and().csrf().disable()
                .authorizeRequests()
                .anyRequest().authenticated()
                .and().logout().logoutUrl("/admin/acl/index/logout")
                .addLogoutHandler(new TokenLogoutHandler(tokenManager)).and()
                .addFilter(new ColumbusTokenAuthenticationFilter(authenticationManager(), tokenManager))
                // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
                //.addFilter(new TokenLoginFilter(authenticationManager(), tokenManager))
                .addFilter(new TokenLoginFilter(authenticationManager(), tokenManager, loginLogRecordService))
                // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End
//                .addFilterBefore(new AuthFilter(myColumbusSecurityMetadataSource), FilterSecurityInterceptor.class)
                .httpBasic();
    }

    /**
     * 配置哪些请求不拦截
     *
     * @param web
     */
    @Override
    public void configure(WebSecurity web) {
        String commonUrl = securityValidate == 1 ? "/aad" : "/**";
        web.ignoring().antMatchers("/aad/login", "/aad/getAuthorizeLoginUrl", "/file/download", "/contractSign/downloadContractZip",
                "/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/**/inner/**"
        );
        //web.ignoring().antMatchers("/*/**");
        web.ignoring().antMatchers("/user/sendVerificationCode/**", "/user/login/**", "/user/refresh/**", "/employ/sendResetPasswordPhoneCode/**",
                "/employ/resetNotLogUserPassword");
    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        // 这里我们使用bcrypt加密算法，安全性比较高
        return new BCryptPasswordEncoder();
    }
}
