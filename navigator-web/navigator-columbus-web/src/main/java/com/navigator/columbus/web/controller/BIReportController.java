package com.navigator.columbus.web.controller;

import com.google.gson.Gson;
import com.navigator.columbus.web.remote.ServiceProviderClient;
import com.navigator.columbus.web.security.entity.ReportInfo;
import com.navigator.columbus.web.security.entity.RequestPowerBI;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.entity.CustomerEntity;
import com.navigator.future.pojo.dto.QueryPriceAllocateDTO;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/BIReport")
@Slf4j
public class BIReportController {

    @Autowired
    @Qualifier("serviceProviderClient")
    private ServiceProviderClient serviceProviderClient;

//    @Value("${BIService.rolesName}")
    private String rolesName;

//    @Value("${BIService.workspaceId}")
    private String workspaceId;

    @Autowired
    private CustomerFacade customerFacade;

    @PostMapping("/queryBIReport")
    Result queryEmbedReportDetail(@RequestBody RequestPowerBI rpb) {
        // need to get customer mdm code according to customerId
        String customerMdmCode = "";
        String customerId = rpb.getCustomerId();
        if (customerId != null && !customerId.isEmpty()) {
            CustomerEntity ce = customerFacade.queryCustomerById(Integer.parseInt(customerId));
            customerMdmCode = ce.getMdmCustomerCode();
        } else {
            log.error("customerId is null or empty, BI cannot get the report without it");
        }

        RequestPowerBI rpb1 = new RequestPowerBI()
                // .setUserName(rpb.getEmployId())
                .setCustomData(customerMdmCode + "," + rpb.getCustomerId() + "," + rpb.getCategoryId() + "," + rpb.getEmployId())
                .setReportId(rpb.getReportId())
                // .setRolesName(this.rolesName)
                .setWorkspaceId(rpb.getWorkspaceId());

        ResponseEntity<String> jsonObj = sendRequestPowerBI(rpb1);
        return Result.success(jsonObj);
    }

    public ResponseEntity<String> sendRequestPowerBI(@RequestBody RequestPowerBI requestPowerBI) {
        ResponseEntity<String> jsonObj = serviceProviderClient.sendRequestPowerBI(requestPowerBI);
        return jsonObj;
    }
}
