package com.navigator.columbus.web.controller.delivery;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.delivery.pojo.dto.DeliveryApplyDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyDriverLogDTO;
import com.navigator.delivery.pojo.dto.DeliveryApplyInvalidDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 提货申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/deliveryApply")
public class DeliveryApplyController {
    private final DeliveryApplyFacade deliveryApplyFacade;
    private final FileProcessFacade fileProcessFacade;
    private final AzureBlobUtil azureBlobUtil;
    private final HttpServletResponse response;


    @ApiOperation("提货委托申请列表")
    @PostMapping("/getDeliveryApplyList")
    public Result getDeliveryApplyList(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        applyQO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        applyQO.setThirdSys(SystemEnum.LKG.getValue());
        return deliveryApplyFacade.getDeliveryApplyList(queryDTO);
    }

    @ApiOperation("新增申请列表")
    @PostMapping("/getDeliveryApplyContractList")
    public Result<List<DeliveryApplyContractVO>> getDeliveryApplyContractList(@RequestBody DeliveryApplyContractQO deliveryApplyContractQO) {
        return deliveryApplyFacade.getDeliveryApplyContractList(deliveryApplyContractQO);
    }

    @ApiOperation("查询申请提货详情")
    @GetMapping("/getDeliveryApplyDetailById")
    public Result<DeliveryApplyDetailVO> getDeliveryApplyDetailById(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getDeliveryApplyDetailById(applyId, SystemEnum.COLUMBUS.getName());
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiOperation("查询司机输入记录")
    @GetMapping("/getDriverInputRecord")
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverInputRecord(
            @ApiParam("客户id") @RequestParam("customerId") Integer customerId,
            @ApiParam("品类id") @RequestParam("goodsCategoryId") Integer goodsCategoryId) {
        return deliveryApplyFacade.getDriverInputRecord(customerId, goodsCategoryId);
    }

    @ApiOperation("查询司机输入记录")
    @PostMapping("/getDriverRecordByCondition")
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverRecordByCondition(@RequestBody DeliveryApplyDriverLogDTO applyDriverLogDTO) {
        return deliveryApplyFacade.getDriverRecordByCondition(applyDriverLogDTO);
    }
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiOperation("查询申请单附件")
    @GetMapping("/getFileListByApplyId")
    public Result<List<DeliveryApplyFileVO>> getFileListByApplyId(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getFileListByApplyId(applyId);
    }

    @ApiOperation("查询申请单审核记录")
    @GetMapping("/getAuditRecordListByApplyId")
    public Result<List<DeliveryApplyApprovalVO>> getAuditRecordListByApplyId(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getAuditRecordListByApplyId(applyId);
    }

    @ApiOperation("查询申请单操作记录")
    @GetMapping("/getOperateRecordListByApplyCode")
    public Result<List<DeliveryApplyOperationVO>> getOperateRecordListByApplyCode(@ApiParam("申请单code") @RequestParam("applyCode") String applyCode) {
        return deliveryApplyFacade.getOperateRecordListByApplyCode(applyCode);
    }

    @ApiOperation("保存/提交申请单")
    @PostMapping("/saveOrSubmitDeliveryApply")
    public Result<String> saveOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO) {
        deliveryApplyDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyFacade.saveOrSubmitDeliveryApply(deliveryApplyDTO);
    }

    @ApiOperation("编辑提货申请单")
    @PostMapping("/updateOrSubmitDeliveryApply")
    // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 Start
    public Result<String> updateOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO) {
        // 1002481 case-提货功能优化-返回提单编号 Author: Mr 2024-04-28 End
        deliveryApplyDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyFacade.updateOrSubmitDeliveryApply(deliveryApplyDTO);
    }

    @ApiOperation("提交申请单")
    @GetMapping("/submitDeliveryApplyById")
    public Result<Boolean> submitDeliveryApplyById(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.submitDeliveryApplyById(applyId);
    }

    @ApiOperation("批量提交申请单")
    @GetMapping("/batchSubmitDeliveryApply")
    public Result<String> batchSubmitDeliveryApply(@ApiParam("申请单id集合") @RequestParam("applyIds") List<Integer> applyIds) {
        return deliveryApplyFacade.batchSubmitDeliveryApply(applyIds);
    }

    @ApiOperation("下载提货申请单")
    @PostMapping("/downloadDeliveryApply")
    public void downloadDeliveryApply(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {
        Result result;
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        // 1002481 case-提货功能优化-下载申请单优化 Author: Mr 2024-05-13 Start
        /*List<Integer> applyIds = applyQO.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {*/
        applyQO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start
        queryDTO.setPageSize(-1);
        // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start
        result = deliveryApplyFacade.getDeliveryApplyList(queryDTO);
        /*} else {
            result = deliveryApplyFacade.getDeliveryApplyListByIds(applyIds);
        }*/
        // 1002481 case-提货功能优化-下载申请单优化 Author: Mr 2024-05-13 End
        if (!result.isSuccess()) {
            throw new BusinessException("无数据可导出！");
        }
        List<DeliveryApplyVOEntity> applyVOEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyVOEntity.class);

        if (CollectionUtil.isEmpty(applyVOEntityList)) {
            throw new BusinessException("无数据可导出！");
        }

        applyVOEntityList.forEach(applyVOEntity -> {
            // 导出豆油不展示包装品类
            if (applyVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
                applyVOEntity.setGoodsSpec(null)
                        .setGoodsPackage(null);
            }
        });

        String fileName = "ColumbusDeliveryApply" + DateUtil.format(DateUtil.date(), "MMddHHmmss");

        EasyPoiUtils.exportExcel(applyVOEntityList, null, "ColumbusDeliveryApply", DeliveryApplyVOEntity.class, fileName, response);
    }

    @ApiOperation("作废提货申请单")
    @PostMapping("/applyInvalidDeliveryApply")
    public Result<Boolean> applyInvalidDeliveryApply(@RequestBody DeliveryApplyInvalidDTO applyInvalidDTO) {
        applyInvalidDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyFacade.applyInvalidDeliveryApply(applyInvalidDTO);
    }

    @ApiOperation("撤回提货申请单")
    @GetMapping("/cancelDeliveryApply")
    public Result<Boolean> cancelDeliveryApply(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.cancelDeliveryApply(applyId);
    }

    @GetMapping("/downloadDeliveryApplyZip")
    public HttpServletResponse downloadDeliveryApplyZip(@RequestParam(value = "fileIdList") List<Integer> fileIdList,
                                                        @RequestParam("applyId") Integer applyId,
                                                        HttpServletResponse response) {
        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.DELIVERY, FilePathType.ZIP, applyId.toString());
        zipPath = zipPath + "deliveryApply" + DateUtil.format(DateUtil.date(), "MMddHHmmss") + ".zip";
        Result fileResult = fileProcessFacade.getFileListByIds(fileIdList);
        List<FileInfoEntity> fileInfoEntityList = JSON.parseArray(JSON.toJSONString(fileResult.getData()), FileInfoEntity.class);
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        List<String> filePathList = new ArrayList<>(fileInfoEntityList.size());
        fileInfoEntityList.forEach(fileInfoEntity -> {
            filePathList.add(fileInfoEntity.getPath());
        });
        azureBlobUtil.downloadZip(filePathList, zipPath, response);
        return response;
    }
}
