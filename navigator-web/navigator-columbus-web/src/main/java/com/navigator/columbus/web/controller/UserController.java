package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.UserFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.RefreshDTO;
import com.navigator.admin.pojo.dto.SignatureDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.VerificationCodeUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.security.authentication.TokenManager;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {
    @Autowired
    private VerificationCodeUtil verificationCodeUtil;

    @Autowired
    private UserFacade userFacade;

    @Autowired
    private TokenManager tokenManager;

    @Value("${token.expire}")
    private Integer tokenExpire;

    @PostMapping("/sendVerificationCode/{mobileNo}")
    public Result sendVerificationCode(@PathVariable("mobileNo") String mobileNo) {
        String verificationCode = verificationCodeUtil.sendVerificationCode(mobileNo, RedisConstants.C_USER_LOGIN_PASSWORD_CODE_SEND, RedisConstants.C_USER_LOGIN_PASSWORD_CODE);
        log.info("columbus sendVerificationCode ,mobileNo:{},verificationCode:{}", mobileNo, verificationCode);
        return Result.success();
    }


    @PostMapping("/login")
    public Result login(@RequestBody LoginDTO loginDTO) {
        return userFacade.loginByPhone(loginDTO);
    }

    @PostMapping("/refresh")
    public Result refresh(@RequestBody RefreshDTO refreshDTO) {
        Claims claims = JwtUtils.parseRefreshToken(refreshDTO.getRefreshToken());
        String id = (String) claims.get("id");
        String phone = (String) claims.get("phone");
        String token = tokenManager.createTokenByPhone(id, phone, false);
        String refreshToken = tokenManager.getRefreshTokenByPhone(id, phone);
        CEmployEntity cEmployEntity = tokenManager.getColumbusEmploy(id);
        CLoginVO cLoginVO = new CLoginVO();
        cLoginVO
                .setToken(token)
                .setRefreshToken(refreshToken)
                .setAadLogin(false)
        ;
        CMenuVO menuVO = tokenManager.getColumbusMenuVO(Integer.parseInt(id));
        List<String> powerCodeList = tokenManager.getColumbusPowerCodeListByEmployId(Integer.parseInt(id));
        cLoginVO.setMenuVO(menuVO);
        cLoginVO.setPowerCodeList(powerCodeList);
        Date date = tokenManager.verifyPasswordNeedModify(DateTimeUtil.parseTimeStamp0000(cEmployEntity.getUpdatedPasswordTime()));
        if (new Date().after(date)) {
            cLoginVO.setNeedModifyPassWord(1);
        } else {
            cLoginVO.setNeedModifyPassWord(0);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 7);
        cLoginVO.setPasswordExpireTime(DateTimeUtil.formatDateStringCN(calendar.getTime()));
        log.info("========> login  success ");
        return Result.success(cLoginVO);
    }

    @PostMapping("/completeSignature")
    public Result completeSignature(@RequestBody SignatureDTO signatureDTO) {
        return userFacade.completeSignature(signatureDTO);
    }



}