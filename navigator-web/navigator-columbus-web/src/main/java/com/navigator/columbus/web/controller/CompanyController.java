package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/company")
public class CompanyController {
    @Autowired
    private CompanyFacade companyFacade;

    @PostMapping("/queryCompanyList")
    public Result queryCompanyList() {
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        return Result.success(companyEntityList);
    }

    @GetMapping("/getAllCompanyBySyncSystem")
    public Result<List<CompanyEntity>> getAllCompanyBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(companyFacade.getAllCompanyBySyncSystem(syncSystem));
    }
}
