package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.columbus.CEmployPermissionFacade;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.pojo.dto.RoleAuthDTO;
import com.navigator.admin.pojo.dto.RoleAuthMenuDTO;
import com.navigator.admin.pojo.dto.RoleAuthPowerDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.security.authentication.TokenManager;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/role")
@Deprecated
public class RoleController {
    @Autowired
    private CRoleFacade roleFacade;
    @Autowired
    private CEmployPermissionFacade employPermissionFacade;
    @Autowired
    private TokenManager tokenManager;


    @PostMapping("/queryRoleList")
    public Result queryRoleList(@RequestBody QueryDTO<CRoleQueryDTO> roleQueryDTO) {
        return roleFacade.queryRoleList(roleQueryDTO);
    }

//not using


    @PostMapping("/addEmployRole")
    public Result addEmployRole(@RequestBody CEmployRoleDTO employRoleDTO) {
        employPermissionFacade.addEmployRole(employRoleDTO);
        return Result.success();
    }

    @GetMapping("/addEmployRoles")
    public Result addEmployRoles(@RequestParam("employId") Integer employId, @RequestParam("roleIds") String roleIds, @RequestParam("roleDefId") Integer roleDefId, @RequestParam("categoryId") Integer categoryId, @RequestParam("customerIds") String customerIds) {
        employPermissionFacade.addEmployRoles(employId, roleIds, roleDefId, categoryId, customerIds);
        return Result.success();
    }

    @PostMapping("/deleteEmployRole")
    public Result deleteEmployRole(@RequestBody CEmployRoleDTO employRoleDTO) {
        employPermissionFacade.deleteEmployRole(employRoleDTO);
        return Result.success();
    }

    @GetMapping("/queryRoleDefDetail")
    Result<RoleQueryVO> queryRoleDefDetail(@RequestParam("roleDefId") Integer roleDefId) {
        Result<RoleQueryVO> roleQueryVOResult = roleFacade.queryRoleDefDetail(roleDefId);
        return roleQueryVOResult;
    }

    @PostMapping("/queryRoleByCondition")
    Result<List<RoleQueryVO>> queryRoleByCondition(@RequestBody CEmployRoleDTO employRoleDTO) {
        return roleFacade.queryRoleByCondition(employRoleDTO);
    }

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    @ApiOperation(value = "根据条件：获取已授权的菜单树及权限树")
    @PostMapping("/getRoleAuth")
    Result<RoleAuthDTO> getRoleAuth(@RequestBody RoleAuthQO roleAuthQO) {
        if (StringUtil.isNullBlank(roleAuthQO.getUserId())) {
            roleAuthQO.setUserId(Integer.parseInt(JwtUtils.getCurrentUserId()));
        }
        Result<RoleAuthDTO> result = roleFacade.getRoleAuth(roleAuthQO);
        //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 start
        if (!result.isSuccess() && null == result.getData()) {
            return result;
        }
        //case:1002963 哥伦布用户获取权限异常处理 Author:Wan 2025-02-21 end
        RoleAuthDTO roleAuthDTO = result.getData();
        CEmployEntity cEmployEntity = tokenManager.getColumbusEmploy(roleAuthQO.getUserId().toString());
        roleAuthDTO.setSignatureStatus(cEmployEntity.getSignatureStatus());
        Date date = tokenManager.verifyPasswordNeedModify(DateTimeUtil.parseTimeStamp0000(cEmployEntity.getUpdatedPasswordTime()));
        if (new Date().after(date)) {
            roleAuthDTO.setNeedModifyPassWord(1);
        } else {
            roleAuthDTO.setNeedModifyPassWord(0);
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 7);
        roleAuthDTO.setPasswordExpireTime(DateTimeUtil.formatDateStringCN(calendar.getTime()));
        return result;
    }

}
