package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.AnnouncementFacade;
import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/announcement")
public class AnnouncementController {
    @Autowired
    private AnnouncementFacade announcementFacade;

    @GetMapping("/queryColumbusAnnouncement")
    Result queryColumbusAnnouncement() {
        return announcementFacade.queryAnnouncementBySystem(SystemEnum.COLUMBUS.getValue());
    }

    @PostMapping("/queryAnnouncementList")
    Result queryAnnouncementList(@RequestBody QueryDTO<AnnouncementDTO> queryDTO) {
        return announcementFacade.queryAnnouncementList(queryDTO);
    }
}