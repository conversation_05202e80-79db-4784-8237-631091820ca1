package com.navigator.columbus.web.controller;

import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.facade.TempateFacade;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/30
 */
@RestController
@RequestMapping("/message")
public class MessageController {

    @Resource
    private MessageFacade messageFacade;

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryInmail")
    public Result queryInmail(@RequestBody QueryDTO<DbmInmailEntity> queryDTO) {
        return messageFacade.queryInmail(queryDTO);
    }

    /**
     * 消息标记已读
     *
     * @param id
     * @return
     */
    @GetMapping("/readInmail")
    public Result readInmail(@RequestParam(value = "id", required = false) List<Integer> id) {
        return messageFacade.readInmail(id);
    }


    /**
     * 站内信数量
     *
     * @return
     */
    @GetMapping("/queryInmailCount")
    public Result queryInmailCount() {
        return messageFacade.queryInmailCount(SystemEnum.COLUMBUS.getValue());
    }
}
