//package com.navigator.columbus.web.security.permission;
//
//import com.navigator.security.permission.MySecurityMetadataSource;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.stereotype.Component;
//
///**
// * Description: No Description
// * Created by <PERSON><PERSON><PERSON> on 2021/10/27 19:42
// */
//
//@Slf4j
//@Component
//public class MyColumbusSecurityMetadataSource extends MySecurityMetadataSource implements CommandLineRunner {
//
//
//    @Override
//    public void run(String... args) {
////        List<OperationEntity> operationEntityList = operationFacade.getOperationList(2);
////        for (OperationEntity operationEntity : operationEntityList) {
////            Resource resource = new Resource();
////            resource.setId(Long.valueOf(operationEntity.getId()));
////            resource.setName(operationEntity.getName());
////            resource.setPath(operationEntity.getUrl());
////            RESOURCES.add(resource);
////        }
//    }
//}
