package com.navigator.columbus.web.security.filter;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.columbus.web.security.entity.UserDetail;
import com.navigator.columbus.web.service.LoginLogRecordService;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.ResponseUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.security.authentication.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;


@Slf4j
public class TokenLoginFilter extends UsernamePasswordAuthenticationFilter {

    private AuthenticationManager authenticationManager;
    private TokenManager tokenManager;
    private LoginLogRecordService loginLogRecordService;
    private LoginDTO currentLoginDTO; // 存储当前登录请求的DTO

    @Value("${token.expire}")
    private Integer tokenExpire;

    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
    public TokenLoginFilter(AuthenticationManager authenticationManager, TokenManager tokenManager, LoginLogRecordService loginLogRecordService) {
        this.authenticationManager = authenticationManager;
        this.tokenManager = tokenManager;
        this.loginLogRecordService = loginLogRecordService;
        this.setPostOnly(false);
        this.setRequiresAuthenticationRequestMatcher(new AntPathRequestMatcher("/user/verifyCode", "POST"));
    }
    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse res) throws AuthenticationException {
        try {
            ServletInputStream inputStream = req.getInputStream();
            if (null != req)
                inputStream = req.getInputStream();
            String info = "";
            if (null != inputStream) {
                info = inputStream.toString();
                info = info.replaceAll("[\n\r\t]", "_");
            }
            logger.info(info);
            LoginDTO loginDTO = new ObjectMapper().readValue(req.getInputStream(), LoginDTO.class);
            // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
            this.currentLoginDTO = loginDTO; // 保存登录DTO供后续使用
            String detail = JSON.toJSONString(loginDTO);
            return authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginDTO.getPhone(), detail, new ArrayList<>()));
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 登录成功
     *
     * @param req
     * @param res
     * @param chain
     * @param auth
     */
    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                            Authentication auth) {
        log.info("========> start login ");
        UserDetail userDetail = (UserDetail) auth.getPrincipal();

        String token = tokenManager.createTokenByPhone(String.valueOf(userDetail.getEmployEntity().getId()), userDetail.getEmployEntity().getPhone(), false);
        String refreshToken = tokenManager.getRefreshTokenByPhone(String.valueOf(userDetail.getEmployEntity().getId()), userDetail.getEmployEntity().getPhone());
        CLoginVO cLoginVO = new CLoginVO();
        CEmployEntity cEmployEntity = userDetail.getEmployEntity();
        cLoginVO
                .setToken(token)
                .setRefreshToken(refreshToken)
                .setId(cEmployEntity.getId())
                .setName(cEmployEntity.getName())
                .setCustomerName(cEmployEntity.getCustomerName())
                .setAadLogin(false)
                //.setSignatureStatus(cEmployEntity.getSignatureStatus())
                .setPasswordModifyStatus(cEmployEntity.getPasswordModifyStatus())
        ;
        log.info("========> login log employId:{}", cEmployEntity.getId());
        //CMenuVO cMenuVO = tokenManager.getColumbusMenuVO(cEmployEntity.getId());

        cLoginVO.setCEmployCustomerVOS(tokenManager.queryCEmployCustomerVOByCEmployId(cEmployEntity.getId()));
        //List<String> powerCodeList = tokenManager.getColumbusPowerCodeListByEmployId(cEmployEntity.getId());
        //cLoginVO.setMenuVO(cMenuVO);
        //cLoginVO.setPowerCodeList(powerCodeList);
        Date date = tokenManager.verifyPasswordNeedModify(DateTimeUtil.parseTimeStamp0000(userDetail.getEmployEntity().getUpdatedPasswordTime()));
        if (new Date().after(date)) {
            cLoginVO.setNeedModifyPassWord(1);
        } else {
            cLoginVO.setNeedModifyPassWord(0);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, 7);
        cLoginVO.setPasswordExpireTime(DateTimeUtil.formatDateStringCN(calendar.getTime()));
        log.info("========> login  success ");

        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
        // 记录登录成功日志
        if (loginLogRecordService != null && currentLoginDTO != null) {
            loginLogRecordService.recordColumbusSuccessLog(currentLoginDTO, cLoginVO);
        }
        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End

        ResponseUtil.out(res, Result.success(cLoginVO));
    }

    /**
     * 登录失败
     *
     * @param request
     * @param response
     * @param e
     */
    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response, AuthenticationException e) {
        log.info("========> login  failed ");

        // 记录登录失败日志
        if (loginLogRecordService != null && currentLoginDTO != null) {
            String failureReason = e != null ? e.getMessage() : "认证失败";
            loginLogRecordService.recordColumbusFailureLog(currentLoginDTO, failureReason);
        }

        if (e != null) {
            ResponseUtil.out(response, Result.failure(e.getMessage()));
        } else {
            ResponseUtil.out(response, Result.failure(ResultCodeEnum.USER_AUTH_FAILURE));
        }
    }
    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End
}
