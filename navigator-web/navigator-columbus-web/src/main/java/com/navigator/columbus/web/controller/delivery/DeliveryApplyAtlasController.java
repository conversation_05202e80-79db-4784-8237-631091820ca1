package com.navigator.columbus.web.controller.delivery;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.delivery.facade.DeliveryApplyAtlasFacade;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyContractAtlasQO;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.qo.PreCheckDeliveryApplyQO;
import com.navigator.delivery.pojo.vo.DeliveryApplyAssignContractVO;
import com.navigator.delivery.pojo.vo.DeliveryApplyContractAtlasVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 提货申请：atlas
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "提货委托-atlas")
@RestController
@RequestMapping("/deliveryApplyAtlas")
public class DeliveryApplyAtlasController {

    @Resource
    private DeliveryApplyFacade deliveryApplyFacade;

    @Resource
    private DeliveryApplyAtlasFacade deliveryApplyAtlasFacade;

    @Resource
    private HttpServletResponse response;

    @ApiOperation("提货委托申请列表")
    @PostMapping("/getDeliveryApplyList")
    public Result getDeliveryApplyList(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        applyQO.setThirdSys(SystemEnum.ATLAS.getValue());
        applyQO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyFacade.getDeliveryApplyList(queryDTO);
    }

    /**
     * 下载提货申请单
     *
     * @param queryDTO
     */
    @ApiOperation("下载提货申请单")
    @PostMapping("/downloadDeliveryApply")
    public void downloadDeliveryApply(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {

        Result result;
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        applyQO.setThirdSys(SystemEnum.ATLAS.getValue());
        applyQO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        List<Integer> applyIds = applyQO.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {
            queryDTO.setPageSize(-1);
            result = deliveryApplyFacade.getDeliveryApplyList(queryDTO);
        } else {
            result = deliveryApplyFacade.getDeliveryApplyListByIds(applyIds);
        }
        if (!result.isSuccess()) {
            throw new BusinessException("无数据可导出！");
        }
        List<DeliveryApplyVOEntity> applyVOEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyVOEntity.class);

        if (CollectionUtil.isEmpty(applyVOEntityList)) {
            throw new BusinessException("无数据可导出！");
        }

        applyVOEntityList.forEach(applyVOEntity -> {
            // 导出豆油不展示包装品类
            if (applyVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
                applyVOEntity.setGoodsSpec(null)
                        .setGoodsPackage(null);
            }
        });
        // 将DeliveryApplyVOEntity转换为DeliveryApplyColumbusAtlasExportDTO
        List<DeliveryApplyColumbusAtlasExportDTO> exportList = BeanConvertUtils.convert2List(DeliveryApplyColumbusAtlasExportDTO.class, applyVOEntityList);
        exportList.forEach(item -> {
            if (SystemEnum.MAGELLAN.getValue().equals(item.getTriggerSys())) {
                item.setTriggerSysName("麦哲伦");
            } else {
                item.setTriggerSysName(item.getCustomerName());
            }
        });

        if (CollectionUtil.isEmpty(exportList)) {
            throw new BusinessException("无数据可导出！");
        }

        String fileName = "ColumbusDeliveryApplyAtlas" + DateUtil.format(DateUtil.date(), "MMddHHmmss");

        EasyPoiUtils.exportExcel(exportList, null, "ColumbusDeliveryApplyAtlas", DeliveryApplyColumbusAtlasExportDTO.class, fileName, response);
    }

    /**
     * 查询提货可申请汇总列表
     *
     * @param deliveryApplyContractAtlasQO
     * @return
     */
    @ApiOperation("查询提货可申请汇总列表")
    @PostMapping("/getDeliveryApplyContractList")
    public Result<List<DeliveryApplyContractAtlasVO>> getDeliveryApplyContractList(@RequestBody DeliveryApplyContractAtlasQO deliveryApplyContractAtlasQO) {
        return deliveryApplyAtlasFacade.getDeliveryApplyContractList(deliveryApplyContractAtlasQO.setTriggerSys(SystemEnum.COLUMBUS.getValue()));
    }

    /**
     * 申请提货预校验
     *
     * @param preCheckDeliveryApplyQO
     * @return
     */
    @ApiOperation("申请提货预校验")
    @PostMapping("/preCheckDeliveryApply")
    Result<PreCheckDeliveryApplyDTO> preCheckDeliveryApply(@RequestBody PreCheckDeliveryApplyQO preCheckDeliveryApplyQO) {
        return deliveryApplyAtlasFacade.preCheckDeliveryApply(preCheckDeliveryApplyQO.setTriggerSys(SystemEnum.COLUMBUS.getValue()));
    }

    /**
     * 保存/提交申请单
     *
     * @param deliveryApplyDTO
     * @return
     */
    @ApiOperation("保存/提交申请单")
    @PostMapping("/saveOrSubmitDeliveryApply")
    public Result<String> saveOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO) {
        deliveryApplyDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyAtlasFacade.saveOrSubmitDeliveryApply(deliveryApplyDTO);
    }

    /**
     * 编辑提货申请单
     *
     * @param deliveryApplyDTO
     * @return
     */
    @ApiOperation("编辑提货申请单")
    @PostMapping("/updateOrSubmitDeliveryApply")
    public Result<String> updateOrSubmitDeliveryApply(@RequestBody DeliveryApplyDTO deliveryApplyDTO) {
        deliveryApplyDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyAtlasFacade.updateOrSubmitDeliveryApply(deliveryApplyDTO);
    }

    /**
     * 提交申请单
     *
     * @param applyId
     * @return
     */
    @ApiOperation("提交申请单")
    @GetMapping("/submitDeliveryApplyById")
    public Result<Boolean> submitDeliveryApplyById(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyAtlasFacade.submitDeliveryApplyById(applyId, SystemEnum.COLUMBUS.getValue());
    }

    /**
     * 批量提交申请单
     *
     * @param applyIds
     * @return
     */
    @ApiOperation("批量提交申请单")
    @GetMapping("/batchSubmitDeliveryApply")
    public Result<String> batchSubmitDeliveryApply(@ApiParam("申请单id集合") @RequestParam("applyIds") List<Integer> applyIds) {
        return deliveryApplyAtlasFacade.batchSubmitDeliveryApply(applyIds);
    }

    /**
     * 作废提货申请单
     *
     * @param applyInvalidDTO
     * @return
     */
    @ApiOperation("作废提货申请单")
    @PostMapping("/applyInvalidDeliveryApply")
    public Result<Boolean> applyInvalidDeliveryApply(@RequestBody DeliveryApplyInvalidDTO applyInvalidDTO) {
        applyInvalidDTO.setTriggerSys(SystemEnum.COLUMBUS.getValue());
        return deliveryApplyAtlasFacade.applyInvalidDeliveryApply(applyInvalidDTO);
    }

    /**
     * 批量上传-模板地址
     */
    @ApiOperation("批量上传-模板地址")
    @GetMapping("/deliveryApplyImportTemplate")
    public Result<String> deliveryApplyImportTemplate() {
        return deliveryApplyAtlasFacade.deliveryApplyImportTemplate(SystemEnum.COLUMBUS.getValue());
    }

    /**
     * 批量上传-导入文件
     *
     * @param file
     * @return
     */
    @ApiOperation("批量上传-导入文件")
    @PostMapping("/deliveryApplyImportFile")
    Result<DeliveryApplyAtlasImportResultDTO> deliveryApplyImportFile(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "customerId") Integer customerId) {
        return deliveryApplyAtlasFacade.deliveryApplyImportFile(file, customerId, SystemEnum.COLUMBUS.getValue());
    }

    /**
     * 批量上传-提交
     *
     * @param deliveryApplyAtlasImportDTOList
     * @return
     */
    @ApiOperation("批量上传-提交")
    @PostMapping("/deliveryApplyImportSubmit")
    Result deliveryApplyImportSubmit(@RequestBody List<DeliveryApplyAtlasImportDTO> deliveryApplyAtlasImportDTOList) {
        return deliveryApplyAtlasFacade.deliveryApplyImportSubmit(deliveryApplyAtlasImportDTOList, SystemEnum.COLUMBUS.getValue());
    }

    @ApiOperation("获取重新分配的合同")
    @PostMapping("/getReAssignContractList")
    public Result<DeliveryApplyAssignContractVO> getReAssignContractList(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO) {
        return deliveryApplyAtlasFacade.getReAssignContractList(assignContractDTO.setBuCode(BuCodeEnum.ST.getValue()));
    }

}
