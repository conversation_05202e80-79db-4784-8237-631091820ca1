package com.navigator.columbus.web.remote;

import com.navigator.columbus.web.security.entity.RequestPowerBI;
import com.navigator.common.config.FeignFormSupportConfig;
import org.json.JSONObject;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

// url = "http://localhost:13600"
// ${BIService.url}
// , configuration = FeignFormSupportConfig.class

@FeignClient(name = "navigator-BI-service", url = "${BIService.url}",qualifier= "serviceProviderClient", configuration = FeignFormSupportConfig.class)
public interface ServiceProviderClient {
    @PostMapping("/api/service_provider/send_request_powerBI")
    ResponseEntity<String> sendRequestPowerBI(@RequestBody RequestPowerBI requestPowerBI);
}
