package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.pojo.entity.CEmployCustomerEntity;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/14
 */

@RestController
@RequestMapping("/cEmployCustomer")
public class CEmployCustomerController {

    @Resource
    private CEmployCustomerFacade cEmployCustomerFacade;

    /**
     * cEmploy绑定customerId
     *
     * @param cEmployCustomerEntity
     * @return
     */
    @PostMapping("/saveCEmployCustomer")
    public Result saveCEmployCustomer(@RequestBody CEmployCustomerEntity cEmployCustomerEntity){
       return Result.success(cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity));
    }

    /**
     * 根据用户id和customerId查询数据
     *
     * @param cEmployId
     * @param customerId
     * @return
     */
    @GetMapping("/queryCEmployCustomerByEmployIdAndCustomerId")
    public Result queryCEmployCustomerByEmployIdAndCustomerId(@RequestParam(value = "cEmployId") Integer cEmployId,
                                                                            @RequestParam(value = "customerId") Integer customerId){
        return Result.success(cEmployCustomerFacade.queryCEmployCustomerByEmployIdAndCustomerId(cEmployId,customerId));
    }

    /**
     * 修改用户主体绑定数据
     *
     * @param cEmployCustomerEntity
     * @return
     */
    @PostMapping("/updateCEmployCustomer")
    public Result updateCEmployCustomer(@RequestBody CEmployCustomerEntity cEmployCustomerEntity){
        return Result.success(cEmployCustomerFacade.updateCEmployCustomer(cEmployCustomerEntity));
    }
    /**
     * 根据cEmployId查询数据
     *
     * @param id
     * @return
     */
    @GetMapping("/getCEmployCustomerById")
    public Result getCEmployCustomerById(@RequestParam(value = "id") Integer id){
        return Result.success(cEmployCustomerFacade.getCEmployCustomerById(id));
    }

    /**
     * 哥伦布用户数据绑定
     */
    @GetMapping("/cEmployDataMigration")
    public Result cEmployDataMigration(){
        cEmployCustomerFacade.cEmployDataMigration();
        return Result.success();
    }

    @GetMapping("/queryCEmployCustomerVO")
    public Result queryCEmployCustomerVOByCEmployId(@RequestParam(value = "cEmployId") Integer cEmployId){
        return Result.success(cEmployCustomerFacade.queryCEmployCustomerVOByCEmployId(cEmployId));
    }

    @GetMapping("/getCEmployCustomerPower")
    public Result getCEmployCustomerPower(@RequestParam(value = "customerId") Integer customerId){
        return cEmployCustomerFacade.getCEmployCustomerPower(customerId);
    }

    @GetMapping("/verifyUserStatus")
    public Result verifyUserStatus(@RequestParam(value = "customerId") Integer customerId){
        return cEmployCustomerFacade.verifyUserStatus(customerId);
    }
}
