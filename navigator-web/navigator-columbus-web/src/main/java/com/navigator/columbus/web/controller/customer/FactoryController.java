package com.navigator.columbus.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.entity.FactoryEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-17 21:53
 */
@RestController
@RequestMapping("/factory")
public class FactoryController {
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    private FactoryFacade factoryFacade;

    /**
     * 获取所有工厂信息
     *
     * @return 工厂集合
     */
    @GetMapping("/getAllFactory")
    public Result getAllFactory(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(factoryWarehouseFacade.getAllFactory(status));
    }

    @GetMapping("/getAllFactoryBySyncSystem")
    public Result<List<FactoryEntity>> getAllFactoryBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(factoryFacade.getAllFactoryBySyncSystem(syncSystem));
    }

    /**
     * 根据ID获取工厂信息
     *
     * @param factoryId 工厂ID
     * @return 工厂详情信息
     */
    @GetMapping("/getFactoryDetailById")
    public Result getFactoryDetailById(@RequestParam(value = "factoryId") Integer factoryId) {
        return Result.success(factoryWarehouseFacade.getFactoryDetailById(factoryId));
    }

    @GetMapping("/queryFactoryByCompanyId")
    public Result queryFactoryByCompanyId(@RequestParam("companyId") Integer companyId) {
        return Result.success(factoryWarehouseFacade.queryFactoryByCompanyId(companyId));
    }

    @GetMapping("/queryFactoryList")
    public Result queryFactoryList() {
        return Result.success(factoryWarehouseFacade.queryFactoryList());
    }
}
