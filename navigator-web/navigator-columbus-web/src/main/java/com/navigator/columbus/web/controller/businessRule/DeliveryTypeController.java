package com.navigator.columbus.web.controller.businessRule;

import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-06 18:13
 */
@RestController
@RequestMapping("/deliveryType")
public class DeliveryTypeController {
    @Resource
    private DeliveryTypeFacade deliveryTypeFacade;

    @GetMapping("/getAllDeliveryTypeList")
    public Result getAllDeliveryTypeList(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                         @RequestParam(value = "siteCode", required = false) String siteCode,
                                         @RequestParam(value = "buCode", required = false) String buCode,
                                         @RequestParam(value = "type", required = false) Integer type) {
        return Result.success(deliveryTypeFacade.getAllDeliveryTypeList(DisableStatusEnum.ENABLE.getValue(), categoryId, siteCode, buCode, type));
    }


    /**
     * 根据品类-获取提货类型（分组）
     *
     * @param categoryId 品类ID
     * @return 分组的提货类型信息
     */
    @GetMapping("/getDeliveryTypeByCategoryId")
    public List<DeliveryTypeVO> getDeliveryTypeByCategoryId(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                                            @RequestParam(value = "siteCode", required = false) String siteCode,
                                                            @RequestParam(value = "buCode", required = false) String buCode) {
        return deliveryTypeFacade.getDeliveryTypeByCategoryId(categoryId, siteCode, buCode);
    }
}
