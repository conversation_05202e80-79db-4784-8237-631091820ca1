package com.navigator.columbus.web.controller.goods;


import cn.hutool.core.lang.tree.Tree;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.dto.CategoryDTO;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 品类信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@RestController
@RequestMapping("/category")
public class CategoryController {
    @Resource
    private CategoryFacade categoryFacade;

    /**
     * 获取所有商品-品类信息
     *
     * @return 品类集合
     */
    @GetMapping("/getAllCategoryList")
    public Result getAllCategoryList() {
        return Result.success(categoryFacade.getAllCategoryList(2));
    }

    /**
     * 根据条件：获取品类品种分页
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryCategoryDTOPage")
    Result<CategoryDTO> queryCategoryDTOPage(@RequestBody QueryDTO<CategoryQO> queryDTO) {
        return Result.page(categoryFacade.queryCategoryDTOPage(queryDTO));
    }

    /**
     * 获取菜单结构
     *
     * @return
     */
    @PostMapping("/queryCategoryMenu")
    Result<List<Tree<Integer>>> queryCategoryMenu(@RequestParam Integer customerId) {
        return Result.success(categoryFacade.queryCategoryMenu(2, customerId));
    }

}
