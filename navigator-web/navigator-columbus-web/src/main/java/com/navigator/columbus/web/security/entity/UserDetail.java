package com.navigator.columbus.web.security.entity;

import com.navigator.admin.pojo.entity.CEmployEntity;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/10/27 20:07
 */
@Getter
@ToString
@EqualsAndHashCode(callSuper = false)
public class UserDetail extends User {

    /**
     * 我们自己的用户实体对象，这里省略掉get/set方法
     */
    private CEmployEntity employEntity;

    public UserDetail(CEmployEntity employEntity, Collection<? extends GrantedAuthority> authorities) {
        // 必须调用父类的构造方法，初始化用户名、密码、权限
        super(employEntity.getPhone(), employEntity.getPassword(), authorities);
        this.employEntity = employEntity;
    }
}
