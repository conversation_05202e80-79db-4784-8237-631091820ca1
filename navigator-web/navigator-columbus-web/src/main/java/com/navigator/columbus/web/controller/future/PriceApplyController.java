package com.navigator.columbus.web.controller.future;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/1/5 18:17
 */
@RestController
@RequestMapping("/future")
public class PriceApplyController {

    @Resource
    private PriceApplyFacade priceApplyFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private CustomerDetailFacade customerDetailFacade;
    @Resource
    private EmployFacade employFacade;
    @Resource
    private CEmployFacade cEmployFacade;

    /**
     * 申请点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/priceApply")
    public Result priceApply(@RequestBody PriceApplyDTO priceApplyDTO) {

        /*if (priceApplyDTO.getType() == PriceTypeEnum.REVERSE_PRICING.getValue()) {

            CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.valueOf(JwtUtils.getCurrentUserId()));

            CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailList(cEmployEntity.getCustomerId(), priceApplyDTO.getCategoryId());
            //判断客户是否油反点价服务
            if (null != customerDetailEntity && customerDetailEntity.getIsReversePrice().equals(GeneralEnum.NO.getValue())) {
                if (customerDetailEntity.getIsReversePrice().equals(GeneralEnum.NO.getValue())) {
                    Result result = new Result();
                    result.setCode(1);
                    result.setData("未开通反点价服务");
                    result.setMessage("未开通反点价服务");
                    return result;
                }
            }
        }*/
        //哥伦布系统发起申请单
        priceApplyDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        return priceApplyFacade.priceApply(priceApplyDTO);
    }

    /**
     * 申请改单/撤单  点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/modifyPriceApply")
    public Result modifyPriceApply(@RequestBody PriceApplyDTO priceApplyDTO) {
        priceApplyDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        return priceApplyFacade.modifyPriceApply(priceApplyDTO);
    }

    /**
     * 分页查询申请单信息
     *
     * @param applyBOQueryDTO
     * @return
     */
    @PostMapping("/queryPriceApply")
    public Result queryPriceApply(@RequestBody QueryDTO<PriceApplyBO> applyBOQueryDTO) {
        return priceApplyFacade.queryPriceApply(applyBOQueryDTO, SystemEnum.COLUMBUS);
    }

    /**
     * 查询申请单申请明细
     *
     * @param id
     * @return
     */
    @GetMapping("/getpriceApplyDetail")
    public Result getpriceApplyDetail(@RequestParam("id") Integer id) {
        return priceApplyFacade.getpriceApplyDetail(id);
    }

    /**
     * 查询申请单的改撤单记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceApplyLog")
    public Result queryPriceApplyLog(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceApplyLog(priceApplyId);
    }

    @GetMapping(value = "/getDealListByStructureContractId")
    public Result getDealListByStructureContractId(@RequestParam(value = "contractId") Integer contractId){
        return priceApplyFacade.getDealListByStructureContractId(contractId);
    }

    /**
     * columbus 查询点转反状态数量
     *
     * @param employId
     * @return
     */
    @GetMapping("/columbusApplyAllocateStatus")
    public Result columbusApplyAllocateStatus(@RequestParam(value = "employId") Integer employId,
                                              @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                              @RequestParam(value = "salesType", required = false) Integer salesType) {

        return priceApplyFacade.columbusApplyAllocateStatus(employId, goodsCategoryId, salesType);
    }

    /**
     * 可反点价量
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/mayReversePricing")
    public Result mayReversePricing(@RequestBody PriceApplyDTO priceApplyDTO) {
        return priceApplyFacade.mayReversePricing(priceApplyDTO);
    }

    /**
     * 申请单操作记录
     *
     * @param code
     * @return
     */
    @GetMapping("/queryOperationLog")
    public Result queryOperationLog(@RequestParam("code") String code) {
        return operationLogFacade.queryOperationDetailByReferBizCode(code, OperationSourceEnum.CUSTOMER.getValue());
    }


}
