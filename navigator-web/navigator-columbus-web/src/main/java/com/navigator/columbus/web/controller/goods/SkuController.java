package com.navigator.columbus.web.controller.goods;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.SkuFacade;
import com.navigator.goods.pojo.dto.SkuAddDTO;
import com.navigator.goods.pojo.dto.SkuDTO;
import com.navigator.goods.pojo.dto.SkuRefreshDTO;
import com.navigator.goods.pojo.entity.SkuEntity;
import com.navigator.goods.pojo.qo.SkuQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * SKU 货品 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@RestController
@RequestMapping("/sku")
public class SkuController {

    @Resource
    private SkuFacade skuFacade;

    /**
     * 根据条件：获取SKU 货品分页
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySkuDTOPage")
    Result<SkuDTO> querySkuDTOPage(@RequestBody QueryDTO<SkuQO> queryDTO) {
        return Result.page(skuFacade.querySkuDTOPage(queryDTO));
    }

    /**
     * 根据条件：获取SKU 货品DTO列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/querySkuDTOList")
    Result<List<SkuDTO>> querySkuDTOList(@RequestBody SkuQO condition) {
        return Result.success(skuFacade.querySkuDTOList(condition));
    }

    /**
     * 根据ID：获取SKU 货品
     *
     * @param id
     * @return
     */
    @GetMapping("/getSkuById")
    Result<SkuEntity> getSkuById(@RequestParam(value = "id") Integer id) {
        return Result.success(skuFacade.getSkuById(id));
    }

    /**
     * 根据编码：获取SKU货品
     *
     * @param skuNo
     * @return
     */
    @GetMapping("/getSkuBySkuNo")
    Result<SkuEntity> getSkuBySkuNo(@RequestParam(value = "skuNo") String skuNo) {
        return Result.success(skuFacade.getSkuBySkuNo(skuNo));
    }

}
