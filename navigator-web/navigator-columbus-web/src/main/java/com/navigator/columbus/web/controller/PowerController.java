package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.columbus.CPowerFacade;
import com.navigator.admin.pojo.dto.columbus.CPowerDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/power")
@Deprecated
public class PowerController {
    @Autowired
    private CPowerFacade powerFacade;

    @PostMapping("/saveOrUpdatePower")
    public Result saveOrUpdatePower(@RequestBody CPowerDTO powerDTO) {
        return powerFacade.saveOrUpdatePower(powerDTO);
    }

    @PostMapping("/queryPowerByRoleId")
    public Result queryPowerByRoleId(@RequestBody CPowerDTO powerDTO) {
        return powerFacade.queryPowerByRoleId(powerDTO);
    }
    
}
