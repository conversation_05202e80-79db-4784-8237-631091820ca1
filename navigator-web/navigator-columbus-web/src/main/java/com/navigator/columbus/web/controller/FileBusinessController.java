package com.navigator.columbus.web.controller;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONException;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.dto.FileBaseInfoDTO;
import com.navigator.common.dto.FileBusinessRelationDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.WatermarkUtil;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * <AUTHOR>
 * @since 2021-11-29 18:23
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileBusinessController {
    @Resource
    private FileBusinessFacade fileBusinessFacade;

    @Autowired
    private AzureBlobUtil azureBlobUtil;


    /**
     * 单个文件上传
     *
     * @param file 文件信息
     * @return 上传的单个文件基本信息
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    @ResponseBody
    @RequestMapping(value = "/singleUpload")
    public Result singleUploadV3(@RequestParam("file") MultipartFile file) {
        //调用工具类完成上传，返回相关数据到页面
        String path = FileConstant.FILE_UPLOAD + "magellan/" + DateTimeUtil.formatDateString(DateTime.now());

        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.upload(file, path, BlobFileContextEnum.PDF.getFileType());
        if (fileBaseInfoDTO == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }

        //记录文件信息，并返回页面
        return Result.success(fileBusinessFacade.saveFileInfo(fileBaseInfoDTO));
    }

    /**
     * 文件下载
     *
     * @param path     下载路径
     * @param response 请求
     * @return 下载文档结果
     */
    @GetMapping("/download")
    public void downloadV3(@RequestParam("path") String path, HttpServletResponse response) {
        azureBlobUtil.download(path, response);
    }

    /**
     * 统一记录存储文件关系
     *
     * @param fileBusinessRelationDTO 记录附件关系请求信息
     */
    @PostMapping("/recordFileRelationInfo")
    public Result recordFileRelationInfo(@RequestBody FileBusinessRelationDTO fileBusinessRelationDTO) {
        return fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);
    }

}
