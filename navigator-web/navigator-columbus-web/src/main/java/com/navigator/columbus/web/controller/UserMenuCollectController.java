package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.UserMenuCollectFacade;
import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.admin.pojo.vo.MenuDetailVO;
import com.navigator.admin.pojo.vo.UserMenuCollectVO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-11 18:27
 **/

@RestController
@RequestMapping("/menuCollect")
@Slf4j
public class UserMenuCollectController {
    @Autowired
    private UserMenuCollectFacade userMenuCollectFacade;

    @PostMapping("/saveUserMenuCollect")
    public Result saveUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect) {
        return userMenuCollectFacade.saveUserMenuCollect(userMenuCollect.setSystem(SystemEnum.COLUMBUS.getValue()));
    }

    @PostMapping("/updateUserMenuCollect")
    public Result updateUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect) {
        return userMenuCollectFacade.updateUserMenuCollect(userMenuCollect);
    }

    @GetMapping("/deleteCollectMenu")
    public Result deleteCollectMenu(@RequestParam(value = "id") Integer id) {
        return userMenuCollectFacade.deleteCollectMenu(id);
    }

    @GetMapping("/sortCollectMenu")
    public Result sortCollectMenu(@RequestParam(value = "menuCollectIdList") List<Integer> menuCollectIdList) {
        return userMenuCollectFacade.sortCollectMenu(menuCollectIdList);
    }

    /**
     * 查询用户的所有有权限的二级菜单列表
     *
     * @param categoryId 分类
     * @param customerId Columbus区分主体
     * @return
     */
    @GetMapping("/getUserPowerMenuList")
    public Result<List<MenuDetailVO>> getUserPowerMenuList(@RequestParam(value = "categoryId") Integer categoryId,
                                                           @RequestParam(value = "customerId", required = false) Integer customerId) {
        return Result.success(userMenuCollectFacade.getUserPowerMenuList(categoryId, customerId, SystemEnum.COLUMBUS.getValue()));
    }


    @GetMapping("/getUserCollectMenuList")
    public Result<List<UserMenuCollectVO>> getUserCollectMenuList(@RequestParam(value = "categoryId") Integer categoryId,
                                                                  @RequestParam(value = "customerId", required = false) Integer customerId) {
        return Result.success(userMenuCollectFacade.getUserCollectMenuList(categoryId,customerId, SystemEnum.COLUMBUS.getValue()));
    }
}
