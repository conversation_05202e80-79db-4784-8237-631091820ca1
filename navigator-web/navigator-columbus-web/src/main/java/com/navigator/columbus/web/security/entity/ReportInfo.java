package com.navigator.columbus.web.security.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class ReportInfo {

    @JsonProperty("embedReports")
    private EmbedReport[] embedReports;

    @JsonProperty("tokenExpiry")
    private String tokenExpiry;

    @JsonProperty("embedToken")
    private String embedToken;


    @Data
    static class EmbedReport {

        @JsonProperty("embedUrl")
        private String embedUrl;
        @JsonProperty("reportId")
        private String reportId;
        @JsonProperty("reportName")
        private String reportName;

    }

}
