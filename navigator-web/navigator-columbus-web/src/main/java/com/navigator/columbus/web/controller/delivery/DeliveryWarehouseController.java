package com.navigator.columbus.web.controller.delivery;


import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 提货库点配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/deliveryWarehouse")
public class DeliveryWarehouseController {
    private final WarehouseFacade warehouseFacade;

    @ApiOperation(value = "查询提货库点配置列表")
    @PostMapping("/getDeliveryWarehouseList")
    public Result<List<WarehouseEntity>> getDeliveryWarehouseList(@RequestBody DeliveryWarehouseDTO warehouseDTO) {
        QueryDTO<WarehouseBO> queryDTO = new QueryDTO<>();
        WarehouseBO condition = new WarehouseBO();
        condition.setName(warehouseDTO.getWarehouseName());
        condition.setCode(warehouseDTO.getWarehouseCode());
        condition.setWarehouseType(warehouseDTO.getWarehouseType());
        condition.setIsUnset(0);
        condition.setStatus(warehouseDTO.getStatus());
        queryDTO.setCondition(condition);
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        return warehouseFacade.getWarehouseList(queryDTO);
    }

}
