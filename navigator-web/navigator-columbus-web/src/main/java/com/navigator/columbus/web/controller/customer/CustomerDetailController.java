package com.navigator.columbus.web.controller.customer;


import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDetailFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/customerDetail")
public class CustomerDetailController {

    @Resource
    private CustomerDetailFacade customerDetailFacade;

    /**
     * 根据客户和品类查询客户配置
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    @GetMapping("/queryCustomerDetailList")
    public Result queryCustomerDetailList(@RequestParam(value = "customerId") Integer customerId,
                                          @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(customerDetailFacade.queryCustomerDetailList(customerId, categoryId));
    }
}
