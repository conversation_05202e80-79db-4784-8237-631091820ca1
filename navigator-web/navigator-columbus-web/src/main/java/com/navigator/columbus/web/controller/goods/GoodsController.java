package com.navigator.columbus.web.controller.goods;

import com.navigator.common.dto.Result;
import com.navigator.goods.facade.GoodsFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/5
 */

@RestController
@RequestMapping("/goods")
public class GoodsController {

    @Resource
    private GoodsFacade goodsFacade;

    /**
     * 根据状态获取所有商品信息
     *
     * @param status 商品状态
     * @return 所有商品名称集合
     */
    @GetMapping("/getAllGoodsList")
    public Result getAllGoodsList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(goodsFacade.getAllGoodsList(status));
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @GetMapping("/getAllGoodsListByCategoryId")
    public Result getAllGoodsListByCategoryId(@RequestParam(value = "status", required = false) Integer status,
                                              @RequestParam(value = "categoryId", required = false) Integer categoryId) {
        return Result.success(goodsFacade.getAllGoodsListByCategoryId(status, categoryId));
    }
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End
}
