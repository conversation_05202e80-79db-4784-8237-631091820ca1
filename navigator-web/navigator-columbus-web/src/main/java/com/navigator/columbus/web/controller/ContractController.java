package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractPaperFacade;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.ContractModifyDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.qo.ContractQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 15:07
 */
@RestController
@RequestMapping("/contract")
public class ContractController {

    @Resource
    private ContractFacade contractFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private ContractPaperFacade contractPaperFacade;
    @Resource
    private WarrantFacade warrantFacade;

    /**
     * Columbus查询合同列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractsColumbus")
    public Result queryContractsColumbus(@RequestBody QueryDTO<QueryContractDTO> queryDTO) {
        return contractFacade.queryContractsColumbus(queryDTO);
    }

    @PostMapping("/queryContract")
    public Result queryContract(@RequestBody QueryDTO<ContractQO> queryDTO) {
        ContractQO contractQO = queryDTO.getCondition();
        contractQO.setTriggerSys(SystemEnum.COLUMBUS.getName());
        return contractFacade.queryContract(queryDTO);
    }

    /**
     * 根据合约号获取当前客户可以分配的合同
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractsByDomainCode")
    public Result queryContractsByDomainCode(@RequestBody QueryDTO<QueryContractDTO> queryDTO) {
        // 期货合约  客户Id  操作类型（1、点价 2、转月 3、反点价）
        QueryContractDTO queryContractDTO = queryDTO.getCondition();

        if (null == queryContractDTO.getSalesType()) {
            queryContractDTO.setSalesType(ContractSalesTypeEnum.SALES.getValue());
        }
        queryDTO.setCondition(queryContractDTO);
        return contractFacade.pageContractsByDomainCode(queryDTO);
    }

    /**
     * 根据合同Id获取合同详情
     *
     * @param contractId
     * @return
     */
    // add required parameter for NumberFormatException issue by Jason Shi at 2025-05-28 start
    @GetMapping("/getContractByContractId")
    public Result getContractByContractId(@RequestParam(value = "contractId", required = true) String contractId) {
        return contractFacade.getContractByContractId(contractId);
    }
    // add required parameter for NumberFormatException issue by Jason Shi at 2025-05-28 end
    /**
     * 根据合同Id获取提货合同
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getDeliveryContractByContractId")
    public Result getDeliveryContractByContractId(@RequestParam("contractId") Integer contractId) {
        return contractFacade.getDeliveryContractByContractId(contractId);
    }

    /**
     * 获取合同操作日志
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractOperationLogs")
    public Result getContractOperationLogs(@RequestParam("contractCode") String contractCode, @RequestParam("id") Integer id) {
        return operationLogFacade.queryOperationLogByCode(contractCode, OperationSourceEnum.CUSTOMER.getValue(), id);
    }

    /**
     * 获取合同操作日志
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getOperationlogs")
    public Result getOperationlogs(@RequestParam("contractId") Integer contractId) {
        return operationLogFacade.queryContractOperationLog(contractId, OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue());
    }

    @GetMapping("/getContractPaper")
    public Result getContractPaper(@RequestParam("contractSignId") Integer contractSignId) {
        return contractPaperFacade.getContractPaper(contractSignId);
    }

    /**
     * 根据合同Id获取电子合同
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractPdfs")
    public Result getContractPdfs(@RequestParam("contractId") String contractId) {
        return contractFacade.getContractPdfs(contractId);
    }

    /**
     * 获取合同含税单价详细
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractUnitPriceDetail")
    public Result getContractUnitPriceDetail(@RequestParam("contractId") String contractId) {
        return contractFacade.getContractUnitPriceDetail(contractId);
    }

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/futureContracts")
    public Result futureContracts(@RequestBody QueryDTO<ContractFuturesDTO> queryDTO) {

        return contractFacade.futureContracts(queryDTO);
    }

    /**
     * 查询合同的拆分和修改的记录
     *
     * @param modifyDTO
     * @return
     */
    @PostMapping("/getModifyLog")
    public Result getContractModifyLog(@RequestBody ContractModifyDTO modifyDTO) {
        return contractFacade.getContractModifyLog(modifyDTO);
    }

    /**
     * 根据合同id关闭合同的尾量
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/closeTailNumByContractId")
    Result closeTailNumByContractId(@RequestParam(value = "contractId") Integer contractId) {
        return contractFacade.closeTailNumByContractId(contractId, SystemEnum.COLUMBUS.getValue());
    }

    /**
     * 根据多选合同关闭合同的尾量
     *
     * @param contractIds 合同id集合
     * @return
     */
    @GetMapping("/batchCloseTailNum")
    Result batchCloseTailNum(@RequestParam(value = "contractIds") List<Integer> contractIds) {
        return contractFacade.batchCloseTailNum(contractIds, SystemEnum.COLUMBUS.getValue());
    }

    /**
     * 根据合同id查询合同的尾量关闭记录
     *
     * @param contractCode 合同id
     * @return
     */
    @GetMapping("/getCloseTailNumRecord")
    Result getCloseTailNumRecord(@RequestParam(value = "contractCode") String contractCode) {
        return contractFacade.getCloseTailNumRecord(contractCode);
    }

    @GetMapping("/getContractTraceList")
    public Result getContractTraceList(@RequestParam("contractId") Integer contractId) {
        return contractFacade.getContractTraceList(contractId);
    }

//    @GetMapping("/getCargoRightsContractById")
//    public Result getCargoRightsContractById(@RequestParam("contractId") Integer contractId) {
//        return contractFacade.getCargoRightsContractById(contractId);
//    }


    /**
     * 根据合同编号查询合同注销记录
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/queryWarrantCancellationByContractCode")
    public Result queryWarrantCancellationByContractCode(@RequestParam("contractCode") String contractCode) {
        return warrantFacade.queryWarrantCancellationByContractCode(contractCode);
    }

}
