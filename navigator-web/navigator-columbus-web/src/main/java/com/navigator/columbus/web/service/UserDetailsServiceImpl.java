package com.navigator.columbus.web.service;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.columbus.web.security.entity.UserDetail;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * Description: 自定义userDetailsService - 认证用户详情
 * Created by <PERSON><PERSON><PERSON> on 2021/10/27 15:28
 */

@Service("userDetailsService")
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private EmployFacade employFacade;

    @Autowired
    PasswordEncoder passwordEncoder;

    /***
     * 根据账号获取用户信息
     * @param username:
     * @return: org.springframework.security.core.userdetails.UserDetails
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 从数据库中取出用户信息
        Result employResult = employFacade.getEmployByPhone(username, SystemEnum.COLUMBUS.getValue());
        List<CEmployEntity> employEntities = JSON.parseArray(JSON.toJSONString(employResult.getData()), CEmployEntity.class);
        // 判断用户是否存在 若没查询到一定要抛出该异常，这样才能被Spring Security的错误处理器处理
        if (CollectionUtils.isEmpty(employEntities)) {
            throw new UsernameNotFoundException("没有找到该用户!");
        }
        return new UserDetail(employEntities.get(0), new ArrayList<>());
    }

}
