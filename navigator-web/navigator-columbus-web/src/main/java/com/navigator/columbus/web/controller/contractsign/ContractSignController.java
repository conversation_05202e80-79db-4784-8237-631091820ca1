package com.navigator.columbus.web.controller.contractsign;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.enums.UseYqqEnum;
import com.navigator.trade.facade.ContractPaperFacade;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractBaseSignDTO;
import com.navigator.trade.pojo.qo.ContractSignQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022-01-27 11:07
 */
@RestController
@RequestMapping("/contractSign")
public class ContractSignController {
    @Resource
    private ContractSignFacade contractSignFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private CustomerFacade customerFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Autowired
    private AzureBlobUtil azureBlobUtil;
    @Autowired
    private ContractPaperFacade contractPaperFacade;

    /**
     * 协议列表高级分页搜索
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContContractSigns")
    public Result queryContContractSigns(@RequestBody QueryDTO<ContractSignQO> queryDTO) {
        return contractSignFacade.queryContContractSigns(queryDTO);
    }

    /**
     * 统计协议个数
     *
     * @param signBO
     * @return
     */
    @PostMapping("/getContractSignStat")
    Result getContractSignStat(@RequestBody QueryContractSignBO signBO) {
        return contractSignFacade.getContractSignStat(signBO);
    }

    /**
     * 根据协议ID，查询协议详情
     *
     * @param contractSignId 协议Id
     * @return 协议详情信息
     */
    @GetMapping("/getContractSignDetailById")
    public Result getContractSignDetailById(@RequestParam(name = "contractSignId") Integer contractSignId) {
        return Result.success(contractSignFacade.getContractSignDetailById(contractSignId));
    }

    /**
     * 根据合同ID，查询所有已签署的协议信息
     *
     * @param contractId 合同Id
     * @return 合同的所有的协议信息（文件只有双签）
     */
    @GetMapping("/getSignFileListByContractId")
    public Result getSignFileListByContractId(@RequestParam(name = "contractId") Integer contractId) {
        return Result.success(contractSignFacade.getSignFileListByContractId(contractId, SystemEnum.COLUMBUS.getValue()));

    }

    /**
     * 根据协议ID，查询所有的协议文件信息
     *
     * @param contractSignId 协议Id
     * @return 协议的所有文件信息
     */
    @GetMapping("/getAllSignFileListById")
    public Result getAllSignFileListById(@RequestParam(name = "contractSignId") Integer contractSignId) {
        return Result.success(contractSignFacade.getAllSignFileListById(contractSignId, SystemEnum.COLUMBUS.getValue()));
    }

    /**
     * 回传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    @PostMapping("/postBackContractSign")
    public Result postBackContract(@RequestBody ContractBaseSignDTO contractBaseSignDTO) {
        return contractSignFacade.postBackContractSign(contractBaseSignDTO);
    }

    /**
     * 查询各个状态下的合同数量
     *
     * @return 结果
     */
    @GetMapping("/getCustomerSignStat")
    public Result getCustomerSignStat(@RequestParam(value = "salesType", required = false) Integer salesType) {
        return Result.success(contractSignFacade.getCustomerSignStat(salesType));
    }


    @PostMapping("/saveContractPaper")
    public Result saveContractPaper(@RequestBody ContractPaperDTO contractPaperDTO) {
        return Result.success(contractPaperFacade.saveContractPaper(contractPaperDTO));
    }

    /**
     * 查询所有配置信息
     *
     * @param employId 品类ID
     * @return
     */
    @GetMapping("/canUseYyqSign")
    public Result canUseYyqSign(@RequestParam(value = "employId") Integer employId) {
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(
                new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.YYQ_CONFIG.getRuleCode())
                        .setStatus(DisableStatusEnum.ENABLE.getValue()));
        boolean yyqStatus = !CollectionUtils.isEmpty(systemRuleVO.getSystemRuleItemVOList()) &&
                DisableStatusEnum.ENABLE.getValue().equals(systemRuleVO.getSystemRuleItemVOList().get(0).getStatus());
        if (!yyqStatus) {
            return Result.success(yyqStatus);
        }
        CEmployEntity cEmployEntity = cEmployFacade.getEmployById(employId);
        if (null != cEmployEntity) {
            CustomerDTO customerDTO = customerFacade.getCustomerById(cEmployEntity.getCustomerId());
            yyqStatus = null != customerDTO && UseYqqEnum.USE_YQQ.getValue().equals(customerDTO.getUseYqq());
        }
        return Result.success(yyqStatus);
    }

    // BUGFIX：Case-1003204-哥伦布端，批量下载有效合同时，勾选无效 Author: Mr 2025-05-12 Start
    @GetMapping("/downloadContractZip")
    public void downloadContractZip(@RequestParam(value = "fileIdList") List<Integer> fileIdList,
                                    @RequestParam(value = "contractId") Integer contractId,
                                    HttpServletResponse response) {
        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.ZIP, contractId.toString());
        zipPath = zipPath + "合同文件.zip";
        Result fileResult = fileProcessFacade.getFileListByIds(fileIdList);
        List<FileInfoEntity> fileInfoEntityList = JSON.parseArray(JSON.toJSONString(fileResult.getData()), FileInfoEntity.class);
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }

//        List<ContractSignFileVO> contractSignFileVOList = contractSignFacade.getSignFileListByContractId(contractId, SystemEnum.COLUMBUS.getValue());
//        List<String> filePathList = contractSignFileVOList.stream().map(ContractSignFileVO::getPath).collect(Collectors.toList());
//        List<String> filePathList = new ArrayList<>(fileInfoEntityList.size());
//        fileInfoEntityList.forEach(fileInfoEntity -> {
//            filePathList.add(fileInfoEntity.getPath());
//        });

        // 从勾选文件中提取路径，而不是从整个合同中提取
        List<String> filePathList = fileInfoEntityList.stream()
                .map(FileInfoEntity::getPath)
                .collect(Collectors.toList());

        azureBlobUtil.downloadZip(filePathList, zipPath, response);
        // BUGFIX：Case-1003204-哥伦布端，批量下载有效合同时，勾选无效 Author: Mr 2025-05-12 End
    }
}
