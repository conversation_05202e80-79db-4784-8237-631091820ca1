package com.navigator.columbus.web.controller.future;

import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.dto.DistributionDTO;
import com.navigator.future.pojo.dto.QueryPriceAllocateDTO;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.pojo.vo.TtPriceEntityVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/10 15:17
 */
@RestController
@RequestMapping("/future")
public class PriceAllocateController {

    @Autowired
    private PriceAllocateFacade priceAllocateFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private ContractPriceFacade contractPriceFacade;


    /**
     * 点、转 分配生成分配单
     *
     * @param distributionDTO
     * @return
     */
    @PostMapping("/genDistributionOrder")
    public Result genDistributionOrder(@RequestBody @Valid DistributionDTO distributionDTO) {
        // 如果操作类型是点价 ，需要没有待分配和分配待审核的分配单
        if (Integer.valueOf(distributionDTO.getOperationType()) == PriceTypeEnum.PRICING.getValue()) {

            if (null == distributionDTO.getCategoryId()) {
                distributionDTO.setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue());
            }
            boolean b = priceAllocateFacade.getPriceDealOrdersByDominantCode(distributionDTO.getCustomerId(), distributionDTO.getDomainCode(), distributionDTO.getCategoryId());
            if (b) {
                return Result.failure("该合约下还有成交未分配的转月单或分配未审核的转月分配单");
            }
        }
        distributionDTO.setSystem(SystemEnum.COLUMBUS.getValue());

        return priceAllocateFacade.genDistributionOrder(distributionDTO);
    }


    /**
     * 根据条件查询待审核的分配单（分页）
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryPriceAllocates")
    public Result queryPriceAllocates(@RequestBody QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        QueryPriceAllocateDTO queryPriceAllocateDTO = queryDTO.getCondition();
        queryPriceAllocateDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        queryDTO.setCondition(queryPriceAllocateDTO);
        return priceAllocateFacade.queryPriceAllocates(queryDTO);
    }

    /**
     * 分配明细查询
     */
    @GetMapping("/getPriceAllocateDetail")
    public Result getPriceAllocateDetail(@RequestParam("applyCode") String applyCode) {
        return priceAllocateFacade.getPriceAllocateDetail(applyCode);
    }

    /**
     * 定价单
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getTtPrice")
    public Result getTtPrice(@RequestParam("contractCode") String contractCode) {
        TtPriceEntityVO ttPriceEntityVO = contractPriceFacade.getTtPrice(contractCode);
        return Result.success(ttPriceEntityVO);
    }


}
