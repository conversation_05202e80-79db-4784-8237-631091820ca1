package com.navigator.columbus.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = "com.navigator", exclude = {DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.navigator.*.facade", "com.navigator.columbus.web.remote"})
public class ColumbusNavigatorApplication {
    public static void main(String[] args) {
        SpringApplication.run(ColumbusNavigatorApplication.class, args);
    }

}
