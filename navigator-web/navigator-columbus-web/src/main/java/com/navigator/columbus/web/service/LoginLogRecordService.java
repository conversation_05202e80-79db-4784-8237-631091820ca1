package com.navigator.columbus.web.service;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.LoginLogFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.bisiness.enums.LoginTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * Columbus登录日志记录服务
 * 支持Columbus登录日志记录
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@Slf4j
public class LoginLogRecordService {

    @Autowired
    private LoginLogFacade loginLogFacade;

    /**
     * 记录Columbus登录成功日志
     */
    public void recordColumbusSuccessLog(LoginDTO loginDTO, CLoginVO cLoginVO) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(loginDTO);

            loginLogDTO.setLoginStatus(1)
                    .setResponseResult(JSON.toJSONString(cLoginVO));

            // 从返回结果中提取用户信息
            if (cLoginVO != null) {
                loginLogDTO.setUserId(cLoginVO.getId())
                        .setUsername(cLoginVO.getName());
            }

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录Columbus成功登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 记录Columbus登录失败日志
     */
    public void recordColumbusFailureLog(LoginDTO loginDTO, String failureReason) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(loginDTO);

            loginLogDTO.setLoginStatus(0)
                    .setFailureReason(failureReason);

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录Columbus失败登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 构建基础登录日志信息
     */
    private LoginLogDTO buildBaseLoginLog(LoginDTO loginDTO) {
        // 获取请求信息
        HttpServletRequest request = getHttpServletRequest();
        String ipAddress = getClientIpAddress(request);
        String userAgent = request != null ? request.getHeader("User-Agent") : "";

        LoginLogDTO loginLogDTO = new LoginLogDTO()
                .setLoginSystem(SystemEnum.COLUMBUS.getValue())
                .setLoginType(LoginTypeEnum.PHONE_CODE.getCode())
                .setIpAddress(ipAddress)
                .setUserAgent(userAgent)
                .setLoginTime(new Date());

        if (loginDTO != null) {
            loginLogDTO.setEmail(loginDTO.getEmail())
                    .setPhone(loginDTO.getPhone())
                    .setRequestParams(JSON.toJSONString(loginDTO));
        }

        return loginLogDTO;
    }

    /**
     * 保存登录日志
     */
    private void saveLoginLog(LoginLogDTO loginLogDTO) {
        try {
            loginLogFacade.saveLoginLog(loginLogDTO);
            log.debug("登录日志保存成功，用户：{}，系统：{}，状态：{}",
                    loginLogDTO.getUsername(),
                    loginLogDTO.getLoginSystem(),
                    loginLogDTO.getLoginStatus());
        } catch (Exception e) {
            log.error("保存登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String[] headers = {"X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP"};
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip.contains(",") ? ip.split(",")[0].trim() : ip;
            }
        }
        return request.getRemoteAddr();
    }
}
