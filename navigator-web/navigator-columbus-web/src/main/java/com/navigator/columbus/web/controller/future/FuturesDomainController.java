package com.navigator.columbus.web.controller.future;

import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 13:51
 */
@RestController
@RequestMapping("/futuresDomain")
public class FuturesDomainController {
    @Resource
    private FuturesDomainFacade futuresDomainFacade;

    /**
     * 查询期货信息
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/queryContractsFutures")
    public Result queryContractsFutures(@RequestBody QueryContractFuturesDTO queryContractFuturesDTO) {
        queryContractFuturesDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        return futuresDomainFacade.queryContractsFutures(queryContractFuturesDTO);
    }

    /**
     * 查询可转月量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayTransferNum")
    public Result mayTransferNum(@RequestBody ContractFuturesDTO contractFuturesDTO) {
        contractFuturesDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        return futuresDomainFacade.mayTransferNum(contractFuturesDTO);
    }

    /**
     * 查询可点价量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayPriceNum")
    public Result mayPriceNum(@RequestBody ContractFuturesDTO contractFuturesDTO) {
        contractFuturesDTO.setSystem(SystemEnum.COLUMBUS.getValue());
        return futuresDomainFacade.mayPriceNum(contractFuturesDTO);
    }
}
