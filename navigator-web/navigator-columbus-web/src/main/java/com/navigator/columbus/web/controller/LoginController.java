package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.EmployDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/12/22 13:51
 */

@RestController
@RequestMapping("customer")
public class LoginController {

    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     *  修改用户名的密码
     *  要求：不允许修改的密码是初始密码
     * @param employDTO
     * @return
     */
    @PostMapping("/modifyPassword")
    public Result modifyPassword(@RequestBody @Validated EmployDTO employDTO) {
        EmployEntity employEntity = employFacade.getEmployById(Integer.valueOf(employDTO.getId()));
        if (passwordEncoder.matches(employDTO.getPassword(),employEntity.getPassword())) {
            return Result.failure(ResultCodeEnum.USER_PASSWORD_INITIAL);
        }
        employEntity.setPassword(passwordEncoder.encode(employDTO.getPassword()));
        return employFacade.modifyPassword(employEntity);
    }


}
