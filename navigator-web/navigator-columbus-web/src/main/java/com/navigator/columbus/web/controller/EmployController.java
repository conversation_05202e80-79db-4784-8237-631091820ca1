package com.navigator.columbus.web.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.pojo.dto.ResetPasswordDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployBusinessDTO;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.pojo.vo.columbus.CEmployDetailVO;
import com.navigator.admin.pojo.vo.columbus.CLoginVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.VerificationCodeUtil;
import com.navigator.security.authentication.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/employ")
@Slf4j
public class EmployController {

    @Resource
    private CEmployFacade employFacade;

    @Resource
    private VerificationCodeUtil verificationCodeUtil;

    @Resource
    private TokenManager tokenManager;

    @Resource
    private CRoleFacade cRoleFacade;

    @Resource
    private CEmployCustomerFacade cEmployCustomerFacade;

    /**
     * 保存员工信息 columbus
     *
     * @param employBusinessDTO
     * @return
     */
    @PostMapping("/saveOrUpdateEmploy")
    public Result saveOrUpdateEmploy(@RequestBody @Valid CEmployBusinessDTO employBusinessDTO) {
        Result result = employFacade.saveOrUpdateEmploy(employBusinessDTO);
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            return Result.success();
        }

        return Result.failure(null == result ? "" : result.getMessage());
    }


    /**
     * 查詢员工信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryEmployList")
    Result queryEmployList(@RequestBody QueryDTO<CEmployDTO> queryDTO) {
        //case:1002963 哥伦布客户突然查到全量用户信息，筛选失效 Author:Wan 2025-02-19 start
        CEmployDTO employDTO = queryDTO.getCondition();
        if (null == employDTO.getCustomerId() || 0 == employDTO.getCustomerId()) {
            return Result.success();
        }
        //case:1002963 哥伦布客户突然查到全量用户信息，筛选失效 Author:Wan 2025-02-19 end
        return employFacade.queryEmployList(queryDTO);
    }

    @PostMapping("/setAdmin")
    public Result setAdmin(@RequestBody CEmployDTO cEmployDTO) {
        return employFacade.setAdmin(cEmployDTO);
    }

    @GetMapping("/queryAdminType")
    public Result queryAdminType(@RequestParam(value = "customerId") Integer customerId) {
        return employFacade.queryAdminType(customerId);
    }

    /**
     * 查詢员工详情信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/queryEmployDetail")
    Result queryEmployDetail(@RequestParam("employId") Integer employId, @RequestParam("customerId") Integer customerId) {
        CEmployDetailVO employDetailVO = employFacade.queryEmployDetail(employId, customerId);
        return Result.success(employDetailVO);
    }

    @GetMapping("/getEmployById")
    public Result getEmployById(@RequestParam("id") Integer id) {
        CEmployEntity cEmployEntity = employFacade.getEmployById(id);
        return Result.success(cEmployEntity);
    }


    @GetMapping("/queryCurrentEmployDetail")
    public Result queryCurrentEmployDetail() {

        String currentUserId = JwtUtils.getCurrentUserId();
        CEmployDetailVO employDetailVO = employFacade.queryCurrentEmployDetail(Integer.parseInt(currentUserId));
        return Result.success(employDetailVO);
    }


    @GetMapping("/queryRole")
    public Result queryRole(@RequestParam("customerId") Integer customerId) {
        //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-04-28 start
        return cRoleFacade.queryRole(customerId);
        //1002612 case-客户端提货委托权限已开通，客户看不到物流文员角色和对应模块 Author:Wan 2024-05-20 End
    }

//    /**
//     * 查詢员工
//     *
//     * @param roleDefId
//     * @return
//     */
//    @GetMapping("/queryEmployListByRoleDefId")
//    Result queryEmployListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId) {
//        return employFacade.queryEmployListByRoleDefId(roleDefId);
//    }
//
//    /**
//     * 查詢可选员工
//     *
//     * @param roleDefId
//     * @return
//     */
//    @GetMapping("/queryAvailableEmployByRoleDefId")
//    Result queryAvailableEmployByRoleDefId(@RequestParam("roleDefId") Integer roleDefId) {
//        return employFacade.queryAvailableEmployByRoleDefId(roleDefId);
//    }
//
//    /**
//     * 查詢已选员工
//     *
//     * @param queryDTO
//     * @return
//     */
//    @PostMapping("/queryChoosedEmployByRoleId")
//    Result queryChoosedEmployByRoleId(@RequestBody QueryDTO<CRoleDTO> queryDTO) {
//        return employFacade.queryChoosedEmployByRoleId(queryDTO);
//    }
//
//    /**
//     * 重置客户密码
//     *
//     * @param employId
//     * @return
//     */
//    @GetMapping("/resetPassword")
//    public Result resetPassword(@RequestParam("employId") Integer employId) {
//        String password = employFacade.resetPassword(employId);
//        return Result.success(password);
//    }
//
//    /**
//     * 查詢品类和主体列表
//     *
//     * @param
//     * @return
//     */
//    @GetMapping("/queryCategoryFactoryByRole")
//    Result queryCategoryFactoryByRole() {
//        return employFacade.queryCategoryFactoryByRole();
//    }

    /**
     * 更改状态
     *
     * @param
     * @return
     */
    @PostMapping("/updateEmployStatus")
    Result updateEmployStatus(@RequestBody CEmployDTO employDTO) {
        return employFacade.updateEmployStatus(employDTO);
    }
//
////----------------原始接口迁移
//
//    /**
//     * 获取达孚商务
//     */
//    @GetMapping("/getBusinessPeople")
//    public Result getBusinessPeople(@RequestParam("roleName") String roleName) {
//        List<CEmployEntity> businessPeopleList = employFacade.getEmployByRoleName(roleName);
//        return Result.success(businessPeopleList);
//    }
//
//
//    /**
//     * 重置客户密码
//     *
//     * @param employId
//     * @return
//     */
//    @GetMapping("/updateEmployResetPassword")
//    public Result updateEmployResetPassword(@RequestParam("employId") Integer employId) {
//        return employFacade.updateEmployResetPassword(employId);
//    }
//
//    //--------------------------
//
//    @PostMapping("/importEmploy")
//    Result importEmploy(@RequestParam("file") MultipartFile file){
//        return employFacade.importEmploy(file);
//    }


    /**
     * 重置客户密码
     *
     * @param
     * @return
     */
    @PostMapping("/resetUserPassword")
    public Result resetUserPassword(@RequestBody @Valid ResetPasswordDTO resetPasswordDTO) {
        if (StrUtil.isNotBlank(resetPasswordDTO.getEmployId())) {
            String employId = JwtUtils.getCurrentUserId();
            resetPasswordDTO.setEmployId(employId);
        }
        Result result = employFacade.resetUserPassword(resetPasswordDTO);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        return Result.success();
    }

    @PostMapping("/sendResetPasswordCode/{mobileNo}")
    public Result sendResetPasswordCode(@PathVariable("mobileNo") String mobileNo) {
        Result result = employFacade.sendResetPasswordCode(mobileNo);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        verificationCodeUtil.sendVerificationCode(mobileNo, RedisConstants.C_USER_RESET_PASSWORD_CODE_SEND, RedisConstants.C_USER_RESET_PASSWORD_CODE);
        return Result.success();
    }

    /**
     * 重置客户密码
     *
     * @param
     * @return
     */
    @PostMapping("/resetNotLogUserPassword")
    public Result resetNotLogUserPassword(@RequestBody @Valid ResetPasswordDTO resetPasswordDTO) {
        String employId = JwtUtils.getCurrentUserId();
        resetPasswordDTO.setEmployId(employId);
        Result result = employFacade.resetNotLogUserPassword(resetPasswordDTO);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        CEmployEntity cEmployEntity = JSON.parseObject(JSON.toJSONString(result.getData()), CEmployEntity.class);
        log.info("========> start login ");
        //1002984 case-重置密码登录报错修改 Author:Wan 2025-02-24 start
        String token = tokenManager.createTokenByPhone(String.valueOf(cEmployEntity.getId()), resetPasswordDTO.getPhone(), false);
        String refreshToken = tokenManager.getRefreshTokenByPhone(String.valueOf(cEmployEntity.getId()), resetPasswordDTO.getPhone());
        List<CEmployCustomerVO> cEmployCustomerVOS = cEmployCustomerFacade.queryCEmployCustomerVOByCEmployId(cEmployEntity.getId());

        CLoginVO cLoginVO = new CLoginVO();
        cLoginVO
                .setCEmployCustomerVOS(cEmployCustomerVOS)
                .setToken(token)
                .setRefreshToken(refreshToken)
                .setId(cEmployEntity.getId())
                .setName(cEmployEntity.getName())
                .setAadLogin(false)
                .setSignatureStatus(cEmployEntity.getSignatureStatus())
        ;
        //1002984 case-重置密码登录报错修改 Author:Wan 2025-02-24 end
        log.info("========> login log employId:{}", cEmployEntity.getId());
        CMenuVO cMenuVO = tokenManager.getColumbusMenuVO(cEmployEntity.getId());
        List<String> powerCodeList = tokenManager.getColumbusPowerCodeListByEmployId(cEmployEntity.getId());
        cLoginVO.setMenuVO(cMenuVO);
        cLoginVO.setPowerCodeList(powerCodeList);
        log.info("========> login  success ");
        return Result.success(cLoginVO);
    }

    @PostMapping("/sendResetPasswordPhoneCode/{mobileNo}")
    public Result sendResetPasswordPhoneCode(@PathVariable("mobileNo") String mobileNo) {
        Result result = employFacade.sendResetPasswordPhoneCode(mobileNo);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        verificationCodeUtil.sendVerificationCode(mobileNo, RedisConstants.C_USER_RESET_PASSWORD_CODE_SEND, RedisConstants.C_USER_RESET_PASSWORD_CODE);
        return Result.success();
    }


    /**
     * 导出用户角色
     *
     * @param response
     * @return
     */
    @GetMapping("/exportEmployRoleList")
    public void exportEmployRoleList(HttpServletResponse response) {
        Result result = employFacade.exportEmployRoleList();
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<EmployVO> employVOList = JSON.parseArray(JSON.toJSONString(result.getData()), EmployVO.class);
        if (CollectionUtils.isEmpty(employVOList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "EmployRole";
        EasyPoiUtils.exportExcel(employVOList, "EmployRole", "用户信息", EmployVO.class, fileName, response);
    }

}
