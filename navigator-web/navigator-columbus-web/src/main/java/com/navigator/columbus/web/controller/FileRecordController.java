package com.navigator.columbus.web.controller;

import com.navigator.admin.facade.FileRecordFacade;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/fileRecord")
public class FileRecordController {
    @Autowired
    private FileRecordFacade fileRecordFacade;

    @GetMapping("/queryColumbusFileRecord")
    Result queryColumbusFileRecord() {
        return fileRecordFacade.queryFileRecordBySystemId(SystemEnum.COLUMBUS.getValue());
    }

}