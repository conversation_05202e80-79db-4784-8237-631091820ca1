package com.navigator.columbus.web.config;


import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseAreaEntity;
import com.navigator.admin.pojo.entity.WarehouseCityEntity;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.admin.pojo.qo.WarehouseSiteQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 库点配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-23
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/warehouse")
public class WarehouseController {
    private final WarehouseFacade warehouseFacade;

    @ApiOperation(value = "新增库点配置")
    @PostMapping("/addWarehouse")
    public Result<Boolean> addDeliveryWarehouse(@RequestBody WarehouseEntity warehouseEntity) {
        return warehouseFacade.addWarehouse(warehouseEntity);
    }

    @ApiOperation(value = "修改库点配置")
    @PostMapping("/updateWarehouse")
    public Result<Boolean> updateDeliveryWarehouse(@RequestBody WarehouseEntity warehouseEntity) {
        return warehouseFacade.updateWarehouse(warehouseEntity);
    }

    @ApiOperation(value = "查询库点配置列表")
    @PostMapping("/getWarehouseList")
    public Result getWarehouseList(@RequestBody QueryDTO<WarehouseBO> queryDTO) {
        return warehouseFacade.getWarehouseList(queryDTO);
    }

    @ApiOperation(value = "获取全部库点")
    @GetMapping("/getAllWarehouse")
    public Result<List<WarehouseEntity>> getAllWarehouse() {
        return warehouseFacade.getAllWarehouse();
    }

    @ApiOperation(value = "根据账套id查询库点")
    @GetMapping("/getWarehouseBySiteCode")
    public Result<List<WarehouseEntity>> getWarehouseBySiteCode(@RequestParam("siteCode") String siteCode) {
        return warehouseFacade.getWarehouseBySiteCode(siteCode);
    }

    @ApiOperation(value = "根据主体id查询库点")
    @GetMapping("/getWarehouseByCompanyId")
    public Result<List<WarehouseEntity>> getWarehouseByCompanyId(@RequestParam("companyId") Integer companyId,
                                                                 @RequestParam("category2") Integer category2) {
        return warehouseFacade.getWarehouseByCompanyId(companyId, category2);
    }

    @ApiOperation(value = "查询所有库点区域")
    @GetMapping("/getGeographicAreaList")
    public Result<List<WarehouseAreaEntity>> getGeographicAreaList() {
        return warehouseFacade.getGeographicAreaList();
    }

    @ApiOperation(value = "根据区域id查询城市")
    @GetMapping("/getGeographicCityByAreaId")
    public Result<List<WarehouseCityEntity>> getGeographicCityByAreaId(@RequestParam("areaId") Integer areaId) {
        return warehouseFacade.getGeographicCityByAreaId(areaId);
    }

    /**
     * 根据工厂编码获取库点信息
     *
     * @param factoryCode
     * @param category2
     * @return
     */
    @ApiOperation(value = "根据工厂编码获取库点信息")
    @GetMapping("/getWarehouseByFactoryCode")
    Result<List<WarehouseEntity>> getWarehouseByFactoryCode(@RequestParam("factoryCode") String factoryCode,
                                                            @RequestParam(value = "category2", required = false) Integer category2) {
        return warehouseFacade.getWarehouseByFactoryCode(factoryCode, category2);
    }

    /**
     * 根据主体和工厂编码获取库点信息
     *
     * @param warehouseSiteQO
     * @return
     */
    @ApiOperation(value = "根据主体和工厂编码获取库点信息")
    @PostMapping("/getWarehouseByCompanyIdAndFactoryCode")
    Result<List<WarehouseEntity>> getWarehouseByCompanyIdAndFactoryCode(@RequestBody WarehouseSiteQO warehouseSiteQO) {
        return warehouseFacade.getWarehouseByCompanyIdAndFactoryCode(warehouseSiteQO);
    }
}
