package com.navigator.security.authentication;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.facade.columbus.CEmployCustomerFacade;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.facade.columbus.CMenuFacade;
import com.navigator.admin.facade.columbus.CPowerFacade;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.facade.magellan.MenuFacade;
import com.navigator.admin.facade.magellan.PowerFacade;
import com.navigator.admin.facade.magellan.RolePowerFacade;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.entity.CEmployEntity;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.enums.systemrule.SystemCodeConfigEnum;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.admin.pojo.vo.columbus.CEmployCustomerVO;
import com.navigator.admin.pojo.vo.columbus.CMenuVO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.ResponseUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * token管理
 * </p>
 * <p>
 * Description: No Description
 * Created by YuYong on 2021/10/27 15:28
 */
@Component
@Slf4j
public class TokenManager {
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private RolePowerFacade rolePowerFacade;
    @Autowired
    private PowerFacade powerFacade;
    @Autowired
    private MenuFacade menuFacade;
    @Autowired
    private CPowerFacade cPowerFacade;
    @Autowired
    private CMenuFacade cMenuFacade;
    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private CEmployCustomerFacade cEmployCustomerFacade;

    @Autowired
    JwtUtils atuoJwtUtils;

    public String createTokenByEmail(String id, String username, boolean isMagellan) {
        String token = atuoJwtUtils.getJwtTokenByEmail(id, username, isMagellan);
        return token;
    }

    public String getRefreshTokenByEmail(String id, String email) {
        return JwtUtils.getRefreshTokenByEmail(id, email);
    }

    public String createTokenByPhone(String id, String phone, boolean isMagellan) {
        return atuoJwtUtils.getJwtTokenByPhone(id, phone, isMagellan);
    }

    public String getRefreshTokenByPhone(String id, String phone) {
        return JwtUtils.getRefreshTokenByPhone(id, phone);
    }

    public String getUserIdFromToken(HttpServletRequest request) {
        String userId = JwtUtils.getMemberIdByJwtToken(request);
        return userId;
    }

    public String getEmailByJwtToken(HttpServletRequest request) {
        String username = JwtUtils.getEmailByJwtToken(request);
        return username;
    }

    public String getPhoneByJwtToken(HttpServletRequest request) {
        String username = JwtUtils.getPhoneByJwtToken(request);
        return username;
    }


    public void removeToken(String token) {
        //jwttoken无需删除，客户端扔掉即可。
    }

    public Boolean verifyNeedRefresh(HttpServletRequest request) {
        Date expiration = JwtUtils.getExpirationByJwtToken(request);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(expiration);
        calendar.add(Calendar.MINUTE, -10);
        Date time = calendar.getTime();
        return time.before(new Date()) && expiration.after(new Date());
    }


    public void verifyForbidden(String employId, HttpServletResponse res) {
        EmployEntity employEntity = employFacade.getEmployById(Integer.parseInt(employId));
        if (employEntity == null || DisableStatusEnum.DISABLE.getValue().equals(employEntity.getStatus())) {
            ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_FORBIDDEN));
        }
    }

    public void verifyColumbusForbidden(String employId, HttpServletResponse res) {
        CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.parseInt(employId));
        if (cEmployEntity == null || DisableStatusEnum.DISABLE.getValue().equals(cEmployEntity.getStatus())) {
            ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_FORBIDDEN));
        }/* else {
            Integer customStatus = cEmployFacade.getCustomColumbusStateById(cEmployEntity.getCustomerId());
            if (customStatus == 0)
                ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_FORBIDDEN));
        }*/
    }

    public MenuVO getMenuVO(Integer employId) {
        RoleDTO roleDTO = new RoleDTO();
        MenuVO menuVO = new MenuVO();
        roleDTO.setEmployId(String.valueOf(employId));
        Result result = menuFacade.getMenuByEmployId(roleDTO);
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            menuVO = JSON.parseObject(JSON.toJSONString(result.getData()), MenuVO.class);
        }
        return menuVO;
    }

    public List<String> getPowerCodeListByEmployId(Integer employId) {
        Result result = powerFacade.queryPowerByEmployId(employId);
        List<String> powerCodeList = new ArrayList<>();
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            powerCodeList = JSON.parseArray(JSON.toJSONString(result.getData()), String.class);
        }
        return powerCodeList;
    }


    public List<CEmployCustomerVO> queryCEmployCustomerVOByCEmployId(Integer cEmployId) {
        return cEmployCustomerFacade.queryCEmployCustomerVOByCEmployId(cEmployId);
    }


    public CMenuVO getColumbusMenuVO(Integer employId) {
        CRoleDTO cRoleDTO = new CRoleDTO();
        CMenuVO cMenuVO = new CMenuVO();
        cRoleDTO.setEmployId(String.valueOf(employId));
        Result result = cMenuFacade.getMenuByEmployIdV2(cRoleDTO);
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            cMenuVO = JSON.parseObject(JSON.toJSONString(result.getData()), CMenuVO.class);
        }
        return cMenuVO;
    }

    public List<String> getColumbusPowerCodeListByEmployId(Integer employId) {
        Result result = cPowerFacade.queryPowerByEmployIdV2(employId, 0);
        List<String> powerCodeList = new ArrayList<>();
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            powerCodeList = JSON.parseArray(JSON.toJSONString(result.getData()), String.class);
        }
        return powerCodeList;
    }

    public Date verifyPasswordNeedModify(Date date) {
        SystemRuleDTO systemRuleDTO = new SystemRuleDTO().setRuleCode(SystemCodeConfigEnum.LOGIN_OVER_TIME.getRuleCode());
        SystemRuleVO systemRuleVO = systemRuleFacade.querySystemRuleDetail(systemRuleDTO);
        String expireTime = systemRuleVO.getSystemRuleItemVOList().get(0).getRuleItemValue();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, Integer.parseInt(expireTime) - 6);
        return calendar.getTime();
    }

    public Integer verifyStatus(String employId) {
        CEmployEntity cEmployEntity = cEmployFacade.getEmployById(Integer.parseInt(employId));
        return cEmployEntity.getStatus();
    }

    public CEmployEntity getColumbusEmploy(String employId) {
        return cEmployFacade.getEmployById(Integer.parseInt(employId));
    }
}
