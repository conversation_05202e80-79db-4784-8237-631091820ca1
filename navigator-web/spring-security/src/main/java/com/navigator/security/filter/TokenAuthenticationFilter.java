package com.navigator.security.filter;

import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.ResponseUtil;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.util.StringUtils;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * Description: 访问过滤器
 * Created by <PERSON><PERSON><PERSON> on 2021/11/3 13:46
 */

public class TokenAuthenticationFilter extends BasicAuthenticationFilter {


    public TokenAuthenticationFilter(AuthenticationManager authManager) {
        super(authManager);
    }

    @Override
    public void doFilterInternal(HttpServletRequest req, HttpServletResponse res, FilterChain chain)
            throws IOException, ServletException {
        logger.info("=================" + req.getRequestURI());

        UsernamePasswordAuthenticationToken authentication = null;
        try {
            authentication = getAuthentication(req, res);
        } catch (Exception e) {
            ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_NOT_LOGGED_IN));
        }

        if (authentication != null) {
            SecurityContextHolder.getContext().setAuthentication(authentication);
        } else {
            ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_NOT_LOGGED_IN));
        }
        if (!verifyStatus(req)) {
            ResponseUtil.out(res, Result.failure(ResultCodeEnum.USER_FORBIDDEN));
        }
        chain.doFilter(req, res);
    }


    /**
     * 子类必需重写
     * 此处为空实现
     *
     * @param request
     * @param res
     * @return
     */
    public UsernamePasswordAuthenticationToken getAuthentication(HttpServletRequest request, HttpServletResponse res) {
        return null;
    }

    public Boolean verifyStatus(HttpServletRequest request) {
        return true;
    }


    public UsernamePasswordAuthenticationToken backAuthentication(String username, String employId, String token) {
        if (!StringUtils.isEmpty(username)) {
            // 查询权限id
            Set<SimpleGrantedAuthority> authorities = new HashSet<>();
            return new UsernamePasswordAuthenticationToken(username, token, authorities);
        }
        return null;
    }
}
