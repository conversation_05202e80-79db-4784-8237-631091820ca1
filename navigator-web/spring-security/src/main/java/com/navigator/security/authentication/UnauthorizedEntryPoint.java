package com.navigator.security.authentication;



import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.ResponseUtil;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description: 认证异常处理器(登录认证异常,非用户名密码的错误，异常为代码异常造成)
 * Created by YuYong on 2021/10/27 15:28
 */
public class UnauthorizedEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) {
        ResponseUtil.out(response, Result.failure(ResultCodeEnum.USER_LOGGED_FAILURE));
    }
}
