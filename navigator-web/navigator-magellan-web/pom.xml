<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>navigator-web</artifactId>
        <groupId>com.navigator</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>navigator-magellan-web</artifactId>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>spring-security</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-core</artifactId>
            <version>1.19.0</version>
        </dependency>

        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>koala-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>navigator-commons</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>dagama-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>admin-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>customer-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>trade-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.azure.spring</groupId>
            <artifactId>azure-spring-boot-starter-active-directory</artifactId>
            <version>3.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.microsoft.graph</groupId>
            <artifactId>microsoft-graph</artifactId>
            <version>5.2.0</version>
            <!--<version>[5.0,)</version>-->
        </dependency>
        <dependency>
            <groupId>com.azure</groupId>
            <artifactId>azure-identity</artifactId>
            <version>1.4.3</version>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>goods-facade</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>future-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>pigeon-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>sparrow-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>cuckoo-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.navigator</groupId>
            <artifactId>husky-facade</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <mainClass>com.navigator.magellan.web.MagellanNavigatorApplication</mainClass>
                </configuration>
            </plugin>
            <!-- 打包复制jar到指定目录 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <tasks>
                                <!--suppress UnresolvedMavenProperty -->
                                <delete dir="../../deploy/${project.artifactId}"/>
                                <mkdir dir="../../deploy/${project.artifactId}"/>
                                <copy overwrite="true"
                                      tofile="../../deploy/${project.artifactId}/${project.artifactId}-${project.version}-${maven.build.timestamp}.jar"
                                      file="${project.build.directory}/${project.artifactId}-${project.version}-${maven.build.timestamp}.jar"/>
                            </tasks>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}-${project.version}-${maven.build.timestamp}</finalName>
    </build>


</project>
