apiVersion: apps/v1
kind: Deployment
metadata:
  name: ldc-navigator-magellan-web-int
  namespace: int
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ldc-navigator-magellan-web-int
  template:
    metadata:
      labels:
        app: ldc-navigator-magellan-web-int
    spec:
      containers:
      - image: csm4nnvgacr001.azurecr.cn/navigator-magellan-web-int:#{Build.BuildId}#
        name: ldc-navigator-magellan-web-int
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "int" 
---
apiVersion: v1
kind: Service
metadata:
  name: ldc-navigator-magellan-web-int
  namespace: int
spec:
  type: ClusterIP
  ports:
  - port: 8080
    protocol: TCP
#    targetPort: 8080
  selector:
    app: ldc-navigator-magellan-web-int