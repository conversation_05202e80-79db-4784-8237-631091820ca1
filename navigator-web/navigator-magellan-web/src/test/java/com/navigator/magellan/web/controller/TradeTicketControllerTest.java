package com.navigator.magellan.web.controller;

import com.navigator.magellan.web.MagellanNavigatorApplication;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = MagellanNavigatorApplication.class)
class TradeTicketControllerTest {

    @Autowired
    TradeTicketController tradeTicketController;

    @Test
    void saveTT() {
        tradeTicketController.saveTT(new OMContractAddTTDTO(),null);
    }
}