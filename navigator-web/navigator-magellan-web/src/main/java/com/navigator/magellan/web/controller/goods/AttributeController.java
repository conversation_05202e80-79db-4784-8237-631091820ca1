package com.navigator.magellan.web.controller.goods;


import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.AttributeFacade;
import com.navigator.goods.pojo.dto.AttributeAddDTO;
import com.navigator.goods.pojo.dto.AttributeDTO;
import com.navigator.goods.pojo.dto.AttributeUpdateDTO;
import com.navigator.goods.pojo.entity.AttributeEntity;
import com.navigator.goods.pojo.qo.AttributeQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 规格表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@RestController
@RequestMapping("/attribute")
public class AttributeController {
    @Resource
    private AttributeFacade attributeFacade;

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param attributeId 规格ID
     * @return 规格列表
     */
    @GetMapping("/getAttributeValueList")
    public Result getAttributeValueList(@RequestParam("attributeId") Integer attributeId) {
        return Result.success(attributeFacade.getAttributeValueList(attributeId));
    }

    /**
     * 根据规格ID，获取规格值列表
     *
     * @param categoryId 分类ID
     * @return 规格列表
     */
    @GetMapping("/getSpecListByCategoryId")
    public Result getSpecListByCategoryId(@RequestParam("categoryId") Integer categoryId) {
        return Result.success(attributeFacade.getSpecListByCategoryId(categoryId));
    }

    /**
     * 根据条件：获取规格DTO分页
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryAttributeDTOPage")
    Result<AttributeDTO> queryAttributeDTOPage(@RequestBody QueryDTO<AttributeQO> queryDTO) {
        return Result.page(attributeFacade.queryAttributeDTOPage(queryDTO));
    }

    /**
     * 根据条件：获取规格DTO列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/queryAttributeDTOList")
    Result<List<AttributeDTO>> queryAttributeDTOList(@RequestBody AttributeQO condition) {
        return Result.success(attributeFacade.queryAttributeDTOList(condition));
    }

    /**
     * 根据ID：获取规格DTO
     *
     * @param id
     * @return
     */
    @GetMapping("/getAttributeDTOById")
    Result<AttributeDTO> getAttributeDTOById(@RequestParam(value = "id") Integer id) {
        return Result.success(attributeFacade.getAttributeDTOById(id));
    }

    /**
     * 新增：规格
     *
     * @param addDTO
     * @return
     */
    @PostMapping("/addAttribute")
    Result addAttribute(@RequestBody AttributeAddDTO addDTO) {
        return attributeFacade.addAttribute(addDTO);
    }

    /**
     * 更新：规格
     *
     * @param updateDTO
     * @return
     */
    @PostMapping("/updateAttribute")
    Result updateAttribute(@RequestBody AttributeUpdateDTO updateDTO) {
        return attributeFacade.updateAttribute(updateDTO);
    }

}
