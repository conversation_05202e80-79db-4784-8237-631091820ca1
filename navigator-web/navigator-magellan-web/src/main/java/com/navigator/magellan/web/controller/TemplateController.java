package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.TemplateAttributeFacade;
import com.navigator.admin.facade.TemplateFacade;
import com.navigator.admin.pojo.dto.QueryTemplateDTO;
import com.navigator.admin.pojo.entity.TemplateAttributeEntity;
import com.navigator.admin.pojo.entity.TemplateEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2021/12/9 18:07
 */
@RestController
@RequestMapping("/template")
public class TemplateController {

    @Autowired
    private TemplateAttributeFacade templateAttributeFacade;
    @Autowired
    private TemplateFacade templateFacade;

    @PostMapping("/queryTemplateAttributeList")
    public Result queryTemplateAttributeList(@RequestBody QueryDTO<TemplateAttributeEntity> queryDTO) {
        return templateAttributeFacade.queryTemplateAttributeList(queryDTO);
    }

    /**
     * 分页查询合同模版信息
     *
     * @param queryDTO 查询模版条件
     * @return 模版基础信息列表
     */
    @PostMapping("/queryTemplate")
    public Result queryTemplate(@RequestBody QueryDTO<QueryTemplateDTO> queryDTO) {
        return templateFacade.queryTemplate(queryDTO);
    }

    @PostMapping("/saveOrUpdateTemplate")
    public Result saveOrUpdateTemplate(@RequestBody TemplateEntity templateEntity) {
        return templateFacade.saveOrUpdateTemplate(templateEntity);
    }

    @GetMapping("/getTemplateByCode")
    public Result getTemplateByCode(@RequestParam("codeList") List<String> codeList) {
        return templateFacade.getTemplateByCode(codeList);
    }
}
