package com.navigator.magellan.web.controller;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.cuckoo.facade.AtlasContractFacade;
import com.navigator.cuckoo.pojo.dto.AtlasRetryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasMappingQueryDTO;
import com.navigator.cuckoo.pojo.dto.query.AtlasQueryDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * Atlas 接口 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/23
 */
@RestController
@RequestMapping("/atlas")
public class AtlasController {
    @Resource
    AtlasContractFacade atlasContractFacade;

    @PostMapping("/getSyncRecordList")
    public Result getSyncRecordList(@RequestBody QueryDTO<AtlasQueryDTO> queryDTO) {
        return atlasContractFacade.getSyncRecordList(queryDTO);
    }

    @PostMapping("/getMappingContractList")
    public Result getMappingContractList(@RequestBody QueryDTO<AtlasMappingQueryDTO> queryDTO) {
        return atlasContractFacade.getMappingContractList(queryDTO);
    }

    @PostMapping("/reSyncContractRequest")
    public Result reSyncContractRequest(@RequestBody AtlasRetryDTO atlasRetryDTO) {
        try {
            atlasContractFacade.reSyncContractRequest(atlasRetryDTO);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.ATLAS_REQUEST_TIMEOUT);
        }
        return Result.success();
    }

    @GetMapping("/getBusinessEntityNameList")
    public Result getBusinessEntityNameList() {
        return atlasContractFacade.getBusinessEntityNameList();
    }

    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 Start
    /**
     * 获取操作类型列表
     */
    @GetMapping("/getOperationTypeList")
    public Result getOperationTypeList() {
        return atlasContractFacade.getOperationTypeList();
    }
    // BUGFIX：case-1003180 N081 传输记录选项缺失 Author: Mr 2025-04-29 End

}
