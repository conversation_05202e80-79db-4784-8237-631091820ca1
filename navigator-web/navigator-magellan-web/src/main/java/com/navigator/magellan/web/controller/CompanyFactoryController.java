package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.CompanyFactoryFacade;
import com.navigator.admin.pojo.dto.FactoryCompanyDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/companyFactory")
public class CompanyFactoryController {
    @Autowired
    private CompanyFactoryFacade companyFactoryFacade;


    @PostMapping("/saveFactoryCompany")
    public Result saveFactoryCompany(@RequestBody FactoryCompanyDTO factoryCompanyDTO) {
        companyFactoryFacade.saveFactoryCompany(factoryCompanyDTO);
        return Result.success();
    }
}
