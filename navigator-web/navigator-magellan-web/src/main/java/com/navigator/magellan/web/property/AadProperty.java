package com.navigator.magellan.web.property;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2021-11-03 19:32
 */
@Data
@Component
@ConfigurationProperties(prefix = "azure.activedirectory")
@ToString
public class AadProperty {
    private String clientId;
    private String tenantId;
    private String clientSecret;
    private String appIdUrl;
    private String redirectUrl;
    private String graphAccessTokenUrl;
}
