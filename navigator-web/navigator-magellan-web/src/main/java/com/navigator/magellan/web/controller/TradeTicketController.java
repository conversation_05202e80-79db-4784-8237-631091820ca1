package com.navigator.magellan.web.controller;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.BuCodeEnum;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.magellan.web.service.TradeTicketRemoteService;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.enums.ContractTypeEnum;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/tradeTicket")
@Slf4j
public class TradeTicketController {

    @Autowired
    private TradeTicketFacade tradeTicketFacade;

    @Autowired
    private ContractPriceFacade contractPriceFacade;

    @Autowired
    private TradeTicketRemoteService tradeTicketService;

    /**
     * @description: 保存销售TT
     * @param: [createTradeTicketDTO]
     * 场景：新增、回购
     * 操作：保存
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/saveTT")
    Result saveTT(@RequestBody @Valid OMContractAddTTDTO omContractAddTTDTO, BindingResult result) {
        log.info("==========saveTT.omContractAddTTDTO:{}", JSON.toJSONString(omContractAddTTDTO));
        if (result.hasErrors()) {
            omContractAddTTDTO.setCompletedStatus(0);
        } else {
            omContractAddTTDTO.setCompletedStatus(1);
        }
        log.info("==========saveTT.result:{}", JSON.toJSONString(result.getAllErrors()));
        // 校验注解无法通过，需要手动校验
        checkSaveResult(omContractAddTTDTO);
        omContractAddTTDTO.setSubmitType(omContractAddTTDTO.getSubmitType() == null ? SubmitTypeEnum.SAVE.getValue() : omContractAddTTDTO.getSubmitType());
        return tradeTicketService.saveTT(omContractAddTTDTO);
    }

    /**
     * @description: 新的submitTT, 走tradeApp
     * @param: [createTradeTicketDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/newSubmitTT")
    Result newSubmitTT(@RequestBody @Valid OMContractAddTTDTO omContractAddTTDTO, BindingResult result) {
        if (result.hasErrors()) {
            omContractAddTTDTO.setCompletedStatus(0);
        } else {
            omContractAddTTDTO.setCompletedStatus(1);
        }
        omContractAddTTDTO.setSubmitType(SubmitTypeEnum.SUBMIT.getValue());
        return tradeTicketService.newSubmitTT(omContractAddTTDTO);
    }


    /**
     * 校验注解无法通过，需要手动校验
     *
     * @param omContractAddTTDTO OMContractAddTTDTO
     */
    private void checkSaveResult(OMContractAddTTDTO omContractAddTTDTO) {
        // 校验点价截止日期
        String contractType = omContractAddTTDTO.getContractType();
        int salesType = omContractAddTTDTO.getSalesType();
        Integer category2 = omContractAddTTDTO.getCategory2();
        String buCode = omContractAddTTDTO.getBuCode();
        String goodsName = omContractAddTTDTO.getGoodsName();
        if (StringUtils.isNotBlank(contractType)
                && (String.valueOf(ContractTypeEnum.JI_CHA.getValue()).equalsIgnoreCase(contractType) ||
                String.valueOf(ContractTypeEnum.ZAN_DING_JIA.getValue()).equalsIgnoreCase(contractType))) {
            // 判断品类
            boolean hasEmptyPriceEndTime = omContractAddTTDTO.getTtKernelDTOList().stream()
                    .anyMatch(ttKernelDTO -> StringUtils.isBlank(ttKernelDTO.getPriceEndTime()));
            // 根据hasEmptyPriceEndTime的值来决定是否设置completedStatus为0
            if (hasEmptyPriceEndTime) {
                log.error("合同类型为基差/暂定价时，点价截止日期未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
        }
        // 交提货时间,提货时间必填，现货
        if (ObjectUtil.isNotEmpty(buCode) && BuCodeEnum.ST.getValue().equals(buCode)) {
            // 判断品类
            boolean hasEmptyDeliveryTime = omContractAddTTDTO.getTtKernelDTOList().stream()
                    .anyMatch(ttKernelDTO -> ObjectUtil.isEmpty(ttKernelDTO.getDeliveryStartTime()));
            // 根据hasEmptyDeliveryTime的值来决定是否设置completedStatus为0
            if (hasEmptyDeliveryTime) {
                log.error("现货业务提货时间未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
        }
        // 采购特有的属性校验
        if (salesType == ContractSalesTypeEnum.PURCHASE.getValue()) {
            // 迟付款罚金
            if (ObjectUtil.isEmpty(omContractAddTTDTO.getDelayPayFine())) {
                log.error("采购合同迟付款罚金未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
            // 签订地
            if (ObjectUtil.isEmpty(omContractAddTTDTO.getSignPlace())) {
                log.error("采购合同签订地未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
            // 采购卖方主体收款账号信息
            if (ObjectUtil.isEmpty(omContractAddTTDTO.getSupplierAccountId())) {
                log.error("采购合同卖方主体收款账号信息未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
        }
        // 特种油脂企标文件
        if (GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(category2)) {
            // 企标|国标类型
            if (ObjectUtil.isEmpty(omContractAddTTDTO.getStandardType())) {
                log.error("特种油脂企标文件类型未填写！");
                omContractAddTTDTO.setCompletedStatus(0);
            }
        }
        // 含税单价必须大于0 期货价格必须大于0 ，副产品可以 = 0
        for (KeyTradeInfoTTDTO item : omContractAddTTDTO.getTtKernelDTOList()) {
            if ("0".equals(item.getUnitPrice())) {
                log.error("商品单价为0，不符合规则！");
                omContractAddTTDTO.setCompletedStatus(0);
                continue;
            }
            // 1、当品种≠脂肪酸、特油脂肪酸、特油脂肪酸且合同类型≠暂定价，必填，可=0,这个条件之外的
            boolean isForwardPrice = !goodsName.contains("脂肪酸")
                    && GoodsCategoryEnum.GRAIN_CORN.getValue().equals(category2)
                    && !String.valueOf(ContractTypeEnum.ZAN_DING_JIA.getValue()).equalsIgnoreCase(contractType);
            if (!isForwardPrice && "0".equals(item.getPriceDetailDTO().getForwardPrice())) {
                log.error("期货价格为0，不符合规则！");
                omContractAddTTDTO.setCompletedStatus(0);
                continue;
            }
            // 副产品 VE单价 > 0 VE含量 > 0
            boolean isVE = goodsName.contains("脂肪酸")
                    && GoodsCategoryEnum.GRAIN_CORN.getValue().equals(category2)
                    && String.valueOf(ContractTypeEnum.ZAN_DING_JIA.getValue()).equalsIgnoreCase(contractType);
            if (isVE && ("0".equals(item.getPriceDetailDTO().getVePrice())
                    || "0".equals(item.getPriceDetailDTO().getVeContent()))) {
                log.error("副产品VE价格或含量为0，不符合规则！");
                omContractAddTTDTO.setCompletedStatus(0);
                continue;
            }

            // 特粕 特油 的基差与基差暂定价|| 除了副产品
            if (!GoodsCategoryEnum.GRAIN_CORN.getValue().equals(category2)) {
                // 校验期货合约和期货月份是否有效
                boolean isSpecialCategory = GoodsCategoryEnum.SPECIAL_OIL.getValue().equals(category2)
                        || GoodsCategoryEnum.SPECIFIC_PROTEIN.getValue().equals(category2);

                // 合同类型为基差或基差暂定价合同
                boolean isBasisContract = String.valueOf(ContractTypeEnum.JI_CHA.getValue()).equalsIgnoreCase(contractType)
                        || String.valueOf(ContractTypeEnum.JI_CHA_ZAN_DING_JIA.getValue()).equalsIgnoreCase(contractType);
                if ((isSpecialCategory && isBasisContract)) {
                    if (ObjectUtil.isEmpty(item.getFutureCode()) || ObjectUtil.isEmpty(item.getDomainCode())) {
                        log.error("{}，期货合约或期货月份无效", GoodsCategoryEnum.getDescByValue(category2));
                        omContractAddTTDTO.setCompletedStatus(0);
                    }
                }
            }
        }

        // 袋皮扣重豆粕必填校验
        if (GoodsCategoryEnum.OSM_MEAL.getValue().equals(category2)
                || GoodsCategoryEnum.SPECIFIC_PROTEIN.getValue().equals(category2)
                ||GoodsCategoryEnum.GRAIN_CORN.getValue().equals(category2)) {
            if (ObjectUtil.isEmpty(omContractAddTTDTO.getPackageWeight())) {
                omContractAddTTDTO.setCompletedStatus(0);
            }
        }
    }

    /**
     * @description: 保存销售TT
     * @param: [createTradeTicketDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/saveStructurePriceTT")
    Result saveStructurePriceTT(@Valid @RequestBody SalesStructurePriceTTDTO salesStructurePriceTTDTO, BindingResult result) {
        return tradeTicketService.saveStructurePriceTT(salesStructurePriceTTDTO);
    }

    /**
     * @description: 批量提交TT
     * @param: [submitTTBatchDTO]
     * 场景：不限
     * 操作：多选并批量提交
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/submitTTBatch")
    Result submitTTBatch(@RequestBody SubmitTTBatchDTO submitTTBatchDTO) {
        return tradeTicketService.submitTTBatch(submitTTBatchDTO);
    }

    /**
     * @description: 提交TT
     * 业务场景：新增、修改、拆分等所有的TT场景
     * 操作：提交
     * @param: [submitTTDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/submitTT")
    Result submitTT(@Valid @RequestBody SubmitTTDTO submitTTDTO, BindingResult result) {
        if (result.hasErrors()) {
            submitTTDTO.getCreateTradeTicketDTO().setCompletedStatus(0);
            log.error("errorMsg:{}", result.getAllErrors());
        } else {
            submitTTDTO.getCreateTradeTicketDTO().setCompletedStatus(1);
        }
        return tradeTicketService.submitTT(submitTTDTO);
    }

    /**
     * @description: 删除TT
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/deleteTT/{ttId}")
    Result deleteTT(@PathVariable("ttId") Integer ttId) {
        return tradeTicketFacade.deleteTT(ttId);
    }


    /**
     * @description: 修改TT
     * 业务场景：修改、拆分
     * 操作：保存
     * @param: [createTradeTicketDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/updateTT")
    Result updateTT(@RequestBody @Valid OMContractAddTTDTO omContractAddTTDTO, BindingResult result) {
        if (result.hasErrors()) {
            omContractAddTTDTO.setCompletedStatus(0);
        } else {
            omContractAddTTDTO.setCompletedStatus(1);
        }
        omContractAddTTDTO.setSubmitType(SubmitTypeEnum.SUBMIT.getValue());
        return tradeTicketService.updateTT(omContractAddTTDTO);
    }

    /**
     * @description: 获取TT列表
     * @param: [queryDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/queryTTList")
    Result queryTTList(@RequestBody QueryDTO<TTQueryDTO> queryDTO) {
        return tradeTicketFacade.queryTTList(queryDTO);
    }

    /**
     * @description: 获取TT详情
     * @param: [contractCode]
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/queryTTDetail/{ttId}")
    Result queryTTDetail(@PathVariable Integer ttId) {
        return tradeTicketFacade.queryTTDetail(ttId);
    }


    /**
     * @description: TT审批
     * @param: [approvalDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/approveTT")
    Result approveTT(@RequestBody ApprovalDTO approvalDTO) {
        log.error("=======================================");
        log.error("=======================================");
        log.error("===================TradeTicketController.approveTT====================");
        log.error(JSON.toJSONString(approvalDTO));
        return tradeTicketFacade.approveTT(approvalDTO);
    }

    @PostMapping("/batchApproveTT")
    Result batchApproveTT(@RequestBody List<ApprovalDTO> approvalDTOList) {
        log.error("=======================================");
        log.error("=======================================");
        log.error("===================TradeTicketController.batchApproveTT====================");
        log.error(JSON.toJSONString(approvalDTOList));
        return tradeTicketFacade.batchApproveTT(approvalDTOList);
    }

    /**
     * @description: TT撤回
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/cancelTT")
    Result cancelTT(@RequestBody OperateTTDTO operateTTDTO) {
        return tradeTicketFacade.cancelTT(operateTTDTO);
    }

    /**
     * @description: TT作废
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/invalidTT")
    Result invalidTT(@RequestBody OperateTTDTO operateTTDTO) {
        return tradeTicketFacade.invalidTT(operateTTDTO);
    }

    /**
     * @description: 查询各状态TT数量
     * @param: [ttId]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/getTTStat")
    Result getTTStat(@RequestBody StatQueryDTO statQueryDTO) {
        return tradeTicketFacade.getTTStat(statQueryDTO);
    }

    /**
     * @description: 计算合同明细值
     * @param: [priceDetailDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/calculatePrice")
    Result calculatePrice(@RequestBody PriceDetailBO priceDetailBO) {
        return contractPriceFacade.calculatePrice(priceDetailBO);
    }

    /**
     * @description: 获取目的地
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/getDestination")
    Result getDestination() {
        return tradeTicketFacade.getDestination();
    }


    /**
     * @description: TT报表
     * @param: [approvalDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/queryTTReport")
    Result queryTTReport(@RequestBody ReportDTO reportDTO) {
        return tradeTicketFacade.queryTTReport(reportDTO);
    }

    /**
     * @description: TT报表
     * @param: [approvalDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/queryTotalReport")
    Result queryTotalReport(@RequestBody ReportDTO reportDTO) {
        return tradeTicketFacade.queryTotalReport(reportDTO);
    }


    /**
     * @description: 生成tt
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/generateTTByContractId")
    Result generateTTByContractId(@RequestParam("idList") Integer[] idList) {
        List<Integer> integers = Arrays.asList(idList);
        return tradeTicketFacade.generateTTByContractId(integers);
    }

    /**
     * @description: 生成tt
     * @return: com.navigator.common.dto.Result
     */
    @GetMapping("/generateTT")
    Result generateTT(@RequestParam("idStart") Integer idStart, @RequestParam("idEnd") Integer idEnd) {
        return tradeTicketFacade.generateTT(idStart, idEnd);


    }
}
