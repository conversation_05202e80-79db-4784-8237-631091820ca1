package com.navigator.magellan.web.controller.config;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.FactoryFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.pojo.dto.FactoryCreateDTO;
import com.navigator.customer.pojo.dto.FactoryQueryDTO;
import com.navigator.customer.pojo.entity.FactoryEntity;
import com.navigator.customer.pojo.entity.FactoryWarehouseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-17 21:53
 */
@RestController
@RequestMapping("/factory")
public class FactoryController {
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Resource
    private FactoryFacade factoryFacade;

    /**
     * 保存/更新工厂信息
     *
     * @param factoryCreateDTO 工厂信息
     * @return 保存/更新结果
     */
    @PostMapping("/saveOrUpdateFactory")
    public Result saveOrUpdateFactory(@RequestBody FactoryCreateDTO factoryCreateDTO) {
        return factoryWarehouseFacade.saveOrUpdateFactory(factoryCreateDTO);
    }

    /**
     * 获取所有工厂信息
     *
     * @return 工厂集合
     */
    @GetMapping("/getAllFactory")
    public Result getAllFactory(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(factoryWarehouseFacade.getAllFactory(status));
    }

    @GetMapping("/getAllFactoryBySyncSystem")
    public Result<List<FactoryEntity>> getAllFactoryBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(factoryFacade.getAllFactoryBySyncSystem(syncSystem));
    }

    /**
     * 根据ID获取工厂信息
     *
     * @param factoryId 工厂ID
     * @return 工厂详情信息
     */
    @GetMapping("/getFactoryDetailById")
    public Result getFactoryDetailById(@RequestParam(value = "factoryId") Integer factoryId) {
        return Result.success(factoryWarehouseFacade.getFactoryDetailById(factoryId));
    }

    /**
     * 更新工厂状态
     *
     * @param factoryId 工厂ID
     * @return 更新结果
     */
    @GetMapping("/updateStatus")
    public Result updateStatus(@RequestParam(value = "factoryId") Integer factoryId) {
        return factoryWarehouseFacade.updateStatus(factoryId);
    }

    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId       工厂ID
     * @param goodsCategoryId 品类ID
     * @return 发货库点集合
     */
    @GetMapping("/getWarehouseList")
    public Result getFactoryWarehouseList(@RequestParam(value = "factoryId", required = false) List<Integer> factoryId,
                                          @RequestParam(value = "factoryCode", required = false) String factoryCode,
                                          @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                          @RequestParam(value = "status", required = false) Integer status) {
        return factoryWarehouseFacade.getFactoryWarehouseList(factoryId, factoryCode, goodsCategoryId, status);
    }

    @PostMapping("/getFactoryWarehouseList")
    public Result getFactoryWarehouseList(@RequestBody FactoryQueryDTO factoryQueryDTO) {
        return factoryWarehouseFacade.getFactoryWarehouseList(factoryQueryDTO);
    }

    @PostMapping("/saveOrUpdateWarehouse")
    public Result saveOrUpdateFactoryWarehouse(@RequestBody FactoryWarehouseEntity warehouseEntity) {
        return factoryWarehouseFacade.saveOrUpdateFactoryWarehouse(warehouseEntity);
    }

    @GetMapping("/invalidWarehouse")
    public Result invalidWarehouse(@RequestParam(value = "id") Integer warehouseId) {
        return factoryWarehouseFacade.invalidWarehouse(warehouseId);
    }


    @GetMapping("/queryFactoryByCompanyId")
    public Result queryFactoryByCompanyId(@RequestParam("companyId") Integer companyId) {
        return Result.success(factoryWarehouseFacade.queryFactoryByCompanyId(companyId));
    }

    @GetMapping("/queryFactoryList")
    public Result queryFactoryList() {
        return Result.success(factoryWarehouseFacade.queryFactoryList());
    }
}
