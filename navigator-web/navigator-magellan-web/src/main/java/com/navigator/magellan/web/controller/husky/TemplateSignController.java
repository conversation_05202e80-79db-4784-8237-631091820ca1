package com.navigator.magellan.web.controller.husky;

import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateSignFacade;
import com.navigator.husky.pojo.entity.TemplateEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-24 11:01
 **/
@RestController
@RequestMapping("/template/sign")
public class TemplateSignController {
    @Resource
    private TemplateSignFacade templateSignFacade;

    @GetMapping("/matchTemplateByRule")
    public Result matchTemplateByRule(@RequestParam(value = "contractSignId") Integer contractSignId) {
        return templateSignFacade.matchTemplateByRule(contractSignId);
    }

    @GetMapping("/checkTemplateByRule")
    public Result checkTemplateByRule(@RequestParam(value = "contractSignId") Integer contractSignId) {
        return templateSignFacade.checkTemplateByRule(contractSignId);
    }

    @GetMapping("/matchTemplateByRuleV2")
    public Result matchTemplateByRuleV2(@RequestParam(value = "contractSignId") Integer contractSignId) {
        return templateSignFacade.matchTemplateByRuleV2(contractSignId);
    }
}
