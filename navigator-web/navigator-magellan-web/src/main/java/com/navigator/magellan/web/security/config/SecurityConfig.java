package com.navigator.magellan.web.security.config;

import com.navigator.magellan.web.security.filter.MagellanTokenAuthenticationFilter;
import com.navigator.magellan.web.security.filter.TokenLoginFilter;
import com.navigator.magellan.web.service.LoginLogRecordService;
import com.navigator.security.authentication.TokenLogoutHandler;
import com.navigator.security.authentication.TokenManager;
import com.navigator.security.authentication.UnauthorizedEntryPoint;
import com.navigator.security.permission.MyDeniedHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Description: Security配置类
 * Created by YuYong on 2021/10/27 15:28
 */
@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class SecurityConfig extends WebSecurityConfigurerAdapter {

    //    private UserDetailsService userDetailsService;
    private TokenManager tokenManager;

    @Autowired
    private LoginLogRecordService loginLogRecordService;
//    @Autowired
//    private AADAppRoleStatelessAuthenticationFilter aadAuthFilter;
//    @Autowired
//    private MyMagellanSecurityMetadataSource myMagellanSecurityMetadataSource;

    @Value("${security.validate}")
    private Integer securityValidate;

//    private RedisTemplate redisTemplate;

    @Autowired
    public SecurityConfig(TokenManager tokenManager) {
        this.tokenManager = tokenManager;
    }

    /**
     * 配置设置
     *
     * @param http
     * @throws Exception
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http.exceptionHandling()
                .authenticationEntryPoint(new UnauthorizedEntryPoint()).accessDeniedHandler(new MyDeniedHandler())
                .and().csrf().disable()
                .authorizeRequests()
//                .antMatchers("/customerMDM/**").authenticated()
                .anyRequest().authenticated()
                .and().logout().logoutUrl("/admin/acl/index/logout")
                .addLogoutHandler(new TokenLogoutHandler(tokenManager)).and()
                .addFilter(new MagellanTokenAuthenticationFilter(authenticationManager(), tokenManager))
                // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
                //.addFilter(new TokenLoginFilter(authenticationManager(), tokenManager))
                .addFilter(new TokenLoginFilter(authenticationManager(), tokenManager, loginLogRecordService))
                // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End
//                .addFilterBefore(new AuthFilter(myMagellanSecurityMetadataSource), FilterSecurityInterceptor.class)
//                .addFilterBefore(aadAuthFilter, UsernamePasswordAuthenticationFilter.class)
                .httpBasic();
    }
    /**
     * 配置哪些请求不拦截
     *
     * @param web
     */
    //MDM安全认证 Date:20250313 By:wan Start
    @Override
    public void configure(WebSecurity web) {
        String commonUrl = securityValidate == 1 ? "/aad" : "/**";
        web.ignoring().antMatchers(commonUrl, "/aad/login", "/aad/getAuthorizeLoginUrl", "/domain/downloadDomainTemplate", "/contractSign/getSignInfoById", "/file/download","/contractSign/downloadContractZip",
                "/swagger-resources/**", "/webjars/**", "/v2/**", "/swagger-ui.html/**", "/**/inner/**", "/file/**", "/**/template/info/**"
//                "/**/dictItem/**","/**/variable/**","/**/template/**","/**/contractSign/**","/**"
        );
        //web.ignoring().antMatchers("/*/**");
        web.ignoring().antMatchers("/user/sendVerificationCode/**","/user/login/**","/aad/sendAadCode/**","/aad/verifyAadCode","/user/refresh/**","/signature/callbackFunction");
        web.ignoring().antMatchers("/job/**");
    }
    //MDM安全认证 Date:20250313 By:wan end
//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//        // 使用内存认证来验证用户
//        auth.inMemoryAuthentication()
//                .withUser("navadmin")
//                .password(passwordEncoder().encode("2d76&d3#dfdDfd2"))
//                .roles("USER");
//    }


    @Bean
    public PasswordEncoder passwordEncoder() {
        // 这里我们使用bcrypt加密算法，安全性比较高
        return new BCryptPasswordEncoder();
    }
//
//    @Autowired
//    MyAuthenticationProvider myAuthenticationProvider;
//    @Override
//    protected void configure(AuthenticationManagerBuilder auth) throws Exception {
//        auth.authenticationProvider(myAuthenticationProvider);
//    }

}
