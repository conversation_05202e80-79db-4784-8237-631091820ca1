package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerProtocolFacade;
import com.navigator.customer.pojo.dto.CustomerProtocolDTO;
import com.navigator.customer.pojo.entity.CustomerProtocolEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/customerProtocol")
public class CustomerProtocolController {
    @Autowired
    private CustomerProtocolFacade customerProtocolFacade;

    @PostMapping("/queryCustomerProtocolList")
    public Result queryCustomerProtocolList(@RequestBody CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolFacade.queryCustomerProtocolList(customerProtocolDTO);
    }

    @PostMapping("/saveCustomerProtocol")
    public Result saveCustomerProtocol(@RequestBody CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolFacade.saveCustomerProtocol(customerProtocolDTO);
    }

    @PostMapping("/queryCustomerProtocolEntity")
    public CustomerProtocolEntity queryCustomerProtocolEntity(@RequestBody CustomerProtocolDTO customerProtocolDTO) {
        return customerProtocolFacade.queryCustomerProtocolEntity(customerProtocolDTO);
    }

    @PostMapping("/updateCustomerProtocol")
    public Result updateCustomerProtocol(@RequestBody CustomerProtocolDTO customerProtocolDTO){
        return customerProtocolFacade.updateCustomerProtocol(customerProtocolDTO);
    }

    @PostMapping("/queryCustomerProtocolSign")
    public Result queryCustomerProtocolSign(@RequestBody CustomerProtocolDTO customerProtocolDTO){
        return customerProtocolFacade.queryCustomerProtocolSign(customerProtocolDTO);
    }
}
