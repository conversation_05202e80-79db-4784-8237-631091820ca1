package com.navigator.magellan.web.controller.husky;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.TemplateFacade;
import com.navigator.husky.pojo.dto.CopyTemplateDTO;
import com.navigator.husky.pojo.dto.MarkMainVersionDTO;
import com.navigator.husky.pojo.dto.TemplateInfoJsonDTO;
import com.navigator.husky.pojo.dto.TemplateItemRelationDTO;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.husky.pojo.entity.VHuskyTemplateEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import com.navigator.husky.pojo.vo.TemplateItemRelationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-18 15:31
 **/
@RestController
@Slf4j
@RequestMapping("/template/info")
public class TemplateInfoController {
    @Autowired
    private TemplateFacade templateFacade;
    @Autowired
    private HttpServletResponse response;
    /**
     * 列表查询模板信息
     *
     * @param queryDTO 模板查询条件
     * @return 查询模板分页结果
     */
    @PostMapping("/queryByCondition")
    public Result queryByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO) {
        return templateFacade.queryByCondition(queryDTO);
    }

    /**
     * 新增模板信息
     *
     * @param templateEntity 模板信息
     * @return
     */
    @PostMapping("/saveTemplate")
    public Result<Boolean> saveTemplate(@RequestBody TemplateEntity templateEntity) {
        return templateFacade.saveTemplate(templateEntity);
    }

    @PostMapping("/updateTemplate")
    public Result<Boolean> updateTemplate(@RequestBody TemplateEntity templateEntity) {
        return templateFacade.updateTemplate(templateEntity);
    }

    @PostMapping("/copyTemplateInfo")
    public Result copyTemplateInfo(@RequestBody @Valid CopyTemplateDTO copyTemplateDTO) {
        return templateFacade.copyTemplateInfo(copyTemplateDTO);
    }

    @GetMapping("/updateTemplateStatus")
    public Result updateTemplateStatus(@RequestParam(value = "templateId") Integer templateId,
                                       @RequestParam(value = "status") Integer status) {
        return templateFacade.updateTemplateStatus(templateId, status);
    }

    @GetMapping("/getTemplateById")
    public Result<TemplateEntity> getTemplateById(@RequestParam(value = "templateId") Integer templateId) {
        return Result.success(templateFacade.getTemplateById(templateId));
    }

    /**
     * 查询所有条款组
     *
     * @param status 状态
     * @return
     */
    @GetMapping("/getAllTemplateList")
    public Result getAllTemplateList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(templateFacade.getAllTemplateList(status));
    }

    /**
     * 根据条款组编码，获取关联的所有模板
     *
     * @param templateGroupCode 条款组编码
     * @return 关联的模板信息
     */
    @GetMapping("/getTemplateListByGroupCode")
    public Result getTemplateListByGroupCode(@RequestParam(value = "templateGroupCode") String templateGroupCode) {
        return Result.success(templateFacade.getTemplateListByGroupCode(templateGroupCode));
    }

    /**
     * 绑定模板条款的关系
     *
     * @param itemRelationDTO 模板条款的绑定关系
     * @return 绑定结果
     */
    @PostMapping("/bindTemplateItemRelation")
    public Result bindTemplateItemRelation(@RequestBody TemplateItemRelationDTO itemRelationDTO) {
        return templateFacade.bindTemplateItemRelation(itemRelationDTO);
    }

    /**
     * 移除模板-条款绑定关系（根据模板编码/条款编码）
     *
     * @param templateCode     模板编码
     * @param templateItemCode 条款编码
     * @return 模板-条款绑定关系
     */
    @GetMapping("/removeTemplateItemRelation")
    public Result removeTemplateItemRelation(@RequestParam(value = "templateCode") String templateCode,
                                             @RequestParam(value = "templateItemCode") String templateItemCode) {
        return templateFacade.removeTemplateItemRelation(templateCode, templateItemCode);
    }


    /**
     * 获取模板-条款绑定关系（根据模板编码/条款编码）
     *
     * @param templateCode     模板编码
     * @param templateItemCode 条款编码
     * @return 模板-条款绑定关系
     */
    @GetMapping("/getTemplateItemRelation")
    public Result<List<TemplateItemRelationVO>> getTemplateItemRelation(@RequestParam(value = "templateCode", required = false) String templateCode,
                                                                        @RequestParam(value = "templateItemCode", required = false) String templateItemCode) {
        return Result.success(templateFacade.getTemplateItemRelation(templateCode, templateItemCode));
    }

    /**
     * 绑定模板条款的关系
     *
     * @param markMainVersionDTO 模板条款的绑定关系
     * @return 绑定结果
     */
    @PostMapping("/markMainVersion")
    public Result markMainVersion(@RequestBody @Valid MarkMainVersionDTO markMainVersionDTO) {
        return templateFacade.markMainVersion(markMainVersionDTO);
    }

    /**
     * 健康检测
     *
     * @param templateId
     * @return
     */
    @GetMapping("/healthCheck")
    public Result healthCheck(@RequestParam(value = "templateId") Integer templateId) {
        return Result.success(templateFacade.healthCheck(templateId));
    }

    /**
     * 导出模板
     *
     * @param templateQO
     * @return
     */
    @PostMapping("/exportTemplateJson")
    public Result exportTemplateJson(@RequestBody QueryTemplateQO templateQO, HttpServletResponse response) {
        Result templateResult = templateFacade.exportTemplateJson(templateQO);
        if (!templateResult.isSuccess()) {
            return templateResult;
        }
        if (null == templateResult.getData()) {
            return Result.failure("无可导出的模板！");
        }
        List<TemplateInfoJsonDTO> templateInfoJsonDTOList = JSON.parseArray(JSON.toJSONString(templateResult.getData()), TemplateInfoJsonDTO.class);
        JsonFileUtil.exportJson(response, templateInfoJsonDTOList, "Template_" + DateTimeUtil.formatDateValue() + ".json");
        return Result.success(templateInfoJsonDTOList);
    }

    /**
     * 导入模板脚本并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importTemplateJson")
    public Result importTemplateJson(@RequestParam("file") MultipartFile file) {
        return templateFacade.importTemplateJson(file);
    }

    @PostMapping("/exportTemplateExcel")
    public void exportTemplateExcel(@RequestBody QueryTemplateQO templateQO) {
        List<VHuskyTemplateEntity> templateEntityList = new ArrayList<>();
        Result templateResult = templateFacade.exportTemplateExcel(templateQO);
        if (templateResult.isSuccess()) {
            templateEntityList = JSON.parseArray(JSON.toJSONString(templateResult.getData()), VHuskyTemplateEntity.class);
        }
        if (CollectionUtils.isEmpty(templateEntityList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "模板数据导出_" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(templateEntityList, "模板信息", "模板导出信息", VHuskyTemplateEntity.class, fileName, response);
//        return Result.success(templateEntityList);
    }

}
