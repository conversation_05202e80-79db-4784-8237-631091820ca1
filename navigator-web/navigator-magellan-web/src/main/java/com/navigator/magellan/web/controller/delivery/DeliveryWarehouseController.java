package com.navigator.magellan.web.controller.delivery;


import com.navigator.admin.facade.WarehouseFacade;
import com.navigator.admin.pojo.bo.WarehouseBO;
import com.navigator.admin.pojo.entity.WarehouseEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.delivery.facade.DeliveryWarehouseFacade;
import com.navigator.delivery.pojo.dto.DeliveryWarehouseDTO;
import com.navigator.delivery.pojo.entity.DeliveryWarehouseEntity;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 提货库点配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/deliveryWarehouse")
public class DeliveryWarehouseController {
    private final DeliveryWarehouseFacade deliveryWarehouseFacade;
    private final WarehouseFacade warehouseFacade;

    @ApiOperation(value = "新增提货库点配置")
    @PostMapping("/addDeliveryWarehouse")
    public Result<Boolean> addDeliveryWarehouse(@RequestBody DeliveryWarehouseEntity deliveryWarehouseEntity) {
        return deliveryWarehouseFacade.addDeliveryWarehouse(deliveryWarehouseEntity);
    }

    @ApiOperation(value = "修改提货库点配置")
    @PostMapping("/updateDeliveryWarehouse")
    public Result<Boolean> updateDeliveryWarehouse(@RequestBody DeliveryWarehouseEntity deliveryWarehouseEntity) {
        return deliveryWarehouseFacade.updateDeliveryWarehouse(deliveryWarehouseEntity);
    }

    @ApiOperation(value = "禁用/启用提货库点配置")
    @GetMapping("/updateDeliveryWarehouseStatus")
    public Result<Boolean> updateDeliveryWarehouseStatus(@ApiParam("提货库点id") @RequestParam("warehouseId") Integer warehouseId,
                                                         @ApiParam("提货库点状态") @RequestParam("status") Integer status) {
        return deliveryWarehouseFacade.updateDeliveryWarehouseStatus(warehouseId, status);
    }

    @ApiOperation(value = "查询提货库点配置列表")
    @PostMapping("/getDeliveryWarehouseList")
    public Result<List<WarehouseEntity>> getDeliveryWarehouseList(@RequestBody DeliveryWarehouseDTO warehouseDTO) {
        QueryDTO<WarehouseBO> queryDTO = new QueryDTO<>();
        WarehouseBO condition = new WarehouseBO();
        condition.setName(warehouseDTO.getWarehouseName());
        condition.setCode(warehouseDTO.getWarehouseCode());
        condition.setWarehouseType(warehouseDTO.getWarehouseType());
        condition.setIsUnset(0);
        condition.setStatus(warehouseDTO.getStatus());
        queryDTO.setCondition(condition);
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(10000);
        return warehouseFacade.getWarehouseList(queryDTO);
    }

}
