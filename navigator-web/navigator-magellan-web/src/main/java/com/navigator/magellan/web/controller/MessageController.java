package com.navigator.magellan.web.controller;


import com.navigator.admin.pojo.dto.RuleEnumValueDTO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.dagama.facade.MessageFacade;
import com.navigator.dagama.facade.TempateFacade;
import com.navigator.dagama.pogo.model.entity.DbmBusinessTemplateEntity;
import com.navigator.dagama.pogo.model.entity.DbmInmailEntity;
import com.navigator.dagama.pogo.model.entity.DbmSendTaskEntity;
import com.navigator.dagama.pogo.model.entity.DbmTemplateEntity;
import com.navigator.dagama.pogo.model.enums.BusinessSceneEnum;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/4 15:34
 */
@RestController
@RequestMapping("/message")
public class MessageController {

    @Resource
    private MessageFacade messageFacade;
    @Resource
    private TempateFacade tempateFacade;

    /**
     * 查看站类信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryInmail")
    public Result queryInmail(@RequestBody QueryDTO<DbmInmailEntity> queryDTO) {
        return messageFacade.queryInmail(queryDTO);
    }

    @GetMapping("/getInmailSceneList")
    public Result getInmailSceneList() {
        List<RuleEnumValueDTO> ruleEnumValueDTOList = BusinessSceneEnum.getBusinessSceneList()
                .stream().map(it -> {
                    return new RuleEnumValueDTO().setValue(it.getValue())
                            .setDesc(it.getName());
                }).collect(Collectors.toList());
        return Result.success(ruleEnumValueDTOList);
    }


    /**
     * 消息标记已读
     *
     * @param id
     * @return
     */
    @GetMapping("/readInmail")
    public Result readInmail(@RequestParam(value = "id", required = false) List<Integer> id) {
        return messageFacade.readInmail(id);
    }

    @GetMapping("/queryInmailCount")
    public Result queryInmailCount() {
        return messageFacade.queryInmailCount(SystemEnum.MAGELLAN.getValue());
    }

    /**
     * 查询模板
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryTemplate")
    public Result queryTemplate(@RequestBody QueryDTO<DbmTemplateEntity> queryDTO) {
        return tempateFacade.queryTemplate(queryDTO);
    }

    @GetMapping("/queryTemplateById")
    public Result queryTemplateById(@RequestParam(name = "id", required = false) Integer id) {
        return tempateFacade.queryTemplateById(id);
    }

    @PostMapping("/updateTemplate")
    public Result updateTemplate(@RequestBody DbmTemplateEntity dbmTemplateEntity) {
        return tempateFacade.updateTemplate(dbmTemplateEntity);
    }

    @PostMapping("/queryBusinessTemplate")
    public Result queryBusinessTemplate(@RequestBody QueryDTO<DbmBusinessTemplateEntity> queryDTO) {
        return tempateFacade.queryBusinessTemplate(queryDTO);
    }

    @PostMapping("/updateBusinessTemplateStatus")
    public Result updateBusinessTemplateStatus(@RequestBody DbmBusinessTemplateEntity dbmBusinessTemplateEntity) {
        return tempateFacade.updateBusinessTemplateStatus(dbmBusinessTemplateEntity);
    }

    /**
     * 发送信息列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySendTas")
    public Result querySendTas(@RequestBody QueryDTO<DbmSendTaskEntity> queryDTO) {
        return messageFacade.querySendTas(queryDTO);
    }
}
