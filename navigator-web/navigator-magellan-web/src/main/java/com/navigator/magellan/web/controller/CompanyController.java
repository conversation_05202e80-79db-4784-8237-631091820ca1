package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.CompanyFacade;
import com.navigator.admin.pojo.dto.CompanyDTO;
import com.navigator.admin.pojo.entity.CompanyEntity;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/company")
public class CompanyController {
    @Autowired
    private CompanyFacade companyFacade;

    @PostMapping("/queryCompanyList")
    public Result queryCompanyList() {
        List<CompanyEntity> companyEntityList = companyFacade.queryCompanyList();
        return Result.success(companyEntityList);
    }

    @GetMapping("/getAllCompany")
    public Result getAllCompany() {
        return Result.success(companyFacade.getAllCompany());
    }

    @PostMapping("/queryCompanyDTOList")
    public Result queryCompanyDTOList() {
        return Result.success(companyFacade.queryCompanyDTOList());
    }

    @GetMapping("/queryCompanyById")
    CompanyEntity queryCompanyById(@RequestParam(value = "id") Integer id) {
        return companyFacade.queryCompanyById(id);
    }

    @PostMapping("/saveCompany")
    Result saveCompany(@RequestBody CompanyDTO companyDTO) {
        return companyFacade.saveCompany(companyDTO);
    }

    @PostMapping("/updateCompany")
    Result updateCompany(@RequestBody CompanyDTO companyDTO) {
        return companyFacade.updateCompany(companyDTO);
    }

    @PostMapping("/updateCompanyStatus")
    Result updateCompanyStatus(@RequestBody CompanyDTO companyDTO) {
        return companyFacade.updateCompanyStatus(companyDTO);
    }

    @GetMapping("/getAllCompanyBySyncSystem")
    public Result<List<CompanyEntity>> getAllCompanyBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(companyFacade.getAllCompanyBySyncSystem(syncSystem));
    }
}
