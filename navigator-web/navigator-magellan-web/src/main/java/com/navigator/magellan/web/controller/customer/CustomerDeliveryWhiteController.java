package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerDeliveryWhiteFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteDTO;
import com.navigator.customer.pojo.dto.CustomerDeliveryWhiteExcelDTO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/10
 */
@RestController
@RequestMapping("/customerDeliveryWhite")
public class CustomerDeliveryWhiteController {

    @Value("${blobFile.url.customerDeliveryWhite}")
    private String fileUrl;

    @Resource
    private CustomerDeliveryWhiteFacade customerDeliveryWhiteFacade;

    @PostMapping("/queryCustomerDeliveryWhite")
    public Result queryCustomerDeliveryWhite(@RequestBody CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        return Result.success(customerDeliveryWhiteFacade.queryCustomerDeliveryWhite(customerDeliveryWhiteDTO));
    }

    /**
     * 根据条件查询列表
     *
     * @param customerDeliveryWhiteDTO
     * @return
     */
    @PostMapping("/queryCustomerDeliveryWhiteList")
    public Result queryCustomerDeliveryWhiteList(@RequestBody CustomerDeliveryWhiteDTO customerDeliveryWhiteDTO) {
        return Result.success(customerDeliveryWhiteFacade.queryCustomerDeliveryWhiteList(customerDeliveryWhiteDTO));
    }

    /**
     * 下载客户提货委托白名单
     *
     * @return
     */
    @PostMapping("/downloadCustomerWhitelist")
    public Result downloadCustomerWhitelist(@RequestBody CustomerDTO customerDTO, HttpServletResponse response) {
        List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteExcelDTOS = customerDeliveryWhiteFacade.downloadCustomerWhitelist(customerDTO);
        if (CollectionUtils.isEmpty(customerDeliveryWhiteExcelDTOS)) {
            String fileName = "提货委托白名单" + DateTimeUtil.formatDateValue();
            EasyPoiUtils.exportExcel(customerDeliveryWhiteExcelDTOS, null, "提货委托白名单查询数据", CustomerDeliveryWhiteExcelDTO.class, fileName, response);
        }

        String fileName = "提货委托白名单" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(customerDeliveryWhiteExcelDTOS, null, "提货委托白名单查询数据", CustomerDeliveryWhiteExcelDTO.class, fileName, response);
        return Result.success();
    }

    /**
     * 客户提货委托白名单上传校验
     *
     * @param file
     * @return
     */
    @PostMapping("/checkCustomerWhitelist")
    public Result checkCustomerWhitelist(@RequestPart(value = "file") MultipartFile file) {
        return customerDeliveryWhiteFacade.checkCustomerWhitelist(file);
    }

    /**
     * 客户提货委托白名单excel上传
     *
     * @param file
     * @return
     */
    @PostMapping("/uploadCustomerWhitelist")
    public Result uploadCustomerWhitelist(@RequestPart(value = "file") MultipartFile file) {
        return customerDeliveryWhiteFacade.uploadCustomerWhitelist(file);
    }

    /**
     * 模板下载
     *
     * @param response
     * @return
     */
    @GetMapping("/downloadCustomerWhiteTemplate")
    public Result downloadCustomerWhiteTemplate(HttpServletResponse response) {
        /*List<CustomerDeliveryWhiteExcelDTO> customerDeliveryWhiteExcelDTOS = new ArrayList<>();
        String fileName = "提货白名单模板" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(customerDeliveryWhiteExcelDTOS, null, "提货白名单模板", CustomerDeliveryWhiteExcelDTO.class, fileName, response);
        */
        return Result.success(fileUrl);
    }
}
