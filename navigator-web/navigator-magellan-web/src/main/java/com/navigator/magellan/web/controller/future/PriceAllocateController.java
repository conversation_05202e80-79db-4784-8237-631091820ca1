package com.navigator.magellan.web.controller.future;

import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.PriceTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.pojo.dto.*;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.pojo.vo.TtPriceEntityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * Description: No Description
 * Created by <PERSON><PERSON><PERSON> on 2022/1/10 15:17
 */
@RestController
@RequestMapping("/future")
@Slf4j
public class PriceAllocateController {

    @Autowired
    private PriceAllocateFacade priceAllocateFacade;
    @Autowired
    private ContractPriceFacade contractPriceFacade;


    /**
     * 点、转 分配生成分配单
     *
     * @param distributionDTO
     * @return
     */
    @PostMapping("/genDistributionOrder")
    public Result genDistributionOrder(@RequestBody @Valid DistributionDTO distributionDTO) {
        // 如果操作类型是点价 ，需要没有待分配和分配待审核的分配单
        if (Integer.valueOf(distributionDTO.getOperationType()) == PriceTypeEnum.PRICING.getValue()) {

            if (null == distributionDTO.getCategoryId()) {
                distributionDTO.setCategoryId(GoodsCategoryEnum.OSM_MEAL.getValue());
            }
            boolean b = priceAllocateFacade.getPriceDealOrdersByDominantCode(distributionDTO.getCustomerId(), distributionDTO.getDomainCode(), distributionDTO.getCategoryId());
            if (b) {
                return Result.failure("该合约下还有成交未分配的转月单或分配未审核的转月分配单");
            }
        }
        distributionDTO.setSystem(SystemEnum.MAGELLAN.getValue());
        return priceAllocateFacade.genDistributionOrder(distributionDTO);
    }

    /**
     * 根据条件查询待审核的分配单（分页）
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryPriceAllocates")
    public Result queryPriceAllocates(@RequestBody QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        return priceAllocateFacade.queryPriceAllocates(queryDTO);
    }

    /**
     * 分配明细查询
     */
    @GetMapping("/getPriceAllocateDetail")
    public Result getPriceAllocateDetail(@RequestParam String applyCode) {
        return priceAllocateFacade.getPriceAllocateDetail(applyCode);
    }

    /**
     * 审核点价和转月的分配单
     *
     * @param auditPriceAllocateDTO
     * @return
     */
    @PostMapping("/auditPriceAllocate")
    public Result auditPriceAllocate(@RequestBody List<AuditPriceAllocateDTO> auditPriceAllocateDTO) {
        return priceAllocateFacade.auditPriceAllocate(auditPriceAllocateDTO);
    }

    /**
     * 定价单
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getTtPrice")
    public Result getTtPrice(@RequestParam("contractCode") String contractCode) {
        TtPriceEntityVO ttPriceEntityVO = contractPriceFacade.getTtPrice(contractCode);
        return Result.success(ttPriceEntityVO);
    }

    /**
     * 分配待审核修改并通过
     *
     * @param distributionDTO
     * @return
     */
    @PostMapping("/modifyAllocates")
    public Result modifyAllocates(@RequestBody @Valid DistributionDTO distributionDTO) {
        return priceAllocateFacade.modifyAllocates(distributionDTO);
    }

    /**
     * 采购合同点价、转月
     *
     * @param priceTransferDTOs
     * @return
     */
    @PostMapping("/purchaseContractPriceTransfer")
    public Result purchaseContractPriceTransfer(@RequestBody List<PriceTransferDTO> priceTransferDTOs) {
        return priceAllocateFacade.purchaseContractPriceTransfer(priceTransferDTOs);
    }


    /**
     * 结构化定价查询分配记录
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryPriceAllocateRecord")
    public Result queryPriceAllocateRecord(@RequestBody QueryDTO<QueryPriceAllocateDTO> queryDTO) {
        return priceAllocateFacade.queryPriceAllocateRecord(queryDTO);
    }

    /**
     * 采购合同反点价
     *
     * @param reverseDTO
     * @return
     */
    @PostMapping("/purchaseContractReverse")
    public Result purchaseContractReverse(@RequestBody ReverseDTO reverseDTO) {
        return priceAllocateFacade.purchaseContractReverse(reverseDTO);
    }

    /**
     * 成交申请单自动分配
     *
     * @return
     */
    @GetMapping("/automaticGenDistributionOrder")
    public Result automaticGenDistributionOrder() {
        return priceAllocateFacade.automaticGenDistributionOrder();
    }


    @PostMapping("/priceAllocateContrary")
    public Result priceAllocateContrary(@RequestBody ApplyContraryDTO applyContraryDTO) {
        return priceAllocateFacade.priceAllocateContrary(applyContraryDTO);
    }

}
