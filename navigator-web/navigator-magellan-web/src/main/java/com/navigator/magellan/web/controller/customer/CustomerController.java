package com.navigator.magellan.web.controller.customer;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.vo.columbus.ColumbusAdminVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerMdmFacade;
import com.navigator.customer.facade.FactoryWarehouseFacade;
import com.navigator.customer.facade.SupplierFacade;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveDTO;
import com.navigator.customer.pojo.dto.SystemAndCustomerDTO;
import com.navigator.customer.pojo.dto.mdm.MDMObjectDataDTO;
import com.navigator.customer.pojo.entity.CrisGlobalEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/6 14:26`
 */
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Resource
    private CustomerFacade customerFacade;
    @Resource
    private SupplierFacade supplierFacade;
    @Resource
    private FactoryWarehouseFacade factoryWarehouseFacade;
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private HttpServletResponse response;
    @Resource
    private CustomerMdmFacade customerMdmFacade;

    /**
     * 查询客户户供应商信息
     *
     * @return
     */
    @GetMapping("/queryCustomerAll")
    public Result queryCustomerAll() {
        return customerFacade.queryCustomerAll();
    }


    /**
     * 根据id查询客户信息 只返回客户属性 不返回客户配置
     *
     * @param id
     * @return
     */
    @GetMapping("/queryCustomerById")
    public Result queryCustomerById(@RequestParam(value = "id") Integer id) {
        return Result.success(customerFacade.queryCustomerById(id));
    }

    /**
     * 查询客户户供应商信息
     *
     * @return
     */
    @PostMapping("/queryCustomerSupplierAll")
    public Result queryCustomerSupplierAll(@RequestBody QueryDTO<CustomerDTO> queryDTO) {
        return customerFacade.queryCustomerSupplierAll(queryDTO);
    }

    /**
     * 查询供应商信息
     *
     * @return
     */
    @GetMapping("/querySupplierList")
    public Result querySupplierList() {
        return supplierFacade.querySupplierList();
    }


    /**
     * 根据工厂id查询出发货库点
     *
     * @param factoryId
     * @return
     */
    @GetMapping("/queryFactoryIdAll")
    public Result getFactoryWarehouse(@RequestParam(value = "factoryId") List<Integer> factoryId,
                                      @RequestParam(value = "factoryCode", required = false) String factoryCode,
                                      @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId,
                                      @RequestParam(value = "status", required = false) Integer status) {
        return factoryWarehouseFacade.getFactoryWarehouseList(factoryId, factoryCode, goodsCategoryId, status);
    }

    /**
     * 查询出货工厂
     *
     * @param supplierId
     * @return
     */
    @GetMapping("/factoryConfigBySuppId")
    public Result factoryConfigBySuppId(@RequestParam(value = "supplierId") Integer supplierId) {
        return Result.success(supplierFacade.factoryConfigBySuppId(supplierId));
    }

    /**
     * 分页查询出客户客户信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryCustomerList")
    public Result queryCustomerList(@RequestBody QueryDTO<CustomerDTO> queryDTO) {
        return customerFacade.queryCustomerList(queryDTO);
    }

    /**
     * 根据id查询出客户信息（详细信息）
     *
     * @param id
     * @return
     */
    @GetMapping("/getCustomerById")
    public Result getCustomerById(@RequestParam(value = "id") Integer id) {
        return Result.success(customerFacade.getCustomerById(id));
    }

    /**
     * 根据供应商id查询油厂
     *
     * @param parentId
     * @return
     */
    @GetMapping("/querySupplierFactory")
    public Result querySupplierFactory(@RequestParam(value = "parentId") Integer parentId) {
        return supplierFacade.querySupplierFactory(parentId);
    }


    /**
     * 根据客户id 查询出客户信息  系统及账号
     *
     * @param customerId
     * @return
     */
    @GetMapping("/getSystemAndCustomerById")
    public Result getSystemAndCustomerById(@RequestParam(value = "customerId") Integer customerId) {
        return customerFacade.getSystemAndCustomerById(customerId);
    }


    /**
     * 系统及账号提交
     *
     * @param systemAndCustomerDTO
     * @return
     */
    @PostMapping("/updateSystemAndCustomer")
    public Result updateSystemAndCustomer(@RequestBody SystemAndCustomerDTO systemAndCustomerDTO) {
        return customerFacade.updateSystemAndCustomer(systemAndCustomerDTO);
    }


    /**
     * 更新客户信息  补充客户信息
     *
     * @param customerDTO
     * @return
     */
    @PostMapping("/replenishCustomerMessage")
    public Result replenishCustomerMessage(@RequestBody CustomerDTO customerDTO) {
        return customerFacade.replenishCustomerMessage(customerDTO);
    }

    /**
     * 合同模板 通知人 信息查询
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    @GetMapping("/queryTemplateContactFactoryByCustomerId")
    public Result queryTemplateContactFactoryByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                                          @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(customerFacade.queryTemplateContactFactoryByCustomerId(customerId, categoryId));
    }

    /**
     * 修改合同模板 通知人 信息
     *
     * @param customerTemplateDTO
     * @return
     */
    @PostMapping("/updateTemplateContactFactory")
    public Result updateTemplateContactFactory(@RequestBody CustomerTemplateDTO customerTemplateDTO) {
        return customerFacade.updateTemplateContactFactory(customerTemplateDTO);
    }

    /**
     * 根据客户id查询 客户是否使用系统,是否使用易企签,是否实名  易企签通用配置是否启用
     *
     * @param customerId
     * @return
     */
    @GetMapping(value = "/customerSignParameter")
    public Result customerSignParameter(@RequestParam("customerId") Integer customerId) {
        return customerFacade.customerSignParameter(customerId);
    }

    /**
     * 下载客户导入数据模板
     *
     * @param response
     * @return
     */
    @GetMapping("/exportCustomer")
    public Result exportCustomer(HttpServletResponse response) {
        List<CustomerTemplateDeriveDTO> customerTemplateDeriveDTOArrayList = new ArrayList<>();
        CustomerTemplateDeriveDTO customerTemplateDeriveDTO = new CustomerTemplateDeriveDTO();

        customerTemplateDeriveDTO.setSignPlace("天津滨海新区");
        customerTemplateDeriveDTO.setShortName("路易达孚（张家港）饲料蛋白有限公司");
        customerTemplateDeriveDTO.setStatus("有效");
        customerTemplateDeriveDTO.setIsAuthorization("是");
        customerTemplateDeriveDTO.setIsSupplier("是");
        customerTemplateDeriveDTO.setEnterprise("是");
        customerTemplateDeriveDTO.setLinkageCustomerCode("1218763");
        customerTemplateDeriveDTO.setCustomerName("路易达孚（张家港）饲料蛋白有限公司");
        customerTemplateDeriveDTO.setCustomerIndexesName("LYDFZJGSLDBYXGS");
        customerTemplateDeriveDTO.setEnterpriseName("");
        customerTemplateDeriveDTO.setEnterpriseCode("");
        customerTemplateDeriveDTO.setFullName("路易达孚（张家港）饲料蛋白有限公司");
        customerTemplateDeriveDTO.setFullNameEnglish("Louis Dafu (Zhangjiagang) Feed protein Co. LTD");
        customerTemplateDeriveDTO.setAddress("天津自贸试验区（东疆保税港区）重庆道以南、呼伦贝尔路以西铭海中心3号楼-5、6-704");
        customerTemplateDeriveDTOArrayList.add(customerTemplateDeriveDTO);


        String fileName = "上传客户主数据模板" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(customerTemplateDeriveDTOArrayList, null, "上传客户主数据模板", CustomerTemplateDeriveDTO.class, fileName, response);
        return Result.success();
    }

    /**
     * 校验客户数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/verifyFileCustomerConfig", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result verifyFileCustomerConfig(@RequestPart("file") MultipartFile file) {
        return customerFacade.verifyFileCustomerConfig(file);
    }

    /**
     * 导入数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/saveCustomerConfig", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result saveCustomerConfig(@RequestPart("file") MultipartFile file) {
        return customerFacade.saveCustomerConfig(file);
    }


    /**
     * 删除客户数据
     *
     * @param customerId
     * @return
     */
    @GetMapping("/deleteCustomerById")
    public Result deleteCustomerById(@RequestParam(value = "customerId") Integer customerId) {
        return customerFacade.deleteCustomerById(customerId);
    }

    @GetMapping("/queryCustomerByCompanyAndLDC")
    public Result queryCustomerByCompanyAndLDC(@RequestParam(value = "companyId") Integer companyId) {
        return Result.success(customerFacade.queryCustomerByCompanyAndLDC(companyId));
    }

    /**
     * 查询管理员列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryColumbusAdminList")
    public Result queryColumbusAdminList(@RequestBody QueryDTO<CEmployDTO> queryDTO) {
        return cEmployFacade.queryColumbusAdminList(queryDTO);
    }

    /**
     * 设置账号状态
     *
     * @param cEmployDTO
     * @return
     */
    @PostMapping("/updateColumbusAdmin")
    public Result updateColumbusAdmin(@RequestBody CEmployDTO cEmployDTO) {
        return cEmployFacade.updateColumbusAdmin(cEmployDTO);
    }

    /**
     * @param cEmployDTO
     * @return
     */
    @PostMapping("/setColumbusAdmin")
    public Result setColumbusAdmin(@RequestBody CEmployDTO cEmployDTO) {
        return cEmployFacade.setColumbusAdmin(cEmployDTO);
    }

    /**
     * 导出管理员列表
     *
     * @param cEmployDTO
     * @return
     */
    @PostMapping("/exportColumbusAdminList")
    public void exportColumbusAdminList(@RequestBody CEmployDTO cEmployDTO) {
        Result result = cEmployFacade.exportColumbusAdminList(cEmployDTO);
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<ColumbusAdminVO> columbusAdminVOList = JSON.parseArray(JSON.toJSONString(result.getData()), ColumbusAdminVO.class);
        if (CollectionUtils.isEmpty(columbusAdminVOList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "exportColumbusAdminList";
        EasyPoiUtils.exportExcel(columbusAdminVOList, null, "exportColumbusAdminList", ColumbusAdminVO.class, fileName, response);
    }

    /**
     * CaseId-1002453: RR status in navigator，Author By NaNa
     * @param customerId
     * @return 客户的剩余风险控制信息
     */
    @GetMapping("/getCustomerResidualRiskInfo")
    public Result getCustomerResidualRiskInfo(@RequestParam("customerId") Integer customerId) {
        CrisGlobalEntity crisGlobalEntity = customerFacade.getCustomerResidualRiskInfo(customerId);
        if (null == crisGlobalEntity) {
            throw new BusinessException(ResultCodeEnum.CUSTOMER_RESIDUAL_RISK_NOT_EXIST);
        }
        return Result.success(crisGlobalEntity);
    }

    /**
     * 对接MDM接口
     *
     * @param mdmObjectDataDTO
     * @return
     */
    @PostMapping(value = "/saveOrUpdateMDMCustomer")
    public Result saveOrUpdateMDMCustomer(@RequestBody MDMObjectDataDTO mdmObjectDataDTO){
        return customerMdmFacade.saveOrUpdateMDMCustomer(mdmObjectDataDTO);
    }
}
