package com.navigator.magellan.web.controller.future;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.future.facade.PriceGradeFacade;
import com.navigator.future.facade.PricePowerTimeFacade;
import com.navigator.future.pojo.entity.PriceGradeEntity;
import com.navigator.future.pojo.entity.PricePowerTimeEntity;
import com.navigator.future.pojo.vo.PriceApplyVO;
import com.navigator.future.pojo.vo.PricePowerTimeVO;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/future")
public class PricePowerTimeController {

    @Resource
    private PricePowerTimeFacade pricePowerTimeFacade;

    @Resource
    private PriceGradeFacade priceGradeFacade;

    @PostMapping("/savePricePowerTime")
    public Result savePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime){
        return pricePowerTimeFacade.savePricePowerTime(pricePowerTime);
    }

    @PostMapping("/updatePricePowerTime")
    public Result updatePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime){
        return pricePowerTimeFacade.updatePricePowerTime(pricePowerTime);
    }

    @PostMapping("/deletePricePowerTime")
    public Result deletePricePowerTime(@RequestBody PricePowerTimeEntity pricePowerTime){
        return pricePowerTimeFacade.deletePricePowerTime(pricePowerTime);
    }

    @PostMapping("/queryPricePowerTimeList")
    public Result queryPricePowerTimeList(@RequestBody QueryDTO<PricePowerTimeEntity> queryDTO) {
        return pricePowerTimeFacade.queryPricePowerTimeList(queryDTO);
    }

    @GetMapping("/exportPricePowerTimeList")
    public void exportPricePowerTimeList(@RequestParam(value = "categoryIdList", required = false) String categoryIdList, HttpServletResponse response){
        List<PricePowerTimeVO> voList = pricePowerTimeFacade.exportPricePowerTimeList(categoryIdList);
        if (CollectionUtils.isEmpty(voList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "挂单时间区间";
        EasyPoiUtils.exportExcel(voList, "挂单时间区间", "挂单时间区间", PricePowerTimeVO.class, fileName, response);
    }

    @PostMapping("/updatePriceGrade")
    public Result updatePriceGrade(@RequestBody PriceGradeEntity priceGradeEntity){
        return priceGradeFacade.updatePriceGrade(priceGradeEntity);
    }

    @PostMapping("/queryPriceGradeList")
    public Result queryPriceGradeList(@RequestBody QueryDTO<PriceGradeEntity> queryDTO){
        return priceGradeFacade.queryPriceGradeList(queryDTO);
    }
}