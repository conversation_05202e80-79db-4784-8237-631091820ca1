package com.navigator.magellan.web.aspect;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;
import com.navigator.common.util.JwtUtils;
import com.navigator.magellan.web.annotation.OperationLog;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;

/**
 * 系统日志：切面处理类
 */
@Aspect
@Component
public class SysLogAspect {

    @Autowired
    OperationLogFacade operationLogFacade;


    private Logger logger = LoggerFactory.getLogger(SysLogAspect.class);


    //定义切点 @Pointcut
    //在注解的位置切入代码
    @Pointcut("@annotation( com.navigator.magellan.web.annotation.OperationLog)")
    public void logPoinCut() {
    }

    //切面 配置通知
    @AfterReturning(pointcut = "logPoinCut()")
    public void saveSysLog(JoinPoint joinPoint) {
        int i;
        OperationDetailDTO operationDetailDTO = new OperationDetailDTO();

        //从切面织入点处通过反射机制获取织入点处的方法
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取切入点所在的方法
        Method method = signature.getMethod();


        //获取操作
        OperationLog myLog = method.getAnnotation(OperationLog.class);

        operationDetailDTO.setBizModule(myLog.bizModule())
                .setOperationName(myLog.operationName())
                .setTriggerSys(myLog.triggerSys());

        //获取用户id
        operationDetailDTO.setOperatorId(Integer.valueOf(JwtUtils.getCurrentUserId()));

        // 获取请求后的结果
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (requestAttributes != null) {
            ServletRequestAttributes attributes = (ServletRequestAttributes) requestAttributes;
            request = attributes.getRequest();
        }

        operationDetailDTO.setData(null == request ? "" : JSON.toJSONString(request.getParameterMap()));

        try {

            //保存操作日志
            logger.info(">>>>>>>>>>> SysLogAspect start.");
            operationLogFacade.saveOperationDetail(operationDetailDTO);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(">>>>>>>>>>> SysLogAspect error:{}", e.getMessage());
        }

    }

}
