package com.navigator.magellan.web.security.filter;

import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.security.authentication.TokenManager;
import com.navigator.security.filter.TokenAuthenticationFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * Description: 访问过滤器
 * Created by <PERSON><PERSON><PERSON> on 2021/11/3 13:46
 */
public class MagellanTokenAuthenticationFilter extends TokenAuthenticationFilter {

    private TokenManager tokenManager;
    //    private RedisTemplate redisTemplate;
    @Autowired
    private EmployFacade employFacade;

    public MagellanTokenAuthenticationFilter(AuthenticationManager authManager, TokenManager tokenManager) {
        super(authManager);
        this.tokenManager = tokenManager;
    }


    @Override
    public UsernamePasswordAuthenticationToken getAuthentication(HttpServletRequest request, HttpServletResponse res) {
        // token置于header里
        String token = request.getHeader("token");
        if (token != null && !"".equals(token.trim())) {
            String email = tokenManager.getEmailByJwtToken(request);
            String employId = tokenManager.getUserIdFromToken(request);
            tokenManager.verifyForbidden(employId, res);
            if (tokenManager.verifyNeedRefresh(request)) {
                res.setHeader("JWT-RefreshStatus", "1");
            } else {
                res.setHeader("JWT-RefreshStatus", "0");
            }
            res.setHeader("Access-Control-Expose-Headers", "JWT-RefreshStatus");
            return backAuthentication(email, employId, token);
        }
        return null;
    }
}
