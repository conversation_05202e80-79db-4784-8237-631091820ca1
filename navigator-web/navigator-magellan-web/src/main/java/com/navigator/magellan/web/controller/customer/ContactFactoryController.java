package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.ContactFactoryFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 18:29
 */
@RestController
@RequestMapping("/contactFactory")
public class ContactFactoryController {
    @Resource
    private ContactFactoryFacade contactFactoryFacade;


    /**
     * 根据客户联系人查询出联系人适用工厂
     *
     * @param contactId
     * @return
     */
    @GetMapping("/queryContactFactoryByContactId")
    public Result queryContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId) {
        return contactFactoryFacade.queryContactFactoryByContactId(contactId);
    }


    /**
     * 删除油厂和客户的关系
     *
     * @param contactId
     * @return
     */
    @GetMapping("/deleteContactFactoryByContactId")
    public Result deleteContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId) {
        return contactFactoryFacade.deleteContactFactoryByContactId(contactId);
    }

    /**
     * 根据客户和联系人信息查询出关系
     *
     * @param contactId
     * @param factoryId
     * @return
     */
    @GetMapping("getContactFactoryByContactId")
    public Result getContactFactoryByContactId(@RequestParam(value = "contactId") Integer contactId,
                                               @RequestParam(value = "factoryId") Integer factoryId) {
        return contactFactoryFacade.getContactFactoryByContactId(contactId, factoryId);
    }

}
