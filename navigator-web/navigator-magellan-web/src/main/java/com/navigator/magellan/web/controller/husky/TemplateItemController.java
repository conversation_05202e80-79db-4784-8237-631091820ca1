package com.navigator.magellan.web.controller.husky;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.TemplateUtil;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.TemplateItemFacade;
import com.navigator.husky.pojo.dto.TemplateItemJsonDTO;
import com.navigator.husky.pojo.entity.TemplateItemEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-18 19:02
 **/
@RestController
@Slf4j
@RequestMapping("/template/item")
public class TemplateItemController {
    @Autowired
    private TemplateItemFacade templateItemFacade;
    @Autowired
    private HttpServletResponse response;

    /**
     * 列表分页检索条款
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryItemByCondition")
    public Result queryItemByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO) {
        return templateItemFacade.queryItemByCondition(queryDTO);
    }

    /**
     * 新增条款
     *
     * @param templateEntity 条款信息
     * @return
     */
    @PostMapping("/saveTemplateItem")
    public Result<Boolean> saveTemplateItem(@RequestBody TemplateItemEntity templateEntity) {
        return Result.success(templateItemFacade.saveTemplateItem(templateEntity));
    }

    /**
     * 编辑更新条款
     *
     * @param templateEntity 条款信息
     * @return
     */
    @PostMapping("/updateTemplateItem")
    public Result updateTemplateItem(@RequestBody TemplateItemEntity templateEntity) {
        return templateItemFacade.updateTemplateItem(templateEntity);
    }

    /**
     * 根据ID查询条款信息
     *
     * @param itemId 条款ID
     * @return 条款信息
     */
    @GetMapping("/getTemplateItemById")
    public Result<TemplateItemEntity> getTemplateItemById(@RequestParam(value = "itemId") Integer itemId) {
        return Result.success(templateItemFacade.getTemplateItemById(itemId));
    }

    /**
     * 根据条款组编码，获取相关联的条款信息
     *
     * @param templateGroupCode 条款组编码
     * @return 相关联的条款信息
     */
    @GetMapping("/getItemListByGroupCode")
    public Result getItemListByGroupCode(@RequestParam(value = "templateGroupCode") String templateGroupCode) {
        return Result.success(templateItemFacade.getItemListByGroupCode(templateGroupCode));
    }


    /**
     * 查询所有条款
     *
     * @param status 状态
     * @return
     */
    @GetMapping("/getAllItemList")
    public Result getAllItemList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(templateItemFacade.getAllItemList(status));
    }

    @PostMapping("/exportItemExcel")
    public void exportItemExcel(@RequestBody QueryTemplateQO queryTemplateQO) {
        List<TemplateItemEntity> itemEntityList = new ArrayList<>();
        Result itemResult = templateItemFacade.exportItemExcel(queryTemplateQO);
        if (itemResult.isSuccess()) {
            itemEntityList = JSON.parseArray(JSON.toJSONString(itemResult.getData()), TemplateItemEntity.class);
        }
        if (CollectionUtils.isEmpty(itemEntityList)) {
            throw new BusinessException("无数据可导出！");
        }
        log.info(FastJsonUtils.getBeanToJson(itemEntityList));
        String fileName = "条款数据导出_" + "_" + DateTimeUtil.formatDateValue();
//        EasyPoiUtils.exportExcel(itemEntityList, "条款信息", "条款信息", TemplateItemEntity.class, fileName, response);
        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("条款信息", "条款信息"), TemplateItemEntity.class, itemEntityList);
        Sheet sheetAt = workbook.getSheetAt(0);
        //获取表格行数
        int lastRowNum = sheetAt.getLastRowNum();
        //获取列数
//        int physicalNumberOfCells = sheetAt.getRow(1).getPhysicalNumberOfCells();
        //没有表格标题,只有表格头
        for (int i = 2; i < lastRowNum + 1; i++) {
            //获取行
            Row row = sheetAt.getRow(i);
//            for (int j = 0; j < physicalNumberOfCells; j++) {
            //获取单元格对象
            Cell cell = row.getCell(11);
            //获取单元格样式对象
            CellStyle cellStyle = workbook.createCellStyle();
            //获取单元格内容对象
            Font font = workbook.createFont();
            //一定要装入 样式中才会生效
            cellStyle.setFont(font);
            //设置单元格背景颜色
//                cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
//                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            //设置居中对齐
            cellStyle.setAlignment(HorizontalAlignment.LEFT);
            //设置单元格字体颜色
            //    font.setColor(IndexedColors.RED.getIndex());
            cell.setCellStyle(cellStyle);
//                if (j == 12) {
            //获取当前值
            String itemContent = cell.getStringCellValue();
            log.info("条款Excel当前列的文本为：" + i + itemContent);
            //获取关键变量
            List<String> keyList = TemplateUtil.renderKeyVariableList(itemContent);
            log.info("关键字变量集合：" + FastJsonUtils.getBeanToJson(keyList));
            if (!CollectionUtils.isEmpty(keyList)) {
                // 创建一个富文本
                HSSFRichTextString xssfRichTextString = new HSSFRichTextString(itemContent.replaceAll("<span>", "").replaceAll("</span>", ""));
                // 创建一个字体样式
                Font font1 = workbook.createFont();
                font1.setColor(IndexedColors.RED.index);
                Font font2 = workbook.createFont();
                font2.setColor(IndexedColors.BLUE.index);
                for (int i1 = 0; i1 < keyList.size(); i1++) {
                    log.info("替换之前的条款文本：" + itemContent);
                    int diyi = itemContent.indexOf("<span>");
                    itemContent = itemContent.replaceFirst("<span>", "");
                    int dier = itemContent.indexOf("</span>");
                    itemContent = itemContent.replaceFirst("</span>", "");
                    log.info("替换之后的条款文本：" + itemContent);
                    xssfRichTextString.applyFont(diyi , diyi + 1, font1);
                    xssfRichTextString.applyFont(diyi + 2, dier - 2, font2);
                    xssfRichTextString.applyFont(dier - 1, dier , font1);

                }
                cell.setCellValue(xssfRichTextString);
            }
        }
        try {
            fileName += ".xls";
            response.setCharacterEncoding("UTF-8");
            response.setHeader("content-Type", "application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("FileName", URLEncoder.encode(fileName, "utf-8"));
            response.setHeader("Access-Control-Expose-Headers", "FileName");
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (null != workbook) {
                    workbook.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
//
//    @PostMapping("/exportItemExcel")
//    public void exportItemExcel(@RequestBody QueryTemplateQO queryTemplateQO) {
//        List<TemplateItemEntity> itemEntityList = new ArrayList<>();
//        Result itemResult = templateItemFacade.exportItemExcel(queryTemplateQO);
//        if (itemResult.isSuccess()) {
//            itemEntityList = JSON.parseArray(JSON.toJSONString(itemResult.getData()), TemplateItemEntity.class);
//        }
//        if (CollectionUtils.isEmpty(itemEntityList)) {
//            throw new BusinessException("无数据可导出！");
//        }
//        log.info(FastJsonUtils.getBeanToJson(itemEntityList));
//        String fileName = "条款数据导出_" + "_" + DateTimeUtil.formatDateValue();
//        EasyPoiUtils.exportExcel(itemEntityList, "条款信息", "条款信息", TemplateItemEntity.class, fileName, response);
////        Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams("条款信息", "条款信息"), TemplateItemEntity.class, itemEntityList);
////        Sheet sheetAt = workbook.getSheetAt(0);
////        //获取表格行数
////        int lastRowNum = sheetAt.getLastRowNum();
////        //获取列数
//////        int physicalNumberOfCells = sheetAt.getRow(1).getPhysicalNumberOfCells();
////        //没有表格标题,只有表格头
////        for (int i = 2; i < lastRowNum + 1; i++) {
////            //获取行
////            Row row = sheetAt.getRow(i);
////            //获取单元格对象
////            Cell cell = row.getCell(12);
////            //获取单元格样式对象
////            CellStyle cellStyle = workbook.createCellStyle();
////            //获取单元格内容对象
////            Font font = workbook.createFont();
////            //一定要装入 样式中才会生效
////            cellStyle.setFont(font);
////            //设置单元格背景颜色
//////                cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
//////                cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
////            //设置居中对齐
////            cellStyle.setAlignment(HorizontalAlignment.LEFT);
////            //设置单元格字体颜色
////            //    font.setColor(IndexedColors.RED.getIndex());
////            cell.setCellStyle(cellStyle);
////            //获取当前值
////            String itemContent = cell.getStringCellValue();
////            //获取关键变量
////            List<String> keyList = TemplateUtil.renderKeyVariableList(itemContent);
////            log.info("关键字变量集合：" + FastJsonUtils.getBeanToJson(keyList));
////            if (!CollectionUtils.isEmpty(keyList)) {
////                // 创建一个富文本
////                HSSFRichTextString xssfRichTextString = new HSSFRichTextString(itemContent);
////                // 创建一个字体样式
////                Font font1 = workbook.createFont();
////                font1.setColor(IndexedColors.BLUE.index);
////                for (int i1 = 0; i1 < keyList.size(); i1++) {
////                    log.info("替换之前的条款文本：" + itemContent);
////                    int diyi = itemContent.indexOf("<span>");
////                    itemContent = itemContent.replaceFirst("<span>", "");
////                    int dier = itemContent.indexOf("</span>");
////                    itemContent = itemContent.replaceFirst("</span>", "");
////                    log.info("替换之后的条款文本：" + itemContent);
////                    xssfRichTextString = new HSSFRichTextString(itemContent);
////                    xssfRichTextString.applyFont(diyi, dier, font1);
////                }
////                cell.setCellValue(xssfRichTextString);
////            }
////        }
////        try {
////            fileName += ".xls";
////            response.setCharacterEncoding("UTF-8");
////            response.setHeader("content-Type", "application/vnd.ms-excel");
////            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
////            response.setHeader("FileName", URLEncoder.encode(fileName, "utf-8"));
////            response.setHeader("Access-Control-Expose-Headers", "FileName");
////            workbook.write(response.getOutputStream());
////        } catch (IOException e) {
////            throw new RuntimeException(e);
////        } finally {
////            try {
////                if (null != workbook) {
////                    workbook.close();
////                }
////            } catch (IOException e) {
////                e.printStackTrace();
////            }
////        }
//    }

    /**
     * 导出条款组Json脚本
     *
     * @param templateQO
     * @return
     */
    @PostMapping("/exportItemJson")
    public Result exportItemJson(@RequestBody QueryTemplateQO templateQO, HttpServletResponse response) {
        Result itemResult = templateItemFacade.exportItemJson(templateQO);
        if (!itemResult.isSuccess()) {
            return itemResult;
        }
        if (null == itemResult.getData()) {
            return Result.failure("无可导出的条款！");
        }
        List<TemplateItemJsonDTO> itemJsonDTOList = JSON.parseArray(JSON.toJSONString(itemResult.getData()), TemplateItemJsonDTO.class);
        String itemName = itemJsonDTOList.size() > 1 ? "TemplateItem" : itemJsonDTOList.get(0).getCode();
        JsonFileUtil.exportJson(response, itemJsonDTOList, itemName + "_" + DateTimeUtil.formatDateValue() + ".json");
        return itemResult;
    }

    /**
     * 导入条款Json脚本，并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importItemJson")
    public Result importItemJson(@RequestParam("file") MultipartFile file) {
        return templateItemFacade.importItemJson(file);
    }

}
