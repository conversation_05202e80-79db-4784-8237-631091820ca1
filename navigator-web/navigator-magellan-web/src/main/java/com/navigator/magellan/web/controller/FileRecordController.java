package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.FileRecordFacade;
import com.navigator.admin.pojo.dto.FileRecordDTO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/fileRecord")
public class FileRecordController {
    @Autowired
    private FileRecordFacade fileRecordFacade;

    @PostMapping("/save")
    Result save(@RequestBody FileRecordDTO FileRecordDTO) {
        return fileRecordFacade.save(FileRecordDTO);
    }

    @PostMapping("/modify")
    Result modify(@RequestBody FileRecordDTO FileRecordDTO) {
        return fileRecordFacade.modify(FileRecordDTO);
    }

    @PostMapping("/queryFileRecordDetail")
    Result queryFileRecordDetail(@RequestBody FileRecordDTO FileRecordDTO) {
        return fileRecordFacade.queryFileRecordDetail(FileRecordDTO);
    }

    @PostMapping("/queryFileRecordList")
    Result queryFileRecordList(@RequestBody QueryDTO<FileRecordDTO> queryDTO) {
        return fileRecordFacade.queryFileRecordList(queryDTO);
    }

    @GetMapping("/queryMagellanFileRecord")
    Result queryMagellanFileRecord() {
        return fileRecordFacade.queryFileRecordBySystemId(SystemEnum.MAGELLAN.getValue());
    }

}