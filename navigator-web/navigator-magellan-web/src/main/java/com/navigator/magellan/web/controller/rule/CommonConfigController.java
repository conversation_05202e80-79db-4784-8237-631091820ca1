package com.navigator.magellan.web.controller.rule;

import com.navigator.admin.facade.rule.CommonConfigFacade;
import com.navigator.admin.pojo.dto.rule.CommonConfigDTO;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-09-19 17:06
 **/
@RestController
@Slf4j
@RequestMapping("/common/config")
public class CommonConfigController {
    @Autowired
    private CommonConfigFacade commonConfigFacade;

    /**
     * 保存系统配置
     *
     * @param commonConfigDTO
     * @return
     */
    @PostMapping("/saveOrUpdateCommonConfig")
    public Result saveOrUpdateCommonConfig(@RequestBody @Valid CommonConfigDTO commonConfigDTO) {
        return commonConfigFacade.saveOrUpdateCommonConfig(commonConfigDTO);
    }

    /**
     * 获取配置详情
     *
     * @param id
     * @return
     */
    @GetMapping("/getCommonConfigDetailById")
    public Result<CommonConfigDTO> getCommonConfigDetailById(@RequestParam(value = "id") Integer id) {
        return Result.success(commonConfigFacade.getCommonConfigDetailById(id));
    }

    /**
     * 查询正本配置列表
     *
     * @return
     */
    @GetMapping("/getSignPaperRuleConfig")
    public Result<List<CommonConfigDTO>> getSignPaperRuleConfig() {
        return Result.success(commonConfigFacade.getSignPaperRuleConfig());
    }
}
