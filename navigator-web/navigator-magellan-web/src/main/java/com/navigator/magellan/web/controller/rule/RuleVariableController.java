package com.navigator.magellan.web.controller.rule;

import com.navigator.admin.facade.rule.RuleVariableFacade;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 16:16
 **/
@RestController
@RequestMapping("/rule")
public class RuleVariableController {
    @Autowired
    private RuleVariableFacade ruleVariableFacade;

    @GetMapping("/getAllVariableList")
    public Result getAllVariableList(@RequestParam(value = "isCondition", required = false) Integer isCondition,
                                     @RequestParam(value = "isKey", required = false) Integer isKey,
                                     @RequestParam(value = "moduleType", required = false) String moduleType,
                                     @RequestParam(value = "systemId", required = false) String systemId) {
        return Result.success(ruleVariableFacade.getAllVariableList(isCondition, isKey, moduleType, systemId));
    }
}
