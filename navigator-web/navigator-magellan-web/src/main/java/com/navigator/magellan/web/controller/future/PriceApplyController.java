package com.navigator.magellan.web.controller.future;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.bo.PriceApplyBO;
import com.navigator.future.pojo.dto.NotDealDTO;
import com.navigator.future.pojo.dto.PriceApplyDTO;
import com.navigator.future.pojo.vo.PriceApplyExportVO;
import com.navigator.future.pojo.vo.PriceApplyVO;
import com.navigator.future.pojo.vo.StructurePriceDealVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/1/5 18:27
 */
@RestController
@RequestMapping("/future")
public class PriceApplyController {

    @Resource
    private PriceApplyFacade priceApplyFacade;
    @Resource
    private OperationLogFacade operationLogFacade;
    @Resource
    private CustomerDetailFacade customerDetailFacade;
    @Autowired
    private HttpServletResponse response;

    /**
     * 申请点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/priceApply")
    public Result priceApply(@RequestBody PriceApplyDTO priceApplyDTO) {
        /*if (priceApplyDTO.getType() == PriceTypeEnum.REVERSE_PRICING.getValue()) {
            CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailList(priceApplyDTO.getCustomerId(), priceApplyDTO.getCategoryId());
            //判断客户是否油反点价服务
            if (null != customerDetailEntity && customerDetailEntity.getIsReversePrice().equals(GeneralEnum.NO.getValue())) {
                Result result = new Result();
                result.setCode(1);
                result.setData("未开通反点价服务");
                result.setMessage("未开通反点价服务");
                return result;
            }
        }*/
        //麦哲伦系统发起申请单
        priceApplyDTO.setSystem(SystemEnum.MAGELLAN.getValue());
        return priceApplyFacade.priceApply(priceApplyDTO);
    }

    /**
     * 申请改单/撤单  点价/转月/反点价
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/modifyPriceApply")
    public Result modifyPriceApply(@RequestBody PriceApplyDTO priceApplyDTO) {
        priceApplyDTO.setSystem(SystemEnum.MAGELLAN.getValue());
        return priceApplyFacade.modifyPriceApply(priceApplyDTO);
    }

    /**
     * 分页查询申请单信息
     *
     * @param applyBOQueryDTO
     * @return
     */
    @PostMapping("/queryPriceApply")
    public Result queryPriceApply(@RequestBody QueryDTO<PriceApplyBO> applyBOQueryDTO) {
        return priceApplyFacade.queryPriceApply(applyBOQueryDTO, SystemEnum.MAGELLAN);
    }

    /**
     * 查询申请单申请明细
     *
     * @param id
     * @return
     */
    @GetMapping("/getpriceApplyDetail")
    public Result getpriceApplyDetail(@RequestParam("id") Integer id) {
        return priceApplyFacade.getpriceApplyDetail(id);
    }

    /**
     * 查询申请单的改撤单记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceApplyLog")
    public Result queryPriceApplyLog(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceApplyLog(priceApplyId);
    }

    /**
     * magellan 查询点转反状态数量
     *
     * @param customerId
     * @return
     */
    @GetMapping("/magellanApplyAllocateStatus")
    public Result magellanApplyAllocateStatus(@RequestParam(value = "customerId") Integer customerId,
                                              @RequestParam(value = "goodsCategoryId ", required = false) Integer goodsCategoryId,
                                              @RequestParam(value = "salesType", required = false) Integer salesType) {

        return priceApplyFacade.magellanApplyAllocateStatus(customerId, goodsCategoryId, salesType);
    }

    /**
     * 下载申请单信息
     *
     * @param response
     * @return
     */
    @GetMapping("/exportPriceApply")
    public void exportPriceApply(HttpServletResponse response) {
        List<PriceApplyVO> priceApplyVOS = priceApplyFacade.exportPriceApply();
        if (CollectionUtils.isEmpty(priceApplyVOS)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "申请挂单信息";
        EasyPoiUtils.exportExcel(priceApplyVOS, "申请挂单信息", "申请挂单信息", PriceApplyVO.class, fileName, response);
    }

    @GetMapping("/exportStructurePriceApply")
    public void exportStructurePriceApply(HttpServletResponse response) {
        List<StructurePriceDealVO> priceDealVOS = priceApplyFacade.exportStructurePriceApply();
        if (CollectionUtils.isEmpty(priceDealVOS)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "结构化定价申请成交信息";
        EasyPoiUtils.exportExcel(priceDealVOS, "申请成交信息", "申请成交信息", StructurePriceDealVO.class, fileName, response);

    }

    /**
     * 校验申请单上传数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/checkPriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result checkPriceApply(@RequestPart("file") MultipartFile file) {
        return priceApplyFacade.checkPriceApply(file);
    }

    /**
     * 校验结构化定价申请单上传数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/checkStructurePriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result checkStructurePriceApply(@RequestPart("file") MultipartFile file) {
        return priceApplyFacade.checkStructurePriceApply(file);
    }


    /**
     * 上传成交数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importPriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result importPriceApply(@RequestPart("file") MultipartFile file) {
        return priceApplyFacade.importPriceApply(file);
    }

    @PostMapping(value = "/importStructurePriceApply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result importStructurePriceApply(@RequestPart("file") MultipartFile file) {
        return priceApplyFacade.importStructurePriceApply(file);
    }

    @GetMapping(value = "/getDealListByStructureContractId")
    public Result getDealListByStructureContractId(@RequestParam(value = "contractId") Integer contractId) {
        return priceApplyFacade.getDealListByStructureContractId(contractId);
    }

    /**
     * 可反点价量
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/mayReversePricing")
    public Result mayReversePricing(@RequestBody PriceApplyDTO priceApplyDTO) {
        return priceApplyFacade.mayReversePricing(priceApplyDTO);
    }


    /**
     * 点价员批量挂单
     *
     * @param ids
     * @return
     */
    @GetMapping("/batchPending")
    public Result batchPending(@RequestParam(value = "ids") List<Integer> ids) {
        return priceApplyFacade.batchPending(ids);
    }

    /**
     * 确认成交
     *
     * @param priceApplyDTO
     * @return
     */
    @PostMapping("/priceApplyDeal")
    public Result priceApplyDeal(@RequestBody PriceApplyDTO priceApplyDTO) {
        return priceApplyFacade.priceApplyDeal(priceApplyDTO);
    }

    /**
     * 批量不成交
     *
     * @param notDealDTOS
     * @return
     */
    @PostMapping("/batchNotDeal")
    public Result batchNotDeal(@RequestBody List<NotDealDTO> notDealDTOS) {
        return priceApplyFacade.batchNotDeal(notDealDTOS);
    }

    /**
     * 确认成交
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/structurePriceApplyDeal")
    public Result structurePriceApplyDeal(@RequestParam("priceApplyId") Integer priceApplyId, @RequestParam("tradeDay") Date tradeDay) {
        return priceApplyFacade.structurePriceApplyDeal(priceApplyId, tradeDay);
    }

    /**
     * 申请单操作记录
     *
     * @param code
     * @return
     */
    @GetMapping("/queryOperationLog")
    public Result queryOperationLog(@RequestParam("code") String code) {
        return operationLogFacade.queryOperationDetailByReferBizCode(code, OperationSourceEnum.EMPLOYEE.getValue());
    }


    /**
     * 改/撤审核
     *
     * @param priceApplyId
     * @param status
     * @return
     */
    /*@GetMapping("/priceApplyUpdateAudit")
    public Result priceApplyUpdateAudit(@RequestParam(value = "priceApplyId") Integer priceApplyId,
                                        @RequestParam(value = "status") Integer status,
                                        @RequestParam(value = "memo") String memo) {
        return priceApplyFacade.priceApplyUpdateAudit(priceApplyId, status, memo);
    }*/

    /**
     * 获取成交详细记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceDealDetail")
    public Result queryPriceDealDetail(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceDealDetail(priceApplyId);
    }

    /**
     * 获取成交信息
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceDealInfo")
    public Result queryPriceDealInfo(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceDealInfo(priceApplyId);
    }

    /**
     * 导出头寸报表
     *
     * @param priceApplyBO
     */
    @PostMapping("/getPriceApplyExport")
    public void getPriceApplyExport(@RequestBody PriceApplyBO priceApplyBO) {
        List<PriceApplyExportVO> priceApplyExportVOS = new ArrayList<>();

        Result result = priceApplyFacade.getPriceApplyExport(priceApplyBO);
        if (result.isSuccess()) {
            priceApplyExportVOS = JSON.parseArray(JSON.toJSONString(result.getData()), PriceApplyExportVO.class);
        }

        if (CollectionUtil.isEmpty(priceApplyExportVOS)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "头寸处理" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        EasyPoiUtils.exportExcel(priceApplyExportVOS, null, "头寸处理", PriceApplyExportVO.class, fileName, response);
        //return Result.success();

    }

    /**
     * 查询头寸报表
     *
     * @param priceApplyBO
     */
    @PostMapping("/getPriceApplyTheReportList")
    public Result getPriceApplyTheReportList(@RequestBody PriceApplyBO priceApplyBO) {
        return Result.success(priceApplyFacade.getPriceApplyTheReportList(priceApplyBO));
    }

    /**
     * 查询申请单刷新记录
     *
     * @param priceApplyId
     * @return
     */
    @GetMapping("/queryPriceApplyRefreshLog")
    public Result queryPriceApplyRefreshLog(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceApplyRefreshLog(priceApplyId);
    }

    /**
     * 根据id撤回成交单
     *
     * @param priceApplyId
     * @return
     */




}
