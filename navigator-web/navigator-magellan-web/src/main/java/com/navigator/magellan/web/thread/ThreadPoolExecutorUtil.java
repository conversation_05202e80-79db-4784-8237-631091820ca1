package com.navigator.magellan.web.thread;

import java.util.concurrent.*;

/**
* @description: 线程池
*
* @author: Mr.Lq
* @date: 2021/1/29 17:21
*/
public class ThreadPoolExecutorUtil {

    // 可优化点，如何设置合理的线程数
    private  static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20,
            60L, TimeUnit.SECONDS,
            new SynchronousQueue<Runnable>());


    public static Integer  multithreading(Callable<Integer> callable) throws ExecutionException, InterruptedException {
        if(null!=callable){
            Future<Integer> future = threadPoolExecutor.submit(callable);
            threadPoolExecutor.shutdown();
//             获取线程结果
            return future.get();
        }
         return null;

    }

}
