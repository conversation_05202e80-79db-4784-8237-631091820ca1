// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.microsoft.graph.models.User;
import com.microsoft.graph.options.HeaderOption;
import com.microsoft.graph.requests.GraphServiceClient;
import com.microsoft.graph.requests.GroupCollectionPage;
import com.microsoft.graph.requests.UserCollectionPage;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.LoginVO;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.VerificationCodeUtil;
import com.navigator.common.util.http.OkHttpUtil;
import com.navigator.magellan.web.property.AadProperty;
import com.navigator.magellan.web.service.LoginLogRecordService;
import com.navigator.security.authentication.TokenManager;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

@RestController
@RequestMapping("/aad")
public class AadController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AadController.class);

    private static final String GRAPH_ME_ENDPOINT_URL = "https://graph.microsoft.com/v1.0/me";

    private static final String GRAPH_ACCESS_TOKEN_URL = "https://login.microsoftonline.com/a8efa93f-024d-4be7-9138-08b471c987d4/oauth2/v2.0/token";

    @Autowired
    private OkHttpUtil okHttpUtil;
    @Autowired
    private AadProperty aadProperty;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private TokenManager tokenManager;
    @Autowired
    private VerificationCodeUtil verificationCodeUtil;
    @Autowired
    private LoginLogRecordService loginLogRecordService;
//    @Value("${nn.test}")
//    private String nnTest;
    /**
     * 组装请求地址，即第三方登录地址（拼参数）
     *
     * @return 第三方登录地址Url
     */
    @GetMapping("/getAuthorizeLoginUrl")
    public Result getAuthorizeLoginUrl(@RequestParam(value = "redirectUrl") String redirectUrl) {
        //初始化参数
        String state = UUID.randomUUID().toString();
        String loginUrl = "https://login.microsoftonline.com/" + aadProperty.getTenantId() +
                "/oauth2/v2.0/authorize?" +
                "client_id=" + aadProperty.getClientId() +
                "&response_type=token+id_token" +
                "&redirect_uri=" + redirectUrl +
                "&response_mode=fragment" +
//                "&scope=user.read+openid+profile+email+Directory.ReadWrite.All" +
                "&scope=user.read+openid+profile+email+Directory.Read.All" +
                "&state=" + state +
                "&nonce=00021455555";
        LOGGER.info("authorizeLogin url = " + loginUrl);
        LOGGER.info( "---------clientId:" + aadProperty.getClientId()
//                + "---------nnTest:" + nnTest

        );
        return Result.success(loginUrl);
    }

    @PostMapping("/login")
    @ResponseBody
    public Result login(HttpServletRequest httpServletRequest, HttpServletResponse res) {
        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
        String userPrincipalName = "";
        try {
            //解析前端返回的AccessToken令牌，用于请求Graph API
            String accessTokenMe = "";
            Optional<HttpServletRequest> httpServletRequest1 = Optional.of(httpServletRequest);
            Optional<String> accessToken = httpServletRequest1.map((r) -> {
                return r.getHeader("accessToken");
            });
            if (accessToken.isPresent()) {
                accessTokenMe = accessToken.get();
            }
            LOGGER.info("accessToken:-----------" + accessTokenMe);
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            LOGGER.info("Authentication：--------------" + FastJsonUtils.getBeanToJson(authentication));
            LOGGER.info("userPrincipal：--------------" + FastJsonUtils.getBeanToJson(authentication.getPrincipal()));
            userPrincipalName = JSON.parseObject(FastJsonUtils.getBeanToJson(authentication.getPrincipal())).getString("userPrincipalName");
            //        String nonce = JSON.parseObject(FastJsonUtils.getBeanToJson(authentication.getPrincipal())).getString("nonce");
            LOGGER.info("userPrincipalName，登录人邮箱：--------------" + userPrincipalName);
            User user = getLoginUserInfo(accessTokenMe);
            LOGGER.info(FastJsonUtils.getBeanToJson(user));
            //        User user = getLoginUserInfo(accessTokenMe);
            EmployEntity employEntity = new EmployEntity()
                    .setEmail(userPrincipalName);
            //        employEntity.setPassword(passwordEncoder.encode("111111"))
            //                .setDepartmentId(1)
            //                .setCompanyId(0)
            //                .setEmail(user.userPrincipalName)
            //                .setRealName(user.displayName)
            //                .setPhone(CollectionUtil.isEmpty(user.businessPhones) ? null : user.businessPhones.get(0));
            // 用户存在返回 不存在新增后返回
            EmployEntity employ = employFacade.ifNotExistToSave(employEntity);
            if (null == employ) {
                // 记录AAD登录失败日志
                loginLogRecordService.recordAadFailureLog(userPrincipalName, "用户不存在");
                return Result.failure(ResultCodeEnum.USER_NOT_EXIST);
            }
            String token = tokenManager.createTokenByEmail(String.valueOf(employ.getId()), employ.getEmail(), true);
            String refreshToken = tokenManager.getRefreshTokenByEmail(String.valueOf(employ.getId()), employ.getEmail());
            employ.setToken(token)
                    .setRealName(employ.getRealName()).setAadLogin(true);
            LoginVO loginVO = new LoginVO();
            loginVO
                    .setToken(token)
                    .setRefreshToken(refreshToken)
                    .setId(employ.getId())
                    .setName(employ.getName())
                    .setAadLogin(true)
            ;
            MenuVO menuVO = tokenManager.getMenuVO(employ.getId());
            List<String> powerCodeList = tokenManager.getPowerCodeListByEmployId(employ.getId());
            loginVO.setMenuVO(menuVO);
            loginVO.setPowerCodeList(powerCodeList);

            // 记录AAD登录成功日志
            loginLogRecordService.recordAadSuccessLog(userPrincipalName, loginVO);

            return Result.success(loginVO);
        } catch (Exception e) {
            LOGGER.error("AAD登录失败：{}", e.getMessage(), e);
            // 记录AAD登录失败日志
            loginLogRecordService.recordAadFailureLog(userPrincipalName, e.getMessage());
            return Result.failure("AAD登录失败：" + e.getMessage());
        }
        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End
    }

    private User getLoginUserInfo(String accessToken) {
        //自定义Graph SDK服务客户端
        GraphServiceClient graphServiceClient = GraphServiceClient
                .builder()
                .httpClient(new OkHttpClient())
                .buildClient();
        //根据AccessToken，查询当前登录用户信息，调用接口：https://graph.microsoft.com/v1.0/me
        User user = graphServiceClient.me()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("当前登录人员UserInfo:-------------" + FastJsonUtils.getBeanToJson(user));
        return user;
    }

    @GetMapping("/public")
    @ResponseBody
    public Result publicMethod(HttpServletRequest httpServletRequest) throws Exception {
//        return "public endpoint response";
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        LOGGER.info("Authentication：--------------" + FastJsonUtils.getBeanToJson(authentication));
        LOGGER.info("userPrincipalName：--------------" + FastJsonUtils.getBeanToJson(authentication.getPrincipal()));
        String userPrincipalName = JSON.parseObject(FastJsonUtils.getBeanToJson(authentication.getPrincipal())).getString("userPrincipalName");
        LOGGER.info("userPrincipalName，登录人邮箱：--------------" + userPrincipalName);
        //解析前端返回的AccessToken令牌，用于请求Graph API
        String accessTokenMe = "";
        Optional<HttpServletRequest> httpServletRequest1 = Optional.of(httpServletRequest);
        Optional<String> accessToken1 = httpServletRequest1.map((r) -> {
            return r.getHeader("accessToken");
        });
        if (accessToken1.isPresent()) {
            accessTokenMe = accessToken1.get();
        }
        //自定义Graph SDK服务客户端
        final GraphServiceClient graphServiceClient = GraphServiceClient
                .builder()
                .httpClient(new OkHttpClient())
                .buildClient();
        //根据AccessToken，查询当前登录用户信息
        User user = graphServiceClient.me()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessTokenMe))
                .get();
        LOGGER.info("当前登录人员UserInfo:-------------" + FastJsonUtils.getBeanToJson(user));

        Map<String, Object> params = ImmutableMap.<String, Object>builder()
                .put("grant_type", "client_credentials")
                .put("client_id", "79da1241-0a4a-4a82-81b7-8d311079b243")
                .put("client_secret", "*************************************")
                .put("scope", "https://graph.microsoft.com/.default")
                .build();
        String tokenResponse = okHttpUtil.postFormUrlencoded(aadProperty.getGraphAccessTokenUrl(), params);
        String accessToken = JSON.parseObject(tokenResponse).getString("access_token");
        LOGGER.info("accessToken：--------------" + accessToken);
        //查询组织架构人员信息
        UserCollectionPage userCollectionPage = graphServiceClient.users()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("所有成员UserList:-------------" + FastJsonUtils.getBeanToJson(userCollectionPage));
        //查询组织架构中，某个人员信息（通过ID/userPrincipalName邮箱）
        User userInfo = graphServiceClient.users("<EMAIL>")
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("某成员检索userInfo:-------------" + FastJsonUtils.getBeanToJson(userInfo));
        GroupCollectionPage groupCollectionPage = graphServiceClient.groups()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("组织架构organizationList:-------------" + FastJsonUtils.getBeanToJson(groupCollectionPage));
        return Result.success(FastJsonUtils.getBeanToJson(user));
    }

    @GetMapping("/getUserInfo")
    @ResponseBody
    public Result getUserInfo(HttpServletRequest httpServletRequest) throws Exception {
        LOGGER.info("getUserInfo开始:-------------");
        Map<String, Object> params = ImmutableMap.<String, Object>builder()
                .put("grant_type", "client_credentials")
                .put("client_id", "79da1241-0a4a-4a82-81b7-8d311079b243")
                .put("client_secret", "*************************************")
                .put("scope", "https://graph.microsoft.com/.default")
                .build();
        String tokenResponse = okHttpUtil.postFormUrlencoded(aadProperty.getGraphAccessTokenUrl(), params);
        String accessToken = JSON.parseObject(tokenResponse).getString("access_token");
//        String accessToken = "***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
        LOGGER.info("accessToken：--------------" + accessToken);
        //自定义Graph SDK服务客户端
        final GraphServiceClient graphServiceClient = GraphServiceClient
                .builder()
                .httpClient(new OkHttpClient())
                .buildClient();
        //查询组织架构人员信息
        UserCollectionPage userCollectionPage = graphServiceClient.users()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("所有成员UserList:-------------" + FastJsonUtils.getBeanToJson(userCollectionPage));
        //查询组织架构中，某个人员信息（通过ID/userPrincipalName邮箱）
        User userInfo = graphServiceClient.users("<EMAIL>")
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("某成员检索userInfo:-------------" + FastJsonUtils.getBeanToJson(userInfo));
        GroupCollectionPage groupCollectionPage = graphServiceClient.groups()
                .buildRequest(new HeaderOption("authorization", "bearer " + accessToken))
                .get();
        LOGGER.info("组织架构organizationList:-------------" + FastJsonUtils.getBeanToJson(groupCollectionPage));
        return Result.success(FastJsonUtils.getBeanToJson(FastJsonUtils.getBeanToJson(groupCollectionPage)));
    }

    @PostMapping("/sendAadCode/{mobileNo}")
    public Result sendAadCode(@PathVariable("mobileNo") String mobileNo) {
        Result result = employFacade.sendAadCode(mobileNo);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        verificationCodeUtil.sendVerificationCode(mobileNo, RedisConstants.M_AAD_LOGIN_PASSWORD_CODE_SEND, RedisConstants.M_AAD_LOGIN_PASSWORD_CODE);
        return Result.success();

    }

    @PostMapping("/verifyAadCode")
    public Result verifyAadCode(@RequestBody LoginDTO loginDTO) {
        Result result = employFacade.verifyAadCode(loginDTO);
        if (result == null || result.getCode() != 0) {
            return result;
        }
        return Result.success();
    }


}
