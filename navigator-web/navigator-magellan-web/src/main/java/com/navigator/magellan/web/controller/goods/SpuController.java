package com.navigator.magellan.web.controller.goods;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.SpuFacade;
import com.navigator.goods.pojo.dto.SpuDTO;
import com.navigator.goods.pojo.dto.SpuRefreshDTO;
import com.navigator.goods.pojo.entity.SpuEntity;
import com.navigator.goods.pojo.qo.SpuQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * SPU商品 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-19
 */
@RestController
@RequestMapping("/spu")
public class SpuController {

    @Resource
    private SpuFacade spuFacade;

    /**
     * 根据条件：获取SPU商品DTO分页
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySpuDTOPage")
    Result<SpuDTO> querySpuDTOPage(@RequestBody QueryDTO<SpuQO> queryDTO) {
        return Result.page(spuFacade.querySpuDTOPage(queryDTO));
    }

    /**
     * 根据条件：获取SPU商品DTO列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/querySpuDTOList")
    Result<List<SpuDTO>> querySpuDTOList(@RequestBody SpuQO condition) {
        return Result.success(spuFacade.querySpuDTOList(condition));
    }

    /**
     * 根据ID：获取SPU商品
     *
     * @param id
     * @return
     */
    @GetMapping("/getSpuById")
    Result<SpuEntity> getSpuById(@RequestParam(value = "id") Integer id) {
        return Result.success(spuFacade.getSpuById(id));
    }

    /**
     * 根据编码：获取SPU商品
     *
     * @param spuNo
     * @return
     */
    @GetMapping("/getSpuBySpuNo")
    Result<SpuEntity> getSpuByNo(@RequestParam(value = "spuNo") String spuNo) {
        return Result.success(spuFacade.getSpuByNo(spuNo));
    }

    /**
     * 刷新：SPU商品
     *
     * @param refreshSpuDTO
     * @return
     */
    @PostMapping("/refreshSpu")
    Result<List<SpuDTO>> refreshSpu(@RequestBody SpuRefreshDTO refreshSpuDTO) {
        return spuFacade.refreshSpu(refreshSpuDTO);
    }

    /**
     * 更新：SPU商品
     *
     * @param spuDTOList
     * @return
     */
    @PostMapping("/saveSpu")
    Result saveSpu(@RequestBody List<SpuDTO> spuDTOList) {
        return spuFacade.saveSpu(spuDTOList);
    }
}
