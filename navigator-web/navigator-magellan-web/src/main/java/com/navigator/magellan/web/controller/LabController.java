package com.navigator.magellan.web.controller;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.pigeon.pojo.dto.SyncRequestDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 实验室
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/23
 */
@RestController
@RequestMapping("/lab")
public class LabController {
    @Resource
    private LkgContractFacade lkgContractFacade;

    /**
     * 获取Lkg合同的合同详情
     *
     * @return
     */
    @GetMapping("/getLkgContractInfo")
    public Result getLkgContractInfo(@RequestParam("contractCode") String contractCode) {
        return lkgContractFacade.getLkgContract(contractCode);
    }

    /**
     * 根据场景重试请求
     *
     * @param syncRequestDTO【 "contractCode": 合同编号
     *                        "syncType": 同步类型
     *                        "tradeType": 交易类型
     *                        "ttId": 业务id
     *                        "isChangeFactory": 是否变更工厂】
     * @return
     */
    @PostMapping("/reSyncContractRequest")
    public Result reSyncContractRequest(@RequestBody SyncRequestDTO syncRequestDTO) {
        return lkgContractFacade.reSyncContract(syncRequestDTO);
    }
}
