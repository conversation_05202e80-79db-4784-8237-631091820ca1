package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDetailUpdateRecordFacade;
import com.navigator.customer.pojo.dto.CustomerDetailUpdateRecordDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/14
 */
@RestController
@RequestMapping("/CustomerDetailUpdateRecordController")
public class CustomerDetailUpdateRecordController {

    @Resource
    private CustomerDetailUpdateRecordFacade customerDetailUpdateRecordFacade;

    /**
     * 查询客户配置修改信息
     *
     * @param customerDetailUpdateRecordDTO
     * @return
     */
    @PostMapping("/detailUpdateSelect")
    public Result detailUpdateSelect(@RequestBody CustomerDetailUpdateRecordDTO customerDetailUpdateRecordDTO) {
        return customerDetailUpdateRecordFacade.detailUpdateSelect(customerDetailUpdateRecordDTO);
    }


}
