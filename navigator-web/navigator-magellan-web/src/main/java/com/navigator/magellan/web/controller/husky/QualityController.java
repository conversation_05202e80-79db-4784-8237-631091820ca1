package com.navigator.magellan.web.controller.husky;

import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.QualityFacade;
import com.navigator.husky.pojo.dto.QualityInfoDTO;
import com.navigator.husky.pojo.entity.QualityEntity;
import com.navigator.husky.pojo.vo.QualityExportVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-09-05 14:22
 **/
@RestController
@Slf4j
@RequestMapping("/template/quality/")
public class QualityController {
    @Autowired
    private QualityFacade qualityFacade;
//    @Autowired
//    private HttpServletResponse response;

    /**
     * 列表查询模板信息
     *
     * @param queryDTO 模板查询条件
     * @return 查询模板分页结果
     */
    @PostMapping("/queryByCondition")
    public Result queryByCondition(@RequestBody QueryDTO<QualityInfoDTO> queryDTO) {
        return qualityFacade.queryQualityByCondition(queryDTO);
    }

    /**
     * 新增模板信息
     *
     * @param qualityEntity 模板信息
     * @return
     */
    @PostMapping("/saveQuality")
    public Result<Boolean> saveQuality(@RequestBody @Valid QualityEntity qualityEntity) {
        return qualityFacade.saveQuality(qualityEntity);
    }

    @PostMapping("/updateQuality")
    public Result<Boolean> updateQuality(@RequestBody @Valid QualityEntity qualityEntity) {
        return qualityFacade.updateQuality(qualityEntity);
    }

    @GetMapping("/updateQualityStatus")
    public Result<Boolean> updateQualityStatus(@RequestParam(value = "qualityId") Integer qualityId,
                                               @RequestParam(value = "status") Integer status) {
        return Result.success(qualityFacade.updateQualityStatus(qualityId, status));
    }

    @GetMapping("/getQualityById")
    public Result getQualityById(@RequestParam(value = "qualityId") Integer qualityId) {
        return Result.success(qualityFacade.getQualityById(qualityId));
    }

    @PostMapping("/exportQualityList")
    public void exportQualityList(@RequestBody QualityInfoDTO qualityInfoDTO, HttpServletResponse response) {
        List<QualityExportVO> qualityExportVOList = qualityFacade.exportQualityList(qualityInfoDTO);
        if(CollectionUtils.isEmpty(qualityExportVOList)){
            throw new BusinessException("无可导出数据！");
        }
        log.info(FastJsonUtils.getBeanToJson(qualityExportVOList));
        String fileName = "质量指标导出_" + GoodsCategoryEnum.getDesc(qualityInfoDTO.getGoodsCategoryId()) + "_" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(qualityExportVOList, "质量指标列表信息", "质量指标列表导出信息", QualityExportVO.class, fileName, response);
    }

    /**
     * 处理历史数据
     *
     * @param id
     * @return
     */
    @PostMapping("/processHistoryData")
    Result processHistoryData(@RequestParam(value = "id", required = false) Integer id) {
        return qualityFacade.processHistoryData(id);
    }
}
