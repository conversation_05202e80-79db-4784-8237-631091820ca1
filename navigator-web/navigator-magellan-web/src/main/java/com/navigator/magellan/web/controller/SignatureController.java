package com.navigator.magellan.web.controller;


import cn.signit.sdk.pojo.webhook.response.WebhookResponse;
import com.navigator.common.dto.Result;
import com.navigator.sparrow.facade.DbtSignatureFacade;
import com.navigator.sparrow.facade.YqqSignParameterFacade;
import com.navigator.sparrow.pojo.entity.YqqSignParameterEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/signature")
public class SignatureController {

    @Resource
    private DbtSignatureFacade dbtSignatureFacade;
    @Resource
    private YqqSignParameterFacade yqqSignParameterFacade;

    /**
     * 回调接口
     *
     * @param webhookResponse
     */
    @PostMapping("/callbackFunction")
    public void callbackFunction(@RequestBody WebhookResponse webhookResponse){

        dbtSignatureFacade.callbackFunction(webhookResponse);
    }

    /**
     * 新增易企签配置
     *
     * @param yqqSignParameterEntity
     * @return
     */
    @PostMapping("/saveYqqSignParameter")
    public Result saveYqqSignParameter(@RequestBody YqqSignParameterEntity yqqSignParameterEntity){
        return yqqSignParameterFacade.saveYqqSignParameter(yqqSignParameterEntity);
    }

    /**
     * 修改易企签配置
     *
     * @param yqqSignParameterEntity
     * @return
     */
    @PostMapping("/updateYqqSignParameter")
    public Result updateYqqSignParameter(@RequestBody YqqSignParameterEntity yqqSignParameterEntity){
        return yqqSignParameterFacade.updateYqqSignParameter(yqqSignParameterEntity);
    }

    /**
     * 查询易企签配置列表
     *
     * @return
     */
    @PostMapping("/queryYqqSignParameterList")
    public Result queryYqqSignParameterList(){
        return yqqSignParameterFacade.queryYqqSignParameterList();
    }
}
