//package com.navigator.magellan.web.controller;
//
//import com.navigator.admin.facade.ApprovalFacade;
//import com.navigator.admin.pojo.dto.QueryApprovalDTO;
//import com.navigator.common.dto.QueryDTO;
//import com.navigator.common.dto.Result;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//@RestController
//@RequestMapping("/approval")
//public class ApprovalController {
//
//    @Autowired
//    public ApprovalFacade approvalFacade;
//
//    @PostMapping("/queryApprovalList")
//    public Result queryApprovalList(@RequestBody QueryDTO<QueryApprovalDTO> queryDTO) {
//        return approvalFacade.queryApprovalList(queryDTO);
//    }
//
//    @GetMapping("/getApproveDetail")
//    public Result getApprovalDetailById(@RequestParam("approvalKey") String approvalKey) {
//        return approvalFacade.getApprovalDetailById(approvalKey);
//    }
//
//}
