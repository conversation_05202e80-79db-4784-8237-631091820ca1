package com.navigator.magellan.web.controller.husky;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.file.JsonFileUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.TemplateGroupFacade;
import com.navigator.husky.pojo.dto.TemplateGroupJsonDTO;
import com.navigator.husky.pojo.entity.TemplateGroupEntity;
import com.navigator.husky.pojo.qo.QueryTemplateQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-18 19:02
 **/
@RestController
@Slf4j
@RequestMapping("/template/group/")
public class TemplateGroupController {
    @Autowired
    private TemplateGroupFacade templateGroupFacade;
    @Autowired
    private HttpServletResponse response;

    /**
     * 列表分页查询条款组信息
     *
     * @param queryDTO 条款组查询条件
     * @return 条款组查询结果
     */
    @PostMapping("/queryGroupByCondition")
    public Result queryGroupByCondition(@RequestBody QueryDTO<QueryTemplateQO> queryDTO) {
        return templateGroupFacade.queryGroupByCondition(queryDTO);
    }

    /**
     * 新增条款组
     *
     * @param groupEntity 条款组信息
     * @return 条款组新增结果
     */
    @PostMapping("/saveTemplateGroup")
    public Result<Boolean> saveTemplateGroup(@RequestBody TemplateGroupEntity groupEntity) {
        return Result.success(templateGroupFacade.saveTemplateGroup(groupEntity));
    }

    /**
     * 修改条款组信息
     *
     * @param groupEntity 条款组信息
     * @return 更新结果
     */
    @PostMapping("/updateTemplateGroup")
    public Result<Boolean> updateTemplateGroup(@RequestBody TemplateGroupEntity groupEntity) {
        return Result.success(templateGroupFacade.updateTemplateGroup(groupEntity));
    }

    /**
     * 更新条款组状态
     *
     * @param templateId
     * @param status
     * @return
     */
    @GetMapping("/updateGroupStatus")
    public Result<Boolean> updateGroupStatus(@RequestParam(value = "templateGroupId") Integer templateId,
                                             @RequestParam(value = "status") Integer status) {
        return Result.success(templateGroupFacade.updateGroupStatus(templateId, status));
    }

    /**
     * 根据ID查询条款组
     *
     * @param templateGroupId 条款组ID
     * @return 条款组详情
     */
    @GetMapping("/getTemplateGroupById")
    public Result<TemplateGroupEntity> getTemplateGroupById(@RequestParam(value = "templateGroupId") Integer templateGroupId) {
        return Result.success(templateGroupFacade.getTemplateGroupById(templateGroupId));
    }

    /**
     * 查询所有条款组
     *
     * @param status 状态
     * @return
     */
    @GetMapping("/getAllGroupList")
    public Result getAllGroupList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(templateGroupFacade.getAllGroupList(status));
    }

    /**
     * 导出条款组excel信息
     *
     * @param queryTemplateQO
     * @return
     */
    @PostMapping("/exportGroupExcel")
    public void exportGroupExcel(@RequestBody QueryTemplateQO queryTemplateQO) {
        List<TemplateGroupEntity> groupEntityList = new ArrayList<>();
        Result groupResult = templateGroupFacade.exportGroupExcel(queryTemplateQO);
        if (groupResult.isSuccess()) {
            groupEntityList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupEntity.class);
        }
        if (CollectionUtils.isEmpty(groupEntityList)) {
            throw new BusinessException("无数据可导出！");
        }
        log.info(FastJsonUtils.getBeanToJson(groupEntityList));
        String fileName = "条款组数据导出_" + "_" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(groupEntityList, "条款组信息", "条款组信息", TemplateGroupEntity.class, fileName, response);
//        return Result.success("导出成功", groupEntityList);
    }

    /**
     * 导出条款组Json脚本
     *
     * @param templateQO
     * @return
     */
    @PostMapping("/exportGroupJson")
    public Result exportGroupJson(@RequestBody QueryTemplateQO templateQO, HttpServletResponse response) {
        Result groupResult = templateGroupFacade.exportGroupJson(templateQO);
        if (!groupResult.isSuccess()) {
            return groupResult;
        }
        if (null == groupResult.getData()) {
            return Result.failure("无可导出的条款组！");
        }
        List<TemplateGroupJsonDTO> groupJsonDTOList = JSON.parseArray(JSON.toJSONString(groupResult.getData()), TemplateGroupJsonDTO.class);
        String groupName = groupJsonDTOList.size() > 1 ? "TemplateGROUP" : groupJsonDTOList.get(0).getCode();
        JsonFileUtil.exportJson(response, groupJsonDTOList, groupName + "_" + DateTimeUtil.formatDateValue() + ".json");
        return groupResult;
    }

    /**
     * 导入条款组Json脚本，并同步
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importGroupJson")
    public Result importGroupJson(@RequestParam("file") MultipartFile file) {
        return templateGroupFacade.importGroupJson(file);
    }
}
