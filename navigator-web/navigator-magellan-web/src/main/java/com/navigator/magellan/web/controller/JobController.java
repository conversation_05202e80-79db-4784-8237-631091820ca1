package com.navigator.magellan.web.controller;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.magellan.web.schedule.SampleSignScheduler;
import com.navigator.magellan.web.schedule.YqqResponseScheduler;
import com.navigator.trade.facade.ContractSignFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/job")
@Slf4j
public class JobController {

    @Resource
    SampleSignScheduler sampleSignScheduler;
    @Resource
    YqqResponseScheduler yqqResponseScheduler;
    @Resource
    ContractSignFacade contractSignFacade;
    @Resource
    PriceApplyFacade priceApplyFacade;


    @GetMapping("/samplesign")
    public Result setSampleSignScheduler() {
        sampleSignScheduler.execute();
        return Result.success();
    }

    @GetMapping("/yqqJobResponseScheduler")
    //@Scheduled(cron = "/10 * * * * ?")
    public Result yqqJobResponseScheduler() {
        yqqResponseScheduler.execute();
        return Result.success();
    }

    @GetMapping("/sendContractSignOriginalPaper")
    @Scheduled(cron = "0 30 17 * * ?")
    public Result sendContractSignOriginalPaper() {
        contractSignFacade.sendContractSignOriginalPaper();
        return Result.success();
    }

    @GetMapping("/sendNotDealInmail")
    @Scheduled(cron = "0 0/30 8-17 * * ?")
    public Result sendNotDealInmail() {
        priceApplyFacade.sendNotDealInmail();
        return Result.success();
    }

}
