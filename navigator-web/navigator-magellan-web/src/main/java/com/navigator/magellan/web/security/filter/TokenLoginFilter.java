package com.navigator.magellan.web.security.filter;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.LoginVO;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.ResponseUtil;
import com.navigator.magellan.web.service.LoginLogRecordService;
import com.navigator.magellan.web.security.entity.UserDetail;
import com.navigator.security.authentication.TokenManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.servlet.FilterChain;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 登录过滤器，继承UsernamePasswordAuthenticationFilter，对用户名密码进行登录校验
 * Description: No Description
 * Created by YuYong on 2021/11/3 13:46
 */
@Slf4j
public class TokenLoginFilter extends UsernamePasswordAuthenticationFilter {

    private AuthenticationManager authenticationManager;
    private TokenManager tokenManager;
    private LoginLogRecordService loginLogRecordService;
    private LoginDTO currentLoginDTO; // 存储当前登录请求的DTO
//    private RedisTemplate redisTemplate;

    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
    public TokenLoginFilter(AuthenticationManager authenticationManager, TokenManager tokenManager, LoginLogRecordService loginLogRecordService) {
        this.authenticationManager = authenticationManager;
        this.tokenManager = tokenManager;
        this.loginLogRecordService = loginLogRecordService;
        this.setPostOnly(false);
        this.setRequiresAuthenticationRequestMatcher(new AntPathRequestMatcher("/user/verifyCode", "POST"));
    }
    // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End

    @Override
    public Authentication attemptAuthentication(HttpServletRequest req, HttpServletResponse res)
            throws AuthenticationException {
        try {
            ServletInputStream inputStream = null;
            if(null!=req)
                inputStream = req.getInputStream();
            String info = "";
            if (null != inputStream){
                info = inputStream.toString();
                info = info.replaceAll("[\n\r\t]", "_");
                logger.info(info);
                LoginDTO loginDTO = new ObjectMapper().readValue(inputStream, LoginDTO.class);
                // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
                this.currentLoginDTO = loginDTO; // 保存登录DTO供后续使用
                String detail = JSON.toJSONString(loginDTO);
                return authenticationManager.authenticate(new UsernamePasswordAuthenticationToken(loginDTO.getEmail(), detail, new ArrayList<>()));
            }
            return null;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 登录成功
     *
     * @param req
     * @param res
     * @param chain
     * @param auth
     */
    @Override
    protected void successfulAuthentication(HttpServletRequest req, HttpServletResponse res, FilterChain chain,
                                            Authentication auth) {
        log.info("========> start login ");
        UserDetail userDetail = (UserDetail) auth.getPrincipal();
        String token = tokenManager.createTokenByEmail(String.valueOf(userDetail.getEmployEntity().getId()), userDetail.getEmployEntity().getEmail(), true);
        String refreshToken = tokenManager.getRefreshTokenByEmail(String.valueOf(userDetail.getEmployEntity().getId()), userDetail.getEmployEntity().getEmail());
        LoginVO loginVO = new LoginVO();
        EmployEntity employEntity = userDetail.getEmployEntity();
        loginVO
                .setToken(token)
                .setRefreshToken(refreshToken)
                .setId(employEntity.getId())
                .setName(employEntity.getName())
                .setAadLogin(false)
        ;
        log.info("========> login log employId:{}", employEntity.getId());
        MenuVO menuVO = tokenManager.getMenuVO(employEntity.getId());
        List<String> powerCodeList = tokenManager.getPowerCodeListByEmployId(employEntity.getId());
        loginVO.setMenuVO(menuVO);
        loginVO.setPowerCodeList(powerCodeList);
        log.info("========> login  success ");

        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
        // 记录登录成功日志
        if (loginLogRecordService != null && currentLoginDTO != null) {
            loginLogRecordService.recordMagellanSuccessLog(currentLoginDTO, loginVO);
        }
        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End

        ResponseUtil.out(res, Result.success(loginVO));
    }

    /**
     * 登录失败
     *
     * @param request
     * @param response
     * @param e
     */
    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request, HttpServletResponse response,
                                              AuthenticationException e) {
        log.info("========> login  failed ");

        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 Start
        // 记录登录失败日志
        if (loginLogRecordService != null && currentLoginDTO != null) {
            loginLogRecordService.recordMagellanFailureLog(currentLoginDTO, e.getMessage());
        }
        // 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30 End

        ResponseUtil.out(response, Result.failure(e.getMessage()));
    }
}
