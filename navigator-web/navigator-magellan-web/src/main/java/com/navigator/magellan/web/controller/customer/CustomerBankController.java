package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerBankFacade;
import com.navigator.customer.pojo.dto.CustomerBankDTO;
import com.navigator.customer.pojo.dto.CustomerBankFactoryDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/4 14:21
 */
@RestController
@RequestMapping("/customerBank")
public class CustomerBankController {

    @Resource
    private CustomerBankFacade customerBankFacade;

    /**
     * 根据客户id查询出客户银行账户信息
     *
     * @param customerId
     * @return
     */
    @GetMapping("/queryCustomerBankByCustomerId")
    public Result queryCustomerBankByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                                @RequestParam(value = "useType") Integer useType) {
        return customerBankFacade.queryCustomerBankByCustomerId(customerId, useType);
    }

    /**
     * 根据客户id查询出客户银行账户信息
     *
     * @param
     * @return
     */
    @PostMapping("/queryCustomerBankFactory")
    public Result queryCustomerBankFactory(@RequestBody CustomerBankFactoryDTO customerBankFactoryDTO) {

        return customerBankFacade.queryCustomerBankFactory(customerBankFactoryDTO);
    }


    /**
     * 根据客户id查询账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/queryBankByCustomerId")
    public Result queryBankByCustomerId(@RequestBody CustomerBankDTO customerBankDTO) {
        return customerBankFacade.queryBankByCustomerId(customerBankDTO);
    }

    /**
     * 根据id 删除账户信息
     *
     * @param id
     * @return
     */
    @GetMapping("/deleteCustomerBankById")
    Result deleteCustomerBankById(@RequestParam(value = "id") Integer id) {
        return customerBankFacade.deleteCustomerBankById(id);

    }

    /**
     * 修改账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/updateCustomerBank")
    Result updateCustomerBank(@RequestBody CustomerBankDTO customerBankDTO) {
        return customerBankFacade.updateCustomerBank(customerBankDTO);

    }


    /**
     * 添加账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/saveCustomerBank")
    Result saveCustomerBank(@RequestBody CustomerBankDTO customerBankDTO) {
        return customerBankFacade.saveCustomerBank(customerBankDTO);

    }


    /**
     * 提交保存账户信息
     *
     * @param customerBankDTO
     * @return
     */
    @PostMapping("/redactCustomerBank")
    Result redactCustomerBank(@RequestBody List<CustomerBankDTO> customerBankDTO) {
        return customerBankFacade.redactCustomerBank(customerBankDTO);

    }
}
