package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.columbus.CMenuFacade;
import com.navigator.admin.pojo.dto.columbus.CMenuDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/cmenu")
public class CMenuController {

    @Autowired
    private CMenuFacade menuFacade;

    @PostMapping("/getMenusByEmploy")
    public Result getMenusByEmploy(@RequestBody CMenuDTO menuDTO) {
        return menuFacade.getMenusByEmployV2(menuDTO);
    }


    /**
     *
     *
     * @return
     */
    @PostMapping("/getMenuByRoleId")
    public Result getMenuByRoleId(@RequestBody CRoleDTO roleDTO) {
        return menuFacade.getMenuByRoleIdV2(roleDTO);
    }

    /**
     *
     *
     * @return
     */
    @PostMapping("/saveRoleMenu")
    public Result saveRoleMenu(@RequestBody CMenuDTO menuDTO) {
        return menuFacade.saveRoleMenuV2(menuDTO);
    }

    @PostMapping("/addRoleMenu")
    public Result addRoleMenu(@RequestBody CMenuDTO menuDTO) {
        return menuFacade.addRoleMenu(menuDTO);
    }
}
