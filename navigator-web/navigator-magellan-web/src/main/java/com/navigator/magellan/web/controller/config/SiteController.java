package com.navigator.magellan.web.controller.config;

import com.navigator.admin.facade.SiteFacade;
import com.navigator.admin.pojo.entity.SiteEntity;
import com.navigator.admin.pojo.qo.SiteQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> NaNa
 * @since : 2024-07-24 16:19
 **/
@RestController
@RequestMapping("/site")
public class SiteController {
    @Resource
    private SiteFacade siteFacade;

    /**
     * 列表分页查询账套信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/querySiteByCondition")
    public Result querySiteByCondition(@RequestBody QueryDTO<SiteQO> queryDTO) {
        return siteFacade.querySiteByCondition(queryDTO);
    }

    /**
     * 根据账套编码查询账套详情
     *
     * @param siteCode
     * @return
     */
    @GetMapping("/getSiteDetailByCode")
    public Result<SiteEntity> getSiteByCode(@RequestParam(value = "siteCode") String siteCode) {
        return Result.success(siteFacade.getSiteDetailByCode(siteCode));
    }

    /**
     * 新增账套
     *
     * @param siteEntity
     * @return
     */
    @PostMapping("/saveSite")
    public Result saveSite(@RequestBody @Valid SiteEntity siteEntity) {
        return siteFacade.saveSite(siteEntity);
    }

    /**
     * 更新账套信息（状态、二级品类、三级品种）
     *
     * @param siteEntity
     * @return
     */
    @PostMapping("/updateSite")
    public Result updateSite(@RequestBody @Valid SiteEntity siteEntity) {
        return siteFacade.updateSite(siteEntity);
    }

    /**
     * 启用/禁用账套
     *
     * @param siteId
     * @param status
     * @return
     */
    @GetMapping("/updateSiteStatus")
    public Result updateSiteStatus(@RequestParam(value = "siteId") Integer siteId,
                                   @RequestParam(value = "status") Integer status) {
        return siteFacade.updateSiteStatus(siteId, status);
    }

    /**
     * 获取账套信息
     *
     * @param companyId
     * @param category2
     * @param status
     * @return
     */
    @GetMapping("/getSiteList")
    public Result<List<SiteEntity>> getSiteList(@RequestParam(value = "companyId", required = false) Integer companyId,
                                                @RequestParam(value = "category2", required = false) Integer category2,
                                                @RequestParam(value = "syncSystem", required = false) String syncSystem,
                                                @RequestParam(value = "status", required = false) Integer status) {
        return Result.success(siteFacade.getSiteList(companyId, category2, syncSystem, status));
    }

    /**
     * 获取工厂编码
     *
     * @param syncSystem
     * @return
     */
    @GetMapping("/queryFactoryCodeBySyncSystem")
    public Result<Set<String>> queryFactoryCodeBySyncSystem(@RequestParam(value = "syncSystem") String syncSystem) {
        return Result.success(siteFacade.queryFactoryCodeBySyncSystem(syncSystem));
    }

}
