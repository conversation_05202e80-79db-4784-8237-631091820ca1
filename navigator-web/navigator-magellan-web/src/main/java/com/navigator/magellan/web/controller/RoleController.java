package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.facade.magellan.RoleFacade;
import com.navigator.admin.pojo.dto.*;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.admin.pojo.vo.RoleQueryVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.LinkedHashSet;
import java.util.List;

@RestController
@RequestMapping("/role")
public class RoleController {
    @Autowired
    private RoleFacade roleFacade;
    @Autowired
    private EmployPermissionFacade employPermissionFacade;

    @PostMapping("/saveOrUpdateRole")
    public Result saveOrUpdateRole(@RequestBody @Valid RoleDTO roleDTO) {
        return roleFacade.saveOrUpdateRole(roleDTO);
    }

    @PostMapping("/queryRoleList")
    public Result queryRoleList(@RequestBody QueryDTO<RoleQueryDTO> roleQueryDTO) {
        return roleFacade.queryRoleList(roleQueryDTO);
    }

    @PostMapping("/queryRoleDefList")
    public Result queryRoleDefList(@RequestBody QueryDTO<RoleQueryDTO> roleQueryDTO) {
        return roleFacade.queryRoleDefList(roleQueryDTO);
    }

    @PostMapping("/addEmployRole")
    public Result addEmployRole(@RequestBody EmployRoleDTO employRoleDTO) {
        employPermissionFacade.addEmployRole(employRoleDTO);
        return Result.success();
    }

    @GetMapping("/addEmployRoles")
    public Result addEmployRoles(@RequestParam("employId") Integer employId, @RequestParam("roleIds") String roleIds, @RequestParam("roleDefId") Integer roleDefId, @RequestParam("categoryId") Integer categoryId, @RequestParam("customerIds") String customerIds) {
        employPermissionFacade.addEmployRoles(employId, roleIds, roleDefId, categoryId, customerIds);
        return Result.success();
    }

    @PostMapping("/deleteEmployRole")
    public Result deleteEmployRole(@RequestBody EmployRoleDTO employRoleDTO) {
        employPermissionFacade.deleteEmployRole(employRoleDTO);
        return Result.success();
    }

    @GetMapping("/queryRoleDefDetail")
    Result<RoleQueryVO> queryRoleDefDetail(@RequestParam("roleDefId") Integer roleDefId) {
        Result<RoleQueryVO> roleQueryVOResult = roleFacade.queryRoleDefDetail(roleDefId);
        return roleQueryVOResult;
    }

    @PostMapping("/queryRoleByFactory")
    Result<List<RoleQueryVO>> queryRoleByFactory(@RequestBody EmployRoleDTO employRoleDTO) {
        return roleFacade.queryRoleByFactory(employRoleDTO);
    }

    @PostMapping("/saveRole")
    public Result saveRole(@RequestBody RoleDTO roleDTO) {
        return roleFacade.saveRole(roleDTO);
    }

    @GetMapping("/queryRoleGroupList")
    public Result queryRoleGroupList() {
        return roleFacade.queryRoleGroupList();
    }

    @PostMapping("/copyPermission")
    public Result copyPermission(@RequestBody RoleDTO roleDTO) {
        return roleFacade.copyPermission(roleDTO);
    }

    /**
     * 根据条件：获取已授权的菜单树及权限树
     *
     * @param roleAuthQO
     * @return
     */
    @ApiOperation(value = "根据条件：获取已授权的菜单树及权限树")
    @PostMapping("/getRoleAuth")
    Result<RoleAuthDTO> getRoleAuth(@RequestBody RoleAuthQO roleAuthQO) {
        return roleFacade.getRoleAuth(roleAuthQO);
    }

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部菜单树及已授权菜单ID列表")
    @PostMapping("/getRoleAuthMenu")
    Result<RoleAuthMenuDTO> getRoleAuthMenu(@RequestBody RoleAuthMenuQO roleAuthMenuQO) {
        return roleFacade.getRoleAuthMenu(roleAuthMenuQO);
    }

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部权限树及已授权权限ID列表")
    @PostMapping("/getRoleAuthPower")
    Result<RoleAuthPowerDTO> getRoleAuthPower(@RequestBody RoleAuthPowerQO roleAuthPowerQO) {
        return roleFacade.getRoleAuthPower(roleAuthPowerQO);
    }

    /**
     * 根据用户ID：获取已授权的账套编码列表
     *
     * @param userId
     * @return
     */
    @GetMapping("/queryRoleSiteCodeSet")
    Result<LinkedHashSet<String>> queryRoleSiteCodeSet(@RequestParam(required = false) Integer userId) {
        return roleFacade.queryRoleSiteCodeSet(userId);
    }
}
