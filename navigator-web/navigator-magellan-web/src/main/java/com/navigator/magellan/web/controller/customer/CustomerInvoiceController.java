package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerInvoiceFacade;
import com.navigator.customer.pojo.dto.CustomerInvoiceDTO;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */

@RestController
@RequestMapping("/customerInvoice")
public class CustomerInvoiceController {

    @Resource
    private CustomerInvoiceFacade customerInvoiceFacade;

    @PostMapping("/queryCustomerInvoiceList")
    public Result queryCustomerInvoiceList(@RequestBody CustomerInvoiceDTO customerInvoiceDTO) {
        return Result.success(customerInvoiceFacade.queryCustomerInvoiceList(customerInvoiceDTO));
    }

}
