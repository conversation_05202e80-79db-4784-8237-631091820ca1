package com.navigator.magellan.web.controller.config;

import com.navigator.admin.facade.TradeDayFacade;
import com.navigator.admin.pojo.dto.TradeDayCycleDTO;
import com.navigator.admin.pojo.dto.systemrule.TradeDayDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-03-18 17:34
 */
@RestController
@RequestMapping("/tradeDay")
public class TradeDayController {
    @Resource
    private TradeDayFacade tradeDayFacade;

    /**
     * 根据月份获取交易日信息
     *
     * @param month 月份
     * @return 交易日信息
     */
    @GetMapping("/getTradeDayByMonth")
    public Result getTradeDayByMonth(@RequestParam("month") String month) {
        return Result.success(tradeDayFacade.getTradeDayByMonth(month));
    }

    /**
     * 获取交易日数
     *
     * @param startDay
     * @param endDay
     * @return
     */
    @GetMapping("/getTradeDays")
    public Result getTradeDays(@RequestParam("startDay") String startDay, @RequestParam("endDay") String endDay) {
        return Result.success(tradeDayFacade.getTradeDays(startDay, endDay));
    }

    /**
     * 获取某年交易日信息
     *
     * @param year 月份
     * @return 交易日信息
     */
    @GetMapping("/getTradeDayByYear")
    public Result getTradeDayByYear(@RequestParam(value = "year", required = false) Integer year) {
        return Result.success(tradeDayFacade.getTradeDayByYear(year));
    }


    /**
     * 设置非交易日
     *
     * @param tradeDayDTOList 日期集合
     * @return 更新结果
     */
    @PostMapping("/setNotTrade")
    public Result setNotTrade(@RequestBody List<TradeDayDTO> tradeDayDTOList) {
        return Result.success(tradeDayFacade.setNotTrade(tradeDayDTOList));
    }

    /**
     * 根据期货代码和分配日期获取注销周期
     *
     * @param futureCode
     * @param dayValue
     * @return
     */
    @GetMapping("/getTradeDayCycleDTO")
    Result<TradeDayCycleDTO> getTradeDayCycleDTO(@RequestParam(value = "futureCode") String futureCode, @RequestParam(value = "dayValue", required = false) String dayValue) {
        return tradeDayFacade.getTradeDayCycleDTO(futureCode, dayValue);
    }
}
