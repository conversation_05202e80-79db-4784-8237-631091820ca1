package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.UpdateCustomerFacade;
import com.navigator.customer.pojo.bo.CustomerBankBO;
import com.navigator.customer.pojo.dto.*;
import com.navigator.customer.pojo.entity.CustomerDeliveryWhiteEntity;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import com.navigator.customer.pojo.entity.CustomerInvoiceEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */

@RestController
@RequestMapping("/updateCustomer")
public class UpdateCustomerController {

    @Resource
    private UpdateCustomerFacade updateCustomerFacade;

    /**
     * 模板属性编辑
     *
     * @param customerProtocolDTO
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerProtocolFactory")
    public Result saveOrUpdateCustomerProtocolFactory(@RequestBody CustomerProtocolDTO customerProtocolDTO){
        return updateCustomerFacade.saveOrUpdateCustomerProtocolFactory(customerProtocolDTO);
    }

    /**
     * 通知人编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerNotice")
    public Result saveOrUpdateContactEntity(@RequestBody ContactDTO contactDTO){
        return updateCustomerFacade.saveOrUpdateContactEntity(contactDTO);
    }

    /**
     * 正本编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerOriginalPaper")
    public Result saveOrUpdateCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO){
        return updateCustomerFacade.saveOrUpdateCustomerOriginalPaper(customerOriginalPaperDTO);
    }

    /**
     * 预约保证金编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerDepositRate")
    public  Result saveOrUpdateCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO){
        return updateCustomerFacade.saveOrUpdateCustomerDepositRate(customerDepositRateDTO);
    }

    /**
     * 客户白名单编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerWhiteList")
    public Result saveOrUpdateCustomerWhiteList(@RequestBody CustomerDetailDTO customerWhiteListDTO){
        return updateCustomerFacade.saveOrUpdateCustomerWhiteList(customerWhiteListDTO);
    }

    /**
     * 账户信息编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerBank")
    public Result saveOrUpdateCustomerBank(@RequestBody CustomerBankBO customerBankBO){
        return updateCustomerFacade.saveOrUpdateCustomerBank(customerBankBO);
    }

    /**
     * 发票类型编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerInvoiceType")
    public Result saveOrUpdateCustomerInvoiceType(@RequestBody CustomerInvoiceEntity customerInvoiceEntity){
        return updateCustomerFacade.saveOrUpdateCustomerInvoiceType(customerInvoiceEntity);
    }

    /**
     * 赊销预付编辑
     *
     * @return
     */
    @PostMapping("/saveOrCustomerCreditPayment")
    public Result saveOrCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO){
        return updateCustomerFacade.saveOrCustomerCreditPayment(customerCreditPaymentDTO);
    }


    /**
     * 评级编辑
     *
     * @return
     */
    @PostMapping("/saveOrUpdateCustomerGradeScore")
    public Result saveOrUpdateCustomerGradeScore(@RequestBody CustomerGradeScoreEntity customerGradeScoreEntity){
        return updateCustomerFacade.saveOrUpdateCustomerGradeScore(customerGradeScoreEntity);
    }

    @PostMapping("/saveOrUpdateCustomerDeliveryWhite")
    public Result saveOrUpdateCustomerDeliveryWhite(@RequestBody CustomerDeliveryWhiteEntity customerDeliveryWhiteEntity){
        return updateCustomerFacade.saveOrUpdateCustomerDeliveryWhite(customerDeliveryWhiteEntity);
    }
}
