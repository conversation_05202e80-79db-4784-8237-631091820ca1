package com.navigator.magellan.web.controller.husky;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.husky.facade.TemplateHistoryFacade;
import com.navigator.husky.pojo.dto.EnumValueDTO;
import com.navigator.husky.pojo.entity.TemplateHistoryEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-12-08 15:24
 **/
@RestController
@Slf4j
@RequestMapping("/template/log/")
public class TemplateHistoryController {
    @Resource
    private TemplateHistoryFacade templateHistoryFacade;

    /**
     * 列表分页检索条款
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryHistoryByCondition")
    public Result queryHistoryByCondition(@RequestBody QueryDTO<TemplateHistoryEntity> queryDTO) {
        return templateHistoryFacade.queryHistoryByCondition(queryDTO);
    }

    /**
     * 获取所有操作类型
     *
     * @return
     */
    @GetMapping("/getOperationTypeList")
    public Result<List<EnumValueDTO>> getOperationTypeList() {
        return Result.success(templateHistoryFacade.getOperationTypeList());
    }

}
