package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.magellan.PowerFacade;
import com.navigator.admin.pojo.dto.PowerDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/power")
public class PowerController {
    @Autowired
    private PowerFacade powerFacade;

    @PostMapping("/saveOrUpdatePower")
    public Result saveOrUpdatePower(@RequestBody PowerDTO powerDTO) {
        return powerFacade.saveOrUpdatePower(powerDTO);
    }

    @PostMapping("/queryPowerByRoleId")
    public Result queryPowerByRoleId(@RequestBody PowerDTO powerDTO) {
        return powerFacade.queryPowerByRoleId(powerDTO);
    }

    /**
     * 添加角色按钮权限
     * <p>
     * powerIdList 按钮id列表
     * roleDefIdList 虚角色列表
     *
     * @return
     */
    @PostMapping("/addRolePower")
    public Result addRolePower(@RequestBody PowerDTO powerDTO) {
        return powerFacade.addRolePower(powerDTO);
    }



}
