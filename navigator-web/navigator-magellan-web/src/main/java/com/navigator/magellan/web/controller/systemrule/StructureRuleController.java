package com.navigator.magellan.web.controller.systemrule;

import com.navigator.admin.facade.StructureRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.StructureRuleDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/structureRule")
public class StructureRuleController {
    @Autowired
    private StructureRuleFacade structureRuleFacade;

    @PostMapping("/save")
    public Result save(@RequestBody StructureRuleDTO structureRuleDTO) {
        return structureRuleFacade.save(structureRuleDTO);
    }

    @PostMapping("/modify")
    public Result modify(@RequestBody StructureRuleDTO structureRuleDTO) {
        return structureRuleFacade.modify(structureRuleDTO);
    }

    @PostMapping("/updateStatus")
    public Result updateStatus(@RequestBody StructureRuleDTO structureRuleDTO) {
        return structureRuleFacade.updateStatus(structureRuleDTO);
    }

    @GetMapping("/queryStructureCode")
    public Result queryStructureCode() {
        return structureRuleFacade.queryStructureCode();
    }


    @PostMapping("/queryStructureList")
    public Result queryStructureList(@RequestBody QueryDTO<StructureRuleDTO> structureRuleDTOQueryDTO) {
        return structureRuleFacade.queryStructureList(structureRuleDTOQueryDTO);
    }

    @GetMapping("/queryAvailableStructureList")
    public Result queryAvailableStructureList() {
        return structureRuleFacade.queryAvailableStructureList();
    }
}
