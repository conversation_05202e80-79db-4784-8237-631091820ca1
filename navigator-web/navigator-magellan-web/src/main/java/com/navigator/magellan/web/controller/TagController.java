package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.TagDetailFacade;
import com.navigator.admin.facade.TagFacade;
import com.navigator.admin.pojo.dto.TagQueryDTO;
import com.navigator.admin.pojo.entity.TagDetailEntity;
import com.navigator.admin.pojo.entity.TagEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/1 15:16
 */
@RestController
@RequestMapping("/tag")
public class TagController {

    @Resource
    private TagDetailFacade tagDetailFacade;
    @Resource
    private TagFacade tagFacade;

    /**
     * 更具标签id查询出绑定的关系表
     *
     * @param tagId
     * @return
     */
    @GetMapping("/queryTagDetailTagId")
    public Result queryTagDetailTagId(@RequestParam(value = "tagId") Integer tagId) {
        return tagDetailFacade.queryTagDetailTagId(tagId);
    }

    /**
     * 取消和标签的绑定绑定
     *
     * @param id
     * @return
     */
    @GetMapping("/cancelTagDetail")
    public Result cancelTagDetail(@RequestParam(value = "id") Integer id) {
        return tagDetailFacade.cancelTagDetail(id);
    }

    /**
     * 绑定标签和业务
     *
     * @param tagDetailEntity
     * @return
     */
    @PostMapping("/saveTagDetail")
    public Result saveTagDetail(@RequestBody TagDetailEntity tagDetailEntity) {
        return Result.success(tagDetailFacade.saveTagDetail(tagDetailEntity));
    }

    /**
     * 分页查询标签
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryTagEntity")
    public Result queryTagEntity(@RequestBody QueryDTO<TagQueryDTO> queryDTO) {
        return tagFacade.queryTagEntity(queryDTO);
    }

    /**
     * 根据id查询标签
     *
     * @param id
     * @return
     */
    @GetMapping("/queryTageEntity")
    public Result queryTageEntity(@RequestParam(value = "id") Integer id) {
        return Result.success(tagFacade.queryTageEntity(id));
    }

    /**
     * 新增标签
     *
     * @param tagEntity
     * @return
     */
    @PostMapping("/saveTagEntity")
    public Result saveTagEntity(@RequestBody TagEntity tagEntity) {
        return tagFacade.saveTagEntity(tagEntity);
    }

    /**
     * 修改标签
     *
     * @param tagEntity
     * @return
     */
    @PostMapping("/updateTagEntity")
    public Result updateTagEntity(@RequestBody TagEntity tagEntity) {
        return tagFacade.updateTagEntity(tagEntity);
    }
}
