package com.navigator.magellan.web.controller.systemrule;

import com.navigator.admin.facade.ProteinPriceConfigFacade;
import com.navigator.admin.facade.SystemRuleFacade;
import com.navigator.admin.pojo.dto.systemrule.BasicPriceConfigQueryDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleCreateDTO;
import com.navigator.admin.pojo.dto.systemrule.SystemRuleDTO;
import com.navigator.admin.pojo.vo.systemrule.SystemRuleVO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/systemRule")
public class SystemRuleController {

    @Autowired
    private SystemRuleFacade systemRuleFacade;
    @Autowired
    private ProteinPriceConfigFacade proteinPriceConfigFacade;

    /**
     * 查询系统配置
     *
     * @return List<SystemRuleVO>
     */
    @PostMapping("/getSystemRule")
    public Result getSystemRule(@RequestBody SystemRuleDTO systemRuleDTO) {
        List<SystemRuleVO> systemRuleVOList = systemRuleFacade.getSystemRule(systemRuleDTO);
        return Result.success(systemRuleVOList);
    }

    /**
     * 查询系统配置
     *
     * @return List<SystemRuleVO>
     */
    @PostMapping("/getNextSystemRule")
    public Result getNextSystemRule(@RequestBody SystemRuleDTO systemRuleDTO) {
        return Result.success(systemRuleFacade.getNextSystemRule(systemRuleDTO));
    }

    /**
     * 查询所有配置信息
     *
     * @param systemRuleDTO
     * @return
     */
    @PostMapping("/querySystemRuleDetail")
    public Result querySystemRuleDetail(@RequestBody SystemRuleDTO systemRuleDTO) {
        return Result.success(systemRuleFacade.querySystemRuleDetail(systemRuleDTO));
    }

    /**
     * 新增更新配置
     *
     * @param systemRuleCreateDTO
     * @return
     */
    @PostMapping("/saveOrUpdateSystemRule")
    public Result saveOrUpdateSystemRule(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleFacade.saveOrUpdateSystemRule(systemRuleCreateDTO);
    }

    /**
     * 禁用启用配置
     *
     * @param ruleItemId 配置ID
     * @return
     */
    @GetMapping("/invalidStatus")
    public Result invalidStatus(@RequestParam(value = "ruleItemId") Integer ruleItemId) {
        return systemRuleFacade.invalidStatus(ruleItemId);
    }

    /**
     * 根据规则查询基差基准价
     *
     * @param basicPriceConfigQueryDTO 规则条件
     * @return
     */
    @PostMapping("/filterBasicPrice")
    public Result filterBasicPrice(@RequestBody BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {
        return systemRuleFacade.filterBasicPrice(basicPriceConfigQueryDTO);
    }

    @PostMapping("/filterBasicProtein")
    public Result filterBasicProtein(@RequestBody BasicPriceConfigQueryDTO basicPriceConfigQueryDTO) {
        return proteinPriceConfigFacade.filterBasicProtein(basicPriceConfigQueryDTO);
    }

    /**
     * 更新LOA值
     *
     * @param systemRuleCreateDTO
     * @return
     */
    @PostMapping("/updateLOAValue")
    public Result updateLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleFacade.updateLOAValue(systemRuleCreateDTO);
    }

    /**
     * 查询LOA值
     *
     * @param systemRuleCreateDTO
     * @return
     */
    @PostMapping("/queryLOAValue")
    public Result queryLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO) {
        return systemRuleFacade.queryLOAValue(systemRuleCreateDTO);
    }

    @GetMapping("/queryBasicPriceGoodsConfigByCategoryId")
    public Result queryBasicPriceGoodsConfigByCategoryId(@RequestParam(value = "categoryId") Integer categoryId) {
        return systemRuleFacade.queryBasicPriceGoodsConfigByCategoryId(categoryId);
    }


    @PostMapping("/saveLOAValue")
    public Result saveLOAValue(@RequestBody SystemRuleCreateDTO systemRuleCreateDTO){
        return systemRuleFacade.saveLOAValue(systemRuleCreateDTO);
    }

    @GetMapping("/queryContractApproveConfigItem")
    public Result queryContractApproveConfigItem(@RequestParam(value = "categoryId") Integer categoryId){
        return Result.success(systemRuleFacade.queryContractApproveConfigItem(categoryId));
    }
}
