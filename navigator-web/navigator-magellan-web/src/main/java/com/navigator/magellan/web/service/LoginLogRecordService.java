package com.navigator.magellan.web.service;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.LoginLogFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.LoginLogDTO;
import com.navigator.admin.pojo.vo.LoginVO;
import com.navigator.bisiness.enums.LoginTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

/**
 * Magellan登录日志记录服务
 * 支持Magellan和AAD登录日志记录
 * <p>
 * 优化：Case-1003313--增加系统登录日志 Author: Mr 2025-06-30
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-26
 */
@Service
@Slf4j
public class LoginLogRecordService {

    @Autowired
    private LoginLogFacade loginLogFacade;

    /**
     * 记录Magellan登录成功日志
     */
    public void recordMagellanSuccessLog(LoginDTO loginDTO, LoginVO loginVO) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(LoginTypeEnum.EMAIL_PASSWORD.getCode(), loginDTO);

            loginLogDTO.setLoginStatus(1)
                    .setResponseResult(JSON.toJSONString(loginVO));

            // 从返回结果中提取用户信息
            if (loginVO != null) {
                loginLogDTO.setUserId(loginVO.getId())
                        .setUsername(loginVO.getName());
            }

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录Magellan成功登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 记录Magellan登录失败日志
     */
    public void recordMagellanFailureLog(LoginDTO loginDTO, String failureReason) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(LoginTypeEnum.EMAIL_PASSWORD.getCode(), loginDTO);

            loginLogDTO.setLoginStatus(0)
                    .setFailureReason(failureReason);

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录Magellan失败登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 记录AAD登录成功日志
     */
    public void recordAadSuccessLog(String userPrincipalName, LoginVO loginVO) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(LoginTypeEnum.AAD_LOGIN.getCode(), null);

            loginLogDTO.setEmail(userPrincipalName)
                    .setLoginStatus(1)
                    .setResponseResult(JSON.toJSONString(loginVO))
                    .setRequestParams(JSON.toJSONString(new AadLoginParams(userPrincipalName)));

            // 从返回结果中提取用户信息
            if (loginVO != null) {
                loginLogDTO.setUserId(loginVO.getId())
                        .setUsername(loginVO.getName());
            }

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录AAD成功登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 记录AAD登录失败日志
     */
    public void recordAadFailureLog(String userPrincipalName, String failureReason) {
        try {
            LoginLogDTO loginLogDTO = buildBaseLoginLog(LoginTypeEnum.AAD_LOGIN.getCode(), null);

            loginLogDTO.setEmail(userPrincipalName)
                    .setLoginStatus(0)
                    .setFailureReason(failureReason)
                    .setRequestParams(JSON.toJSONString(new AadLoginParams(userPrincipalName)));

            // 保存日志
            saveLoginLog(loginLogDTO);

        } catch (Exception e) {
            log.error("记录AAD失败登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 构建基础登录日志信息
     */
    private LoginLogDTO buildBaseLoginLog(int loginType, LoginDTO loginDTO) {
        // 获取请求信息
        HttpServletRequest request = getHttpServletRequest();
        String ipAddress = getClientIpAddress(request);
        String userAgent = request != null ? request.getHeader("User-Agent") : "";

        LoginLogDTO loginLogDTO = new LoginLogDTO()
                .setLoginSystem(SystemEnum.MAGELLAN.getValue())
                .setLoginType(loginType)
                .setIpAddress(ipAddress)
                .setUserAgent(userAgent)
                .setLoginTime(new Date());

        if (loginDTO != null) {
            loginLogDTO.setEmail(loginDTO.getEmail())
                    .setPhone(loginDTO.getPhone())
                    .setRequestParams(JSON.toJSONString(loginDTO));
        }

        return loginLogDTO;
    }

    /**
     * AAD登录参数内部类
     */
    @Getter
    private static class AadLoginParams {
        private final String userPrincipalName;

        public AadLoginParams(String userPrincipalName) {
            this.userPrincipalName = userPrincipalName;
        }

    }

    /**
     * 保存登录日志
     */
    private void saveLoginLog(LoginLogDTO loginLogDTO) {
        try {
            loginLogFacade.saveLoginLog(loginLogDTO);
            log.debug("登录日志保存成功，用户：{}，系统：{}，状态：{}",
                    loginLogDTO.getUsername(),
                    loginLogDTO.getLoginSystem(),
                    loginLogDTO.getLoginStatus());
        } catch (Exception e) {
            log.error("保存登录日志失败：{}", e.getMessage(), e);
        }
    }

    /**
     * 获取HttpServletRequest
     */
    private HttpServletRequest getHttpServletRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attributes != null ? attributes.getRequest() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }

        String[] headers = {"X-Forwarded-For", "Proxy-Client-IP", "WL-Proxy-Client-IP"};
        for (String header : headers) {
            String ip = request.getHeader(header);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                return ip.contains(",") ? ip.split(",")[0].trim() : ip;
            }
        }

        return request.getRemoteAddr();
    }
}
