package com.navigator.magellan.web.controller.koala;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.koala.facade.WarrantFacade;
import com.navigator.koala.pojo.bo.QueryWarrantBO;
import com.navigator.koala.pojo.dto.*;
import com.navigator.koala.pojo.entity.WarrantEntity;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/18
 */

@RestController
@RequestMapping("/warrant")
public class WarrantController {

    @Resource
    private WarrantFacade warrantFacade;

    @Value("${blobFile.url.exportWarrant}")
    private String fileUrl;

    /**
     * 提交仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/submitWarrant")
    public Result submitWarrant(@RequestBody WarrantEntity warrantEntity) {
        return warrantFacade.submitWarrant(warrantEntity);
    }

    /**
     * 保存仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/saveWarrant")
    public Result saveWarrant(@RequestBody WarrantEntity warrantEntity) {
        return warrantFacade.saveWarrant(warrantEntity);
    }

    /**
     * 更新仓单
     *
     * @param warrantEntity
     * @return
     */
    @PostMapping("/updateWarrant")
    public Result updateWarrant(@RequestBody WarrantEntity warrantEntity) {
        return warrantFacade.updateWarrant(warrantEntity);
    }

    /**
     * LDC注销仓单
     *
     * @param warrantDTO
     * @return
     */
    @PostMapping("/LDCCancelledWarrant")
    public Result LDCCancelledWarrant(@RequestBody WarrantDTO warrantDTO) {
        return warrantFacade.LDCCancelledWarrant(warrantDTO);
    }

    /**
     * 仓单分配/转让
     *
     * @param allocateAssignWarrantDTO
     * @return
     */
    @PostMapping("/allocateWarrant")
    public Result allocateWarrant(@RequestBody AllocateAssignWarrantDTO allocateAssignWarrantDTO) {
        return warrantFacade.allocateWarrant(allocateAssignWarrantDTO);
    }

    /**
     * 更新仓单注销量
     *
     * @param updateWarrantNumDTO
     * @return
     */
    @PostMapping("/updateWarrantNum")
    public Result updateWarrantNum(@RequestBody UpdateWarrantNumDTO updateWarrantNumDTO) {
        return warrantFacade.updateWarrantNum(updateWarrantNumDTO);
    }

    /**
     * 仓单上传
     *
     * @param file
     */
    @PostMapping(value = "/uploadingFileWarrant")
    public Result uploadingFileWarrant(@RequestPart("file") MultipartFile file) {
        return warrantFacade.uploadingFileWarrant(file);
    }


    /**
     * 根据合同编号查询合同注销记录
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/queryWarrantCancellationByContractCode")
    public Result queryWarrantCancellationByContractCode(@RequestParam("contractCode") String contractCode) {
        return warrantFacade.queryWarrantCancellationByContractCode(contractCode);
    }

    /**
     * 根据仓单编号查询注销记录
     *
     * @param warrantCode
     * @return
     */
    @GetMapping("/queryWarrantCancellationByWarrantCode")
    public Result queryWarrantCancellationByWarrantCode(@RequestParam("warrantCode") String warrantCode) {
        return warrantFacade.queryWarrantCancellationByWarrantCode(warrantCode);
    }

    /**
     * 仓单上传校验
     *
     * @param file
     */
    @PostMapping(value = "/verifyFileWarrant")
    public Result verifyFileWarrant(@RequestPart("file") MultipartFile file) {
        return warrantFacade.verifyFileWarrant(file);
    }

    /**
     * 仓单列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryWarrant")
    public Result queryWarrant(@RequestBody QueryDTO<QueryWarrantBO> queryDTO) {
        return warrantFacade.queryWarrant(queryDTO);
    }

    /**
     * 根据仓单id查询仓单信息
     *
     * @param id
     * @return
     */
    @GetMapping("/queryWarrantByID")
    public Result queryWarrantByID(@RequestParam("id") Integer id) {
        return warrantFacade.queryWarrantByID(id);
    }

    /**
     * 仓单详情
     *
     * @param id
     * @return
     */
    @GetMapping("/queryWarrantVOByID")
    public Result queryWarrantVOByID(@RequestParam("id") Integer id) {
        return warrantFacade.queryWarrantVOByID(id);
    }

    /**
     * 总计
     *
     * @param queryWarrantBO
     * @return
     */
    @PostMapping("/queryWarrantSum")
    public Result queryWarrantSum(@RequestBody QueryWarrantBO queryWarrantBO) {
        return warrantFacade.queryWarrantSum(queryWarrantBO);
    }

    @PostMapping("/queryWarrantExcel")
    public Result queryWarrantExcel(@RequestBody QueryWarrantBO queryWarrantBO, HttpServletResponse response) {
        List<DownloadWarrantExcelDTO> downloadWarrantExcelDTOS = warrantFacade.queryWarrantExcel(queryWarrantBO);

        if (CollectionUtils.isEmpty(downloadWarrantExcelDTOS)) {
            String fileName = "仓单查询数据" + DateTimeUtil.formatDateValue();
            EasyPoiUtils.exportExcel(new ArrayList<DownloadWarrantExcelDTO>(), null, "仓单查询数据", DownloadWarrantExcelDTO.class, fileName, response);
            return Result.success();
        }

        String fileName = "仓单查询数据" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(downloadWarrantExcelDTOS, null, "仓单查询数据", DownloadWarrantExcelDTO.class, fileName, response);
        return Result.success();
    }

    /**
     * 根据仓单code查询仓单信息
     *
     * @param code
     * @return
     */
    @GetMapping("/queryWarrantByCode")
    public Result queryWarrantByCode(@RequestParam("code") String code) {
        return warrantFacade.queryWarrantByCode(code);
    }

    /**
     * 下载仓单模板
     *
     * @param
     */
    @GetMapping("/exportWarrant")
    public Result exportWarrant() {
       /* List<WarrantExcelDTO> warrantExcelDTOS = new ArrayList<>();
        WarrantExcelDTO warrantExcelDTO = new WarrantExcelDTO();
        warrantExcelDTO.setSiteCode("示例:TJIB001");
        warrantExcelDTO.setRegisteName("路易达孚(天津)国际贸易有限公司");
        warrantExcelDTO.setCategory2("豆粕");
        warrantExcelDTO.setCategoryCode("M");
        warrantExcelDTO.setRegisteHandCount("1000");
        warrantExcelDTO.setWarehouseName("东莞豆粕库");
        warrantExcelDTO.setCategory("仓库仓单");
        warrantExcelDTO.setDepositPaymentType("现金");
        warrantExcelDTO.setDepositAmount("80000");
        warrantExcelDTOS.add(warrantExcelDTO);

        String fileName = "下载仓单模板" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(warrantExcelDTOS, null, "下载仓单模板", WarrantExcelDTO.class, fileName, response);*/
        //String url = "https://csm4invgsto001.blob.core.chinacloudapi.cn/int/int/fileRecord/upload/file/magellan/2024-09-13/3607054ea27d428f97ca064e42149b9b_仓单模版.xlsx";
        return Result.success(fileUrl);
    }

    @GetMapping("/deleteWarrant")
    public Result deleteWarrant(@RequestParam("id") Integer id) {
        return warrantFacade.deleteWarrant(id);
    }
}
