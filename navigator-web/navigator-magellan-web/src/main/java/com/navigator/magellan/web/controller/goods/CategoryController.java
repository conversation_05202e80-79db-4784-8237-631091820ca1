package com.navigator.magellan.web.controller.goods;


import cn.hutool.core.lang.tree.Tree;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.CategoryFacade;
import com.navigator.goods.pojo.dto.*;
import com.navigator.goods.pojo.vo.CategoryQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 品类信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@RestController
@RequestMapping("/category")
public class CategoryController {
    @Resource
    private CategoryFacade categoryFacade;

    /**
     * 获取所有商品-品类信息
     *
     * @return 品类集合
     */
    @GetMapping("/getAllCategoryList")
    public Result getAllCategoryList(@RequestParam(value = "level", required = false) Integer level) {
        return Result.success(categoryFacade.getAllCategoryList(level));
    }

    /**
     * 根据条件分页查询品种信息
     *
     * @param categorySearchDTO 品种查询条件
     * @return 查询的品种结果集合
     */
    @PostMapping("/queryCategoryList")
    public Result queryCategoryList(@RequestBody QueryDTO<CategorySearchDTO> categorySearchDTO) {
        return categoryFacade.queryCategoryList(categorySearchDTO);
    }

    /**
     * 禁用/启用品种
     *
     * @param categoryId 品种ID
     * @param status     品种状态
     * @return 禁用/启用结果
     */
    @GetMapping("/invalidCategory")
    public Result invalidCategory(@RequestParam(value = "categoryId") Integer categoryId,
                                  @RequestParam(value = "status") Integer status) {
        return categoryFacade.invalidCategory(categoryId, status);
    }

    /**
     * 更新品类信息
     *
     * @param categoryCreateDTO 品类信息
     * @return 更新结果
     */
    @PostMapping("/updateCategory")
    public Result updateCategory(@RequestBody CategoryCreateDTO categoryCreateDTO) {
        return categoryFacade.updateCategoryStatus(categoryCreateDTO);
    }


    // 新

    /**
     * 根据条件：获取品类品种分页
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryCategoryDTOPage")
    Result<CategoryDTO> queryCategoryDTOPage(@RequestBody QueryDTO<CategoryQO> queryDTO) {
        return Result.page(categoryFacade.queryCategoryDTOPage(queryDTO));
    }

    /**
     * 根据条件：获取品类品种列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/queryCategoryDTOList")
    Result<List<CategoryDTO>> queryCategoryDTOList(@RequestBody CategoryQO condition) {
        return Result.success(categoryFacade.queryCategoryDTOList(condition));
    }

    /**
     * 根据ID：获取品类品种
     *
     * @param id
     * @return
     */
    @GetMapping("/getCategoryDTOById")
    Result<CategoryDTO> getCategoryById(@RequestParam(value = "id") Integer id) {
        return Result.success(categoryFacade.getCategoryDTOById(id));
    }

    /**
     * 根据主编码：获取品类品种
     *
     * @param serialNo
     * @return
     */
    @GetMapping("/getCategoryDTOBySerialNo")
    Result<CategoryDTO> getCategoryDTOBySerialNo(@RequestParam(value = "serialNo") Integer serialNo) {
        return Result.success(categoryFacade.getCategoryDTOBySerialNo(serialNo));
    }

    /**
     * 新增：品类品种
     *
     * @param addDTO
     * @return
     */
    @PostMapping("/addCategory")
    Result addCategory(@RequestBody CategoryAddDTO addDTO) {
        return categoryFacade.addCategory(addDTO);
    }

    /**
     * 更新：品类品种
     *
     * @param updateDTO
     * @return
     */
    @PostMapping("/updateCategoryStatus")
    Result updateCategoryStatus(@RequestBody CategoryUpdateDTO updateDTO) {
        return categoryFacade.updateCategoryStatus(updateDTO);
    }

    /**
     * 获取菜单结构
     *
     * @return
     */
    @PostMapping("/queryCategoryMenu")
    Result<List<Tree<Integer>>> queryCategoryMenu() {
        return Result.success(categoryFacade.queryCategoryMenu(1, null));
    }

    /**
     * 获取树形结构
     *
     * @return
     */
    @PostMapping("/queryCategoryTree")
    Result<List<Tree<Integer>>> queryCategoryTree() {
        return Result.success(categoryFacade.queryCategoryTree());
    }
}
