package com.navigator.magellan.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.DepositRuleFacade;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.facade.magellan.EmployPermissionFacade;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.dto.systemrule.DepositRuleDTO;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.StringUtil;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.pigeon.facade.LkgContractDailyCheckFacade;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.trade.facade.ContractExportFacade;
import com.navigator.trade.facade.ContractFacade;
import com.navigator.trade.facade.ContractPaperFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.bo.ContractBO;
import com.navigator.trade.pojo.dto.QueryContractDTO;
import com.navigator.trade.pojo.dto.contract.*;
import com.navigator.trade.pojo.dto.future.ConfirmPriceDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import com.navigator.trade.pojo.entity.ContractVOEntity;
import com.navigator.trade.pojo.enums.ContractStructurePricingStatusEnum;
import com.navigator.trade.pojo.qo.ContractQO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * Description: No Description
 * Created by YuYong on 2021/12/3 13:57
 */

@RestController
@RequestMapping("/contract")
public class ContractController {

    @Autowired
    private ContractFacade contractFacade;
    @Autowired
    private ContractPaperFacade contractPaperFacade;
    @Autowired
    private OperationLogFacade operationLogFacade;
    @Autowired
    private DepositRuleFacade depositRuleFacade;
    @Autowired
    private TradeTicketFacade tradeTicketFacade;
    @Resource
    private PriceApplyFacade priceApplyFacade;
    @Resource
    private LkgContractFacade lkgContractFacade;
    @Resource
    private LkgContractDailyCheckFacade lkgContractDailyCheckFacade;
    @Autowired
    private EmployPermissionFacade employPermissionFacade;
    @Autowired
    private ContractExportFacade contractExportFacade;
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private PayConditionFacade payConditionFacade;

    /**
     * 支持现货|仓单查询
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContract")
    public Result queryContract(@RequestBody QueryDTO<ContractQO> queryDTO) {
        ContractQO contractQO = queryDTO.getCondition();
        contractQO.setTriggerSys(SystemEnum.MAGELLAN.getName());
        return contractFacade.queryContract(queryDTO);
    }

    /**
     * 根据合同Id获取合同详情 | 现货 | 仓单
     *
     * @param contractId
     * @return
     */
    // add required parameter for NumberFormatException issue by Jason Shi at 2025-05-28 start
    @GetMapping("/getContractByContractId")
    public Result getContractByContractId(@RequestParam(value = "contractId", required = true) String contractId) {
        return contractFacade.getContractByContractId(contractId);
    }
    // add required parameter for NumberFormatException issue by Jason Shi at 2025-05-28 end

    /**
     * 根据合同Code获取合同详情
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractByContractCode")
    public Result getContractByContractCode(@RequestParam("contractCode") String contractCode) {
        return contractFacade.getContractByContractCode(contractCode);
    }

    /**
     * 根据合同Code获取合同Id
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractIdByCode")
    public Result getContractIdByCode(@RequestParam("contractCode") String contractCode) {
        return contractFacade.getContractIdByCode(contractCode);
    }

    /**
     * 获取合同正本
     * @param contractSignId
     * @return
     */
    @GetMapping("/getContractPaper")
    public Result getContractPaper(@RequestParam("contractSignId") Integer contractSignId) {
        return contractPaperFacade.getContractPaper(contractSignId);
    }

    /**
     * 保存合同正本
     * @param contractPaperDTO
     * @return
     */
    @PostMapping("/saveContractPaper")
    public Result saveContractPaper(@RequestBody ContractPaperDTO contractPaperDTO) {
        return Result.success(contractPaperFacade.saveContractPaper(contractPaperDTO));
    }

    /**
     * 确认合规
     *
     * @param contractBaseDTO
     * @return
     */
    @PostMapping("/confirm")
    public Result confirmContract(@RequestBody ContractBaseDTO contractBaseDTO) {
        try {
            contractFacade.confirmContract(contractBaseDTO);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResultCodeEnum.HTTP_REQUEST_TIMEOUT);
        }
        return Result.success();
    }


    /**
     * 根据条件查询结构化定价合同
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractStructure")
    public Result queryContractStructure(@RequestBody QueryDTO<ContractBO> queryDTO) {
        return contractFacade.queryContractStructure(queryDTO);
    }


    @GetMapping("/updateStructureContractPricingStatus")
    public Result updateStructureContractPricingStatus(@RequestParam(value = "contractStructureId") Integer contractStructureId) {

        ContractStructureDTO contractStructureDTO = new ContractStructureDTO();
        contractStructureDTO.setId(contractStructureId)
                .setPriceStatus(ContractStructurePricingStatusEnum.CANCELED.getValue());

        return Result.success(contractFacade.updateStructureContractPricingStatus(contractStructureDTO));
    }

    /**
     * 补充合同信息
     *
     * @param contractBaseDTO
     * @return
     */
    @PostMapping("/fill")
    public Result fillContract(@RequestBody ContractBaseDTO contractBaseDTO) {
        return contractFacade.fillContract(contractBaseDTO);
    }

    /**
     * 查询合同是否可签章
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/canSign/{contractId}")
    public Result canSign(@PathVariable("contractId") String contractId) {
        return contractFacade.canSign(contractId);
    }

    /**
     * 根据合同Id获取电子合同
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractPdfs")
    public Result getContractPdfs(@RequestParam("contractId") String contractId) {
        return contractFacade.getContractPdfs(contractId);
    }

    /**
     * 获取合同操作日志
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/getContractOperationLogs")
    public Result getContractOperationLogs(@RequestParam("contractCode") String contractCode, @RequestParam("id") Integer id) {
        return operationLogFacade.queryOperationLogByCode(contractCode, OperationSourceEnum.EMPLOYEE.getValue(), id);
    }

    /**
     * 获取合同操作日志
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getOperationlogs")
    public Result getOperationlogs(@RequestParam("contractId") Integer contractId) {
        return operationLogFacade.queryContractOperationLog(contractId, OperationSourceEnum.CUSTOMER_OR_EMPLOYEE.getValue());
    }

    /**
     * 获取合同含税单价详细
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractUnitPriceDetail")
    public Result getContractUnitPriceDetail(@RequestParam("contractId") String contractId) {
        return contractFacade.getContractUnitPriceDetail(contractId);
    }

    /**
     * 获取保证金规则
     *
     * @param depositRuleDTO
     * @return
     */
    @PostMapping("/findDepositRule")
    Result findDepositRule(@RequestBody DepositRuleDTO depositRuleDTO) {
        return depositRuleFacade.findDepositRule(depositRuleDTO);
    }


    /**
     * 根据合约号获取当前客户可以分配的合同
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContractsByDomainCode")
    public Result queryContractsByDomainCode(@RequestBody QueryDTO<QueryContractDTO> queryDTO) {
        return contractFacade.pageContractsByDomainCode(queryDTO);
    }

    /**
     * 暂定价合同定价（生成定价单）TODO 需要迁移到TT域里面
     *
     * @param confirmPriceDTO
     * @return
     */
    @PostMapping("/createTtPrice")
    public Result createTtPrice(@RequestBody ConfirmPriceDTO confirmPriceDTO) {
        return contractFacade.createTtPrice(confirmPriceDTO);
    }

    /**
     * 根据合约查询出当前客户的合同信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/futureContracts")
    public Result futureContracts(@RequestBody QueryDTO<ContractFuturesDTO> queryDTO) {
        return contractFacade.futureContracts(queryDTO);
    }

    /**
     * 根据合同号查询修改记录
     *
     * @param contractCode
     * @return
     */
    @GetMapping("/queryModifyLog")
    public Result queryModifyLog(@RequestParam("contractCode") String contractCode) {
        return tradeTicketFacade.queryModifyLog(contractCode);
    }

    /**
     * 修改合同
     *
     * @param contractModifyDTO 合同修改DTO
     * @return
     */
    @PostMapping("/revise")
    public Result reviseContract(@RequestBody ContractModifyDTO contractModifyDTO) {
        return contractFacade.reviseContract(contractModifyDTO);
    }

    /**
     * 拆分合同
     *
     * @param contractModifyDTO
     * @return
     */
    @PostMapping("/split")
    public Result splitContract(@RequestBody ContractModifyDTO contractModifyDTO) {
        return contractFacade.splitContract(contractModifyDTO);
    }

    /**
     * 查询合同的拆分和修改的记录
     *
     * @param modifyDTO
     * @return
     */
    @PostMapping("/getModifyLog")
    public Result getContractModifyLog(@RequestBody ContractModifyDTO modifyDTO) {
        return contractFacade.getContractModifyLog(modifyDTO);
    }

    /**
     * 获取合同变更所需要的数量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getModifyNumInfo")
    public Result getContractModifyNumInfo(@RequestParam("contractId") Integer contractId) {
        return contractFacade.getContractModifyNumInfo(contractId);
    }

    /**
     * 获取合同变更所需要的数量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getConfirmPriceList")
    public Result getConfirmPriceList(@RequestParam("contractId") Integer contractId) {
        return contractFacade.getConfirmPriceList(contractId);
    }

    @GetMapping("/getStructurePriceDealDetailList")
    public Result getStructurePriceDealDetailList(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceDealDetail(priceApplyId);
    }

    @GetMapping("/getStructurePriceDealInfoList")
    public Result getStructurePriceDealInfoList(@RequestParam("priceApplyId") Integer priceApplyId) {
        return priceApplyFacade.queryPriceDealInfo(priceApplyId);
    }

    /**
     * 销售合同回购
     *
     * @param contractBuyBackDTO
     * @return
     */
    @PostMapping("/applyBuyBack")
    public Result applyBuyBack(@RequestBody ContractBuyBackDTO contractBuyBackDTO) {
        return contractFacade.applyBuyBack(contractBuyBackDTO);
    }

    /**
     * 解约定赔
     *
     * @param contractWashOutDTO
     * @return
     */
    @PostMapping("/applyWashOut")
    public Result applyWashOut(@RequestBody ContractWashOutDTO contractWashOutDTO) {
        return contractFacade.applyWashOut(contractWashOutDTO);
    }

    /**
     * 仓单合同注销
     *
     * @param contractWriteOffDTO
     * @return
     */
    @PostMapping("/applyWriteOff")
    public Result applyWriteOff(@RequestBody ContractWriteOffDTO contractWriteOffDTO) {
        return contractFacade.applyWriteOff(contractWriteOffDTO);
    }

    /**
     * 仓单合同豆二注销
     *
     * @param contractWriteOffOMDTO
     * @return
     */
    @PostMapping("/applyWriteOffOM")
    Result applySoyBean2WriteOff(@RequestBody ContractWriteOffOMDTO contractWriteOffOMDTO){
        System.out.println(999999);
        return contractFacade.applySoyBean2WriteOff(contractWriteOffOMDTO);
    }

    /**
     * 仓单合同注销撤回
     * @param contractWriteOffWithDrawDTO
     * @return
     */
    @PostMapping("/applyWriteOffWithDraw")
    public Result applyWriteOffWithDraw(@RequestBody ContractWriteOffWithDrawDTO contractWriteOffWithDrawDTO){
        return contractFacade.applyWriteOffWithDraw(contractWriteOffWithDrawDTO);
    }

    /**
     * 仓单合同作废
     * @param contractId
     * @return
     */
    @GetMapping("/applyWarrantContractInvalid")
    public Result applyWarrantContractInvalid(@RequestParam("contractId") Integer contractId){
        return contractFacade.applyWarrantContractInvalid(contractId);
    }

    /**
     * 合同关闭
     *
     * @param contractId
     * @return
     */
    @GetMapping("/applyClosed")
    public Result applyClosed(@RequestParam("contractId") Integer contractId) {
        return contractFacade.applyClosed(contractId);
    }

    /**
     * 合同作废
     *
     * @param contractId
     * @return
     */
    @GetMapping("/applyInvalid")
    public Result applyInvalid(@RequestParam("contractId") Integer contractId) {
        return contractFacade.applyInvalid(contractId);
    }

    /**
     * 判断合同是否有申请中的申请单
     *
     * @param contractId
     * @return
     */
    @GetMapping("/judgeExistApply")
    public Result judgeExistApply(@RequestParam("contractId") Integer contractId) {
        return priceApplyFacade.getNotDealByContractId(contractId);
    }

    /**
     * 判断是否能编辑货品
     *
     * @param contractId
     * @return
     */
    @GetMapping("/judgeCanModifyGoods")
    public Result judgeCanModifyGoods(@RequestParam("contractId") Integer contractId) {
        return contractFacade.judgeCanModifyGoods(contractId);
    }

    @GetMapping("/getContractStructureById")
    public Result getContractStructureById(@RequestParam("contractId") Integer contractId) {
        return Result.success(contractFacade.getContractStructureVOById(contractId));
    }

    /**
     * 神奇博士
     *
     * @param method
     * @param condition
     * @return
     */
    @GetMapping("/doctorm")
    public Result doctorM(@RequestParam("m") String method, @RequestParam("c") String condition) {
        System.out.println("===================ContractController.resync====================");
        System.out.println(method + "," + condition);

        if ("resync".equals(method)) {
            Integer reqId = Integer.valueOf(condition);
            return lkgContractFacade.reSyncContractRequest(reqId);
        } else if ("sync".equals(method)) {
            Integer reqId = Integer.valueOf(condition);
            return lkgContractFacade.reBuildSyncContractRequest(reqId);
        } else if ("sendmail".equals(method)) {

        } else if ("addroles".equals(method)) {
            List<String> ls = StringUtil.split(condition, ";");
            Integer employId = new Integer(ls.get(0));
            String roleIds = ls.get(1);
            Integer roleDefId = new Integer(ls.get(2));
            Integer categoryId = new Integer(ls.get(3));
            String customerIds = ls.get(4);

            if ("0".equals(roleIds)) {
                roleIds = "";
            }

            employPermissionFacade.addEmployRoles(employId, roleIds, roleDefId, categoryId, customerIds);
        }

        return Result.success("no process：" + method + "," + condition);
    }

    /**
     * 获取Lkg合同的合同详情
     *
     * @return
     */
    @GetMapping("/getLkgContractInfo")
    public Result getLkgContractInfo(@RequestParam("contractCode") String contractCode) {
        return lkgContractFacade.getLkgContract(contractCode);
    }

    /**
     * 导入lkg合同数据
     *
     * @param file
     * @param salesType
     * @return
     */
    @PostMapping("/importContract")
    Result importEmploy(@RequestParam(value = "file") MultipartFile file,
                        @RequestParam(value = "salesType") Integer salesType) {
        return lkgContractFacade.importContract(file, salesType);
    }

    /**
     * 导入的数据同步到PrePare
     *
     * @return
     */
    @GetMapping("/syncPrepareContract")
    public Result syncPrepareContract() {
        return lkgContractFacade.syncPrepareContract();
    }

    /**
     * prepare到contract
     *
     * @return
     */
    @GetMapping("/syncContract")
    public Result syncContract() {
        return lkgContractFacade.syncContract();
    }

    /**
     * 合同校核
     *
     * @return
     */
    @GetMapping("/dailyCheck")
    public Result dailyCheck() {
        return lkgContractDailyCheckFacade.dailyCheck();
    }

    /**
     * 合同报表导出
     *
     * @param contractBO
     */
    @PostMapping("/exportContractExcel")
    public void exportContractExcel(@RequestBody ContractBO contractBO) {
        List<ContractVOEntity> contractVOList = new ArrayList<>();

        Result result = contractExportFacade.exportContractExcel(contractBO);
        if (result.isSuccess()) {
            contractVOList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractVOEntity.class);
        }

        if (CollectionUtil.isEmpty(contractVOList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "合同管理" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        EasyPoiUtils.exportExcel(contractVOList, null, "合同管理", ContractVOEntity.class, fileName, response);
        //return Result.success();
    }

    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 start
    /**
     * 合同报表导出（支持多选状态）
     *
     * @param contractBO
     */
    @PostMapping("/exportContractExcelMultiStatus")
    public void exportContractExcelMultiStatus(@RequestBody ContractBO contractBO) {
        List<ContractVOEntity> contractVOList = new ArrayList<>();

        Result result = contractExportFacade.exportContractExcelMultiStatus(contractBO);
        if (result.isSuccess()) {
            contractVOList = JSON.parseArray(JSON.toJSONString(result.getData()), ContractVOEntity.class);
        }

        if (CollectionUtil.isEmpty(contractVOList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "合同管理" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        EasyPoiUtils.exportExcel(contractVOList, null, "合同管理", ContractVOEntity.class, fileName, response);
    }
    // 1003312 界面优化-报表中心 changed by Jason Shi at 2025-7-1 end

    @PostMapping("/getPayCondition")
    public Result queryPayCondition(@RequestBody PayConditionDTO payConditionDTO) {
        return payConditionFacade.queryPayCondition(payConditionDTO);
    }

    /**
     * 默认合同关闭，传状态按传的合同号批量更新合同状态
     *
     * @param uploadFile
     * @return
     */
    @PostMapping("/updateContractStatus")
    public Result updateContractStatus(@RequestParam(value = "file") MultipartFile uploadFile, @RequestParam(value = "status", required = false) Integer status) {
        return contractFacade.updateContractStatus(uploadFile, status);
    }

    @GetMapping("/getContractTraceList")
    public Result getContractTraceList(@RequestParam("contractId") Integer contractId) {
        return contractFacade.getContractTraceList(contractId);
    }

    /**
     * 根据合同id关闭合同的尾量
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/closeTailNumByContractId")
    Result closeTailNumByContractId(@RequestParam(value = "contractId") Integer contractId) {
        return contractFacade.closeTailNumByContractId(contractId, SystemEnum.MAGELLAN.getValue());
    }

    /**
     * 根据多选合同关闭合同的尾量
     *
     * @param contractIds 合同id集合
     * @return
     */
    @GetMapping("/batchCloseTailNum")
    Result batchCloseTailNum(@RequestParam(value = "contractIds") List<Integer> contractIds) {
        return contractFacade.batchCloseTailNum(contractIds, SystemEnum.MAGELLAN.getValue());
    }

    /**
     * 取消尾量关闭
     *
     * @param contractId 合同id
     * @return
     */
    @GetMapping("/cancelCloseTailNumByContractId")
    Result cancelCloseTailNumByContractId(@RequestParam(value = "contractId") Integer contractId) {
        return contractFacade.cancelCloseTailNumByContractId(contractId);
    }

    /**
     * 批量取消关闭尾数
     *
     * @param contractIds 合同id集合
     * @return
     */
    @GetMapping("/batchCancelCloseTailNum")
    Result batchCancelCloseTailNum(@RequestParam(value = "contractIds") List<Integer> contractIds) {
        return contractFacade.batchCancelCloseTailNum(contractIds);
    }

    /**
     * 根据合同id查询合同的尾量关闭记录
     *
     * @param contractCode 合同id
     * @return
     */
    @GetMapping("/getCloseTailNumRecord")
    Result getCloseTailNumRecord(@RequestParam(value = "contractCode") String contractCode) {
        return contractFacade.getCloseTailNumRecord(contractCode);
    }

}
