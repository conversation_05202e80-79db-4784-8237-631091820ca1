package com.navigator.magellan.web.schedule;

import com.navigator.trade.pojo.entity.ContractSignEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class SampleSignScheduler extends BaseMagellanScheduleService<ContractSignEntity> {

    /**
     *     @Resource
     *     ContractSignFacade contractSignFacade;
     *     说明：真正处理的服务
     */


    /**
     * 获取需要处理的数据
     * @param batchRecordCount
     * @return
     */
    @Override
    protected List<ContractSignEntity> fetchList(int batchRecordCount) {
        List<ContractSignEntity> list = new ArrayList<>();

        list.add(new ContractSignEntity());
        //TODO 获取想要处理的数据
        return list;
    }

    /**
     * 处理单条数据
     * @param item
     */
    @Override
    protected void process(ContractSignEntity item) {
        //单条数据处理逻辑
    }
}
