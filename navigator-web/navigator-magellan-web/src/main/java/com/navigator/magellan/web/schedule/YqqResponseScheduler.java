package com.navigator.magellan.web.schedule;

import com.navigator.sparrow.facade.YqqTaskRecordFacade;
import com.navigator.sparrow.pojo.entity.YqqTaskRecordEntity;
import com.navigator.sparrow.pojo.enums.DisposeNodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/29 13:38
 */

@Service
@Slf4j
public class YqqResponseScheduler extends BaseMagellanScheduleService<YqqTaskRecordEntity> {


    @Resource
    private YqqTaskRecordFacade yqqTaskRecordFacade;

    /**
     * 获取需要处理的数据
     *
     * @param batchRecordCount
     * @return
     */
    @Override
    protected List<YqqTaskRecordEntity> fetchList(int batchRecordCount) {

        List<YqqTaskRecordEntity> yqqTaskRecordEntities = yqqTaskRecordFacade.unfinishedYqqTaskRecord();

        return yqqTaskRecordEntities;
    }

    /**
     * 处理单条数据
     *
     * @param item
     */
    @Override
    protected void process(YqqTaskRecordEntity item) {

        DisposeNodeEnum disposeNodeEnum = DisposeNodeEnum.getByValue(item.getDisposeNode());


        switch (disposeNodeEnum) {
            //新数据
            case SAVE_DATA:
                if (null != item.getBizId()) {
                    //保存进入blob中
                    yqqTaskRecordFacade.yqqFileSaveBlob(item.getBizId());
                    //绑定文件关系
                    yqqTaskRecordFacade.bindingSignFileRelation(item.getBizId());
                    //发送邮件
                    yqqTaskRecordFacade.callbackAfterProcess(item.getBizId());
                }
                break;
            //下载文件未保存
            case FILE_SAVE_BLOB:
                if (null != item.getBizId()) {
                    //绑定文件关系
                    yqqTaskRecordFacade.bindingSignFileRelation(item.getBizId());
                    //发送邮件
                    yqqTaskRecordFacade.callbackAfterProcess(item.getBizId());
                }
                break;
            //保存后未发送邮件
            case BINDING_SIGN_FILE_RELATION:
                if (null != item.getBizId()) {
                    //发送邮件
                    yqqTaskRecordFacade.callbackAfterProcess(item.getBizId());
                }
                break;
            //文件存储中
            case FILE_SAVE_BLOB_EXECUTORY:

                break;
        }
    }
}
