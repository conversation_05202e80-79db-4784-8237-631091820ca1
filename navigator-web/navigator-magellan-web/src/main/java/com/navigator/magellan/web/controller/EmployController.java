package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.EmployBusinessDTO;
import com.navigator.admin.pojo.dto.EmployDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.admin.pojo.vo.EmployDetailVO;
import com.navigator.admin.pojo.vo.EmployVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/employ")
public class EmployController {

    @Autowired
    private EmployFacade employFacade;

    /**
     * 保存员工信息 magellen
     *
     * @param employBusinessDTO
     * @return
     */
    @PostMapping("/modifyEmploy")
    public Result modifyEmploy(@RequestBody @Valid EmployBusinessDTO employBusinessDTO) {
        Result result = employFacade.modifyEmploy(employBusinessDTO);
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            return Result.success();
        }
        return Result.failure(null == result ? "" : result.getMessage());
    }


    /**
     * 查詢员工信息
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryEmployList")
    Result queryEmployList(@RequestBody QueryDTO<EmployDTO> queryDTO) {
        return employFacade.queryEmployList(queryDTO);
    }


    /**
     * 导出用户角色
     *
     * @param response
     * @return
     */
    @GetMapping("/exportEmployRoleList")
    public void exportEmployRoleList(HttpServletResponse response) {
        Result result = employFacade.exportEmployRoleList();
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<EmployVO> employVOList = JSON.parseArray(JSON.toJSONString(result.getData()), EmployVO.class);
        if (CollectionUtils.isEmpty(employVOList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "EmployRole";
        EasyPoiUtils.exportExcel(employVOList, "EmployRole", "用户信息", EmployVO.class, fileName, response);
    }

    /**
     * 查詢员工详情信息
     *
     * @param employId
     * @return
     */
    @GetMapping("/queryEmployDetail")
    Result queryEmployDetail(@RequestParam("employId") Integer employId) {
        EmployDetailVO employDetailVO = employFacade.queryEmployDetail(employId);
        return Result.success(employDetailVO);
    }

    /**
     * 查詢员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/queryEmployListByRoleDefId")
    Result queryEmployListByRoleDefId(@RequestParam("roleDefId") Integer roleDefId) {
        return employFacade.queryEmployListByRoleDefId(roleDefId);
    }

    /**
     * 查詢可选员工
     *
     * @param roleDefId
     * @return
     */
    @GetMapping("/queryAvailableEmployByRoleDefId")
    Result queryAvailableEmployByRoleDefId(@RequestParam("roleDefId") Integer roleDefId) {
        return employFacade.queryAvailableEmployByRoleDefId(roleDefId);
    }

    /**
     * 查詢已选员工
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryChoosedEmployByRoleId")
    Result queryChoosedEmployByRoleId(@RequestBody QueryDTO<RoleDTO> queryDTO) {
        return employFacade.queryChoosedEmployByRoleId(queryDTO);
    }

    /**
     * 重置客户密码
     *
     * @param employId
     * @return
     */
    @GetMapping("/resetPassword")
    public Result resetPassword(@RequestParam("employId") Integer employId) {
        String password = employFacade.resetPassword(employId);
        return Result.success(password);
    }

    /**
     * 查詢品类和主体列表
     *
     * @param
     * @return
     */
    @GetMapping("/queryCategoryFactoryByRole")
    Result queryCategoryFactoryByRole() {
        return employFacade.queryCategoryFactoryByRole();
    }

    /**
     * 更改状态
     *
     * @param
     * @return
     */
    @PostMapping("/updateEmployStatus")
    Result updateEmployStatus(@RequestBody EmployDTO employDTO) {
        return employFacade.updateEmployStatus(employDTO);
    }

//----------------原始接口迁移

    /**
     * 获取达孚商务
     */
    @GetMapping("/getBusinessPeople")
    public Result getBusinessPeople(@RequestParam("roleName") String roleName) {
        List<EmployEntity> businessPeopleList = employFacade.getEmployByRoleName(roleName);
        return Result.success(businessPeopleList);
    }


    /**
     * 重置客户密码
     *
     * @param employId
     * @return
     */
    @GetMapping("/updateEmployResetPassword")
    public Result updateEmployResetPassword(@RequestParam("employId") Integer employId) {
        return employFacade.updateEmployResetPassword(employId);
    }

    //--------------------------

    @PostMapping("/importEmploy")
    Result importEmploy(@RequestParam("file") MultipartFile file){
        return employFacade.importEmploy(file);
    }

    @PostMapping("/saveOrUpdateEmployByFile")
    Result saveOrUpdateEmployByFile(@RequestParam("file") MultipartFile file){
        return employFacade.saveOrUpdateEmployByFile(file);
    }

    /**
     * 创建员工 magellen
     *
     * @param employBusinessDTO
     * @return
     */
    @PostMapping("/createEmploy")
    public Result createEmploy(@RequestBody @Valid EmployBusinessDTO employBusinessDTO) {
        Result result = employFacade.createEmploy(employBusinessDTO);
        if (result != null && result.getCode() == ResultCodeEnum.OK.getCode()) {
            return Result.success();
        }
        return Result.failure(null == result ? "" : result.getMessage());
    }

}
