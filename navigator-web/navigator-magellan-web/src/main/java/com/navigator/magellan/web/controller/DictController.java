package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.DictFacade;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021-12-01 11:51
// */
//@RestController
@RequestMapping("/dict")
public class DictController {

    @Resource
    private DictFacade dictFacade;

//    @GetMapping("/getDiceListByCode")
//    public Result getDiceListByCode(@RequestParam(value = "bizModuleCode") String bizModuleCode) {
//        return Result.success(dictFacade.getDiceListByCode(bizModuleCode));
//    }
//
//    @GetMapping("/getCommonDictInfo")
//    public Result getCommonDictInfo() {
//        return Result.success(dictFacade.getCommonDictInfo());
//    }
}
