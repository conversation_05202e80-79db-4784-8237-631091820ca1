package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.future.facade.PositionFacade;
import com.navigator.future.pojo.dto.PositionDTO;
import com.navigator.future.pojo.dto.PositionQueryDTO;
import com.navigator.future.pojo.vo.PositionVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/position")
public class PositionController {
    @Autowired
    private PositionFacade positionFacade;
    @Autowired
    private HttpServletResponse response;

    @PostMapping("/save")
    public Result save(@RequestBody PositionDTO positionDTO) {
        return positionFacade.save(positionDTO);
    }

    @PostMapping("/queryPositionList")
    public Result queryPositionList(@RequestBody QueryDTO<PositionQueryDTO> positionQueryDTO) {
        return positionFacade.queryPositionList(positionQueryDTO);
    }

    @GetMapping("/queryPositionDetail")
    public Result queryPositionDetail(@RequestParam("id") Integer id) {
        return positionFacade.queryPositionDetail(id);
    }

    @PostMapping("/updatePositionStatus")
    public Result updatePositionStatus(@RequestBody PositionDTO positionDTO) {
        return positionFacade.updatePositionStatus(positionDTO);
    }

    @PostMapping("/dealBatch")
    public Result dealBatch(@RequestBody PositionDTO positionDTO) {
        return positionFacade.dealBatch(positionDTO);
    }

    /**
     * 下载申请单信息
     *
     * @param response
     * @return
     */
    @GetMapping("/export")
    public Result export(@RequestParam("categoryId") Integer categoryId, HttpServletResponse response) {
        Result result = positionFacade.export(categoryId);
        if (!result.isSuccess()) {
            return Result.failure("系统错误,稍后再试");
        }
        List<PositionVO> positionVOList = JSON.parseArray(JSON.toJSONString(result.getData()), PositionVO.class);
        if (CollectionUtils.isEmpty(positionVOList)) {
            return Result.failure("无可导出数据！");
        }
        String fileName = "申请挂单信息";
        EasyPoiUtils.exportExcel(positionVOList, "申请挂单信息", "申请挂单信息", PositionVO.class, fileName, response);
        return Result.success();
    }

    /**
     * 校验申请单上传数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/check", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result check(@RequestPart("file") MultipartFile file) {
        return positionFacade.check(file);
    }

    /**
     * 上传成交数据
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/importFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result importFile(@RequestPart("file") MultipartFile file) {
        return positionFacade.importFile(file);
    }


    /**
     * 导出客户
     *
     * @param positionQueryDTO
     * @return
     */
    @PostMapping("/exportSubmitList")
    public void exportSubmitList(@RequestBody PositionQueryDTO positionQueryDTO) {
        Result result = positionFacade.exportSubmitList(positionQueryDTO);
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<PositionVO> positionVOList = JSON.parseArray(JSON.toJSONString(result.getData()), PositionVO.class);
        if (CollectionUtils.isEmpty(positionVOList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "exportSubmitList";
        EasyPoiUtils.exportExcel(positionVOList, null, "exportSubmitList", PositionVO.class, fileName, response);
    }

    /**
     * 查询刷新记录
     *
     * @param positionId
     * @return
     */
    @GetMapping("/queryPositionRefreshLog")
    public Result queryPositionRefreshLog(@RequestParam("positionId") Integer positionId) {
        return positionFacade.queryPositionRefreshLog(positionId);
    }

    @PostMapping("/modify")
    public Result modify(@RequestBody PositionDTO positionDTO) {
        return positionFacade.modify(positionDTO);
    }


    @PostMapping("/cancel")
    public Result cancel(@RequestBody PositionDTO positionDTO) {
        return positionFacade.cancel(positionDTO);
    }

    @GetMapping("/queryModifyLog")
    public Result queryModifyLog(@RequestParam("positionId") Integer positionId) {
        return positionFacade.queryModifyLog(positionId);
    }
}
