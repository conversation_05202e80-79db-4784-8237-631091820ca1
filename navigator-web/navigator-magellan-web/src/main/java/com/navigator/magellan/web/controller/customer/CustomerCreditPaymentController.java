package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerCreditPaymentFacade;
import com.navigator.customer.pojo.dto.CustomerCreditPaymentDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/15 16:53
 */
@RestController
@RequestMapping("/customerCreditPayment")
public class CustomerCreditPaymentController {

    @Resource
    CustomerCreditPaymentFacade customerCreditPaymentFacade;

    /**
     * 查询客户赊销&预付款信息
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/customerCreditPaymentAllList")
    public Result customerCreditPaymentAllList(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return customerCreditPaymentFacade.customerCreditPaymentAllList(customerCreditPaymentDTO);
    }

    /**
     * 修改客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/updateCustomerCreditPayment")
    public Result updateCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return customerCreditPaymentFacade.updateCustomerCreditPayment(customerCreditPaymentDTO);
    }

    /**
     * 添加客户赊销&预付款状态
     *
     * @param customerCreditPaymentDTO
     * @return
     */
    @PostMapping("/addCustomerCreditPayment")
    public Result addCustomerCreditPayment(@RequestBody CustomerCreditPaymentDTO customerCreditPaymentDTO) {
        return customerCreditPaymentFacade.addCustomerCreditPayment(customerCreditPaymentDTO);
    }
}
