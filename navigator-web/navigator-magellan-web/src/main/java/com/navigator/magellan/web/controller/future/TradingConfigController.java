package com.navigator.magellan.web.controller.future;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.TradingConfigFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/22
 */
@RestController
@RequestMapping("/future")
public class TradingConfigController {

    @Resource
    private TradingConfigFacade tradingConfigFacade;

    @RequestMapping("/getDomainTypeByCategoryCode")
    public Result getDomainTypeByCategoryCode(@RequestParam("domainType") String domainType) {
        return Result.success(tradingConfigFacade.getDomainTypeByCategoryCode(domainType));
    }

    @RequestMapping("/queryTradingConfigList")
    public Result queryTradingConfigList() {
        return tradingConfigFacade.queryTradingConfigList();
    }

    /**
     * 获取二级品类下的合约
     *
     * @param category2
     * @return
     */
    @GetMapping("/getDomainTypeByCategory2")
    public Result getDomainTypeByCategory2(@RequestParam(value = "category2")String category2){
        return tradingConfigFacade.getDomainTypeByCategory2(category2);
    }
}
