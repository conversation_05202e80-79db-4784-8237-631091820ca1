package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.customer.facade.CustomerDetailFacade;
import com.navigator.customer.pojo.bo.CustomerDetailBO;
import com.navigator.customer.pojo.dto.CustomerDTO;
import com.navigator.customer.pojo.dto.CustomerDetailDTO;
import com.navigator.customer.pojo.dto.CustomerTemplateDeriveGradeScoreDTO;
import com.navigator.customer.pojo.vo.CustomerGradeScoreExcelVO;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;
import com.navigator.customer.pojo.entity.CustomerDetailEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 11:42
 */
@RestController
@RequestMapping("/customerDetail")
public class CustomerDetailController {

    @Resource
    private CustomerDetailFacade customerDetailFacade;

    /**
     * 根据客户和品类查询客户配置
     *
     * @param customerId
     * @param categoryId
     * @return
     */
    @GetMapping("/queryCustomerDetailList")
    public Result queryCustomerDetailList(@RequestParam(value = "customerId") Integer customerId,
                                         @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(customerDetailFacade.queryCustomerDetailList(customerId,categoryId));
    }

    @PostMapping("/queryCustomerDetailListByCondition")
    public Result queryCustomerDetailListByCondition(@RequestBody CustomerDetailBO customerDetailBO){
        return Result.success(customerDetailFacade.queryCustomerDetailListByCondition(customerDetailBO));
    }

    /**
     * 新增客户配置
     *
     * @param customerDetailDTO
     * @return
     */
    @PostMapping("/saveCustomerDetail")
    public Result saveCustomerDetail(@RequestBody CustomerDetailDTO customerDetailDTO) {
        return Result.success(customerDetailFacade.saveCustomerDetail(customerDetailDTO));
    }

    /**
     * 修改客户配置信息
     *
     * @param customerDetailBO
     * @return
     */
    @PostMapping("/updateCustomerDetail")
    public Result updateCustomerDetail(@RequestBody CustomerDetailBO customerDetailBO) {
        return Result.success(customerDetailFacade.updateCustomerDetail(customerDetailBO));
    }

    @PostMapping("/queryCustomerInvoiceList")
    public Result queryCustomerInvoiceList(@RequestParam(value = "customerId") Integer customerId
            , @RequestParam(value = "categoryId")  Integer categoryId) {
        List<CustomerDetailEntity> customerDetailEntityList = customerDetailFacade.queryCustomerInvoiceList(customerId, categoryId);
        return Result.success(customerDetailEntityList);
    }

    @PostMapping("/saveCustomerInvoice")
    public Result saveCustomerInvoice(@RequestBody CustomerDetailBO customerDetailBO) {
        return customerDetailFacade.saveCustomerInvoice(customerDetailBO);
    }

    @PostMapping("/modifyCustomerInvoice")
    public Result modifyCustomerInvoice(@RequestBody CustomerDetailBO customerDetailBO) {
        return customerDetailFacade.modifyCustomerInvoice(customerDetailBO);
    }

    @PostMapping("/queryCustomerDetailEntity")
    public Result queryCustomerDetailEntity(@RequestParam(value = "customerId") Integer customerId,
                                            @RequestParam(value = "categoryId") Integer categoryId) {
        CustomerDetailEntity customerDetailEntity = customerDetailFacade.queryCustomerDetailEntity(customerId, categoryId);
        return Result.success(customerDetailEntity);

    }

    @PostMapping("/copyCustomerInvoice")
    public Result copyCustomerInvoice() {
        return customerDetailFacade.copyCustomerInvoice();
    }

    @GetMapping("/downloadGradeScoreTemplate")
    public void downloadGradeScoreTemplate(HttpServletResponse response){
        List<CustomerGradeScoreExcelVO> list = new ArrayList<>();
        //CustomerGradeScoreVO vo = new CustomerGradeScoreVO();
        EasyPoiUtils.exportExcel(list,"评级模板","评级模板", CustomerGradeScoreExcelVO.class,"评级模板",response);
        //return customerDetailUpdateRecordFacade.down
    }

    @PostMapping(value = "/uploadGradeScoreTemplate", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result uploadGradeScoreTemplate(@RequestPart("file") MultipartFile file) {
        return customerDetailFacade.uploadGradeScoreTemplate(file);
    }


    @PostMapping("/exportGradeScoreList")
    public void exportGradeScoreList(@RequestBody CustomerDTO queryDTO,HttpServletResponse response){
        List<CustomerTemplateDeriveGradeScoreDTO> resultList = customerDetailFacade.exportGradeScoreList(queryDTO);
        if (CollectionUtils.isEmpty(resultList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "客户评分信息";
        EasyPoiUtils.exportExcel(resultList, "客户评分信息", "客户评分信息", CustomerTemplateDeriveGradeScoreDTO.class, fileName, response);
    }

}
