package com.navigator.magellan.web.controller.future;

import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.trade.facade.DomainCodeFacade;
import com.navigator.trade.pojo.bo.QueryDomainPriceBO;
import com.navigator.trade.pojo.dto.future.DomainPriceAuditDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceLeadDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceQueryDTO;
import com.navigator.trade.pojo.dto.future.DomainPriceTodayDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 收盘价上传
 * @date 2021/12/20 12:00
 */
@RestController
@RequestMapping("/domain")
public class DomainController {

    @Resource
    private DomainCodeFacade domainCodeFacade;

    /**
     * 获取收盘价
     *
     * @param categoryId
     * @param name
     * @param categoryCode
     * @return
     */
    @GetMapping("/getClosingPrice")
    public Result getClosingPrice(@RequestParam(value = "categoryId", required = false) Integer categoryId, @RequestParam("name") String name, @RequestParam(value = "categoryCode", required = false) String categoryCode) {
        return domainCodeFacade.getClosingPrice(categoryId, name, categoryCode);
    }

    /**
     * 下载收盘价模板
     *
     * @param response
     * @return
     */
    @GetMapping("/downloadDomainTemplate")
    public Result exportPriceApply(HttpServletResponse response) {
        List<DomainPriceLeadDTO> domainPriceLeadList = new ArrayList<>();
        domainPriceLeadList.add(new DomainPriceLeadDTO()
                .setCategoryName("豆粕")
                .setCategoryCode(GoodsCategoryEnum.OSM_MEAL.getLkgFutureSymbol())
                .setDomainCode("2205")
                .setTradeDate(DateTimeUtil.parseDateString("2022-3-9"))
                .setPrice(new BigDecimal(2350))
        );

        domainPriceLeadList.add(new DomainPriceLeadDTO()
                .setCategoryName("豆油")
                .setCategoryCode(GoodsCategoryEnum.OSM_OIL.getLkgFutureSymbol())
                .setDomainCode("2209")
                .setTradeDate(DateTimeUtil.parseDateString("2022-2-18"))
                .setPrice(new BigDecimal(3750).setScale(0, RoundingMode.HALF_UP))
        );
        String fileName = "收盘价上传模版" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(domainPriceLeadList, "收盘价上传信息", "收盘价上传信息", DomainPriceLeadDTO.class, fileName, response);
        return Result.success();
    }

    /**
     * 预览收盘价信息
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/previewDomainPrice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result previewDomainPrice(@RequestPart("file") MultipartFile file) {
        return Result.success(domainCodeFacade.previewDomainPrice(file));
    }

    /**
     * 上传收盘价信息
     *
     * @param file
     * @return
     */
    @PostMapping(value = "/uploadDomainPrice", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result uploadDomainPrice(@RequestPart("file") MultipartFile file) {
        return domainCodeFacade.uploadDomainPrice(file);
    }

    @PostMapping("/queryDomainPrice")
    public Result queryDomainPrice(@RequestBody QueryDTO<QueryDomainPriceBO> queryDTO) {
        return domainCodeFacade.queryDomainPrice(queryDTO);
    }

    /**
     * 审核收盘价信息
     *
     * @param domainPriceAuditDTO 审核信息
     * @return 审核结果
     */
    @PostMapping("/audit")
    public Result audit(@RequestBody DomainPriceAuditDTO domainPriceAuditDTO) {
        return domainCodeFacade.audit(domainPriceAuditDTO);
    }


    /**
     * 结构化定价收盘价列表
     *
     * @param domainPriceTodayDTO
     * @return 审核结果
     */
    @PostMapping("/queryDomainPriceToday")
    public Result queryDomainPriceToday(@RequestBody DomainPriceTodayDTO domainPriceTodayDTO) {
        return domainCodeFacade.queryDomainPriceToday(domainPriceTodayDTO);
    }

    /**
     * 获取最近的收盘价
     *
     * @param
     * @param
     * @return
     */
    @PostMapping("/getLastestClosingPrice")
    Result getLastestClosingPrice(@RequestBody DomainPriceQueryDTO domainPriceQueryDTO) {
        return domainCodeFacade.getLastestClosingPrice(domainPriceQueryDTO.getCategoryId(), domainPriceQueryDTO.getDomainCode(), domainPriceQueryDTO.getSignDate(),domainPriceQueryDTO.getCategoryCode());
    }
}
