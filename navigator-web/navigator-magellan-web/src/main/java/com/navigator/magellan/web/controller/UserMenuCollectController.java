package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.UserMenuCollectFacade;
import com.navigator.admin.pojo.entity.UserMenuCollectEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-03-11 18:27
 **/

@RestController
@RequestMapping("/menuCollect")
@Slf4j
public class UserMenuCollectController {
    @Autowired
    private UserMenuCollectFacade userMenuCollectFacade;

    @PostMapping("/saveUserMenuCollect")
    public Result saveUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect) {
        return userMenuCollectFacade.saveUserMenuCollect(userMenuCollect.setSystem(SystemEnum.MAGELLAN.getValue()));
    }

    @PostMapping("/updateUserMenuCollect")
    public Result updateUserMenuCollect(@RequestBody UserMenuCollectEntity userMenuCollect) {
        return userMenuCollectFacade.updateUserMenuCollect(userMenuCollect);
    }

    @GetMapping("/deleteCollectMenu")
    public Result deleteCollectMenu(@RequestParam(value = "id") Integer id) {
        return userMenuCollectFacade.deleteCollectMenu(id);
    }

    @GetMapping("/sortCollectMenu")
    public Result sortCollectMenu(@RequestParam(value = "menuCollectIdList") List<Integer> menuCollectIdList) {
        return userMenuCollectFacade.sortCollectMenu(menuCollectIdList);
    }

    @GetMapping("/getUserPowerMenuList")
    public Result getUserPowerMenuList(@RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(userMenuCollectFacade.getUserPowerMenuList(categoryId, null, SystemEnum.MAGELLAN.getValue()));
    }


    @GetMapping("/getUserCollectMenuList")
    public Result getUserCollectMenuList(@RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(userMenuCollectFacade.getUserCollectMenuList(categoryId, null, SystemEnum.MAGELLAN.getValue()));
    }

}
