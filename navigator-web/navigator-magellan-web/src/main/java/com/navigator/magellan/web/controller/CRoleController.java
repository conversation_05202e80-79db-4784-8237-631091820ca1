package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.pojo.dto.RoleAuthDTO;
import com.navigator.admin.pojo.dto.RoleAuthMenuDTO;
import com.navigator.admin.pojo.dto.RoleAuthPowerDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.admin.pojo.qo.RoleAuthMenuQO;
import com.navigator.admin.pojo.qo.RoleAuthPowerQO;
import com.navigator.admin.pojo.qo.RoleAuthQO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedHashSet;

@RestController
@RequestMapping("/crole")
public class CRoleController {
    @Autowired
    private CRoleFacade roleFacade;

    @PostMapping("/queryRoleList")
    public Result queryRoleList(@RequestBody QueryDTO<CRoleQueryDTO> roleQueryDTO) {
        return roleFacade.queryRoleList(roleQueryDTO);
    }


    @PostMapping("/copyPermission")
    public Result copyPermission(@RequestBody RoleDTO roleDTO) {
        return roleFacade.copyPermission(roleDTO);
    }

    /**
     * 根据角色ID：获取全部菜单树及已授权菜单ID列表
     *
     * @param roleAuthMenuQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部菜单树及已授权菜单ID列表")
    @PostMapping("/getRoleAuthMenu")
    Result<RoleAuthMenuDTO> getRoleAuthMenu(@RequestBody RoleAuthMenuQO roleAuthMenuQO) {
        return roleFacade.getRoleAuthMenu(roleAuthMenuQO);
    }

    /**
     * 根据角色ID：获取全部权限树及已授权权限ID列表
     *
     * @param roleAuthPowerQO
     * @return
     */
    @ApiOperation(value = "根据角色ID：获取全部权限树及已授权权限ID列表")
    @PostMapping("/getRoleAuthPower")
    Result<RoleAuthPowerDTO> getRoleAuthPower(@RequestBody RoleAuthPowerQO roleAuthPowerQO) {
        return roleFacade.getRoleAuthPower(roleAuthPowerQO);
    }

}
