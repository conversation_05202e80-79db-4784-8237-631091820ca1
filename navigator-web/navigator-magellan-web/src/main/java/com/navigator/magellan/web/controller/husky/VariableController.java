package com.navigator.magellan.web.controller.husky;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.VariableFacade;
import com.navigator.husky.pojo.entity.VariableEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-07-19 16:58
 **/
@RestController
@RequestMapping("/variable")
public class VariableController {
    @Resource
    private VariableFacade variableFacade;
    @Autowired
    private HttpServletResponse response;

    /**
     * 根据条件查询条件/关键变量
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryVariableByCondition")
    public Result queryVariableByCondition(@RequestBody QueryDTO<VariableEntity> queryDTO) {
        return variableFacade.queryVariableByCondition(queryDTO);
    }

    /**
     * 更新变量信息（设置典型值，修改展示名称）
     *
     * @param variableEntity
     * @return
     */
    @PostMapping("/updateVariable")
    public Result<Boolean> updateVariable(@RequestBody VariableEntity variableEntity) {
        return Result.success(variableFacade.updateVariable(variableEntity));
    }

    /**
     * 获取所有变量信息
     *
     * @return
     */
    @GetMapping("/getAllVariableList")
    public Result getAllVariableList(@RequestParam(value = "isCondition", required = false) Integer isCondition,
                                     @RequestParam(value = "isKey", required = false) Integer isKey) {
        return Result.success(variableFacade.getAllVariableList(isCondition, isKey));
    }

    @PostMapping("/exportVariableList")
//    public void exportVariableList() {
    public void exportVariableList(@RequestBody VariableEntity queryDTO) {
        List<VariableEntity> variableEntityList = new ArrayList<>();
        Result VariableResult = variableFacade.queryExportVariableList(queryDTO);
        if (VariableResult.isSuccess()) {
            variableEntityList = JSON.parseArray(JSON.toJSONString(VariableResult.getData()), VariableEntity.class);
        }
        if (CollectionUtils.isEmpty(variableEntityList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "变量信息_" + "_" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(variableEntityList, "变量信息", "变量信息", VariableEntity.class, fileName, response);
    }
}
