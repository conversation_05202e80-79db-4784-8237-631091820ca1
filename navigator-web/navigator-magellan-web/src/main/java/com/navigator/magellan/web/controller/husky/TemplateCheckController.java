package com.navigator.magellan.web.controller.husky;

import com.alibaba.fastjson.JSON;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.husky.facade.TemplateCheckFacade;
import com.navigator.husky.pojo.entity.TemplateCheckEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-27 15:40
 **/
@RestController
@Slf4j
@RequestMapping("/template/check/")
public class TemplateCheckController {

    @Resource
    private TemplateCheckFacade templateCheckFacade;
    @Autowired
    private HttpServletResponse response;

    /**
     * 出具模板标识配置-列表查询
     *
     * @param queryDTO 出具模板标识配置-查询条件
     * @return 出具模板标识配置-查询结果
     */
    @PostMapping("/queryCheckByCondition")
    public Result queryCheckByCondition(@RequestBody QueryDTO<TemplateCheckEntity> queryDTO) {
        return templateCheckFacade.queryCheckByCondition(queryDTO);
    }

    /**
     * 新增出具模板标识配置
     *
     * @param checkEntity 出具模板标识配置-信息
     * @return 出具模板标识配置新增结果
     */
    @PostMapping("/saveTemplateCheck")
    public Result saveTemplateCheck(@RequestBody TemplateCheckEntity checkEntity) {
        return Result.success(templateCheckFacade.saveTemplateCheck(checkEntity));
    }

    /**
     * 修改出具模板标识配置信息
     *
     * @param checkEntity 出具模板标识配置信息
     * @return 更新结果
     */
    @PostMapping("/updateTemplateCheck")
    public Result updateTemplateCheck(@RequestBody TemplateCheckEntity checkEntity) {
        return Result.success(templateCheckFacade.updateTemplateCheck(checkEntity));

    }

    @GetMapping("/getTemplateCheckById")
    public Result getTemplateCheckById(@RequestParam(value = "id") Integer id) {
        return Result.success(templateCheckFacade.getTemplateCheckById(id));
    }

    @GetMapping("/updateCheckStatus")
    public Result updateCheckStatus(@RequestParam(value = "id") Integer id,
                                    @RequestParam(value = "status") Integer status) {
        return Result.success(templateCheckFacade.updateCheckStatus(id, status));
    }

    @PostMapping("/exportTemplateCheck")
    public void exportTemplateCheck(@RequestBody TemplateCheckEntity checkQueryDTO, HttpServletResponse response) {
        List<TemplateCheckEntity> checkList = new ArrayList<>();
        Result checkResult = templateCheckFacade.queryExportTemplateCheckList(checkQueryDTO);
        if (checkResult.isSuccess()) {
            checkList = JSON.parseArray(JSON.toJSONString(checkResult.getData()), TemplateCheckEntity.class);
        }
        if (CollectionUtils.isEmpty(checkList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "模板出具标识信息" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(checkList, null, "模板出具标识规则", TemplateCheckEntity.class, fileName, response);
    }
}
