package com.navigator.magellan.web.controller;


import com.navigator.admin.facade.MemoFacade;
import com.navigator.admin.pojo.entity.MemoEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-03
 */
@RestController
@RequestMapping("/memo")
public class MemoController {

    @Resource
    private MemoFacade memoFacade;

    /**
     * 查看备忘录
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryMemo")
    public Result queryMemo(@RequestBody QueryDTO<MemoEntity> queryDTO) {
        return memoFacade.queryMemo(queryDTO);
    }

    /**
     * 添加备忘录
     * @param memo
     * @return
     */
    @PostMapping("saveMemo")
    public Result saveMemo(@RequestBody MemoEntity memo) {
        return memoFacade.saveMemo(memo);
    }

    /**
     * 修改备忘录
     * @param memo
     * @return
     */
    @PostMapping("updateMemo")
    public Result updateMemo(@RequestBody MemoEntity memo) {
        return memoFacade.updateMemo(memo);
    }

    @GetMapping("memoById")
    public Result memoById(@RequestParam(value = "id") Integer id) {
        return memoFacade.memoById(id);
    }

}
