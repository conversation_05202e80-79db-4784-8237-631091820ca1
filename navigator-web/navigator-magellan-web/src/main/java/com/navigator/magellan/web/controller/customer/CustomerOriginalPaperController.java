package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerOriginalPaperFacade;
import com.navigator.customer.pojo.dto.CustomerOriginalPaperDTO;
import com.navigator.customer.pojo.entity.CustomerOriginalPaperEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/originalPaper")
public class CustomerOriginalPaperController {
    @Autowired
    private CustomerOriginalPaperFacade customerOriginalPaperFacade;

    @PostMapping("/queryCustomerOriginalPaperList")
    Result queryCustomerOriginalPaperList(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperFacade.queryCustomerOriginalPaperList(customerOriginalPaperDTO);
    }

    @PostMapping("/saveCustomerOriginalPaper")
    Result saveCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperFacade.saveCustomerOriginalPaper(customerOriginalPaperDTO);
    }

    @PostMapping("/updateCustomerOriginalPaperStatus")
    Result updateCustomerOriginalPaperStatus(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperFacade.updateCustomerOriginalPaperStatus(customerOriginalPaperDTO);
    }

    @PostMapping("/updateCustomerOriginalPaper")
    Result updateCustomerOriginalPaper(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperFacade.updateCustomerOriginalPaper(customerOriginalPaperDTO);
    }

    @PostMapping("/queryCustomerNonFrame")
    public Result queryCustomerNonFrame(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO){
        return Result.success(customerOriginalPaperFacade.queryCustomerNonFrame(customerOriginalPaperDTO));
    }

    @PostMapping("/queryCustomerOriginalPaperEntity")
    CustomerOriginalPaperEntity queryCustomerOriginalPaperEntity(@RequestBody CustomerOriginalPaperDTO customerOriginalPaperDTO) {
        return customerOriginalPaperFacade.queryCustomerOriginalPaperEntity(customerOriginalPaperDTO);
    }

    @PostMapping(value = "/importCustomer")
    Result importCustomer(@RequestParam("file") MultipartFile file) {
        return customerOriginalPaperFacade.importCustomer(file);
    }
}
