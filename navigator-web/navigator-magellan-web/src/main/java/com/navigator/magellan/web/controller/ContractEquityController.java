package com.navigator.magellan.web.controller;

import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.trade.facade.ContractEquityFacade;
import com.navigator.trade.pojo.dto.contractEquity.ContractChangeEquityDTO;
import com.navigator.trade.pojo.dto.contractEquity.ContractEquityQueryDTO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 合同权益变更的前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/18
 */

@RestController
@RequiredArgsConstructor
@RequestMapping("/contract")
public class ContractEquityController {

    private final ContractEquityFacade contractEquityFacade;

    @ApiOperation("权益变更列表")
    @PostMapping("/getChangeContractEquityList")
    public Result getChangeContractEquityList(@RequestBody QueryDTO<ContractEquityQueryDTO> queryDTO) {
        return contractEquityFacade.getChangeContractEquityList(queryDTO);
    }

    @ApiOperation("权益变更")
    @PostMapping("/changeContractEquity")
    public Result changeContractEquity(@RequestBody ContractChangeEquityDTO changeEquityDTO) {
        return contractEquityFacade.changeContractEquity(changeEquityDTO);
    }

    @ApiOperation("权益变更详情")
    @GetMapping("/getChangeEquityDetailByApplyCode")
    public Result getChangeEquityDetailByApplyCode(@RequestParam("applyCode") String applyCode) {
        return contractEquityFacade.getChangeEquityDetailByApplyCode(applyCode);
    }

    @ApiOperation("权益变更记录")
    @GetMapping("/getChangeContractEquityRecord")
    public Result getChangeContractEquityRecord(@RequestParam("contractCode") String contractCode) {
        return contractEquityFacade.getChangeContractEquityRecord(contractCode);
    }


}
