package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.FastJsonUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.husky.pojo.entity.TemplateEntity;
import com.navigator.trade.facade.ContractSignFacade;
import com.navigator.trade.pojo.bo.QueryContractSignBO;
import com.navigator.trade.pojo.dto.contract.ContractPaperDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractBaseSignDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractQrCodeDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignProvideDTO;
import com.navigator.trade.pojo.dto.contractsign.ContractSignReviewDTO;
import com.navigator.trade.pojo.entity.ContractSignEntity;
import com.navigator.trade.pojo.qo.ContractSignQO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/21 15:27
 */
@RestController
@Slf4j
@RequestMapping("/contractSign")
public class ContractSignController {

    @Resource
    private ContractSignFacade contractSignFacade;
    @Resource
    private FileProcessFacade fileProcessFacade;
    @Autowired
    private AzureBlobUtil azureBlobUtil;


    /**
     * 协议列表高级分页搜索
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryContContractSigns")
    public Result queryContContractSigns(@RequestBody QueryDTO<ContractSignQO> queryDTO) {
        return contractSignFacade.queryContContractSigns(queryDTO);
    }

    /**
     * 根据协议ID，查询协议详情
     *
     * @param contractSignId 协议Id
     * @return 协议详情信息
     */
    @GetMapping("/getContractSignDetailById")
    public Result getContractSignDetailById(@RequestParam(name = "contractSignId") Integer contractSignId) {
        return Result.success(contractSignFacade.getContractSignDetailById(contractSignId));
    }

    /**
     * 二维码扫码，合同信息查询
     *
     * @param contractSignId 协议Id
     * @return 协议详情信息
     */
    @GetMapping("/getSignInfoById")
    public Result getContractSignInfoById(@RequestParam(name = "contractSignId") Integer contractSignId) {
        ContractSignEntity contractSignEntity = contractSignFacade.getContractSignDetailById(contractSignId);
        ContractQrCodeDTO contractQrCodeDTO = BeanConvertUtils.convert(ContractQrCodeDTO.class, contractSignEntity);
        contractQrCodeDTO.setCategoryName(GoodsCategoryEnum.getByValue(contractSignEntity.getGoodsCategoryId()).getDesc());
        return Result.success(contractQrCodeDTO);
    }

    /**
     * 根据合同ID，查询所有已签署的协议信息
     *
     * @param contractId 合同Id
     * @return 合同的所有的协议信息（文件只有双签）
     */
    @GetMapping("/getSignFileListByContractId")
    public Result getSignFileListByContractId(@RequestParam(name = "contractId") Integer contractId) {
        return Result.success(contractSignFacade.getSignFileListByContractId(contractId, SystemEnum.MAGELLAN.getValue()));

    }

    /**
     * 根据协议ID，查询所有的协议文件信息
     *
     * @param contractSignId 协议Id
     * @return 协议的所有文件信息
     */
    @GetMapping("/getAllSignFileListById")
    public Result getAllSignFileListById(@RequestParam(name = "contractSignId") Integer contractSignId) {
        return Result.success(contractSignFacade.getAllSignFileListById(contractSignId, SystemEnum.MAGELLAN.getValue()));
    }

    /**
     * 查询各个状态下的合同数量
     */
    @GetMapping("/getContractStat")
    public Result getContractStat(@RequestParam(value = "ldcFrame", required = false) Integer ldcFrame,
                                  @RequestParam(value = "salesType", required = false) Integer salesType,
                                  @RequestParam(value = "goodsCategoryId", required = false) Integer goodsCategoryId) {
        return Result.success(contractSignFacade.getMagellanSignStat(ldcFrame, salesType, goodsCategoryId));
    }

    /**
     * 生成协议PDF
     *
     * @param contractSignId 协议ID
     * @return 协议PDF文件信息
     */
    @GetMapping("/generateSignTemplate")
    @Deprecated
    public Result generateSignTemplate(@RequestParam(name = "contractSignId") Integer contractSignId) {
        return contractSignFacade.generateSignTemplate(contractSignId);
    }

    /**
     * 出具合同
     *
     * @param contractSignProvideDTO 出具协议修改数据
     * @return 出具结果
     */
    @PostMapping("/provide")
    public Result provideContractSign(@RequestBody ContractSignProvideDTO contractSignProvideDTO) {
        return contractSignFacade.provideContractSign(contractSignProvideDTO);
    }
    /**
     * 预览PDF文件
     * @param templateEntity
     * @return
     */
    @PostMapping("/previewHuskyContractPdf")
    Result previewHuskyContractPdf(@RequestBody TemplateEntity templateEntity){
        return contractSignFacade.previewHuskyContractPdf(templateEntity);
    }
    /**
     * 数字合同，用户提交出局
     *
     * @param templateEntity
     * @return
     */
    @PostMapping("/provideHuskyContractSign")
    public Result provideHuskyContractSign(@RequestBody TemplateEntity templateEntity){
        return contractSignFacade.provideHuskyContractSign(templateEntity);
    }
    /**
     * 测试生成PDF
     *
     * @param signProvideDTO
     * @return
     */
    @PostMapping("/generateSignPdfTest")
    public Result generateSignPdfTest(@RequestBody ContractSignProvideDTO signProvideDTO) {
        return contractSignFacade.generateSignPdfTest(signProvideDTO);
    }

    /**
     * 审核合同
     *
     * @param contractSignReviewDTO 协议审核参数
     * @return 审核协议结果
     */
    @PostMapping("/review")
    public Result reviewContractSign(@RequestBody ContractSignReviewDTO contractSignReviewDTO) {
        return contractSignFacade.reviewContractSign(contractSignReviewDTO);
    }


    @PostMapping("/contractSignStartSignature")
    public Result contractSignStartSignature(@RequestBody ContractSignReviewDTO signReviewDTO) {
        return contractSignFacade.contractSignStartSignature(signReviewDTO);
    }


    /**
     * 保存快递信息
     *
     * @param contractPaperDTO
     * @return
     */
    @PostMapping("/saveContractPaperSign")
    public Result saveContractPaperSign(@RequestBody ContractPaperDTO contractPaperDTO) {
        return contractSignFacade.saveContractPaperSign(contractPaperDTO);
    }


    /**
     * 回传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    @PostMapping("/postBackContractSign")
    public Result postBackContract(@RequestBody ContractBaseSignDTO contractBaseSignDTO) {
        return contractSignFacade.postBackContractSign(contractBaseSignDTO);
    }


    /**
     * 上传合同
     *
     * @param contractBaseSignDTO
     * @return
     */
    @PostMapping("/uploadingBackContractSign")
    public Result uploadingBackContractSign(@RequestBody ContractBaseSignDTO contractBaseSignDTO) {
        return contractSignFacade.uploadingBackContractSign(contractBaseSignDTO);
    }

    @GetMapping("/downloadContractZip")
    public HttpServletResponse downloadContractZip(@RequestParam(value = "fileIdList") List<Integer> fileIdList,
                                                   @RequestParam(value = "contractId") Integer contractId,
                                                   HttpServletResponse response) {
        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.CONTRACT, FilePathType.ZIP, contractId.toString());
        zipPath = zipPath + "合同文件.zip";
        Result fileResult = fileProcessFacade.getFileListByIds(fileIdList);
        log.info("合同下载" + FastJsonUtils.getBeanToJson(fileResult));
        List<FileInfoEntity> fileInfoEntityList = JSON.parseArray(JSON.toJSONString(fileResult.getData()), FileInfoEntity.class);
        log.info("合同下载" + FastJsonUtils.getBeanToJson(fileInfoEntityList));
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        List<String> filePathList = new ArrayList<>(fileInfoEntityList.size());
        fileInfoEntityList.forEach(fileInfoEntity -> {
            filePathList.add(fileInfoEntity.getPath());
        });
        azureBlobUtil.downloadZip(filePathList, zipPath, response);
        return response;
    }
}
