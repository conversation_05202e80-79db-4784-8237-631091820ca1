package com.navigator.magellan.web.controller.husky;

import com.navigator.admin.facade.DictItemFacade;
import com.navigator.admin.pojo.entity.DictItemEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.time.DateTimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2023-11-23 14:15
 **/
@RestController
@RequestMapping("/dictItem/")
public class DictItemController {
    @Resource
    private DictItemFacade dictItemFacade;
    @Autowired
    private HttpServletResponse response;

    @PostMapping("/queryByCondition")
    public Result queryByCondition(@RequestBody QueryDTO<DictItemEntity> queryDTO) {
        return dictItemFacade.queryByCondition(queryDTO);
    }

    @GetMapping("/getDictById")
    public Result getDictItemById(@RequestParam(value = "dictIdList") List<Integer> dictIdList) {
        return Result.success(dictItemFacade.getDictItemById(dictIdList));
    }

    @GetMapping("/getItemByDictCode")
    public Result getItemByDictCode(@RequestParam(value = "dictCode") String dictCode) {
        return Result.success(dictItemFacade.getItemByDictCode(dictCode));
    }

    @PostMapping("/saveDictItem")
    public Result saveDictItem(@RequestBody DictItemEntity dictItemEntity) {
        return dictItemFacade.saveDictItem(dictItemEntity);
    }

    @PostMapping("/updateDictItem")
    public Result updateDictItem(@RequestBody DictItemEntity dictItemEntity) {
        return dictItemFacade.updateDictItem(dictItemEntity);
    }

    @PostMapping("/exportVipCustomerList")
//    public void exportVipCustomerList() {
    public void exportVipCustomerList(@RequestBody DictItemEntity dictItemQO) {
        List<DictItemEntity> dictItemEntityList = dictItemFacade.queryExportVipCustomerList(dictItemQO);
        if (CollectionUtils.isEmpty(dictItemEntityList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "特殊模板客户配置_" + "_" + DateTimeUtil.formatDateValue();
        EasyPoiUtils.exportExcel(dictItemEntityList, "特殊模板客户配置", "特殊模板客户配置", DictItemEntity.class, fileName, response);
    }

}
