package com.navigator.magellan.web.controller.systemrule;

import com.navigator.common.dto.Result;
import com.navigator.common.enums.DisableStatusEnum;
import com.navigator.trade.facade.DeliveryTypeFacade;
import com.navigator.trade.pojo.entity.DeliveryTypeEntity;
import com.navigator.trade.pojo.vo.DeliveryTypeVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2021-12-06 18:13
 */
@RestController
@RequestMapping("/deliveryType")
public class DeliveryTypeController {
    @Resource
    private DeliveryTypeFacade deliveryTypeFacade;

    @GetMapping("/getAllDeliveryTypeList")
    public Result getValidDeliveryTypeList(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                           @RequestParam(value = "siteCode", required = false) String siteCode,
                                           @RequestParam(value = "buCode", required = false) String buCode,
                                           @RequestParam(value = "type", required = false) Integer type) {
        return Result.success(deliveryTypeFacade.getAllDeliveryTypeList(DisableStatusEnum.ENABLE.getValue(), categoryId, siteCode, buCode, type));
    }

    @GetMapping("/getAllDeliveryType")
    public Result getAllDeliveryTypeList(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                         @RequestParam(value = "siteCode", required = false) String siteCode,
                                         @RequestParam(value = "buCode", required = false) String buCode,
                                         @RequestParam(value = "type", required = false) Integer type) {

        return Result.success(deliveryTypeFacade.getAllDeliveryTypeList(null, categoryId, siteCode, buCode, type));
    }

    /**
     * 根据品类-获取提货类型（分组）
     *
     * @param categoryId 品类ID
     * @return 分组的提货类型信息
     */
    @GetMapping("/getDeliveryTypeByCategoryId")
    public List<DeliveryTypeVO> getDeliveryTypeByCategoryId(@RequestParam(value = "categoryId", required = false) Integer categoryId,
                                                            @RequestParam(value = "siteCode", required = false) String siteCode,
                                                            @RequestParam(value = "buCode", required = false) String buCode) {
        return deliveryTypeFacade.getDeliveryTypeByCategoryId(categoryId, siteCode, buCode);
    }

    @GetMapping("/getAllDeliveryByAddressType")
    public Result getAllDeliveryByAddressType(@RequestParam(value = "status", required = false) Integer status,
                                              @RequestParam(value = "addressType", required = false) Integer addressType) {
        return Result.success(deliveryTypeFacade.getAllDeliveryByAddressType(status, addressType));
    }

    /**
     * 新增或编辑交提货方式
     *
     * @param deliveryTypeEntity
     * @return
     */
    @PostMapping("/saveOrUpdateDeliveryType")
    public Result saveOrUpdateDeliveryType(@RequestBody DeliveryTypeEntity deliveryTypeEntity) {
        return deliveryTypeFacade.saveOrUpdateDeliveryType(deliveryTypeEntity);
    }

    /**
     * 启用禁用
     *
     * @param deliveryTypeId
     * @return
     */
    @GetMapping("/invalidStatus")
    public Result invalidStatus(@RequestParam(value = "deliveryTypeId") Integer deliveryTypeId) {
        return deliveryTypeFacade.invalidStatus(deliveryTypeId);
    }
}
