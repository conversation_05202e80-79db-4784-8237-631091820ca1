package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerDepositRateFacade;
import com.navigator.customer.pojo.bo.CustomerDepositRateBO;
import com.navigator.customer.pojo.dto.CustomerDepositRateDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/16 18:30
 */
@RestController
@RequestMapping("/customerDepositRate")
public class CustomerDepositRateController {

    @Resource
    private CustomerDepositRateFacade customerDepositRateFacade;

    /**
     * 根据客户id 履约保证金比例
     *
     * @param customerId
     * @return
     */
    @PostMapping("/getCustomerDepositRateByCustomerId")
    public Result getCustomerDepositRateByCustomerId(@RequestBody CustomerDepositRateBO customerDepositRateBO) {
        return customerDepositRateFacade.getCustomerDepositRateByCustomerId(customerDepositRateBO);
    }


    /**
     * TT新增查询客保证金
     *
     * @param customerDepositRateBO
     * @return
     */
    @PostMapping("/getCustomerDepositRateAddTT")
    public Result getCustomerDepositRateAddTT(@RequestBody CustomerDepositRateBO customerDepositRateBO) {
        return customerDepositRateFacade.getCustomerDepositRateAddTT(customerDepositRateBO);
    }


    /**
     * 添加客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/saveCustomerDepositRate")
    public Result saveCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO) {
        return customerDepositRateFacade.saveCustomerDepositRate(customerDepositRateDTO);

    }

    /**
     * 履约保证金 状态修改
     *
     * @param id
     * @return
     */
    @GetMapping("/updateCustomerDepositRateStatus")
    public Result updateCustomerDepositRateStatus(@RequestParam(value = "id") Integer id) {
        return customerDepositRateFacade.updateCustomerDepositRateStatus(id);
    }


    /**
     * 修改客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/updateCustomerDepositRate")
    public Result updateCustomerDepositRate(@RequestBody CustomerDepositRateDTO customerDepositRateDTO) {

        return customerDepositRateFacade.updateCustomerDepositRate(customerDepositRateDTO);

    }


    /**
     * 删除客户 履约保证金 比例
     *
     * @param customerDepositRateId
     * @return
     */
    @GetMapping("/deleteCustomerDepositRate")
    public Result deleteCustomerDepositRate(@RequestParam(value = "customerDepositRateId") Integer customerDepositRateId) {
        return customerDepositRateFacade.deleteCustomerDepositRate(customerDepositRateId);

    }

    /**
     * 编辑提交客户 履约保证金 比例
     *
     * @param customerDepositRateDTO
     * @return
     */
    @PostMapping("/redactCustomerDepositRate")
    public Result redactCustomerDepositRate(@RequestBody List<CustomerDepositRateDTO> customerDepositRateDTO) {
        return customerDepositRateFacade.redactCustomerDepositRate(customerDepositRateDTO);

    }

}
