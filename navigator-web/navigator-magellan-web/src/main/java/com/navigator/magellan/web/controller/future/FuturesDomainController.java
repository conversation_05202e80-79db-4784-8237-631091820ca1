package com.navigator.magellan.web.controller.future;

import com.navigator.common.dto.Result;
import com.navigator.future.facade.FuturesDomainFacade;
import com.navigator.future.pojo.dto.QueryContractFuturesDTO;
import com.navigator.trade.pojo.dto.future.ContractFuturesDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/6 13:51
 */
@RestController
@RequestMapping("/futuresDomain")
public class FuturesDomainController {

    @Resource
    private FuturesDomainFacade futuresDomainFacade;

    /**
     * 查询期货信息
     *
     * @param queryContractFuturesDTO
     * @return
     */
    @PostMapping("/queryContractsFutures")
    public Result queryContractsFutures(@RequestBody QueryContractFuturesDTO queryContractFuturesDTO) {
        return futuresDomainFacade.queryContractsFutures(queryContractFuturesDTO);
    }


    /**
     * 查询可转月量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayTransferNum")
    public Result mayTransferNum(@RequestBody ContractFuturesDTO contractFuturesDTO) {
        return futuresDomainFacade.mayTransferNum(contractFuturesDTO);
    }

    /**
     * 查询可点价量
     *
     * @param contractFuturesDTO
     * @return
     */
    @PostMapping("/mayPriceNum")
    public Result mayPriceNum(@RequestBody ContractFuturesDTO contractFuturesDTO) {
        return futuresDomainFacade.mayPriceNum(contractFuturesDTO);
    }


    /**
     * 查询可点价量
     *
     * @param contractId
     * @return
     */
    @GetMapping("/structurePriceNum")
    public Result structurePriceNum(@RequestParam("contractId") Integer contractId) {
        return futuresDomainFacade.structurePriceNum(contractId);
    }
}
