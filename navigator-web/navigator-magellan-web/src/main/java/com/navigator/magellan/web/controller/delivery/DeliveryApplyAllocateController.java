package com.navigator.magellan.web.controller.delivery;

import com.navigator.common.dto.Result;
import com.navigator.delivery.facade.DeliveryApplyAllocateFacade;
import com.navigator.delivery.pojo.dto.DeliveryApplyAllocateDTO;
import com.navigator.delivery.pojo.entity.DeliveryApplyAllocateEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyAllocateQO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 提货申请预分配 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-05
 */
@RestController
@RequestMapping("/deliveryApplyAllocate")
public class DeliveryApplyAllocateController {

    @Resource
    private DeliveryApplyAllocateFacade deliveryApplyAllocateFacade;

    /**
     * 根据条件：获取提货申请预分配列表
     *
     * @param condition
     * @return
     */
    @PostMapping("/queryDeliveryApplyAllocateList")
    Result<List<DeliveryApplyAllocateDTO>> queryDeliveryApplyAllocateList(@RequestBody DeliveryApplyAllocateQO condition) {
        return Result.success(deliveryApplyAllocateFacade.queryDeliveryApplyAllocateList(condition));
    }

    /**
     * 根据ID：获取提货申请预分配
     *
     * @param id
     * @return
     */
    @GetMapping("/getDeliveryApplyAllocateById")
    Result<DeliveryApplyAllocateEntity> getDeliveryApplyAllocateById(@RequestParam(value = "id") Integer id) {
        return Result.success(deliveryApplyAllocateFacade.getDeliveryApplyAllocateById(id));
    }
}
