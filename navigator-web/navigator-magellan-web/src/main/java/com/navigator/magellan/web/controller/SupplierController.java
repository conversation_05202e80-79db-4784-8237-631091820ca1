package com.navigator.magellan.web.controller;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.SupplierFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2021-12-02 18:10
 */
@RestController
@RequestMapping("/supplier")
public class SupplierController {
    @Resource
    private SupplierFacade supplierFacade;

    /**
     * 获取供应商/油厂集合
     *
     * @param type 0供应商 1油厂
     * @return 供应商/油厂集合
     */
    @GetMapping("/getSupplierListByType")
    public Result getSupplierListByType(@RequestParam(value = "type", required = false) Integer type) {
        return Result.success(supplierFacade.getSupplierListByType(type));
    }
}
