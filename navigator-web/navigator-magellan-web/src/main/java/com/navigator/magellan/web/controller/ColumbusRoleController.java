package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.columbus.CRoleFacade;
import com.navigator.admin.pojo.dto.columbus.CRoleDTO;
import com.navigator.admin.pojo.dto.columbus.CRoleQueryDTO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/columbusRole")
public class ColumbusRoleController {
    @Autowired
    private CRoleFacade roleFacade;

    @PostMapping("/saveOrUpdateRole")
    public Result saveOrUpdateRole(@RequestBody @Valid CRoleDTO cRoleDTO) {
        return roleFacade.saveOrUpdateRole(cRoleDTO);
    }

    @PostMapping("/updateDefRoleStatus")
    public Result updateDefRoleStatus(@RequestBody CRoleDTO cRoleDTO) {
        return roleFacade.updateDefRoleStatus(cRoleDTO);
    }

    @PostMapping("/queryRoleDefList")
    public Result queryRoleDefList(@RequestBody QueryDTO<CRoleQueryDTO> roleQueryDTO) {
        return roleFacade.queryRoleDefList(roleQueryDTO);
    }
}
