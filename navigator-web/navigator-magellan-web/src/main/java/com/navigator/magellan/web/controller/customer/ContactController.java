package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.ContactFacade;
import com.navigator.customer.pojo.dto.ContactDTO;
import com.navigator.customer.pojo.entity.ContactEntity;
import com.navigator.customer.pojo.vo.CustomerContactVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/22 9:49
 */

@RestController
@RequestMapping("/contact")
public class ContactController {

    @Resource
    private ContactFacade contactFacade;

    /**
     * 修改联系人
     *
     * @param contactEntity
     * @return
     */
    @PostMapping("/updateContactEntity")
    public Result updateContactEntity(@RequestBody ContactEntity contactEntity) {
        return Result.success(contactFacade.updateContactEntity(contactEntity));
    }

    /**
     * 添加联系人信息
     *
     * @param contactEntity
     * @return
     */
    @PostMapping("/vaseContactEntity")
    public Result vaseContactEntity(@RequestBody ContactEntity contactEntity) {
        return Result.success(contactFacade.vaseContactEntity(contactEntity));
    }

    /**
     * 根据id查询信息
     *
     * @param id
     * @return
     */
    @GetMapping("/getContactEntityById")
    public Result getContactEntityById(@RequestParam(value = "id") Integer id) {
        return Result.success(contactFacade.getContactEntityById(id));
    }


    @PostMapping("/queryContactFactoryList")
    public Result queryContactFactoryList(@RequestBody ContactDTO contactDTO){
        return Result.success(contactFacade.queryContactFactoryList(contactDTO));
    }

    /**
     * 根据客户查询出联系人
     *
     * @param referId
     * @param categoryId
     * @param supplierId
     * @return
     */
    @GetMapping("/getContactCustomerByList")
    public Result getContactCustomerByList(@RequestParam(value = "referId") Integer referId,
                                           @RequestParam(value = "categoryId") Integer categoryId,
                                           @RequestParam(value = "supplierId") Integer supplierId) {
        return Result.success(contactFacade.getContactCustomerByList(referId, categoryId, supplierId));
    }

    /**
     * 根据LDC查询出联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    @GetMapping("/getContactLDCByList")
    public Result getContactLDCByList(@RequestParam(value = "referId") Integer referId,
                                      @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(contactFacade.getContactLDCByList(referId, categoryId));
    }


    /**
     * 根据供应商/LDC查询出联系人
     *
     * @param referId
     * @param categoryId
     * @return
     */
    @GetMapping("/getContactSupplierIdByList")
    public Result getContactSupplierIdByList(@RequestParam(value = "referId") Integer referId,
                                             @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(contactFacade.getContactSupplierIdByList(referId, categoryId));
    }

    /**
     * 根据客户id查询客户联系人
     *
     * @param customerId
     * @param referType
     * @return
     */
    @GetMapping("/getCustomerByCustomerId")
    public Result getCustomerByCustomerId(@RequestParam(value = "customerId") Integer customerId,
                                          @RequestParam(value = "referType") Integer referType,
                                          @RequestParam(value = "categoryId") Integer categoryId) {
        return Result.success(contactFacade.getCustomerByCustomerId(customerId, referType, categoryId));

    }
}
