package com.navigator.magellan.web.controller;

import com.navigator.common.dto.Result;
import com.navigator.magellan.web.service.TradeTicketRemoteService;
import com.navigator.trade.facade.ContractPriceFacade;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/structure")
@Slf4j
public class StructureController {


    @Autowired
    private TradeTicketRemoteService tradeTicketService;

    /**
     * @description: 保存销售TT
     * @param: [createTradeTicketDTO]
     * @return: com.navigator.common.dto.Result
     */
    @PostMapping("/saveStructurePriceTT")
    Result saveStructurePriceTT(@Valid @RequestBody SalesStructurePriceTTDTO salesStructurePriceTTDTO, BindingResult result) {
        return tradeTicketService.saveStructurePriceTT(salesStructurePriceTTDTO);
    }

}
