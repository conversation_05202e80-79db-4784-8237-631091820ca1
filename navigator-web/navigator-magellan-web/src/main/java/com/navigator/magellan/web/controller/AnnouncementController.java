package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.AnnouncementFacade;
import com.navigator.admin.pojo.dto.AnnouncementDTO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/announcement")
public class AnnouncementController {
    @Autowired
    private AnnouncementFacade announcementFacade;

    @PostMapping("/save")
    Result save(@RequestBody AnnouncementDTO announcementDTO) {
        return announcementFacade.save(announcementDTO);
    }

    @PostMapping("/modify")
    Result modify(@RequestBody AnnouncementDTO announcementDTO) {
        return announcementFacade.modify(announcementDTO);
    }

    @PostMapping("/queryAnnouncementDetail")
    Result queryAnnouncementDetail(@RequestBody AnnouncementDTO announcementDTO) {
        return announcementFacade.queryAnnouncementDetail(announcementDTO);
    }

    @PostMapping("/queryAnnouncementList")
    Result queryAnnouncementList(@RequestBody QueryDTO<AnnouncementDTO> queryDTO) {
        return announcementFacade.queryAnnouncementList(queryDTO);
    }

    @GetMapping("/queryMagellanAnnouncement")
    Result queryMagellanAnnouncement() {
        return announcementFacade.queryAnnouncementBySystem(SystemEnum.MAGELLAN.getValue());
    }

}

