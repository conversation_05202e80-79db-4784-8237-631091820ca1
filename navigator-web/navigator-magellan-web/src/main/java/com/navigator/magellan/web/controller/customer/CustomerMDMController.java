//package com.navigator.magellan.web.controller.customer;
//
//import com.navigator.common.dto.Result;
//import com.navigator.customer.facade.CustomerMdmFacade;
//import com.navigator.customer.pojo.dto.mdm.MDMObjectDataDTO;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.*;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.annotation.Resource;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2024/9/30
// */
//
//@Slf4j
//@RestController
//@RequiredArgsConstructor
//@RequestMapping("/customerMDM")
//public class CustomerMDMController {
//
//    @Resource
//    private CustomerMdmFacade customerMdmFacade;
//
//    /**
//     * 对接MDM接口
//     *
//     * @param mdmObjectDataDTO
//     * @return
//     */
//    @PostMapping(value = "/saveOrUpdateMDMCustomer")
//    public Result saveOrUpdateMDMCustomer(@RequestBody MDMObjectDataDTO mdmObjectDataDTO){
//        return customerMdmFacade.saveOrUpdateMDMCustomer(mdmObjectDataDTO);
//    }
//}
