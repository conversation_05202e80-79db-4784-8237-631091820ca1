package com.navigator.magellan.web.controller.customer;

import com.navigator.common.dto.Result;
import com.navigator.customer.facade.CustomerFacade;
import com.navigator.customer.facade.CustomerGradeScoreFacade;
import com.navigator.customer.pojo.dto.CustomerGradeScoreDTO;
import com.navigator.customer.pojo.entity.CustomerGradeScoreEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/8/2
 */

@RestController
@RequestMapping("/customerGradeScore")
public class CustomerGradeScoreController {

    @Resource
    private CustomerGradeScoreFacade customerGradeScoreFacade;

    @PostMapping("/queryCustomerGradeScoreList")
    public Result queryCustomerGradeScoreList(@RequestBody CustomerGradeScoreDTO customerGradeScoreDTO) {
        return Result.success(customerGradeScoreFacade.queryCustomerGradeScoreList(customerGradeScoreDTO));
    }

}
