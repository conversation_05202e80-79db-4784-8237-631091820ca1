package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.columbus.CEmployFacade;
import com.navigator.admin.pojo.dto.columbus.CEmployDTO;
import com.navigator.admin.pojo.vo.columbus.ColumbusAdminVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/columbusOperationLog")
public class ColumbusOperationLogController {
    @Autowired
    private CEmployFacade cEmployFacade;
    @Autowired
    private HttpServletResponse response;


    /**
     * 查询管理员列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryColumbusList")
    public Result queryColumbusList(@RequestBody QueryDTO<CEmployDTO> queryDTO) {
        return cEmployFacade.queryColumbusList(queryDTO);
    }

    /**
     * 导出管理员列表
     *
     * @param cEmployDTO
     * @return
     */
    @PostMapping("/exportColumbusList")
    public void exportColumbusList(@RequestBody CEmployDTO cEmployDTO) {
        Result result = cEmployFacade.exportColumbusList(cEmployDTO);
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<ColumbusAdminVO> columbusAdminVOList = JSON.parseArray(JSON.toJSONString(result.getData()), ColumbusAdminVO.class);
        if (CollectionUtils.isEmpty(columbusAdminVOList)) {
            throw new BusinessException("无可导出数据！");
        }

        String fileName = "导出Columbus用户数据";
        EasyPoiUtils.exportExcel(columbusAdminVOList, "导出Columbus用户数据", "导出Columbus用户数据", ColumbusAdminVO.class, fileName, response);
    }

}
