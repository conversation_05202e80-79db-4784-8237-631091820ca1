package com.navigator.magellan.web.thread;


import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.OperationDetailDTO;

import java.util.concurrent.Callable;

/**
 * @description: 保存日志
 * @author: Mr.Lq
 * @date: 2021/1/29 15:01
 */
public class LogTask implements Callable<Integer> {

    private OperationLogFacade operationLogFacade;
    private OperationDetailDTO operationDetailDTO;

    public LogTask(OperationLogFacade operationLogFacade, OperationDetailDTO operationDetailDTO) {
        this.operationLogFacade = operationLogFacade;
        this.operationDetailDTO = operationDetailDTO;
    }

    @Override
    public Integer call() {
        int i;
        operationLogFacade.saveOperationDetail(operationDetailDTO);
        return null;
    }
}
