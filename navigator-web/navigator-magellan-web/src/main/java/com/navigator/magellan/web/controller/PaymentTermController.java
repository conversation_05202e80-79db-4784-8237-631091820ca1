package com.navigator.magellan.web.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.PayConditionFacade;
import com.navigator.admin.pojo.dto.PayConditionDTO;
import com.navigator.admin.pojo.entity.PayConditionEntity;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * PaymentTerm 接口 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/10
 */
@RestController
@RequestMapping("/payCondition")
public class PaymentTermController {
    @Resource
    private PayConditionFacade payConditionFacade;
    @Resource
    private HttpServletResponse response;

    @GetMapping("/getPayConditionById")
    Result<PayConditionEntity> getPayConditionById(@RequestParam("payConditionId") Integer payConditionId) {
        return payConditionFacade.getPayConditionById(payConditionId);
    }

    @PostMapping("/getPayConditionList")
    public Result getPayConditionList(@RequestBody QueryDTO<PayConditionDTO> queryDTO) {
        return payConditionFacade.getPayConditionList(queryDTO);
    }

    @PostMapping("/getPayConditionByCondition")
    public Result queryPayCondition(@RequestBody PayConditionDTO payConditionDTO) {
        Result<List<PayConditionEntity>> listResult = payConditionFacade.queryPayCondition(payConditionDTO.setStatus(1));
        if (listResult.isSuccess() && CollectionUtil.isNotEmpty(listResult.getData())) {
            return Result.success(listResult.getData().get(0));
        }
        return Result.success();
    }

    @PostMapping("/addPayCondition")
    public Result addPayCondition(@RequestBody PayConditionDTO payConditionDTO) {
        return payConditionFacade.addPayCondition(payConditionDTO);
    }

    @PostMapping("/updatePayCondition")
    public Result updatePayCondition(@RequestBody PayConditionDTO payConditionDTO) {
        return payConditionFacade.updatePayCondition(payConditionDTO);
    }

    @GetMapping("/exportPayCondition")
    public void exportPayCondition() {
        List<PayConditionEntity> payConditionList = new ArrayList<>();

        Result result = payConditionFacade.queryPayCondition(new PayConditionDTO());
        if (result.isSuccess()) {
            payConditionList = JSON.parseArray(JSON.toJSONString(result.getData()), PayConditionEntity.class);
        }
        if (CollectionUtil.isEmpty(payConditionList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "PaymentTerm" + DateUtil.format(DateUtil.date(), "yyyyMMddHHmmssSSS");
        EasyPoiUtils.exportExcel(payConditionList, null, "PaymentTerm", PayConditionEntity.class, fileName, response);
    }
}
