package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.UserFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.dto.RefreshDTO;
import com.navigator.admin.pojo.vo.LoginVO;
import com.navigator.admin.pojo.vo.MenuVO;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.util.JwtUtils;
import com.navigator.common.util.VerificationCodeUtil;
import com.navigator.security.authentication.TokenManager;
import com.navigator.trade.facade.RedisFacade;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/user")
@Slf4j
public class UserController {
    @Autowired
    private VerificationCodeUtil verificationCodeUtil;
    @Autowired
    private UserFacade userFacade;
    @Autowired
    private RedisFacade redisFacade;
    @Autowired
    private TokenManager tokenManager;

    @PostMapping("/sendVerificationCode/{mobileNo}")
    public Result sendVerificationCode(@PathVariable("mobileNo") String mobileNo) {
        String verificationCode = verificationCodeUtil.sendVerificationCode(mobileNo, RedisConstants.M_USER_LOGIN_PASSWORD_CODE_SEND, RedisConstants.M_USER_LOGIN_PASSWORD_CODE);
        log.info("magellan sendVerificationCode ,mobileNo:{},verificationCode:{}", mobileNo, verificationCode);
        return Result.success();
    }

    @PostMapping("/login")
    public Result login(@RequestBody LoginDTO loginDTO) {
        return userFacade.login(loginDTO);
    }

    @PostMapping("/refresh")
    public Result refresh(@RequestBody RefreshDTO refreshDTO) {
        Claims claims = JwtUtils.parseRefreshToken(refreshDTO.getRefreshToken());
        String id = (String) claims.get("id");
        String email = (String) claims.get("email");
        String token = tokenManager.createTokenByEmail(id, email, true);
        String refreshToken = tokenManager.getRefreshTokenByEmail(id, email);

        LoginVO loginVO = new LoginVO();
        loginVO
                .setToken(token)
                .setRefreshToken(refreshToken)
                .setAadLogin(false)
        ;
        MenuVO menuVO = tokenManager.getMenuVO(Integer.parseInt(id));
        List<String> powerCodeList = tokenManager.getPowerCodeListByEmployId(Integer.parseInt(id));
        loginVO.setMenuVO(menuVO);
        loginVO.setPowerCodeList(powerCodeList);
        return Result.success(loginVO);
    }

    @GetMapping("/saveRedis")
    Result saveRedis(@RequestParam("version") String version) {
        return redisFacade.saveRedis(version);
    }

    @GetMapping("/reloadRedis")
    Result reloadRedis(@RequestParam("version") String version) {
        return redisFacade.reloadRedis(version);
    }
}