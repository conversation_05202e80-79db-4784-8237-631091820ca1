package com.navigator.magellan.web.controller.goods;


import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.GoodsFacade;
import com.navigator.goods.pojo.dto.GoodsSearchDTO;
import com.navigator.goods.pojo.dto.GoodsSpecDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品基础表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@RestController
@RequestMapping("/goods")
public class GoodsController {
    @Resource
    private GoodsFacade goodsFacade;

    /**
     * 根据状态获取所有商品信息
     *
     * @param status 商品状态
     * @return 所有商品名称集合
     */
    @GetMapping("/getAllGoodsList")
    public Result getAllGoodsList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(goodsFacade.getAllGoodsList(status));
    }

    @GetMapping("/getAllGoodsListByCategoryId")
    public Result getAllGoodsListByCategoryId(@RequestParam(value = "status", required = false) Integer status,
                                              @RequestParam(value = "categoryId", required = false) Integer categoryId) {
        return Result.success(goodsFacade.getAllGoodsListByCategoryId(status, categoryId));
    }

    @GetMapping("/getAllLkgGoodsList")
    public Result getAllLkgGoodsList(@RequestParam(value = "status", required = false) Integer status) {
        return Result.success(goodsFacade.getAllLkgGoodsList(status));
    }

    @PostMapping("/queryGoodsTaxRate")
    public Result queryGoodsTaxRate(@RequestBody GoodsSpecDTO goodsSpecDTO) {
        return goodsFacade.queryGoodsTaxRate(goodsSpecDTO);
    }

    /**
     * 根据条件分页查询商品信息
     *
     * @param goodsSearchDTO 商品查询条件
     * @return 查询的商品结果集合
     */
    @PostMapping("/queryGoodsList")
    public Result queryGoodsList(@RequestBody QueryDTO<GoodsSearchDTO> goodsSearchDTO) {
        return goodsFacade.queryGoodsList(goodsSearchDTO);
    }

    /**
     * 禁用/启用商品
     *
     * @param goodsId 商品ID
     * @param status  商品状态
     * @return 禁用/启用结果
     */
    @GetMapping("/invalidGoods")
    public Result invalidGoods(@RequestParam(value = "goodsId") Integer goodsId,
                               @RequestParam(value = "status", required = false) Integer status,
                               @RequestParam(value = "futurePrefix", required = false) String futurePrefix,
                               @RequestParam(value = "futureSuffix", required = false) String futureSuffix,
                               @RequestParam(value = "taxRate", required = false) String taxRate
    ) {
        return goodsFacade.invalidGoods(goodsId, status, futurePrefix, futureSuffix, taxRate);
    }

    @GetMapping("/removeGoodsCache")
    public void removeGoodsCache(@RequestParam(value = "goodsId") Integer id) {
        goodsFacade.removeGoodsCache(id);
    }

    @GetMapping("/removeAllGoodsCache")
    public void removeAllGoodsCache() {
        goodsFacade.removeAllGoodsCache();
    }

    @GetMapping("/updateGoodsDeliveryStatus")
    public Result updateGoodsDeliveryStatus(@RequestParam(value = "goodsId") Integer goodsId,
                                            @RequestParam(value = "deliveryStatus") Integer deliveryStatus) {
        return goodsFacade.updateGoodsDeliveryStatus(goodsId, deliveryStatus);
    }

}
