package com.navigator.magellan.web.controller.rule;

import com.navigator.admin.facade.rule.BusinessRuleFacade;
import com.navigator.admin.pojo.dto.rule.ConditionVariableDTO;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> NaNa
 * @since : 2024-04-08 17:36
 **/
@RestController
@RequestMapping("/rule/business")
public class BusinessRuleController {
    @Autowired
    private BusinessRuleFacade businessRuleFacade;

    @PostMapping("/jointRuleConditionInfo")
    public Result jointRuleConditionInfo(@RequestBody List<ConditionVariableDTO> conditionVariableList) {
        return Result.success(businessRuleFacade.jointRuleConditionInfo(conditionVariableList));
    }


}
