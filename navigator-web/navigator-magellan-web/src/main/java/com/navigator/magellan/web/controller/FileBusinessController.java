package com.navigator.magellan.web.controller;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.navigator.admin.facade.FileBusinessFacade;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.common.constant.FileConstant;
import com.navigator.common.dto.*;
import com.navigator.common.enums.BlobFileContextEnum;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.file.FileUploadUtil;
import com.navigator.common.util.file.WatermarkUtil;
import com.navigator.common.util.qrcode.QrCodeUtil;
import com.navigator.common.util.time.DateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;

/**
 * <AUTHOR>
 * @since 2021-11-29 18:23
 */
@RestController
@RequestMapping("/file")
@Slf4j
public class FileBusinessController {
    @Resource
    private FileBusinessFacade fileBusinessFacade;

    @Resource
    private FileProcessFacade fileProcessFacade;

    @Autowired
    private AzureBlobUtil azureBlobUtil;

    @Autowired
    private WatermarkUtil watermarkUtil;


    /**
     * 单个文件上传
     *
     * @param file 文件信息
     * @return 上传的单个文件基本信息
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    @ResponseBody
    @RequestMapping(value = "/singleUpload")
    public Result singleUploadV3(@RequestParam("file") MultipartFile file) {
        //调用工具类完成上传，返回相关数据到页面
        String path = FileConstant.FILE_UPLOAD + "magellan/" + DateTimeUtil.formatDateString(DateTime.now());
        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.upload(file, path, file.getContentType());
        if (fileBaseInfoDTO == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);
        if (StringUtils.isNotBlank(fileBaseInfoDTO.getAttachStuff())
                && BlobFileContextEnum.PDF.getTypeInfo().equalsIgnoreCase(fileBaseInfoDTO.getAttachStuff())) {
            WatermarkDTO watermarkDTO = watermarkUtil.generateWaterMark(file, 1);
            azureBlobUtil.uploadWhiteMark(watermarkDTO.getMultipartFile(), path, BlobFileContextEnum.PDF.getFileType(), watermarkDTO.getInputStream(), fileInfoEntity.getNewFileName());
        }
        //记录文件信息，并返回页面
        return Result.success(fileInfoEntity);
    }

    @RequestMapping("/testFile")
    public Result testFile(@RequestParam("file") MultipartFile file) {
        String path = FileConstant.FILE_UPLOAD + "magellan/" + DateTimeUtil.formatDateString(DateTime.now());
        FileBaseInfoDTO fileBaseInfoDTO = azureBlobUtil.upload(file, path, file.getContentType());
        if (fileBaseInfoDTO == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        FileInfoEntity fileInfoEntity = fileBusinessFacade.saveFileInfo(fileBaseInfoDTO);

        WatermarkDTO watermarkDTO = watermarkUtil.generateWaterMark(file, 1);
        azureBlobUtil.uploadWhiteMark(watermarkDTO.getMultipartFile(), path, BlobFileContextEnum.PDF.getFileType(), watermarkDTO.getInputStream(), fileInfoEntity.getNewFileName());
        //记录文件信息，并返回页面
        return Result.success(fileInfoEntity);
    }

    /**
     * 单个文件上传
     *
     * @param file 文件信息
     * @return 上传的单个文件基本信息
     * @throws IllegalStateException
     * @throws IOException
     * @throws JSONException
     */
    @ResponseBody
    @RequestMapping(value = "/singleUploadV2")
    public Result singleUpload(@RequestParam("file") MultipartFile file) throws IllegalStateException, IOException, JSONException {
        //调用工具类完成上传，返回相关数据到页面
        FileBaseInfoDTO fileBaseInfoDTO = FileUploadUtil.singleUpload(file);
        if (fileBaseInfoDTO == null) {
            return Result.failure(ResultCodeEnum.FILE_UPLOAD_FAIL);
        }
        //记录文件信息，并返回页面
        return Result.success(fileBusinessFacade.saveFileInfo(fileBaseInfoDTO));
    }

    /**
     * 文件下载
     *
     * @param path     下载路径
     * @param response 请求
     * @return 下载文档结果
     */
    @GetMapping("/download")
    public void downloadV3(@RequestParam("path") String path, HttpServletResponse response) {
        azureBlobUtil.download(path, response);
    }

    /**
     * 文件下载
     *
     * @param path     下载路径
     * @param response 请求
     * @return 下载文档结果
     */
    @GetMapping("/downloadV3")
    public HttpServletResponse download(@RequestParam("path") String path, HttpServletResponse response) {
        if (null != path && !path.equals(""))
            return fileProcessFacade.download(path, response);
        else
            return null;
    }

    /**
     * 统一记录存储文件关系
     *
     * @param fileBusinessRelationDTO 记录附件关系请求信息
     */
    @PostMapping("/recordFileRelationInfo")
    public Result recordFileRelationInfo(@RequestBody FileBusinessRelationDTO fileBusinessRelationDTO) {
        return fileBusinessFacade.recordFileRelation(fileBusinessRelationDTO);
    }

    /**
     * 下载二维码图片
     *
     * @param url      跳转地址
     * @param response
     */
    @GetMapping(value = "/downloadQrCodePic")
    public Result downloadQrCodePic(String url, HttpServletResponse response) {
        try {
            if (null != url) {
                // 获取二维码的数据流
                BufferedImage image = QrCodeUtil.getBufferedImage(url, 500, null);
                ByteArrayOutputStream os = new ByteArrayOutputStream();
                ImageIO.write(image, "jpg", os);
                InputStream inputStream = new ByteArrayInputStream(os.toByteArray());
                String path = "upload/magellan/barcode";
                return Result.success(azureBlobUtil.uploadByInputStream(inputStream, path, "二维码.jpg", BlobFileContextEnum.JPG.getFileType()));
//                // 下载图片
//                if (null != inputStream) {
//                    downloadPic(response, inputStream, "二维码.jpg", "image/jpeg;charset=UTF-8");
//                }
            }
        } catch (IOException e) {
            log.error("下载二维码图片，错误信息：" + e.getMessage());
            e.printStackTrace();
        }
        return Result.success();
    }

    @PostMapping("/html2Pdf")
    public Result html2Pdf(@RequestBody HtmlInfoDTO htmlInfoDTO) {
        return Result.success(fileProcessFacade.html2Pdf(htmlInfoDTO));
    }

    /**
     * 通过流数据下文件
     *
     * @param response
     * @param inputStream
     * @param imageName
     * @param contentType
     */
    private void downloadPic(HttpServletResponse response, InputStream inputStream, String imageName, String contentType) {
        try {
            response.reset(); // 清除buffer缓存
            response.setContentType(contentType);
            response.setDateHeader("Expires", 0);
            response.addHeader("Access-Control-Allow-Origin", "*");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(imageName, "UTF-8"));
            if (null != inputStream) {
                OutputStream output = response.getOutputStream();
                int len = 0;
                byte[] by = new byte[1024 * 10];
                while ((len = inputStream.read(by)) > 0) {
                    output.write(by, 0, len);
                }
                BufferedOutputStream bufferedOutPut = new BufferedOutputStream(output);
                bufferedOutPut.flush();
                bufferedOutPut.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("下载图片出错:{}!", JSONObject.toJSONString(e));
        }
    }


}
