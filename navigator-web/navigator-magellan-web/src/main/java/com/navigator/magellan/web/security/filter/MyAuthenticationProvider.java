package com.navigator.magellan.web.security.filter;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.magellan.EmployFacade;
import com.navigator.admin.pojo.dto.LoginDTO;
import com.navigator.admin.pojo.entity.EmployEntity;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.config.properties.CommonProperties;
import com.navigator.common.constant.RedisConstants;
import com.navigator.common.dto.Result;
import com.navigator.common.redis.RedisUtil;
import com.navigator.magellan.web.security.entity.UserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MyAuthenticationProvider implements AuthenticationProvider {

    @Autowired
    private EmployFacade employFacade;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CommonProperties commonProperties;
    //自定义密码验证
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        if(authentication == null)
            return null;
        String credentials = (String) authentication.getCredentials();
        if(null == credentials)
            return null;
        if(!credentials.contains("\\")){
            int i;
        }
        LoginDTO loginDTO = JSON.parseObject(credentials, LoginDTO.class);
        //todo 登录验证功能后续释放
        String key = loginDTO == null ? "" : RedisConstants.M_USER_LOGIN_PASSWORD_CODE + loginDTO.getPhone();
        String code = redisUtil.getString(key);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmm");
        String timeCode = simpleDateFormat.format(new Date()).substring(6, 12);
        if (StringUtils.isBlank(code)) {
            log.error("========> login  failed ,验证码已过期,请重新获取");
            throw new BadCredentialsException("验证码已过期,请重新获取");
        }
        if(loginDTO!=null){
            if (!code.equalsIgnoreCase(loginDTO.getCaptcha())
                   /* && !timeCode.equalsIgnoreCase(loginDTO.getCaptcha())*/
                    && (commonProperties.getCaptcha() == null
                    || commonProperties.getCaptcha())) {
                log.error("========> login  failed ,验证码错误");
                throw new BadCredentialsException("验证码错误");
            }
            Result resultData = employFacade.getEmployByEmail(loginDTO.getEmail().trim(), SystemEnum.MAGELLAN.getValue());
            List<EmployEntity> employEntityList = JSON.parseArray(JSON.toJSONString(resultData.getData()) , EmployEntity.class);
            if (CollectionUtil.isEmpty(employEntityList)) {
                log.error("========> login  failed ,用户不存在");
                throw new BadCredentialsException("用户不存在");
            }
            UserDetail userDetail = new UserDetail(employEntityList.get(0), getUserAuthority(1L));
            if (authentication!=null&&authentication.getCredentials() == null) {
                log.error("========> login  failed ,凭证为空");
                throw new BadCredentialsException("凭证为空");
            }
            UsernamePasswordAuthenticationToken result = new UsernamePasswordAuthenticationToken(userDetail, authentication.getCredentials(), userDetail.getAuthorities());
            result.setDetails(authentication.getDetails());
            return result;
        }

        return null;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return true;
    }


    //获取用户权限
    public List<GrantedAuthority> getUserAuthority(Long userId) {
        return AuthorityUtils.commaSeparatedStringToAuthorityList("");
    }

}
 