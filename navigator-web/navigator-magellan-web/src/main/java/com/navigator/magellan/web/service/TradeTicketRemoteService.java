package com.navigator.magellan.web.service;

import com.navigator.common.dto.Result;
import com.navigator.trade.pojo.dto.tradeticket.OMContractAddTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SalesStructurePriceTTDTO;
import com.navigator.trade.pojo.dto.tradeticket.SubmitTTBatchDTO;
import com.navigator.trade.pojo.dto.tradeticket.SubmitTTDTO;

public interface TradeTicketRemoteService {

    Result saveTT(OMContractAddTTDTO omContractAddTTDTO);

    /**
     * TODO 确实验证的规则的TT提交
     * @param omContractAddTTDTO
     * @return
     */
    Result newSubmitTT(OMContractAddTTDTO omContractAddTTDTO);

    /**
     * 保存提交结构化定价
     * @param salesStructurePriceTTDTO
     * @return
     */
    Result saveStructurePriceTT(SalesStructurePriceTTDTO salesStructurePriceTTDTO);

    Result submitTTBatch(SubmitTTBatchDTO submitTTBatchDTO);

    Result submitTT(SubmitTTDTO submitTTDTO);

    Result updateTT(OMContractAddTTDTO omContractAddTTDTO);
}
