package com.navigator.magellan.web.controller;

import com.navigator.common.dto.Result;
import com.navigator.pigeon.facade.LkgContractFacade;
import com.navigator.trade.facade.ContractCheckFacade;
import com.navigator.trade.pojo.dto.contract.ContractCheckDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <p>
 * 合同校验
 * 数据巡检
 * 对内（合同层面）
 * 基础信息
 * 数量
 * 价格
 * 状态
 * 转月次数
 * 接口调用情况
 * request
 * record
 * 校核LKG接口数据
 * 数量
 * 状态
 * 对外（接口层面）
 * request
 * record
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/23
 */
@RestController
@Slf4j
@RequestMapping("/contractCheck")
public class ContractCheckController {

    @Resource
    private LkgContractFacade lkgContractFacade;
    @Resource
    private ContractCheckFacade checkFacade;

    /**
     * 根据条件校验Record记录
     *
     * @param startCheckTime 开始校验时间
     * @param endCheckTime   截止校验时间
     * @return
     */
    @GetMapping("/checkLkgRecord")
    public Result checkLkgRecord(@RequestParam(value = "startCheckTime", required = false) String startCheckTime, @RequestParam(value = "endCheckTime", required = false) String endCheckTime) {
        return lkgContractFacade.checkLkgRecord(startCheckTime, endCheckTime);
    }

    /**
     * 根据条件校验nav合同数据
     *
     * @param contractCheckDTO
     * @return
     */
    @PostMapping("/checkContract")
    public Result checkContract(@RequestBody ContractCheckDTO contractCheckDTO) {
        return checkFacade.checkContract(contractCheckDTO);
    }

    /**
     * 根据合同号校验lkg数据
     *
     * @param contractCheckDTO
     * @return
     */
    @PostMapping("/checkLkgContract")
    public Result checkLkgContract(@RequestBody ContractCheckDTO contractCheckDTO) {
        return checkFacade.checkLkgContract(contractCheckDTO);
    }

    /**
     * 每日校验
     *
     * @return
     */
    @GetMapping("/checkDailyContract")
    public Result checkDailyContract() {
        return checkFacade.checkDailyContract();
    }

}



