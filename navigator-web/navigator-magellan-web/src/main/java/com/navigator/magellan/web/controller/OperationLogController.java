package com.navigator.magellan.web.controller;

import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.OperationLogFacade;
import com.navigator.admin.pojo.dto.LogDTO;
import com.navigator.admin.pojo.enums.OperationSourceEnum;
import com.navigator.admin.pojo.vo.LogVO;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.EasyPoiUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/7 18:53
 */
@RestController
@RequestMapping("/log")
public class OperationLogController {
    @Resource
    OperationLogFacade operationLogFacade;
    @Autowired
    private HttpServletResponse response;

    @GetMapping("/queryOperationLogByCode")
    public Result queryOperationLogByCode(@RequestParam("code") String code, @RequestParam("id") Integer id) {
        return operationLogFacade.queryOperationLogByCode(code, OperationSourceEnum.EMPLOYEE.getValue(), id);
    }

    /**
     * 查詢日志列表
     *
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryLogList")
    Result queryLogList(@RequestBody QueryDTO<LogDTO> queryDTO) {
        return operationLogFacade.queryLogList(queryDTO);
    }

    /**
     * 导出日志列表
     *
     * @param
     * @return
     */
    @PostMapping("/exportLogList")
    public void exportLogList(@RequestBody LogDTO logDTO) {
        QueryDTO<LogDTO> queryDTO = new QueryDTO<>();
        queryDTO.setPageNo(1);
        queryDTO.setPageSize(1000000);
        queryDTO.setCondition(logDTO);
        Result result = operationLogFacade.exportLogList(queryDTO);
        if (!result.isSuccess()) {
            throw new BusinessException("系统错误,稍后再试");
        }
        List<LogVO> LogVOList = JSON.parseArray(JSON.toJSONString(result.getData()), LogVO.class);
        if (CollectionUtils.isEmpty(LogVOList)) {
            throw new BusinessException("无可导出数据！");
        }
        String fileName = "Log";
        EasyPoiUtils.exportExcel(LogVOList, "操作记录", "", LogVO.class, fileName, response);
    }

    /**
     * 查詢场景列表
     *
     * @param
     * @return
     */
    @GetMapping("/queryScenesList")
    Result queryScenesList() {
        return operationLogFacade.queryScenesList();
    }
}

