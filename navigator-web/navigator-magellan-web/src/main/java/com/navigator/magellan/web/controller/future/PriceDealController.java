package com.navigator.magellan.web.controller.future;

import com.azure.core.annotation.Post;
import com.navigator.common.dto.Result;
import com.navigator.future.facade.PriceAllocateFacade;
import com.navigator.future.facade.PriceApplyFacade;
import com.navigator.future.pojo.dto.ApplyContraryDTO;
import com.navigator.future.pojo.dto.PriceDealDetailDTO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/4 18:30
 */

@RestController
@RequestMapping("/future")
public class PriceDealController {

    @Resource
    private PriceAllocateFacade priceAllocateFacade;
    @Resource
    private PriceApplyFacade priceApplyFacade;


    /**
     * 结构化定价成交
     *
     * @param priceDealDetailDTO
     * @return
     */
    @PostMapping("/structuringPriceDeal")
    public Result structuringPriceDeal(@RequestBody PriceDealDetailDTO priceDealDetailDTO) {
        return priceAllocateFacade.structuringPriceDeal(priceDealDetailDTO);
    }


    @PostMapping("/priceDealContrary")
    public Result priceDealContrary(@RequestBody ApplyContraryDTO applyContraryDTO){
        return priceApplyFacade.priceDealContrary(applyContraryDTO);
    }
}
