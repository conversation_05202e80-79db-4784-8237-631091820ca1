package com.navigator.magellan.web.controller.cache;

import com.navigator.admin.facade.AdminIdNameFacade;
import com.navigator.common.dto.Result;
import com.navigator.goods.facade.GoodsIdNameFacade;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Id-Name转换器
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/idName")
public class IdNameController {

    @Resource
    private AdminIdNameFacade adminIdNameFacade;

    @Resource
    private GoodsIdNameFacade goodsIdNameFacade;

    @GetMapping("/refreshCache")
    public Result refreshCache() {
        adminIdNameFacade.refreshCache();
        goodsIdNameFacade.refreshCache();
        return Result.success("刷新缓存成功！");
    }


}
