package com.navigator.magellan.web.service.impl;

import com.alibaba.fastjson.JSON;
import com.navigator.bisiness.enums.ContractSalesTypeEnum;
import com.navigator.bisiness.enums.TTTypeEnum;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.TTHandlerUtil;
import com.navigator.common.util.time.DateTimeUtil;
import com.navigator.magellan.web.service.TradeTicketRemoteService;
import com.navigator.trade.facade.TradeTicketFacade;
import com.navigator.trade.pojo.bo.PriceDetailBO;
import com.navigator.trade.pojo.dto.tradeticket.*;
import com.navigator.trade.pojo.enums.SubmitTypeEnum;
import com.navigator.trade.pojo.vo.TTQueryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class TradeTicketRemoteServiceImpl implements TradeTicketRemoteService {

    @Autowired
    private TradeTicketFacade tradeTicketFacade;

    @Override
    public Result saveTT(OMContractAddTTDTO omContractAddTTDTO) {
        List<TTQueryVO> list = getTtQueryVOS(omContractAddTTDTO, true);
        return Result.success(list);
    }

    @Override
    public Result newSubmitTT(OMContractAddTTDTO omContractAddTTDTO) {
        List<TTQueryVO> list = getTtQueryVOS(omContractAddTTDTO, true);
        return Result.success(list);
    }

    @Override
    public Result updateTT(OMContractAddTTDTO omContractAddTTDTO) {
        List<TTQueryVO> list = getTtQueryVOS(omContractAddTTDTO, false);
        return Result.success(list);
    }

    private List<TTQueryVO> getTtQueryVOS(OMContractAddTTDTO omContractAddTTDTO, boolean saveStatus) {
        List<TTQueryVO> list = new ArrayList<>();
        if (null != omContractAddTTDTO.getSalesType()) {
            for (KeyTradeInfoTTDTO keyTradeInfoTTDTO : omContractAddTTDTO.getTtKernelDTOList()) {
                SalesContractAddTTDTO salesContractAddTTDTO = new SalesContractAddTTDTO();
                BeanUtils.copyProperties(keyTradeInfoTTDTO, salesContractAddTTDTO);
                BeanUtils.copyProperties(omContractAddTTDTO, salesContractAddTTDTO);
                salesContractAddTTDTO.setDomainCode(keyTradeInfoTTDTO.getDomainCode());
                salesContractAddTTDTO.setFutureCode(keyTradeInfoTTDTO.getFutureCode());
                salesContractAddTTDTO.setContractNum(keyTradeInfoTTDTO.getContractNum());
                salesContractAddTTDTO.setWriteOffEndTime(keyTradeInfoTTDTO.getWriteOffEndTime());
                salesContractAddTTDTO.setWriteOffStartTime(keyTradeInfoTTDTO.getWriteOffStartTime());
                salesContractAddTTDTO.setSupplierId(StringUtils.isBlank(omContractAddTTDTO.getSupplierId()) ? null : Integer.parseInt(omContractAddTTDTO.getSupplierId()));
                salesContractAddTTDTO.setCustomerId(StringUtils.isBlank(omContractAddTTDTO.getCustomerId()) ? null : Integer.parseInt(omContractAddTTDTO.getCustomerId()));
                salesContractAddTTDTO.setGoodsCategoryId(StringUtils.isBlank(omContractAddTTDTO.getGoodsCategoryId()) ? null : Integer.parseInt(omContractAddTTDTO.getGoodsCategoryId()));
                salesContractAddTTDTO.setAddedDepositRate(StringUtils.isBlank(keyTradeInfoTTDTO.getAddedDepositRate()) ? 0 : Integer.parseInt(keyTradeInfoTTDTO.getAddedDepositRate()));
                salesContractAddTTDTO.setPayConditionId(StringUtils.isBlank(keyTradeInfoTTDTO.getPayConditionId()) ? null : Integer.parseInt(keyTradeInfoTTDTO.getPayConditionId()));
                salesContractAddTTDTO.setUsage(omContractAddTTDTO.getUsage());
                salesContractAddTTDTO.setPriceEndTime(keyTradeInfoTTDTO.getPriceEndTime());
                salesContractAddTTDTO.setPriceEndType(keyTradeInfoTTDTO.getPriceEndType());
                //转换价格数据
                PriceDetailBO priceDetailBO = BeanConvertUtils.map(PriceDetailBO.class, keyTradeInfoTTDTO.getPriceDetailDTO());

                //处理
                Result result = getResult(omContractAddTTDTO, salesContractAddTTDTO, priceDetailBO, saveStatus);

                if (result.getCode() == ResultCodeEnum.OK.code()) {
                    List<TTQueryVO> ttQueryVOList = JSON.parseArray(JSON.toJSONString(result.getData()), TTQueryVO.class);
                    list.addAll(ttQueryVOList);
                } else {
                    // 处理异常
                    if (result.getCode() == ResultCodeEnum.TT_MODIFY_NOT_EQUAL.getCode()) {
                        throw new BusinessException(ResultCodeEnum.TT_MODIFY_NOT_EQUAL);
                    }
                    throw new BusinessException(result.getMessage());
                }
            }
        }
        return list;
    }

    @Override
    public Result saveStructurePriceTT(SalesStructurePriceTTDTO salesStructurePriceTTDTO) {
        TTDTO ttdto = new TTDTO();
        salesStructurePriceTTDTO.setStartTime(DateTimeUtil.parseTimeStamp2359(salesStructurePriceTTDTO.getStartTime()));
        ttdto.setSalesStructurePriceTTDTO(salesStructurePriceTTDTO);
        String processorType = TTHandlerUtil.getTTProcessor(ContractSalesTypeEnum.SALES.getValue(),
                TTTypeEnum.STRUCTURE_PRICE.getType(), salesStructurePriceTTDTO.getGoodsCategoryId());
        ttdto.setProcessorType(processorType);
        // TODO 走新的逻辑
        ttdto.setTtType(TTTypeEnum.STRUCTURE_PRICE.getType());
        return tradeTicketFacade.saveTT(ttdto);
    }

    @Override
    public Result submitTTBatch(SubmitTTBatchDTO submitTTBatchDTO) {
        return tradeTicketFacade.submitTTBatch(submitTTBatchDTO);
    }

    @Override
    public Result submitTT(SubmitTTDTO submitTTDTO) {
        log.info("SalesContractAddTTDTO:{}", JSON.toJSONString(submitTTDTO.getCreateTradeTicketDTO()));
        return tradeTicketFacade.submitTT(submitTTDTO);
    }


    private Result getResult(@RequestBody @Valid OMContractAddTTDTO omContractAddTTDTO, SalesContractAddTTDTO salesContractAddTTDTO, PriceDetailBO priceDetailBO, boolean saveStatus) {
        TTDTO ttdto = new TTDTO();
        ttdto.setSalesContractAddTTDTO(salesContractAddTTDTO);
        ttdto.setPriceDetailBO(priceDetailBO);
        ttdto.setBuCode(salesContractAddTTDTO.getBuCode());
        ttdto.setSalesType(salesContractAddTTDTO.getSalesType());

        String processorType;
        // 所有的复制保存都设置为新TT
        if (omContractAddTTDTO.getSubmitType() == SubmitTypeEnum.COPY_SAVE.getValue()) {
            processorType = TTHandlerUtil.getTTProcessor(omContractAddTTDTO.getSalesType(), TTTypeEnum.NEW.getType(), null);
        } else {
            // 其他的保存都设置为不同的TT类型
            processorType = TTHandlerUtil.getTTProcessor(omContractAddTTDTO.getSalesType(), omContractAddTTDTO.getType(), null);
        }
        ttdto.setProcessorType(processorType);

        // 设置TT类型
        ttdto.setTtType(omContractAddTTDTO.getType() == null ? TTTypeEnum.NEW.getType() : omContractAddTTDTO.getType());

        // 设置提交类型:保存
        ttdto.setSubmitType(omContractAddTTDTO.getSubmitType());

        return saveStatus ? tradeTicketFacade.saveTT(ttdto) : tradeTicketFacade.updateTT(ttdto);
    }
}
