package com.navigator.magellan.web.controller.goods;

import com.navigator.common.dto.Result;
import com.navigator.goods.facade.GoodsDataFacade;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 商品域数据处理 Controller
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-17
 */
@RestController
@RequestMapping("/goodsData")
public class GoodsDataController {

    @Resource
    private GoodsDataFacade goodsDataFacade;

    /**
     * 导入基础数据
     *
     * @param file
     * @return
     */
    @PostMapping("/doImport")
    Result processHistoryData(@RequestParam(value = "file") MultipartFile file,
                              @RequestParam(value = "processHistory", required = false) Boolean processHistory,
                              @RequestParam(value = "rangeList", required = false) List<String> rangeList) {
        return goodsDataFacade.doImport(file, processHistory, rangeList);
    }
}
