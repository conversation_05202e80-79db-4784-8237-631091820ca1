package com.navigator.magellan.web.controller.delivery;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.navigator.admin.facade.FileProcessFacade;
import com.navigator.admin.pojo.entity.FileInfoEntity;
import com.navigator.bisiness.enums.GoodsCategoryEnum;
import com.navigator.bisiness.enums.ModuleTypeEnum;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.QueryDTO;
import com.navigator.common.dto.Result;
import com.navigator.common.enums.FilePathType;
import com.navigator.common.enums.ResultCodeEnum;
import com.navigator.common.exception.BusinessException;
import com.navigator.common.util.BeanConvertUtils;
import com.navigator.common.util.EasyPoiUtils;
import com.navigator.common.util.file.AzureBlobUtil;
import com.navigator.common.util.html2pdf.FilePathUtil;
import com.navigator.delivery.facade.DeliveryApplyFacade;
import com.navigator.delivery.pojo.dto.*;
import com.navigator.delivery.pojo.entity.DeliveryApplyDriverLogEntity;
import com.navigator.delivery.pojo.entity.DeliveryApplyVOEntity;
import com.navigator.delivery.pojo.qo.DeliveryApplyQO;
import com.navigator.delivery.pojo.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 提货申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-17
 */
@Api(tags = "提货申请")
@RestController
@RequiredArgsConstructor
@RequestMapping("/deliveryApply")
public class DeliveryApplyController {
    private final DeliveryApplyFacade deliveryApplyFacade;
    private final FileProcessFacade fileProcessFacade;
    private final AzureBlobUtil azureBlobUtil;
    private final HttpServletResponse response;

    @ApiOperation("提货委托申请列表")
    @PostMapping("/getDeliveryApplyList")
    public Result getDeliveryApplyList(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        applyQO.setTriggerSys(SystemEnum.MAGELLAN.getValue());
        applyQO.setThirdSys(SystemEnum.LKG.getValue());
        return deliveryApplyFacade.getDeliveryApplyList(queryDTO);
    }

    @ApiOperation("查询申请提货详情")
    @GetMapping("/getDeliveryApplyDetailById")
    public Result<DeliveryApplyDetailVO> getDeliveryApplyDetailById(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getDeliveryApplyDetailById(applyId, SystemEnum.MAGELLAN.getName());
    }

    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 Start
    @ApiOperation("查询司机输入记录")
    @GetMapping("/getDriverInputRecord")
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverInputRecord(
            @ApiParam("客户id") @RequestParam("customerId") Integer customerId,
            @ApiParam("品类id") @RequestParam("goodsCategoryId") Integer goodsCategoryId) {
        return deliveryApplyFacade.getDriverInputRecord(customerId, goodsCategoryId);
    }

    @ApiOperation("查询司机输入记录")
    @PostMapping("/getDriverRecordByCondition")
    public Result<List<DeliveryApplyDriverLogEntity>> getDriverRecordByCondition(@RequestBody DeliveryApplyDriverLogDTO applyDriverLogDTO) {
        return deliveryApplyFacade.getDriverRecordByCondition(applyDriverLogDTO);
    }
    // 1002481 case-提货功能优化 Author: Mr 2024-04-28 End

    @ApiOperation("查询申请单附件")
    @GetMapping("/getFileListByApplyId")
    public Result<List<DeliveryApplyFileVO>> getFileListByApplyId(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getFileListByApplyId(applyId);
    }

    @ApiOperation("查询申请单审核记录")
    @GetMapping("/getAuditRecordListByApplyId")
    public Result<List<DeliveryApplyApprovalVO>> getAuditRecordListByApplyId(@ApiParam("申请单id") @RequestParam("applyId") Integer applyId) {
        return deliveryApplyFacade.getAuditRecordListByApplyId(applyId);
    }

    @ApiOperation("查询申请单操作记录")
    @GetMapping("/getOperateRecordListByApplyCode")
    public Result<List<DeliveryApplyOperationVO>> getOperateRecordListByApplyCode(@ApiParam("申请单code") @RequestParam("applyCode") String applyCode) {
        return deliveryApplyFacade.getOperateRecordListByApplyCode(applyCode);
    }

    @ApiOperation("批量录入申请单开单状态")
    @GetMapping("/inputDeliveryApplyBillStatus")
    public Result<Boolean> inputDeliveryApplyBillStatus(
            @ApiParam("申请单id集合") @RequestParam("applyIds") List<Integer> applyIds,
            @ApiParam("开单状态") @RequestParam("billStatus") Integer billStatus) {
        return deliveryApplyFacade.inputDeliveryApplyBillStatus(applyIds, billStatus);
    }

    @ApiOperation("审核提货申请")
    @PostMapping("/auditDeliveryApply")
    public Result<Boolean> auditDeliveryApply(@RequestBody DeliveryApplyApprovalDTO deliveryApplyApprovalDTO) {
        return deliveryApplyFacade.auditDeliveryApply(deliveryApplyApprovalDTO);
    }

    @ApiOperation("批量审核通过")
    @GetMapping("/batchAuditPassDeliveryApply")
    public Result<Boolean> batchAuditPassDeliveryApply(@ApiParam("申请单id集合") @RequestParam("applyIds") List<Integer> applyIds) {
        return deliveryApplyFacade.batchAuditPassDeliveryApply(applyIds);
    }

    @ApiOperation("批量审核驳回")
    @GetMapping("/batchAuditRejectDeliveryApply")
    public Result<Boolean> batchAuditRejectDeliveryApply(@ApiParam("申请单id集合") @RequestParam("applyIds") List<Integer> applyIds) {
        return deliveryApplyFacade.batchAuditRejectDeliveryApply(applyIds);
    }

    @ApiOperation("作废提货申请单")
    @PostMapping("/applyInvalidDeliveryApply")
    public Result<Boolean> applyInvalidDeliveryApply(@RequestBody DeliveryApplyInvalidDTO applyInvalidDTO) {
        applyInvalidDTO.setTriggerSys(SystemEnum.MAGELLAN.getValue());
        return deliveryApplyFacade.applyInvalidDeliveryApply(applyInvalidDTO);
    }

    @ApiOperation("重新分配合同")
    @PostMapping("/reAssignContract")
    public Result<Boolean> reAssignContract(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO) {
        return deliveryApplyFacade.reAssignContract(assignContractDTO);
    }

    @ApiOperation("移除分配合同")
    @GetMapping("/removeAssignContract")
    public Result<Boolean> removeAssignContract(@RequestParam("applyId") Integer applyId,
                                                @RequestParam("contractId") Integer contractId) {
        return deliveryApplyFacade.removeAssignContract(applyId, contractId);
    }

    @ApiOperation("获取重新分配的合同")
    @PostMapping("/getReAssignContractList")
    public Result<DeliveryApplyAssignContractVO> getReAssignContractList(@RequestBody DeliveryApplyAssignContractDTO assignContractDTO) {
        return deliveryApplyFacade.getReAssignContractList(assignContractDTO);
    }

    @ApiOperation("下载提货申请单")
    @PostMapping("/downloadDeliveryApply")
    public void downloadDeliveryApply(@RequestBody QueryDTO<DeliveryApplyQO> queryDTO) {

        Result result;
        DeliveryApplyQO applyQO = queryDTO.getCondition();
        List<Integer> applyIds = applyQO.getApplyIds();
        if (CollectionUtils.isEmpty(applyIds)) {
            applyQO.setTriggerSys(SystemEnum.MAGELLAN.getValue());
            // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start
            queryDTO.setPageSize(-1);
            // BUGFIX：case-1002595 提货委托下载分配详情不完整 Author: Mr 2024-05-27 Start
            result = deliveryApplyFacade.getDeliveryApplyList(queryDTO);
        } else {
            result = deliveryApplyFacade.getDeliveryApplyListByIds(applyIds);
        }

        if (!result.isSuccess()) {
            throw new BusinessException("无数据可导出！");
        }
        List<DeliveryApplyVOEntity> applyVOEntityList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyVOEntity.class);

        applyVOEntityList.forEach(applyVOEntity -> {
            // 导出豆油不展示包装品类
            if (applyVOEntity.getGoodsCategoryId().equals(GoodsCategoryEnum.OSM_OIL.getValue())) {
                applyVOEntity.setGoodsSpec(null)
                        .setGoodsPackage(null);
            }
        });

        // 将DeliveryApplyVOEntity转换为DeliveryApplyMagellanExportDTO
        List<DeliveryApplyMagellanExportDTO> deliveryApplyMagellanExportDTOList = BeanConvertUtils.convert2List(DeliveryApplyMagellanExportDTO.class, applyVOEntityList);

        if (CollectionUtil.isEmpty(deliveryApplyMagellanExportDTOList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "MagellanDeliveryApply" + DateUtil.format(DateUtil.date(), "MMddHHmmss");

        EasyPoiUtils.exportExcel(deliveryApplyMagellanExportDTOList, null, "MagellanDeliveryApply", DeliveryApplyMagellanExportDTO.class, fileName, response);
    }

    @ApiOperation("下载RPA提货申请单")
    @PostMapping("/downloadRpaTemplateFile")
    public void downloadRpaTemplateFile() {
        Result result = deliveryApplyFacade.getRpaDeliveryApplyList();
        if (!result.isSuccess()) {
            throw new BusinessException("无数据可导出！");
        }

        List<DeliveryApplyRpaExportDTO> rpaExportDTOList = JSON.parseArray(JSON.toJSONString(result.getData()), DeliveryApplyRpaExportDTO.class);

        if (CollectionUtil.isEmpty(rpaExportDTOList)) {
            throw new BusinessException("无数据可导出！");
        }
        String fileName = "RpaDeliveryApply" + DateUtil.format(DateUtil.date(), "MMddHHmmss");

        EasyPoiUtils.exportExcel(rpaExportDTOList, null, "RpaDeliveryApply", DeliveryApplyRpaExportDTO.class, fileName, response);

    }

    @GetMapping("/downloadDeliveryApplyZip")
    public HttpServletResponse downloadDeliveryApplyZip(@RequestParam(value = "fileIdList") List<Integer> fileIdList,
                                                        @RequestParam("applyId") Integer applyId,
                                                        HttpServletResponse response) {
        String zipPath = FilePathUtil.getCommonFilePath(ModuleTypeEnum.DELIVERY, FilePathType.ZIP, applyId.toString());
        zipPath = zipPath + "deliveryApply" + DateUtil.format(DateUtil.date(), "MMddHHmmss") + ".zip";
        Result fileResult = fileProcessFacade.getFileListByIds(fileIdList);
        List<FileInfoEntity> fileInfoEntityList = JSON.parseArray(JSON.toJSONString(fileResult.getData()), FileInfoEntity.class);
        if (CollectionUtils.isEmpty(fileInfoEntityList)) {
            throw new BusinessException(ResultCodeEnum.DOWNLOAD_FAILED);
        }
        List<String> filePathList = new ArrayList<>(fileInfoEntityList.size());
        fileInfoEntityList.forEach(fileInfoEntity -> {
            filePathList.add(fileInfoEntity.getPath());
        });
        azureBlobUtil.downloadZip(filePathList, zipPath, response);
        return response;
    }

}
