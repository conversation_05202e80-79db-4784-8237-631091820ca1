package com.navigator.magellan.web.controller;

import com.navigator.admin.facade.magellan.MenuFacade;
import com.navigator.admin.pojo.dto.MenuDTO;
import com.navigator.admin.pojo.dto.RoleDTO;
import com.navigator.bisiness.enums.SystemEnum;
import com.navigator.common.dto.Result;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/menu")
public class MenuController {

    @Autowired
    private MenuFacade menuFacade;

    @PostMapping("/getMenusByEmploy")
    public Result getMenusByEmploy(@RequestBody MenuDTO menuDTO) {
        menuDTO.setSystemId(String.valueOf(SystemEnum.MAGELLAN.getValue()));
        return menuFacade.getMenusByEmploy(menuDTO);
    }

    @PostMapping("/getMenuByRoleId")
    public Result getMenuByRoleId(@RequestBody RoleDTO roleDTO) {
        return menuFacade.getMenuByRoleId(roleDTO);
    }

    @PostMapping("/saveRoleMenu")
    public Result saveRoleMenu(@RequestBody MenuDTO menuDTO) {
        return menuFacade.saveRoleMenu(menuDTO);
    }

    /**
     * 添加角色菜单权限
     * <p>
     * menuIdList 菜单id列表
     * roleDefIdList 虚角色列表
     *
     * @return
     */
    @PostMapping("/addRoleMenu")
    public Result addRoleMenu(@RequestBody MenuDTO menuDTO) {
        return menuFacade.addRoleMenu(menuDTO);
    }
}
